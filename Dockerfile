FROM golang:alpine as builder

WORKDIR /build

ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn/,direct

#RUN apk update --no-cache \
#    && apk upgrade \
#    && apk add --no-cache bash \
#            bash-doc \
#            bash-completion \
#    && apk add --no-cache tzdata \
#    && rm -rf /var/cache/apk/*

COPY . .

RUN go build -o main ./main.go

FROM alpine:latest as prod

# TODO(cqf): 优化配置参数
ENV config ./config/test.ini

#RUN apk update --no-cache \
#    && apk upgrade

RUN  apk add -q --no-cache libgcc tini curl


RUN  mkdir -pv /data/tsf
RUN  mkdir -pv /data/tsf/config
RUN  mkdir -pv /data/tsf/cmd

COPY --from=builder /build/main /data/tsf/cmd
COPY --from=builder /build/config/test.ini  /data/tsf/cmd/config/test.ini
COPY --from=builder /build/config/apiclient_key.pem /data/tsf/cmd/config/apiclient_key.pem

WORKDIR /data/tsf/cmd

ENTRYPOINT ["tini", "--"]


CMD  ["./main"]
