package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strings"

	"blind_box/pkg/utils"
)

func main() {
	var (
		merchantCode string
		merchantName string
		ipWhitelist  string
		skuWhitelist string
		output       string
		showHelp     bool
	)

	flag.StringVar(&merchantCode, "code", "", "商户编码（必填）")
	flag.StringVar(&merchantName, "name", "", "商户名称（必填）")
	flag.StringVar(&ipWhitelist, "ip", "", "IP白名单，逗号分隔")
	flag.StringVar(&skuWhitelist, "sku", "", "SKU白名单，逗号分隔的数字")
	flag.StringVar(&output, "output", "", "输出文件路径")
	flag.BoolVar(&showHelp, "help", false, "显示帮助信息")

	flag.Parse()

	if showHelp || merchantCode == "" || merchantName == "" {
		fmt.Println("OpenAPI 商户配置生成工具")
		fmt.Println()
		fmt.Println("使用方法:")
		fmt.Println("  go run generate_merchant_config.go -code=<商户编码> -name=<商户名称> [options]")
		fmt.Println()
		fmt.Println("选项:")
		fmt.Println("  -code     商户编码（必填）")
		fmt.Println("  -name     商户名称（必填）")
		fmt.Println("  -ip       IP白名单，逗号分隔（可选）")
		fmt.Println("  -sku      SKU白名单，逗号分隔的数字（可选）")
		fmt.Println("  -output   输出文件路径（可选，默认输出到控制台）")
		fmt.Println("  -help     显示帮助信息")
		fmt.Println()
		fmt.Println("示例:")
		fmt.Println("  go run generate_merchant_config.go -code=TEST001 -name=\"测试商户\"")
		fmt.Println("  go run generate_merchant_config.go -code=TEST001 -name=\"测试商户\" -ip=\"***********/24\" -output=config.json")

		if merchantCode == "" || merchantName == "" {
			os.Exit(1)
		}
		return
	}

	// 解析IP白名单
	var ips []string
	if ipWhitelist != "" {
		for _, ip := range strings.Split(ipWhitelist, ",") {
			ip = strings.TrimSpace(ip)
			if ip != "" {
				ips = append(ips, ip)
			}
		}
	}

	// 解析SKU白名单
	var skus []uint64
	if skuWhitelist != "" {
		for _, skuStr := range strings.Split(skuWhitelist, ",") {
			skuStr = strings.TrimSpace(skuStr)
			if skuStr != "" {
				var sku uint64
				fmt.Sscanf(skuStr, "%d", &sku)
				if sku > 0 {
					skus = append(skus, sku)
				}
			}
		}
	}

	// 生成配置
	config := utils.GenerateMerchantConfig(
		merchantCode,
		merchantName,
		"https://api.example.com", // 可以根据需要修改
		ips,
		skus,
	)

	// 导出为JSON
	jsonData, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		fmt.Printf("生成配置失败: %v\n", err)
		os.Exit(1)
	}

	// 输出结果
	if output != "" {
		err = os.WriteFile(output, jsonData, 0644)
		if err != nil {
			fmt.Printf("保存文件失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("配置已保存到: %s\n", output)

		// 同时显示关键信息
		fmt.Println("\n=== 重要信息（请妥善保存）===")
		fmt.Printf("AppID: %s\n", config.Authentication.AppID)
		fmt.Printf("AppSecret: %s\n", config.Authentication.AppSecret)
		fmt.Println("================================")
	} else {
		// 输出到控制台
		fmt.Println("生成的配置:")
		fmt.Println(string(jsonData))

		fmt.Println("\n=== 重要信息（请妥善保存）===")
		fmt.Printf("AppID: %s\n", config.Authentication.AppID)
		fmt.Printf("AppSecret: %s\n", config.Authentication.AppSecret)
		fmt.Println("================================")
	}
}
