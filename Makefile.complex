SHELL:=/bin/sh
.PHONY: build start stop restart clean version graceful-restart health-check wait-ready graceful-stop

# ==============================================================================
# 平滑重启方案说明
# ==============================================================================
# graceful-restart: 零停机时间的平滑重启
#   - 先启动新实例，等待就绪后再停止旧实例
#   - 自动回滚机制，新实例启动失败时保持旧实例运行
#   - 支持健康检查确保服务可用性
#
# graceful-stop: 优雅停止服务
#   - 发送SIGTERM信号，等待应用自然关闭
#   - 超时保护，避免无限等待
#   - 失败时自动降级为强制停止
#
# restart: 传统重启方式（有停机时间）
# stop: 强制停止（保留原有功能）
# ==============================================================================

# 服务名
SERVICE_NAME := blind_box
# 二进制文件名
BINARY_NAME := $(SERVICE_NAME)
# 主程序文件
MAIN_FILE := main.go
# nohup运行日志文件
LOG_FILE := $(SERVICE_NAME)_$$(date +%m%d%H%M%S).log
# PID文件
PID_FILE := $(SERVICE_NAME).pid
# 新实例PID文件（用于平滑重启）
NEW_PID_FILE := $(SERVICE_NAME)_new.pid
# 健康检查URL
HEALTH_CHECK_URL := http://127.0.0.1:8080/blind_box/version
# 健康检查最大重试次数
HEALTH_CHECK_MAX_RETRIES := 30
# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL := 1
# 优雅停止超时时间（秒）
GRACEFUL_STOP_TIMEOUT := 15

# 检查可用内存并设置GOGC（兼容Linux和macOS）
MEM_AVAILABLE := $(shell \
    if command -v free >/dev/null 2>&1; then \
        free -g | awk '/^Mem:/ {print $$7}'; \
    elif command -v vm_stat >/dev/null 2>&1; then \
        vm_stat | awk '/Pages free/ {gsub(/\./, "", $$3); free_mb=int($$3 * 16384 / 1024 / 1024); print int(free_mb / 1024)}'; \
    else \
        echo 4; \
    fi)

# 在低内存环境下使用较小的GOGC值
ifeq ($(shell [ "$(MEM_AVAILABLE)" != "" ] && [ $(MEM_AVAILABLE) -lt 1 ] && echo true), true)
    GOGC = 20
else
    GOGC =
endif

version:
	@echo "VERSION_INFO: $$(date +%Y-%m-%dT%H:%M:%S%z)"

# 构建二进制文件
build: version
	@echo "Building $(SERVICE_NAME)..."
	@echo "mem available: $(MEM_AVAILABLE)G"
	@echo "GOGC=$(GOGC)"
	@VERSION_INFO=$$(date +%Y-%m-%dT%H:%M:%S%z) && GIT_COMMIT=$$(git rev-parse --short HEAD) && \
	if [ -n "$(GOGC)" ]; then \
		GOGC=$(GOGC) go build -ldflags "-X blind_box/router.buildTime=$$VERSION_INFO -X blind_box/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	else \
		go build -ldflags "-X blind_box/router.buildTime=$$VERSION_INFO -X blind_box/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	fi
# 启动服务
start: build
	@echo "Starting $(SERVICE_NAME)..."
	@nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(PID_FILE)
	@echo "$(SERVICE_NAME) started with PID $$(cat $(PID_FILE))"

# @sleep 20
# @if ! curl -sSf $(HEALTH_CHECK_URL) > /dev/null; then \
# 	echo "$(SERVICE_NAME) health check failed. Stopping service..."; \
# 	$(MAKE) stop; \
# 	exit 1; \
# else \
# 	echo "$(SERVICE_NAME) is active."; \
# fi

# 停止服务（强制停止，保留原有功能）
stop:
	@echo "Stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		kill -9 `cat $(PID_FILE)`; \
		rm $(PID_FILE); \
		echo "$(SERVICE_NAME) stopped."; \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 优雅停止服务
graceful-stop:
	@echo "Gracefully stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		PID=`cat $(PID_FILE)`; \
		echo "Sending SIGTERM to process $$PID..."; \
		kill -TERM $$PID; \
		echo "Waiting for graceful shutdown (max $(GRACEFUL_STOP_TIMEOUT)s)..."; \
		for i in $$(seq 1 $(GRACEFUL_STOP_TIMEOUT)); do \
			if ! ps -p $$PID > /dev/null 2>&1; then \
				echo "$(SERVICE_NAME) stopped gracefully."; \
				rm -f $(PID_FILE); \
				exit 0; \
			fi; \
			sleep 1; \
		done; \
		echo "Graceful shutdown timeout, force killing..."; \
		kill -9 $$PID; \
		rm -f $(PID_FILE); \
		echo "$(SERVICE_NAME) force stopped."; \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 重启服务（原有方式，非平滑）
restart: stop start

# 健康检查
health-check:
	@echo "Checking $(SERVICE_NAME) health..."
	@if curl -sSf $(HEALTH_CHECK_URL) > /dev/null 2>&1; then \
		echo "$(SERVICE_NAME) is healthy."; \
		exit 0; \
	else \
		echo "$(SERVICE_NAME) health check failed."; \
		exit 1; \
	fi

# 等待服务就绪
wait-ready:
	@echo "Waiting for $(SERVICE_NAME) to be ready..."
	@for i in $$(seq 1 $(HEALTH_CHECK_MAX_RETRIES)); do \
		if curl -sSf $(HEALTH_CHECK_URL) > /dev/null 2>&1; then \
			echo "$(SERVICE_NAME) is ready after $${i}s."; \
			exit 0; \
		fi; \
		echo "Attempt $$i/$(HEALTH_CHECK_MAX_RETRIES): $(SERVICE_NAME) not ready, waiting $(HEALTH_CHECK_INTERVAL)s..."; \
		sleep $(HEALTH_CHECK_INTERVAL); \
	done; \
	echo "$(SERVICE_NAME) failed to become ready after $(HEALTH_CHECK_MAX_RETRIES)s."; \
	exit 1

# 平滑重启（零停机时间）
graceful-restart: build
	@echo "Starting graceful restart of $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		OLD_PID=`cat $(PID_FILE)`; \
		echo "Current $(SERVICE_NAME) running with PID: $$OLD_PID"; \
		echo "Starting new $(SERVICE_NAME) instance..."; \
		nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(NEW_PID_FILE); \
		NEW_PID=`cat $(NEW_PID_FILE)`; \
		echo "New $(SERVICE_NAME) started with PID: $$NEW_PID"; \
		echo "Waiting for new instance to be ready..."; \
		if $(MAKE) wait-ready; then \
			echo "New instance is ready, stopping old instance..."; \
			echo "Sending SIGTERM to old process $$OLD_PID..."; \
			kill -TERM $$OLD_PID; \
			echo "Waiting for old instance to stop gracefully (max $(GRACEFUL_STOP_TIMEOUT)s)..."; \
			for i in $$(seq 1 $(GRACEFUL_STOP_TIMEOUT)); do \
				if ! ps -p $$OLD_PID > /dev/null 2>&1; then \
					echo "Old instance stopped gracefully."; \
					break; \
				fi; \
				sleep 1; \
			done; \
			if ps -p $$OLD_PID > /dev/null 2>&1; then \
				echo "Old instance didn't stop gracefully, force killing..."; \
				kill -9 $$OLD_PID; \
			fi; \
			mv $(NEW_PID_FILE) $(PID_FILE); \
			echo "Graceful restart completed successfully!"; \
		else \
			echo "New instance failed to start properly, rolling back..."; \
			NEW_PID=`cat $(NEW_PID_FILE)`; \
			kill -9 $$NEW_PID; \
			rm -f $(NEW_PID_FILE); \
			echo "Rollback completed, old instance still running."; \
			exit 1; \
		fi; \
	else \
		echo "No running instance found, starting normally..."; \
		$(MAKE) start; \
	fi

clean:
	@pwd
	@rm -rf ./data/logs
	@rm -f $(BINARY_NAME) $(PID_FILE) $(NEW_PID_FILE) *.log
	@echo "Cleanup done."




