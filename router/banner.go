package router

import (
	"blind_box/app/handler/market"
	mw "blind_box/app/middleware"
	"github.com/gin-gonic/gin"
)

func loadMarketApi(e *gin.RouterGroup) {
	e.GET("/market/banner", market.BannerList)
}

func loadAdminMarketApi(e *gin.RouterGroup) {
	bn := e.Group("/admin/market/banner", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		bn.POST("/create", market.AdminBannerCreate)
		bn.POST("/update", market.AdminBannerUpdate)
		bn.GET("/list", market.AdminBannerList)
		bn.GET("/detail", market.AdminBannerDetail)
		bn.POST("/delete", market.AdminBannerDelete)
	}

}
