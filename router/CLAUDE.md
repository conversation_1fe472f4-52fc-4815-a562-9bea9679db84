# router 目录规范文档

本文档记录 `router` 目录下路由配置的代码规范和最佳实践。

## 目录结构

```
router/
├── router.go        # 路由注册核心文件
├── admin.go        # 纯后台管理路由
├── user.go         # 用户相关路由
├── goods.go        # 商品相关路由
├── order.go        # 订单相关路由
├── box.go          # 盲盒相关路由
├── activity.go     # 活动相关路由
├── coupon.go       # 优惠券相关路由
├── card.go         # 卡片相关路由
├── tide.go         # 潮气值相关路由
├── config.go       # 配置相关路由
├── banner.go       # 横幅相关路由
├── resource.go     # 资源管理路由
├── openapi.go      # 开放API路由
├── external.go     # 外部接口路由
└── script.go       # 脚本相关路由
```

## 核心设计规范

### 路由注册机制

#### 1. 路由函数定义
```go
// 统一的路由函数签名
type routerFun func(*gin.RouterGroup)

// 路由函数列表
var routerList = []routerFun{}
```

#### 2. 初始化注册
```go
func init() {
    regRouter(
        loadAdminApi,                    // 后台管理基础路由
        loadAdminUserApi, loadUserApi,   // 用户模块路由
        loadAdminGoodsApi, loadGoodsApi, // 商品模块路由
        loadOrderApi, loadAdminOrderApi, // 订单模块路由
        // ... 其他模块
        loadOpenAPIRouter,               // OpenAPI路由
    )
}
```

#### 3. 路由加载
```go
func LoadRouter(r *gin.Engine) *gin.RouterGroup {
    // 全局中间件
    r.Use(middleware.CORSMiddleware())
    
    // 创建路由组（带应用前缀）
    ginRouter := r.Group(config.AppCfg.AppName)
    ginRouter.Use(middleware.RecoveryWithWriter())
    
    // 加载所有注册的路由
    for _, routerF := range routerList {
        routerF(ginRouter)
    }
    
    return ginRouter
}
```

## 路由组织规范

### 文件命名规范
- **业务模块**: `模块名.go` (如 `user.go`, `order.go`, `box.go`)
- **通用功能**: `功能名.go` (如 `router.go`, `external.go`)

### 函数命名规范
- **小程序端**: `load[Module]Api` (如 `loadUserApi`, `loadBoxApi`)
- **管理后台**: `loadAdmin[Module]Api` (如 `loadAdminUserApi`, `loadAdminBoxApi`)
- **开放API**: `loadOpenAPIRouter`
- **其他接口**: `load[Function]Api` (如 `loadExternalApi`, `loadScriptApi`)

### 路由路径规范

#### 小程序端路由
```go
func loadUserApi(e *gin.RouterGroup) {
    // 公开接口（无需登录）
    e.POST("/user/wechat/login", user.UserWechatLogin)
    e.GET("/area/list", admin.AdminAreaList)
    
    // 可选登录接口
    e.GET("/user/sign/config/info", mw.CheckUser(), user.GetUserSignConfigInfo)
    
    // 必须登录接口组
    au := e.Group("/user", mw.CheckUserLogin(), mw.UserOperationLog())
    {
        au.POST("/logout", user.UserLogout)
        au.GET("/basic", user.UserBasic)
        au.POST("/edit", user.UserEdit)
    }
}
```

#### 管理后台路由
```go
func loadAdminUserApi(e *gin.RouterGroup) {
    // 管理后台路由组（需要登录、权限验证、操作日志）
    au := e.Group("/admin/user", 
        mw.CheckAccountLogin(),     // 登录验证
        mw.CheckAccountAuth(),      // 权限验证
        mw.AdminOperationLog(),     // 操作日志
    )
    {
        au.GET("/list", user.AdminUserList)
        au.GET("/info", user.AdminUserInfo)
        au.POST("/operate", user.AdminOperateUser)
    }
}
```

#### OpenAPI 路由
```go
func loadOpenAPIRouter(e *gin.RouterGroup) {
    // OpenAPI路由组
    openapiGroup := e.Group("/openapi/v1")
    
    // 添加认证和限流中间件
    openapiGroup.Use(
        middleware.OpenAPIAuth(),       // API认证
        middleware.OpenAPIRateLimit(),   // 速率限制
    )
    
    // 扫码支付接口
    {
        openapiGroup.POST("/scan-orders", openapi.CreateScanOrder)
        openapiGroup.POST("/scan-orders/query", openapi.QueryScanOrder)
    }
}
```

## 中间件使用规范

### 通用中间件

#### 全局中间件
```go
// 在 LoadRouter 中配置
r.Use(middleware.CORSMiddleware())        // CORS处理
ginRouter.Use(middleware.RecoveryWithWriter()) // 恢复中间件
```

#### 路由组中间件
```go
// 小程序端
mw.CheckUser()         // 可选用户认证
mw.CheckUserLogin()    // 必须用户登录
mw.UserOperationLog()  // 用户操作日志

// 管理后台
mw.CheckAccountLogin() // 管理员登录验证
mw.CheckAccountAuth()  // 管理员权限验证
mw.AdminOperationLog() // 管理员操作日志

// OpenAPI
middleware.OpenAPIAuth()      // API签名认证
middleware.OpenAPIRateLimit()  // API速率限制

// 特定功能
mw.PickupCodeRateLimit()      // 取货码速率限制
```

### 中间件使用顺序
```go
// 推荐顺序
e.Group("/path",
    认证中间件,      // 1. 认证
    权限中间件,      // 2. 权限
    限流中间件,      // 3. 限流
    日志中间件,      // 4. 日志
)
```

## 路由分组规范

### 按功能分组
```go
func loadOrderApi(e *gin.RouterGroup) {
    // 订单相关接口
    od := e.Group("/order")
    {
        // 公开接口
        od.GET("/status", order.OrderStatus)
        
        // 需要登录的接口
        od.GET("/detail", mw.CheckUserLogin(), order.OrderDetail)
        od.GET("/list", mw.CheckUserLogin(), order.OrderList)
        
        // 取货码相关（带限流）
        od.POST("/pickup-code/generate", 
            mw.CheckUserLogin(), 
            mw.PickupCodeRateLimit(), 
            order.GeneratePickupCode)
        
        // 退款相关
        od.POST("/refund", mw.CheckUserLogin(), order.RefundCreate)
        od.GET("/refund/detail", mw.CheckUserLogin(), order.RefundDetail)
    }
}
```

### 按权限分组
```go
func loadBoxApi(e *gin.RouterGroup) {
    // 公开接口组（可选登录）
    bo := e.Group("/api/box", mw.CheckUser(), mw.UserOperationLog())
    {
        bo.GET("/search", box.BoxSearch)
        bo.GET("/list", box.BoxList)
        bo.GET("/active", box.BoxActive)
        
        // 需要登录的子功能
        bo.GET("/queue", mw.CheckUserLogin(), box.BoxQueue)
        bo.POST("/queue", mw.CheckUserLogin(), box.BoxQueuePush)
    }
}
```

## RESTful 规范

### HTTP 方法使用
- **GET**: 查询操作（list, detail, info, status）
- **POST**: 创建、操作类（create, add, login, operate）
- **PUT**: 更新操作（update）
- **DELETE**: 删除操作（delete, del）

### 路径设计
```go
// 资源列表
GET    /user/list           // 用户列表
GET    /order/list          // 订单列表

// 资源详情
GET    /user/info           // 用户信息
GET    /order/detail        // 订单详情

// 创建资源
POST   /user/create         // 创建用户
POST   /order/create        // 创建订单

// 更新资源
POST   /user/update         // 更新用户（兼容性）
PUT    /user/update         // 更新用户（RESTful）

// 删除资源
POST   /user/del            // 删除用户
DELETE /user/:id            // 删除用户（RESTful）

// 操作类
POST   /user/operate        // 批量操作
POST   /order/cancel        // 取消订单
POST   /order/refund        // 申请退款
```

## 路由注释规范

### 路由注释格式
```go
func loadUserApi(e *gin.RouterGroup) {
    // 用户登录相关
    e.POST("/user/wechat/login", user.UserWechatLogin)  // 微信登录
    e.POST("/user/login/third", user.UserLoginThird)    // 第三方登录
    
    // 用户信息管理
    au := e.Group("/user", mw.CheckUserLogin())
    {
        au.GET("/basic", user.UserBasic)       // user/basic - 基本信息
        au.POST("/edit", user.UserEdit)        // user/edit - 编辑信息
        au.POST("/edit/pwd", user.UserEditPwd) // user/edit/pwd - 修改密码
    }
}
```

### 文件头注释
```go
/*
* 纯后台管理路由,与业务无关
* 业务后台的路由建议写到对应的业务路由文件中
*/
func loadAdminApi(e *gin.RouterGroup) {
    // ...
}
```

## 路由安全规范

### 认证层级
1. **公开接口**: 无需认证
```go
e.GET("/area/list", handler.AreaList)
```

2. **可选认证**: 登录用户有更多功能
```go
e.GET("/box/list", mw.CheckUser(), handler.BoxList)
```

3. **必须认证**: 必须登录才能访问
```go
e.GET("/user/info", mw.CheckUserLogin(), handler.UserInfo)
```

4. **权限认证**: 需要特定权限
```go
e.POST("/admin/user/del", 
    mw.CheckAccountLogin(), 
    mw.CheckAccountAuth(), 
    handler.AdminDelUser)
```

### 限流保护
```go
// 通用限流
e.Use(middleware.RateLimit())

// 特定接口限流
e.POST("/pickup-code/generate", 
    mw.CheckUserLogin(),
    mw.PickupCodeRateLimit(),  // 1分钟3次，10分钟10次
    handler.GeneratePickupCode)

// OpenAPI限流
openapiGroup.Use(middleware.OpenAPIRateLimit())
```

## 最佳实践

### 1. 模块化组织
- 每个业务模块独立文件
- 管理后台和小程序端分离
- 公共路由集中管理

### 2. 中间件复用
```go
// 定义通用中间件组合
var adminMiddlewares = []gin.HandlerFunc{
    mw.CheckAccountLogin(),
    mw.CheckAccountAuth(),
    mw.AdminOperationLog(),
}

// 使用
au := e.Group("/admin/user", adminMiddlewares...)
```

### 3. 路由版本管理
```go
// API版本控制
v1 := e.Group("/api/v1")
v2 := e.Group("/api/v2")

// OpenAPI版本
openapiV1 := e.Group("/openapi/v1")
```

### 4. 错误处理统一
```go
// 所有路由使用统一的错误处理
func handler(ctx *gin.Context) {
    if err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), data)
}
```

### 5. 路由测试友好
```go
// 提供版本信息接口
func loadVersionApi(e *gin.RouterGroup) {
    e.GET("/version", func(c *gin.Context) {
        c.JSON(200, gin.H{
            "buildTime": buildTime,
            "gitCommit": gitCommit,
        })
    })
}

// 提供性能分析接口（仅开发环境）
func loadProfileApi(e *gin.RouterGroup) {
    pprof.Register(e, "/admin/pprof")
}
```

## 扩展指南

### 添加新模块路由

1. **创建路由文件**
```go
// router/newmodule.go
package router

import (
    "blind_box/app/handler/newmodule"
    mw "blind_box/app/middleware"
    "github.com/gin-gonic/gin"
)
```

2. **定义路由函数**
```go
// 小程序端
func loadNewModuleApi(e *gin.RouterGroup) {
    nm := e.Group("/newmodule")
    {
        nm.GET("/list", handler.List)
    }
}

// 管理后台
func loadAdminNewModuleApi(e *gin.RouterGroup) {
    anm := e.Group("/admin/newmodule",
        mw.CheckAccountLogin(),
        mw.CheckAccountAuth(),
    )
    {
        anm.GET("/list", handler.AdminList)
    }
}
```

3. **注册路由**
```go
// 在 router.go 的 init() 函数中添加
func init() {
    regRouter(
        // ... 现有路由
        loadNewModuleApi,
        loadAdminNewModuleApi,
    )
}
```

## 常见问题

### Q: 何时使用路由分组？
A: 当多个路由共享相同的前缀、中间件或逻辑关联时使用分组。

### Q: 如何处理路由冲突？
A: 
- 静态路由优先于动态路由
- 具体路径优先于通配路径
- 注册顺序影响匹配优先级

### Q: 如何实现路由级别的配置？
A: 使用中间件在路由级别注入配置，或在 Handler 中读取配置。

### Q: 如何处理路由参数？
A: 
- 路径参数: `/user/:id` 使用 `ctx.Param("id")`
- 查询参数: `/user?name=xxx` 使用 `ctx.Query("name")`
- 请求体: 使用 `ctx.ShouldBind(&req)`

## 注意事项

1. **路由注册顺序**: 静态路由应在动态路由之前注册
2. **中间件顺序**: 认证 → 权限 → 限流 → 日志
3. **路径规范**: 使用小写，单词用连字符分隔
4. **版本管理**: 重大变更使用新版本路径
5. **安全考虑**: 敏感操作必须有适当的认证和权限验证
6. **性能优化**: 避免在路由层做重操作，应在 Service 层处理
7. **日志记录**: 关键操作需要操作日志中间件