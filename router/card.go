package router

import (
	"blind_box/app/handler/card"
	mw "blind_box/app/middleware"
	"github.com/gin-gonic/gin"
)

func loadCardApi(e *gin.RouterGroup) {

}

func loadAdminCardApi(e *gin.RouterGroup) {
	conf := e.Group("/admin/box/card/config", mw.CheckAccountLogin())
	{
		conf.POST("/list", card.AdminCardConfigListInfo)          // 道具配置列表 // 1
		conf.POST("/add", card.AdminCardConfigAdd)                // 1
		conf.POST("/update", card.AdminCardConfigUpdate)          // 1
		conf.GET("", card.AdminCardConfigInfo)                    // 道具配置详情 // 1
		conf.POST("/simple/list", card.AdminCardConfigSimpleList) // 道具配置简单列表 // 1
	}

	source := e.Group("/admin/box/card/source", mw.CheckAccountLogin())
	{
		source.POST("/add", card.AdminCardSourceAdd)       // 1
		source.POST("/update", card.AdminCardSourceUpdate) // 1
		source.POST("/list", card.AdminCardSourceList)     // 1
	}

	e.POST("/admin/box/cardSend", mw.CheckAccountLogin(), card.AdminCardSend)        // 道具发放 // 1
	e.POST("/admin/box/userCard", mw.CheckAccountLogin(), card.AdminUserCard)        // 道具明细列表 // 1
	e.POST("/admin/box/userCard/del", mw.CheckAccountLogin(), card.AdminUserCardDel) // 道具作废 // 1
}
