package router

import (
	"blind_box/app/handler/order"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadOrderApi(e *gin.RouterGroup) {
	// 订单相关接口
	od := e.Group("/order")
	{
		od.GET("/status", order.OrderStatus)                      // 订单状态校验
		od.GET("/detail", mw.CheckUserLogin(), order.OrderDetail) // 订单详情
		od.GET("/list", mw.CheckUserLogin(), order.OrderList)     // 订单列表

		od.POST("/cancel", mw.CheckUserLogin(), order.OrderCancel) // 订单取消

		// 取货码相关接口
		od.POST("/pickup-code/generate", mw.CheckUserLogin(), mw.PickupCodeRateLimit(), order.GeneratePickupCode) // 生成取货码（带限流）
		od.POST("/pickup-code/refresh", mw.CheckUserLogin(), mw.PickupCodeRateLimit(), order.RefreshPickupCode)   // 刷新取货码（带限流）
		od.GET("/pickup-code/query", mw.CheckUserLogin(), order.QueryPickupCode)                                  // 查询取货码

		// 退款相关接口
		od.POST("/refund", mw.CheckUserLogin(), order.RefundCreate)        // 申请退款
		od.GET("/refund/detail", mw.CheckUserLogin(), order.RefundDetail)  // 退款详情
		od.GET("/refund/list", mw.CheckUserLogin(), order.RefundList)      // 退款列表
		od.POST("/refund/cancel", mw.CheckUserLogin(), order.RefundCancel) // 取消退款
	}
}

func loadAdminOrderApi(e *gin.RouterGroup) {
	// 管理后台订单相关路由
	aOrder := e.Group("/admin/order", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aOrder.POST("/cancel", order.AdminOrderCancel) // 管理后台订单取消
		aOrder.GET("/list", order.AdminOrderList)      // 订单列表
		aOrder.GET("/detail", order.AdminOrderDetail)  // 订单详情

		// 发货管理
		aOrder.POST("/delivery", order.AdminOrderDeliveryCreate)   // 创建发货记录
		aOrder.PUT("/delivery", order.AdminOrderDeliveryUpdate)    // 更新发货记录
		aOrder.GET("/delivery", order.AdminOrderDeliveryDetail)    // 获取发货详情（通过订单ID查询参数）
		aOrder.GET("/delivery/list", order.AdminOrderDeliveryList) // 发货记录列表

		// 取货码验证 + 快递单号验证（分离的接口）
		aOrder.POST("/pickup-code/verify", order.AdminPickupCodeVerify)         // 管理后台验证取货码（到店取货）
		aOrder.POST("/tracking-number/verify", order.AdminTrackingNumberVerify) // 管理后台验证快递单号（快递配送）

		// 订单完成 + 发货状态更新
		aOrder.POST("/complete", order.AdminOrderComplete) // 管理后台完成订单+发货状态更新

		// 退款相关接口
		aOrder.POST("/refund", order.AdminRefundCreate)       // 管理后台发起退款
		aOrder.GET("/refund/list", order.AdminRefundList)     // 管理后台退款列表
		aOrder.GET("/refund/detail", order.AdminRefundDetail) // 管理后台退款详情
		aOrder.POST("/refund/retry", order.AdminRefundRetry)  // 管理后台重试退款
	}
}
