package router

import (
	"blind_box/app/handler/user"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadTideApi(e *gin.RouterGroup) {
	ut := e.Group("/user/tide", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		ut.GET("/task/info", user.GetTideTaskInfo)           // user/tide/task/info 潮气值每日任务信息列表
		ut.GET("/card/info", user.GetTideCardInfo)           // user/tide/card/info 潮气值可兑换道具卡配置
		ut.POST("/get/task/val", user.UserGetTaskVal)        // user/tide/get/task/val 领取完成任务潮气值
		ut.POST("/exchange/card", user.UserTideExchangeCard) // user/tide/exchange/card 使用潮气值兑换道具卡
		ut.GET("/bill", user.GetUserTideBill)                // user/tide/bill 用户潮气值明细账单(近7日)
	}
}

func loadAdminTideApi(e *gin.RouterGroup) {
	at := e.Group("/admin/tide", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		at.GET("/source/list", user.AdminTideSourceList) // admin/tide/source/list 潮气值类型列表
		at.GET("/bill", user.AdminTideBill)              // admin/tide/bill 用户潮气值明细账单
		at.POST("/send", user.AdminTideSend)             // admin/tide/send	 发放潮气值
	}
}
