package router

import (
	"blind_box/app/handler/coupon"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadCouponApi(e *gin.RouterGroup) {
	// 首页弹出优惠券奖励
	e.GET("/coupon/homepage/list", mw.CheckUser(), coupon.HomepageCouponList)

	uc := e.Group("/coupon", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		uc.GET("/homepage/get", coupon.GetHomepageCoupon)                   // coupon/homepage/get  领取首页优惠券
		uc.POST("/user/coupon/list", coupon.UserCouponList)                 // coupon/user/coupon/list 用户优惠券列表
		uc.GET("/range/act/list", coupon.CouponRangeActList)                // coupon/range/act/list 优惠券使用范围-活动列表
		uc.GET("/act/user/available/coupon", coupon.ActUserAvailableCoupon) // coupon/act/user/available/coupon 查询活动-用户可使用的优惠券列表
		uc.POST("/use/coupon", coupon.UseCoupon)                            // coupon/use/coupon 使用优惠券
	}
}

func loadAdminCouponApi(e *gin.RouterGroup) {
	ac := e.Group("/admin/coupon", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		ac.GET("/list", coupon.AdminCouponList)        // admin/coupon/list 优惠券列表
		ac.POST("/set", coupon.AdminSetCoupon)         // admin/coupon/set 设置优惠券
		ac.GET("/detail", coupon.AdminCouponDetail)    // admin/coupon/detail 优惠券详情
		ac.POST("/operate", coupon.AdminOperateCoupon) // admin/coupon/operate 操作优惠券

		ac.GET("/issue/list", coupon.AdminCouponIssueList)                  // admin/coupon/issue/list 优惠券发放列表
		ac.GET("/issue/contain/count", coupon.AdminCountIssueContainCoupon) // admin/coupon/issue/contain/count 统计优惠券在发放中的数量
		ac.GET("/issue/detail", coupon.AdminCouponIssueDetail)              // admin/coupon/issue/detail 优惠券发放详情
		ac.POST("/issue/set", coupon.AdminSetCouponIssue)                   // admin/coupon/issue/set 设置优惠券发放
		ac.POST("/issue/operate", coupon.AdminOperateCouponIssue)           // admin/coupon/issue/operate 操作优惠券发放

		ac.POST("/issue/target/set", coupon.AdminCouponIssueTargetSet)       // admin/coupon/issue/target/set 设置优惠券发放目标
		ac.POST("/issue/target/upload", coupon.AdminCouponIssueTargetUpload) // admin/coupon/issue/target/upload 上传优惠券发放附件
		ac.GET("/issue/target/export", coupon.AdminCouponIssueTargetExport)  // admin/coupon/issue/target/export 导出优惠券发放附件

		ac.GET("/user/list", coupon.AdminCouponUserList)        // admin/coupon/user/list 优惠券领取列表
		ac.POST("/user/operate", coupon.AdminOperateCouponUser) // admin/coupon/user/operate 操作优惠券领取

		ac.GET("/source/list", coupon.AdminCouponSourceList) // admin/coupon/source/list 优惠券来源列表
		ac.POST("/source/set", coupon.AdminSetCouponSource)  // admin/coupon/source/set 设置优惠券来源
	}
}
