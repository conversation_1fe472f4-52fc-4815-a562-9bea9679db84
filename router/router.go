package router

import (
	"blind_box/app/middleware"
	"blind_box/config"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
)

type routerFun func(*gin.RouterGroup)

var routerList = []routerFun{}

var (
	buildTime string
	gitCommit string
)

func init() {
	regRouter(
		loadAdminApi,
		loadAdminUserApi, loadUserApi,
		loadAdminGoodsApi, loadGoodsApi,
		loadVersionApi,
		loadProfileApi,
		loadActApi, loadAdminActApi,
		loadBoxApi, loadAdminBoxApi,
		loadOrderApi, loadAdminOrderApi,
		loadTideApi, loadAdminTideApi,
		loadCouponApi, loadAdminCouponApi,
		loadCardApi, loadAdminCardApi,
		loadAdminResourceApi,
		loadScriptApi,
		loadConfigApi, loadAdminConfigApi,
		loadMarketApi, loadAdminMarketApi,
		loadExternal<PERSON>pi,

		loadOpenAPIRouter,
	)
}

func regRouter(routerFun ...routerFun) {
	routerList = append(routerList, routerFun...)
}

func LoadRouter(r *gin.Engine) *gin.RouterGroup {
	r.Use(middleware.CORSMiddleware())
	ginRouter := r.Group(config.AppCfg.AppName)
	ginRouter.Use(middleware.RecoveryWithWriter())

	for _, routerF := range routerList {
		routerF(ginRouter)
	}

	return ginRouter
}

func loadProfileApi(e *gin.RouterGroup) {
	pprof.Register(e, "/admin/pprof")
}

func loadVersionApi(e *gin.RouterGroup) {
	e.GET("/version", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"buildTime": buildTime,
			"gitCommit": gitCommit,
		})
	})
}
