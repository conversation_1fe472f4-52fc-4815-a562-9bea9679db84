package router

import (
	"blind_box/app/handler/box"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadBoxApi(e *gin.RouterGroup) {
	bo := e.Group("/api/box", mw.CheckUser(), mw.UserOperationLog())
	{
		bo.GET("/search", box.BoxSearch)  // /api/box/search 盒子搜索
		bo.GET("/list", box.BoxList)      // /api/box/list 盒子列表
		bo.GET("/action", box.BoxActions) // /api/box/action 盒子操作列表

		bo.GET("/queue", mw.CheckUserLogin(), box.BoxQueue)      // /api/box/queue 盒子排队查询
		bo.POST("/queue", mw.CheckUserLogin(), box.BoxQueuePush) // /api/box/queue 盒子排队操作

		bo.GET("/bag/all", mw.CheckUserLogin(), box.BoxBagAll)                            // /api/box/bag/all 盒子背包列表
		bo.GET("/trade/list", mw.CheckUserLogin(), box.BoxTradeList)                      // /api/box/trade/list
		bo.GET("/trade/detail", mw.CheckUserLogin(), box.BoxTradeDetail)                  // /api/box/trade/detail
		bo.GET("/card/count", mw.CheckUserLogin(), box.BoxCardCount)                      // /api/box/card/count 道具卡数量
		bo.GET("/card/user/historyList", mw.CheckUserLogin(), box.BoxCardUserHistoryList) // /api/box/card/user/historyList 道具卡使用记录
		bo.GET("/card/user/list", mw.CheckUserLogin(), box.BoxCardUserList)               // /api/box/card/user/list 道具卡列表
		bo.POST("/card/user/loginSend", mw.CheckUserLogin(), box.BoxCardLoginSend)        // /api/box/card/user/loginSend 道具卡登录赠送

		bo.GET("/active", box.BoxActive)                  // /api/box/active 盒子活动详情
		bo.POST("", mw.CheckUserLogin(), box.BoxGet)      // /api/box/ 盒子创建
		bo.GET("", mw.CheckUserLogin(), box.BoxDetail)    // /api/box/:boxId 盒子详情
		bo.GET("/slot", mw.CheckUserLogin(), box.BoxSlot) // /api/box/:boxId/:slot 盒子槽位详情

		bo.POST("/itemCards", mw.CheckUserLogin(), box.BoxItemUse)
		bo.POST("/pay", mw.CheckUserLogin(), box.BoxPay) // /api/box/pay 盒子支付
		bo.POST("/pay/test", mw.CheckUserLogin(), box.BoxPayTest)

	}
}

func loadAdminBoxApi(e *gin.RouterGroup) {
	bC := e.Group("/admin/boxActiveConfig", mw.CheckAccountLogin(), mw.AdminOperationLog())
	{
		bC.POST("/create", box.AdminBoxActiveConfigCreate)            // 1
		bC.POST("/update", box.AdminBoxActiveConfigUpdate)            // 1
		bC.GET("/info", box.AdminBoxActiveConfigInfo)                 // 1
		bC.POST("/box/create", box.AdminBoxMustConfigBagCreate)       // 1
		bC.POST("/user/create", box.AdminBoxMustConfigUserCreate)     // 1
		bC.POST("/active/create", box.AdminBoxMustConfigActiveCreate) // 1
		bC.GET("/mustInfo", box.AdminBoxMustConfigInfo)               // 1
		bC.POST("/del", box.AdminBoxMustConfigActiveDel)              // 1
	}

	bg := e.Group("/admin/box/goods", mw.CheckAccountLogin())
	{
		bg.POST("/add", box.AdminBoxGoodsAdd)
		bg.POST("/update", box.AdminBoxGoodsUpdate)
		bg.POST("/sort", box.AdminBoxGoodsSort)
		bg.POST("/del", box.AdminBoxGoodsDel)
		bg.POST("/update/stock", box.AdminBoxGoodsUpdateStock)
		bg.GET("/list", box.AdminBoxGoodsList)
		bg.GET("/info", box.AdminBoxGoodsInfo)
	}

	bL := e.Group("/admin/box/level", mw.CheckAccountLogin())
	{
		bL.POST("/add", box.AdminBoxLevelAdd)       // 1
		bL.POST("/update", box.AdminBoxLevelUpdate) // 1
		bL.POST("/list", box.AdminBoxLevelList)     // 1
	}
}
