package router

import (
	"blind_box/app/api/aliyun"
	"blind_box/app/handler/admin"
	"blind_box/app/handler/openapi"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

/*
* 纯后台管理路由,与业务无关
* 业务后台的路由建议写到对应的业务路由文件中
 */
func loadAdminApi(e *gin.RouterGroup) {
	e.POST("/admin/account/login", admin.AdminAccountLogin)
	e.POST("/admin/account/logout", mw.CheckAccountLogin(), admin.AdminAccountLogout)
	e.GET("/admin/account/basic", mw.CheckAccountLogin(), admin.AdminAccountInfo)
	e.GET("/admin/judge", mw.CheckAccountLogin(), admin.AdminJudge)
	e.POST("/admin/account/edit/pwd", mw.CheckAccountLogin(), admin.AdminAccountEditPwd)

	e.GET("/admin/role/all", mw.CheckAccountLogin(), admin.AdminRoleAll)
	e.GET("/admin/account/all", mw.CheckAccountLogin(), admin.AdminAccountAll)
	e.GET("/admin/menu/option", mw.CheckAccountLogin(), admin.AdminMenuOption)
	e.GET("/admin/menu/router", mw.CheckAccountLogin(), admin.AdminMenuRouter)
	e.GET("/admin/area/list", mw.CheckAccountLogin(), admin.AdminAreaList)

	e.GET("/aliyun/oss/token", mw.CheckUserLogin(), aliyun.AliyunOssToken)
	e.GET("/aliyun/oss/applet/token", mw.CheckUserLogin(), aliyun.AliyunOssAppletToken)
	e.GET("/admin/aliyun/oss/token", mw.CheckAccountLogin(), aliyun.AliyunOssToken)

	aRole := e.Group("/admin/role", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aRole.GET("/list", admin.AdminRoleList)         // admin/role/list
		aRole.POST("/set", admin.AdminSetRole)          // admin/role/set
		aRole.POST("/set/auth", admin.AdminSetRoleAuth) // admin/role/set/auth
		aRole.POST("/del", admin.AdminDelRole)          // admin/role/del

	}

	aAcount := e.Group("/admin/account", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aAcount.GET("/list", admin.AdminAccountList)       // admin/account/list
		aAcount.POST("/set", admin.AdminSetAccount)        // admin/account/set
		aAcount.POST("/set/pwd", admin.AdminSetAccountPwd) // admin/account/set/pwd
		aAcount.POST("/del", admin.AdminDelAccount)        // admin/account/set

	}

	aMenu := e.Group("/admin/menu", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aMenu.GET("/list", admin.AdminMenuList) // admin/menu/list
		aMenu.POST("/set", admin.AdminSetMenu)  // admin/menu/set
		aMenu.POST("/del", admin.AdminDelMenu)  // admin/menu/del
		aMenu.GET("/init", admin.AdminMenuInit) // admin/menu/init
	}

	// OpenAPI商户管理路由（后台管理）
	apiMerchant := e.Group("/admin/openapi", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		apiMerchant.POST("/merchant", openapi.AdminCreateMerchant)
		apiMerchant.PUT("/merchant", openapi.AdminUpdateMerchant)
		apiMerchant.GET("/merchant", openapi.AdminListMerchants)
		apiMerchant.GET("/merchant/config", openapi.AdminGetMerchantConfig)
		apiMerchant.GET("/merchant/export", openapi.AdminExportConfig)
		apiMerchant.POST("/merchant/reset-secret", openapi.AdminResetAppSecret)
		apiMerchant.POST("/merchant/test", openapi.AdminTestConnection)
	}

}
