package router

import (
	"blind_box/app/handler/admin"
	"blind_box/app/handler/user"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadUserApi(e *gin.RouterGroup) {
	e.POST("/user/wechat/login", user.UserWechatLogin)
	e.POST("/user/login/third", user.UserLoginThird)
	e.GET("/area/list", admin.AdminAreaList)

	e.GET("/user/sign/config/info", mw.CheckUser(), user.GetUserSignConfigInfo) //盲盒-获取用户签到信息
	au := e.Group("/user", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		au.POST("/logout", user.UserLogout)       // user/logout
		au.GET("/basic", user.UserBasic)          // user/basic
		au.POST("/edit", user.UserEdit)           // user/edit
		au.POST("/edit/pwd", user.UserEditPwd)    // user/edit/pwd
		au.GET("/level/list", user.UserLevelList) // user/level/list

		au.GET("/addr/list", user.UserAddrList) // user/addr/list
		au.POST("/addr/set", user.SetUserAddr)  // user/addr/set
		au.GET("/addr/info", user.UserAddrInfo) // user/addr/info
		au.POST("/addr/del", user.DelUserAddr)  // user/addr/del

		au.GET("/cart/list", user.UserCartList)   // user/cart/list
		au.POST("/cart/add", user.UserCartAdd)    // user/cart/add
		au.POST("/cart/edit", user.UserCartEdit)  // user/cart/edit
		au.POST("/cart/del", user.UserCartDel)    // user/cart/del
		au.GET("/cart/count", user.UserCartCount) // user/cart/count

		au.GET("/sign", user.UserSign)                 //盲盒-用户签到
		au.GET("/points/bill", user.GetUserPointsBill) // user/points/bill 用户积分明细账单(近一年)
	}

	e.GET("/reward/new/user/info", mw.CheckUser(), user.RewardNewUserInfo) // reward/new/user/info
	ar := e.Group("/reward", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		ar.GET("/new/user/status", user.RewardNewUserStatus) // reward/new/user/status
		ar.POST("/new/user/get", user.RewardNewUserGet)      // reward/new/user/get
	}

	us := e.Group("/user/subscribe", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		us.GET("/list", user.SubscribeList) // user/subscribe/list
		us.POST("/add", user.SubscribeAdd)  // user/subscribe/add
		us.POST("/del", user.SubscribeDel)  // user/subscribe/del
	}
}

func loadAdminUserApi(e *gin.RouterGroup) {
	au := e.Group("/admin/user", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		au.GET("/list", user.AdminUserList)        // admin/user/list
		au.GET("/info", user.AdminUserInfo)        // admin/user/info
		au.POST("/operate", user.AdminOperateUser) // admin/user/operate

		au.GET("/addr/list", user.AdminUserAddrList) // admin/user/addr/list
		au.POST("/addr/set", user.AdminSetUserAddr)  // admin/user/addr/set
		au.GET("/addr/info", user.AdminUserAddrInfo) // admin/user/addr/info
		au.POST("/addr/del", user.AdminDelUserAddr)  // admin/user/addr/del

		// 用户积分管理
		au.POST("/points/edit", user.AdminEditUserPoints)  // 编辑用户积分
		au.GET("/points/log", user.AdminUserPointsLog)     // 查询用户积分记录
		au.GET("/points/stats", user.AdminUserPointsStats) // 查询用户积分统计
	}

	aull := e.Group("/admin/user/login/log", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aull.GET("/list", user.AdminUserLoginLogList) // admin/user/login/log/list
	}

	// 用户等级管理
	al := e.Group("/admin/user/level", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		al.GET("/list", user.AdminLevelList)        // admin/user/level/list
		al.POST("/set", user.AdminLevelSet)         // admin/user/level/set
		al.POST("/operate", user.AdminLevelOperate) // admin/user/level/operate
	}
}
