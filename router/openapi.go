package router

import (
	"blind_box/app/handler/openapi"
	"blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

// loadOpenAPIRouter 初始化OpenAPI路由
func loadOpenAPIRouter(e *gin.RouterGroup) {
	// OpenAPI路由组
	openapiGroup := e.Group("/openapi/v1")

	// 添加认证和限流中间件
	openapiGroup.Use(
		middleware.OpenAPIAuth(),
		middleware.OpenAPIRateLimit(),
	)

	// 扫码支付接口
	{
		// 创建扫码支付订单
		openapiGroup.POST("/scan-orders", openapi.CreateScanOrder)
		// 查询扫码订单状态
		openapiGroup.POST("/scan-orders/query", openapi.QueryScanOrder)
	}

	// 支付回调接口
	{
		// 支付回调通知
		openapiGroup.POST("/callbacks/payment", openapi.HandleCallback)
	}
}
