package router

import (
	"blind_box/app/handler/config"

	"github.com/gin-gonic/gin"
)

func loadConfigApi(e *gin.RouterGroup) {
	e.GET("/box/config", config.BoxConfig) // /box/config

	cf := e.Group("/config")
	{
		cf.GET("/page", config.PageConfig) // /config/page
	}
}

func loadAdminConfigApi(e *gin.RouterGroup) {

	cf := e.Group("/admin/config")
	{
		cf.GET("/page", config.AdminPageConfig)        // /admin/config/page
		cf.POST("/page", config.AdminUpdatePageConfig) // /admin/config/page
	}
}
