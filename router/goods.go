package router

import (
	"blind_box/app/handler/box"
	"blind_box/app/handler/goods"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadGoodsApi(e *gin.RouterGroup) {
	// goods := e.Group("/goods", mw.CheckUserLogin(), mw.UserOperationLog())
	gs := e.Group("/goods", mw.CheckUser())
	{
		gs.GET("/spu/list", goods.GoodsSpuList)     // goods/spu/list 商品列表
		gs.GET("/spu/detail", goods.GoodsSpuDetail) // goods/spu/detail 商品详情

		// 商品购物相关路由
		gs.GET("/list", box.GoodsList)                                // /goods/list 商品列表
		gs.GET("/detail", box.GoodsDetail)                            // /goods/detail 商品详情
		gs.POST("/stock/check", box.GoodsStockCheck)                  // /goods/stock/check SKU库存检查
		gs.POST("/price/calc", box.GoodsPriceCalc)                    // /goods/price/calc 价格计算（统一CartItems模式）
		gs.POST("/purchase", mw.CheckUserLogin(), box.DirectPurchase) // /goods/purchase 直接购买商品

		// // /spu/field/list
		// goods.GET("/field/list", goods.SpuFieldList) // 商品字段列表

		// // /spu/field/detail
		// goods.GET("/field/detail", goods.SpuFieldDetail) // 商品字段详情

		// // /spu/field/detail/attr
		// goods.GET("/field/detail/attr", goods.SpuFieldDetailAttr) // 商品字段属性列表

		// // /spu/stock/check
		// goods.GET("/stock/check", goods.CheckSpuStock) // 检查spu库存
	}

	// sk := e.Group("/sku", mw.CheckUserLogin(), mw.UserOperationLog())
	// {
	// 	// /sku/stock/check
	// 	sk.GET("/stock/check", goods.CheckSkuStock) // 检查sku库存
	// }
}

func loadAdminGoodsApi(e *gin.RouterGroup) {
	sx := e.Group("/admin/goods", mw.CheckAccountLogin())
	{
		sx.GET("/code/gen", goods.GoodsCodeGen) // admin/goods/code/gen 生成商品编码
	}

	sp := e.Group("/admin/spu", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		sp.GET("/list", goods.AdminSpuList)        // admin/spu/list spu列表
		sp.GET("/detail", goods.AdminSpuDetail)    // admin/spu/detail spu详情
		sp.POST("/create", goods.AdminSpuCreate)   // admin/spu/create 创建spu
		sp.POST("/update", goods.AdminSpuUpdate)   // admin/spu/update 更新spu
		sp.POST("/operate", goods.AdminSpuOperate) // admin/spu/operate 操作spu
	}

	sk := e.Group("/admin/sku", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		sk.GET("/list", goods.AdminSkuList)                   // admin/sku/list sku列表
		sk.GET("/detail", goods.AdminSkuDetail)               // admin/sku/detail sku详情
		sk.POST("/operate", goods.AdminSkuOperate)            // admin/sku/operate 操作sku
		sk.POST("/stock/set", goods.AdminSkuStockSet)         // admin/sku/stock/set 设置sku库存
		sk.GET("/stock/log/list", goods.AdminSkuStockLogList) // admin/sku/stock/log/list sku库存log列表
	}

	batch := e.Group("/admin/goods/batch", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		batch.GET("/list", goods.AdminBatchList)        // admin/goods/batch/list 批次列表
		batch.GET("/detail", goods.AdminBatchDetail)    // admin/goods/batch/detail 批次详情
		batch.POST("/create", goods.AdminBatchCreate)   // admin/goods/batch/create 创建批次
		batch.POST("/update", goods.AdminBatchUpdate)   // admin/goods/batch/update 更新批次
		batch.POST("/delete", goods.AdminBatchDelete)   // admin/goods/batch/delete 删除批次
		batch.POST("/operate", goods.AdminBatchOperate) // admin/goods/batch/operate 操作批次
	}

	e.GET("/admin/goods/tag/all", mw.CheckAccountLogin(), goods.AdminTagCommonList)
	tg := e.Group("/admin/goods/tag", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		tg.GET("/group/list", goods.AdminTagGroupList) // admin/tag/group/list 标签组列表

		tg.GET("/list", goods.AdminTagList)        // admin/tag/list 标签列表
		tg.POST("/create", goods.AdminTagCreate)   // admin/tag/create 添加标签
		tg.POST("/update", goods.AdminTagUpdate)   // admin/tag/update 更新标签
		tg.POST("/delete", goods.AdminTagDelete)   // admin/tag/delete 删除标签
		tg.POST("/operate", goods.AdminTagOperate) // admin/tag/operate 操作标签
	}

}
