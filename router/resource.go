package router

import (
	"blind_box/app/handler/resource"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadAdminResourceApi(e *gin.RouterGroup) {
	e.GET("/admin/brand/all", mw.CheckAccountLogin(), resource.AdminBrandAll)
	e.GET("/admin/vendor/all", mw.CheckAccountLogin(), resource.AdminVendorAll)
	e.GET("/admin/supplier/all", mw.CheckAccountLogin(), resource.AdminSupplierAll)
	e.GET("/admin/shop/all", mw.CheckAccountLogin(), resource.AdminShopAll)

	rs := e.Group("/admin/shop", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		rs.GET("/list", resource.AdminShopList)        // admin/shop/list
		rs.POST("/set", resource.AdminShopSet)         // admin/shop/set
		rs.POST("/operate", resource.AdminShopOperate) // admin/shop/operate
	}

	b := e.Group("/admin/brand", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		b.GET("/list", resource.AdminBrandList)        // admin/brand/list
		b.POST("/set", resource.AdminBrandSet)         // admin/brand/set
		b.POST("/operate", resource.AdminBrandOperate) // admin/brand/operate
	}
	s := e.Group("/admin/supplier", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		s.GET("/list", resource.AdminSupplierList)        // admin/supplier/list
		s.POST("/set", resource.AdminSupplierSet)         // admin/supplier/set
		s.POST("/operate", resource.AdminSupplierOperate) // admin/supplier/operate
	}
	v := e.Group("/admin/vendor", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		v.GET("/list", resource.AdminVendorList)        // admin/vendor/list
		v.POST("/set", resource.AdminVendorSet)         // admin/vendor/set
		v.POST("/operate", resource.AdminVendorOperate) // admin/vendor/operate
	}

}
