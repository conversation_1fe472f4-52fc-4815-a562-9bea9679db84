package router

import (
	"blind_box/app/handler/activity"
	mw "blind_box/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadActApi(e *gin.RouterGroup) {
	e.GET("/sale/calendar/count", mw.CheckUser(), activity.CountSaleCalendar) // 发售日历统计
	e.GET("/sale/calendar/list", mw.CheckUser(), activity.SaleCalendarList)   // 发售日历列表

	ua := e.Group("/user/activity", mw.CheckUserLogin(), mw.UserOperationLog())
	{
		ua.GET("/subscribe/list", activity.ActivitySubscribeList)   // 用户订阅活动列表
		ua.POST("/subscribe", activity.SubscribeActivity)           // 用户订阅活动
		ua.POST("/subscribe/delete", activity.DelSubscribeActivity) // 用户删除订阅活动
	}
}

func loadAdminActApi(e *gin.RouterGroup) {
	// 活动筛选
	e.POST("/admin/activity/search", mw.CheckAccountLogin(), activity.AdminActivitySearch)

	bA := e.Group("/admin/boxActive", mw.CheckAccountLogin())
	{
		bA.POST("/create", activity.AdminActiveCreate) // admin/boxActive/create 创建盲盒活动
		bA.POST("/update", activity.AdminActiveUpdate) // admin/boxActive/update 更新盲盒活动
		bA.GET("/list", activity.AdminActiveList)      // admin/boxActive/list 盲盒活动列表
		bA.GET("/info", activity.AdminActiveInfo)      // admin/boxActive/info 盲盒活动详情
	}

	as := e.Group("/admin/sale/calendar", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		as.GET("/list", activity.AdminSaleCalendarList)   // admin/sale/calendar/list 发售日历列表
		as.POST("/set", activity.AdminSetSaleCalendar)    // admin/sale/calendar/set 设置发售日历
		as.POST("/delete", activity.AdminDelSaleCalendar) // admin/sale/calendar/delete 删除发售日历
	}
}
