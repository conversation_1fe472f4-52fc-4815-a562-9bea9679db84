package router

import (
	"blind_box/app/api/wechat"
	"github.com/gin-gonic/gin"
)

func loadExternalApi(e *gin.RouterGroup) {
	// e.POST("/external/jky/action", jky.ExternalJkyAction)
	// e.POST("/jky/create/order", jky.JkyCreateOrder)

	e.POST("/external/wechat/order/pay/notify", wechat.WxOrderPayNotify)       // external/wechat/order/pay/notify
	e.POST("/external/wechat/order/refund/notify", wechat.WxOrderRefundNotify) // external/wechat/order/refund/notify
}
