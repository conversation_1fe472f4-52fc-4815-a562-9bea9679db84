package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

// ==================== 配置部分 ====================

const (
	// API配置
	API_URL    = "http://localhost:8080"  // API服务地址
	APP_ID     = "test_app_id"            // 替换为实际的APP_ID
	APP_SECRET = "test_app_secret"        // 替换为实际的APP_SECRET

	// 测试数据配置
	TEST_SHOP_ID = 1 // 店铺ID

	// 请求超时时间
	REQUEST_TIMEOUT = 10 * time.Second
)

// ==================== 数据结构定义 ====================

// GoodsItem 商品项
type GoodsItem struct {
	Code string `json:"code"` // 商品条码
	Num  int    `json:"num"`  // 商品数量
}

// CreateScanOrderRequest 创建扫码订单请求
type CreateScanOrderRequest struct {
	Timestamp int64                  `json:"timestamp"`        // 请求时间戳
	Nonce     string                 `json:"nonce"`            // 随机字符串，32位
	OrderNo   string                 `json:"order_no"`         // POS机订单号
	ShopID    uint64                 `json:"shop_id"`          // 店铺ID
	Goods     []GoodsItem            `json:"goods"`            // 商品列表
	Extra     map[string]interface{} `json:"extra,omitempty"`  // 额外参数
}

// QueryScanOrderRequest 查询扫码订单请求
type QueryScanOrderRequest struct {
	Timestamp int64  `json:"timestamp"` // 请求时间戳
	Nonce     string `json:"nonce"`     // 随机字符串，32位
	OrderNo   string `json:"order_no"`  // POS机订单号
}

// APIResponse 通用API响应
type APIResponse struct {
	Code      string                 `json:"code"`      // 响应码
	Msg       string                 `json:"msg"`       // 响应消息
	Data      map[string]interface{} `json:"data"`      // 响应数据
	Timestamp int64                  `json:"timestamp"` // 响应时间戳
	Sign      string                 `json:"sign"`      // 响应签名
	Debug     map[string]interface{} `json:"debug"`     // 调试信息（开发环境）
}

// ==================== 工具函数 ====================

// generateNonce 生成32位随机字符串（字母数字组合）
func generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateOrderNo 生成POS机订单号
func generateOrderNo() string {
	timestamp := time.Now().Format("20060102150405")
	randomSuffix := fmt.Sprintf("%06d", rand.Intn(1000000))
	return fmt.Sprintf("POS_%s_%s", timestamp, randomSuffix)
}

// calculateSignature 计算签名
// 签名算法: SHA256(JSON_BODY + "&key=" + APP_SECRET)
func calculateSignature(jsonBody string, appSecret string) string {
	signStr := jsonBody + "&key=" + appSecret
	hash := sha256.Sum256([]byte(signStr))
	signature := hex.EncodeToString(hash[:])
	return strings.ToUpper(signature)
}

// sendRequest 发送OpenAPI请求
func sendRequest(url string, data interface{}, appID string, appSecret string) (*APIResponse, error) {
	// 转换为JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("JSON编码失败: %v", err)
	}
	jsonBody := string(jsonBytes)

	// 计算签名
	signature := calculateSignature(jsonBody, appSecret)

	// 获取nonce（从请求数据中提取）
	var nonce string
	if reqMap, ok := data.(map[string]interface{}); ok {
		if n, ok := reqMap["nonce"].(string); ok {
			nonce = n
		}
	} else {
		// 使用反射获取nonce字段
		dataBytes, _ := json.Marshal(data)
		var tempMap map[string]interface{}
		json.Unmarshal(dataBytes, &tempMap)
		if n, ok := tempMap["nonce"].(string); ok {
			nonce = n
		}
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-App-Id", appID)
	req.Header.Set("X-Signature", signature)
	req.Header.Set("X-Nonce", nonce)

	// 发送请求
	client := &http.Client{Timeout: REQUEST_TIMEOUT}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(body))
	}

	return &apiResp, nil
}

// ==================== 测试函数 ====================

// testCreateScanOrder 测试创建扫码支付订单
func testCreateScanOrder(orderNo string, shopID uint64, goods []GoodsItem, extra map[string]interface{}) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("测试创建扫码支付订单")
	fmt.Println(strings.Repeat("=", 60))

	// 如果未提供参数，使用默认值
	if orderNo == "" {
		orderNo = generateOrderNo()
	}
	if shopID == 0 {
		shopID = TEST_SHOP_ID
	}
	if goods == nil {
		goods = []GoodsItem{
			{Code: "SKU001", Num: 1},
			{Code: "SKU002", Num: 2},
		}
	}

	// 构建请求
	request := CreateScanOrderRequest{
		Timestamp: time.Now().Unix(),
		Nonce:     generateNonce(),
		OrderNo:   orderNo,
		ShopID:    shopID,
		Goods:     goods,
		Extra:     extra,
	}

	// 打印请求信息
	url := fmt.Sprintf("%s/openapi/scan-orders", API_URL)
	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("POS订单号: %s\n", orderNo)
	fmt.Printf("店铺ID: %d\n", shopID)
	goodsJSON, _ := json.Marshal(goods)
	fmt.Printf("商品列表: %s\n", string(goodsJSON))

	// 发送请求
	response, err := sendRequest(url, request, APP_ID, APP_SECRET)
	if err != nil {
		fmt.Printf("\n❌ 请求失败: %v\n", err)
		return
	}

	// 打印响应
	fmt.Println("\n响应结果:")
	respJSON, _ := json.MarshalIndent(response, "", "  ")
	fmt.Println(string(respJSON))

	// 解析响应
	if response.Code == "0" {
		data := response.Data
		fmt.Println("\n✅ 订单创建成功!")
		fmt.Printf("系统订单号: %v\n", data["order_id"])
		fmt.Printf("小程序支付链接: %v\n", data["miniprogram_url"])
		fmt.Printf("二维码内容: %v\n", data["qr_code"])
		
		if totalAmount, ok := data["total_amount"].(float64); ok {
			fmt.Printf("订单总金额: %.2f元\n", totalAmount/100)
		}
		
		if expireTime, ok := data["expire_time"].(float64); ok {
			expireDateTime := time.Unix(int64(expireTime), 0)
			fmt.Printf("支付过期时间: %s\n", expireDateTime.Format("2006-01-02 15:04:05"))
		}
	} else {
		fmt.Println("\n❌ 订单创建失败!")
		fmt.Printf("错误码: %s\n", response.Code)
		fmt.Printf("错误信息: %s\n", response.Msg)
		
		if response.Debug != nil {
			debugJSON, _ := json.MarshalIndent(response.Debug, "", "  ")
			fmt.Printf("调试信息:\n%s\n", string(debugJSON))
		}
	}
}

// testQueryScanOrder 测试查询扫码订单状态
func testQueryScanOrder(orderNo string) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("测试查询扫码订单状态")
	fmt.Println(strings.Repeat("=", 60))

	// 构建请求
	request := QueryScanOrderRequest{
		Timestamp: time.Now().Unix(),
		Nonce:     generateNonce(),
		OrderNo:   orderNo,
	}

	// 打印请求信息
	url := fmt.Sprintf("%s/openapi/scan-orders/query", API_URL)
	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("POS订单号: %s\n", orderNo)

	// 发送请求
	response, err := sendRequest(url, request, APP_ID, APP_SECRET)
	if err != nil {
		fmt.Printf("\n❌ 请求失败: %v\n", err)
		return
	}

	// 打印响应
	fmt.Println("\n响应结果:")
	respJSON, _ := json.MarshalIndent(response, "", "  ")
	fmt.Println(string(respJSON))

	// 订单状态映射
	statusMap := map[float64]string{
		1: "订单生成（待支付）",
		2: "支付中",
		3: "支付成功",
		4: "支付失败",
		5: "已退款",
		6: "订单取消/关闭",
		7: "订单完成",
		8: "订单发货",
	}

	// 解析响应
	if response.Code == "0" {
		data := response.Data
		fmt.Println("\n✅ 查询成功!")
		fmt.Printf("系统订单号: %v\n", data["order_id"])
		
		if status, ok := data["status"].(float64); ok {
			statusText := statusMap[status]
			if statusText == "" {
				statusText = fmt.Sprintf("未知状态(%v)", status)
			}
			fmt.Printf("订单状态: %s\n", statusText)
			
			// 如果已支付，显示支付信息
			if status == 3 {
				if paidAmount, ok := data["paid_amount"].(float64); ok {
					fmt.Printf("实际支付金额: %.2f元\n", paidAmount/100)
				}
				fmt.Printf("支付方式: %v\n", data["payment_method"])
				fmt.Printf("第三方流水号: %v\n", data["transaction_id"])
				
				if paidTime, ok := data["paid_time"].(float64); ok {
					paidDateTime := time.Unix(int64(paidTime), 0)
					fmt.Printf("支付时间: %s\n", paidDateTime.Format("2006-01-02 15:04:05"))
				}
			}
		}
		
		if totalAmount, ok := data["total_amount"].(float64); ok {
			fmt.Printf("订单总金额: %.2f元\n", totalAmount/100)
		}
	} else {
		fmt.Println("\n❌ 查询失败!")
		fmt.Printf("错误码: %s\n", response.Code)
		fmt.Printf("错误信息: %s\n", response.Msg)
		
		if response.Debug != nil {
			debugJSON, _ := json.MarshalIndent(response.Debug, "", "  ")
			fmt.Printf("调试信息:\n%s\n", string(debugJSON))
		}
	}
}

// testCompleteFlow 测试完整流程
func testCompleteFlow() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("开始测试完整流程")
	fmt.Println(strings.Repeat("=", 60))

	// 1. 创建订单
	orderNo := generateOrderNo()
	extra := map[string]interface{}{
		"pos_operator": "张三",
		"pos_terminal": "POS001",
		"customer_note": "测试订单",
	}
	
	testCreateScanOrder(orderNo, TEST_SHOP_ID, nil, extra)

	// 2. 等待2秒后查询订单
	fmt.Println("\n等待2秒后查询订单状态...")
	time.Sleep(2 * time.Second)

	// 3. 查询订单状态
	testQueryScanOrder(orderNo)

	// 4. 测试多次查询（测试幂等性）
	fmt.Println("\n测试幂等性：再次查询同一订单...")
	time.Sleep(1 * time.Second)
	testQueryScanOrder(orderNo)

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("完整流程测试结束")
	fmt.Println(strings.Repeat("=", 60))
}

// testErrorCases 测试错误场景
func testErrorCases() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("开始测试错误场景")
	fmt.Println(strings.Repeat("=", 60))

	// 测试1: 使用过期的时间戳
	fmt.Println("\n测试场景1: 使用过期的时间戳")
	expiredRequest := CreateScanOrderRequest{
		Timestamp: time.Now().Unix() - 400, // 400秒前
		Nonce:     generateNonce(),
		OrderNo:   generateOrderNo(),
		ShopID:    TEST_SHOP_ID,
		Goods: []GoodsItem{
			{Code: "SKU001", Num: 1},
		},
	}
	url := fmt.Sprintf("%s/openapi/scan-orders", API_URL)
	response, err := sendRequest(url, expiredRequest, APP_ID, APP_SECRET)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
	} else {
		fmt.Printf("响应: Code=%s, Msg=%s\n", response.Code, response.Msg)
	}

	// 测试2: 使用重复的nonce
	fmt.Println("\n测试场景2: 使用重复的nonce")
	nonce := generateNonce()
	
	// 第一次请求
	request1 := CreateScanOrderRequest{
		Timestamp: time.Now().Unix(),
		Nonce:     nonce,
		OrderNo:   generateOrderNo(),
		ShopID:    TEST_SHOP_ID,
		Goods: []GoodsItem{
			{Code: "SKU001", Num: 1},
		},
	}
	response1, _ := sendRequest(url, request1, APP_ID, APP_SECRET)
	fmt.Printf("第一次请求响应: Code=%s, Msg=%s\n", response1.Code, response1.Msg)

	// 使用相同的nonce再次请求
	time.Sleep(1 * time.Second)
	request2 := CreateScanOrderRequest{
		Timestamp: time.Now().Unix(),
		Nonce:     nonce, // 相同的nonce
		OrderNo:   generateOrderNo(),
		ShopID:    TEST_SHOP_ID,
		Goods: []GoodsItem{
			{Code: "SKU001", Num: 1},
		},
	}
	response2, _ := sendRequest(url, request2, APP_ID, APP_SECRET)
	fmt.Printf("第二次请求响应: Code=%s, Msg=%s\n", response2.Code, response2.Msg)

	// 测试3: 使用无效的商品数据
	fmt.Println("\n测试场景3: 使用无效的商品数据")
	invalidRequest := CreateScanOrderRequest{
		Timestamp: time.Now().Unix(),
		Nonce:     generateNonce(),
		OrderNo:   generateOrderNo(),
		ShopID:    TEST_SHOP_ID,
		Goods: []GoodsItem{
			{Code: "", Num: 1}, // 空的商品代码
		},
	}
	response3, _ := sendRequest(url, invalidRequest, APP_ID, APP_SECRET)
	fmt.Printf("响应: Code=%s, Msg=%s\n", response3.Code, response3.Msg)

	// 测试4: 查询不存在的订单
	fmt.Println("\n测试场景4: 查询不存在的订单")
	testQueryScanOrder("NON_EXISTENT_ORDER_123456")

	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("错误场景测试结束")
	fmt.Println(strings.Repeat("=", 60))
}

// ==================== 主函数 ====================

func main() {
	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	fmt.Println(`
╔════════════════════════════════════════════════════════════╗
║          OpenAPI 扫码支付接口客户端测试工具               ║
╚════════════════════════════════════════════════════════════╝
`)

	fmt.Printf("API地址: %s\n", API_URL)
	fmt.Printf("APP_ID: %s\n", APP_ID)
	fmt.Printf("测试时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	for {
		fmt.Println("\n请选择测试功能:")
		fmt.Println("1. 创建扫码支付订单")
		fmt.Println("2. 查询订单状态")
		fmt.Println("3. 测试完整流程")
		fmt.Println("4. 测试错误场景")
		fmt.Println("0. 退出")

		var choice string
		fmt.Print("\n请输入选项 (0-4): ")
		fmt.Scanln(&choice)

		switch choice {
		case "1":
			testCreateScanOrder("", 0, nil, nil)

		case "2":
			var orderNo string
			fmt.Print("请输入POS订单号: ")
			fmt.Scanln(&orderNo)
			if orderNo != "" {
				testQueryScanOrder(orderNo)
			} else {
				fmt.Println("订单号不能为空")
			}

		case "3":
			testCompleteFlow()

		case "4":
			testErrorCases()

		case "0":
			fmt.Println("\n感谢使用，再见！")
			return

		default:
			fmt.Println("无效的选项，请重新选择")
		}
	}
}