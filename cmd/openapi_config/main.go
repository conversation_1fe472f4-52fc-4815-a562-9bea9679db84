package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"strings"

	"blind_box/app/common/dbs"
	"blind_box/app/service/openapi"
	"blind_box/config"
	"blind_box/pkg/utils"
)

func main() {
	// 定义命令行参数
	var (
		action       string
		configFile   string
		merchantName string
		merchantCode string
		merchantID   uint64
		format       string
		output       string
		ipWhitelist  string
		skuWhitelist string
		showHelp     bool
	)

	flag.StringVar(&action, "action", "", "操作类型: create, generate, export, list, test")
	flag.StringVar(&configFile, "config", "../config/local.ini", "配置文件路径")
	flag.StringVar(&merchantName, "name", "", "商户名称")
	flag.StringVar(&merchantCode, "code", "", "商户编码")
	flag.Uint64Var(&merchantID, "id", 0, "商户ID")
	flag.StringVar(&format, "format", "json", "导出格式: json, yaml")
	flag.StringVar(&output, "output", "", "输出文件路径")
	flag.StringVar(&ipWhitelist, "ip", "", "IP白名单，逗号分隔")
	flag.StringVar(&skuWhitelist, "sku", "", "SKU白名单，逗号分隔")
	flag.BoolVar(&showHelp, "help", false, "显示帮助信息")

	flag.Parse()

	if showHelp || action == "" {
		printHelp()
		return
	}

	// 初始化配置
	config.Init(configFile)
	dbs.Init()

	// 创建服务实例
	manager := openapi.NewMerchantManager()
	ctx := context.Background()

	switch action {
	case "create":
		createMerchant(ctx, manager, merchantName, merchantCode, ipWhitelist, skuWhitelist)
	case "generate":
		generateConfig(merchantCode, merchantName, ipWhitelist, skuWhitelist, output)
	case "export":
		exportConfig(ctx, manager, merchantID, format, output)
	case "list":
		listMerchants(ctx, manager)
	case "test":
		testConnection(ctx, manager, merchantID)
	default:
		fmt.Printf("未知操作: %s\n", action)
		printHelp()
	}
}

// printHelp 打印帮助信息
func printHelp() {
	fmt.Println("OpenAPI 配置管理工具")
	fmt.Println()
	fmt.Println("使用方法:")
	fmt.Println("  openapi_config -action=<action> [options]")
	fmt.Println()
	fmt.Println("操作类型:")
	fmt.Println("  create    创建新商户")
	fmt.Println("  generate  生成商户配置（不保存到数据库）")
	fmt.Println("  export    导出商户配置")
	fmt.Println("  list      列出所有商户")
	fmt.Println("  test      测试商户连接")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -config   配置文件路径 (默认: ../config/local.ini)")
	fmt.Println("  -name     商户名称")
	fmt.Println("  -code     商户编码")
	fmt.Println("  -id       商户ID")
	fmt.Println("  -format   导出格式: json, yaml (默认: json)")
	fmt.Println("  -output   输出文件路径")
	fmt.Println("  -ip       IP白名单，逗号分隔")
	fmt.Println("  -sku      SKU白名单，逗号分隔")
	fmt.Println("  -help     显示帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 创建新商户")
	fmt.Println("  openapi_config -action=create -name=\"测试商户\" -code=\"TEST001\" -ip=\"***********/24\"")
	fmt.Println()
	fmt.Println("  # 生成配置（不保存到数据库）")
	fmt.Println("  openapi_config -action=generate -name=\"测试商户\" -code=\"TEST001\" -output=config.json")
	fmt.Println()
	fmt.Println("  # 导出商户配置")
	fmt.Println("  openapi_config -action=export -id=1001 -format=json -output=merchant.json")
	fmt.Println()
	fmt.Println("  # 列出所有商户")
	fmt.Println("  openapi_config -action=list")
	fmt.Println()
	fmt.Println("  # 测试商户连接")
	fmt.Println("  openapi_config -action=test -id=1001")
}

// createMerchant 创建商户
func createMerchant(ctx context.Context, manager *openapi.MerchantManager, name, code, ipWhitelist, skuWhitelist string) {
	if name == "" || code == "" {
		fmt.Println("错误: 商户名称和编码不能为空")
		return
	}

	// 解析IP白名单
	var ips []string
	if ipWhitelist != "" {
		ips = strings.Split(ipWhitelist, ",")
	}

	// 解析SKU白名单
	var skus []uint64
	if skuWhitelist != "" {
		skuStrs := strings.Split(skuWhitelist, ",")
		for _, s := range skuStrs {
			if sku, err := strconv.ParseUint(strings.TrimSpace(s), 10, 64); err == nil {
				skus = append(skus, sku)
			}
		}
	}

	req := &openapi.CreateMerchantRequest{
		MerchantName: name,
		MerchantCode: code,
		IPWhitelist:  ips,
		SKUWhitelist: skus,
		QPSLimit:     100,
		DailyLimit:   100000,
	}

	resp, err := manager.CreateMerchant(ctx, req)
	if err != nil {
		fmt.Printf("创建商户失败: %v\n", err)
		return
	}

	fmt.Println("商户创建成功！")
	fmt.Printf("商户ID: %d\n", resp.MerchantID)
	fmt.Printf("AppKey: %s\n", resp.AppKey)
	fmt.Printf("AppSecret: %s\n", resp.AppSecret)
	fmt.Println()
	fmt.Println("完整配置:")
	configJSON, _ := json.MarshalIndent(resp.Config, "", "  ")
	fmt.Println(string(configJSON))
}

// generateConfig 生成配置（不保存到数据库）
func generateConfig(code, name, ipWhitelist, skuWhitelist, output string) {
	if code == "" || name == "" {
		fmt.Println("错误: 商户编码和名称不能为空")
		return
	}

	// 解析IP白名单
	var ips []string
	if ipWhitelist != "" {
		ips = strings.Split(ipWhitelist, ",")
	}

	// 解析SKU白名单
	var skus []uint64
	if skuWhitelist != "" {
		skuStrs := strings.Split(skuWhitelist, ",")
		for _, s := range skuStrs {
			if sku, err := strconv.ParseUint(strings.TrimSpace(s), 10, 64); err == nil {
				skus = append(skus, sku)
			}
		}
	}

	// 生成配置
	config := utils.GenerateMerchantConfig(code, name, "https://api.example.com", ips, skus)

	// 导出JSON
	configJSON, err := utils.ExportConfigToJSON(config)
	if err != nil {
		fmt.Printf("生成配置失败: %v\n", err)
		return
	}

	// 输出或保存到文件
	if output != "" {
		err = ioutil.WriteFile(output, []byte(configJSON), 0644)
		if err != nil {
			fmt.Printf("保存配置文件失败: %v\n", err)
			return
		}
		fmt.Printf("配置已保存到: %s\n", output)
	} else {
		fmt.Println("生成的配置:")
		fmt.Println(configJSON)
	}
}

// exportConfig 导出商户配置
func exportConfig(ctx context.Context, manager *openapi.MerchantManager, merchantID uint64, format, output string) {
	if merchantID == 0 {
		fmt.Println("错误: 商户ID不能为空")
		return
	}

	configStr, err := manager.ExportConfig(ctx, merchantID, format)
	if err != nil {
		fmt.Printf("导出配置失败: %v\n", err)
		return
	}

	// 输出或保存到文件
	if output != "" {
		err = ioutil.WriteFile(output, []byte(configStr), 0644)
		if err != nil {
			fmt.Printf("保存配置文件失败: %v\n", err)
			return
		}
		fmt.Printf("配置已导出到: %s\n", output)
	} else {
		fmt.Println("导出的配置:")
		fmt.Println(configStr)
	}
}

// listMerchants 列出所有商户
func listMerchants(ctx context.Context, manager *openapi.MerchantManager) {
	merchants, err := manager.ListMerchants(ctx, 0, "")
	if err != nil {
		fmt.Printf("获取商户列表失败: %v\n", err)
		return
	}

	if len(merchants) == 0 {
		fmt.Println("暂无商户")
		return
	}

	fmt.Println("商户列表:")
	fmt.Println("--------------------------------------------------------------")
	fmt.Printf("%-8s %-20s %-20s %-10s %-20s\n", "ID", "商户编码", "商户名称", "状态", "创建时间")
	fmt.Println("--------------------------------------------------------------")

	for _, m := range merchants {
		status := "启用"
		if m.Status != 1 {
			status = "禁用"
		}
		fmt.Printf("%-8d %-20s %-20s %-10s %-20s\n",
			m.ID,
			m.MerchantCode,
			m.MerchantName,
			status,
			m.CreatedAt.Format("2006-01-02 15:04:05"),
		)
	}
	fmt.Println("--------------------------------------------------------------")
	fmt.Printf("共 %d 个商户\n", len(merchants))
}

// testConnection 测试商户连接
func testConnection(ctx context.Context, manager *openapi.MerchantManager, merchantID uint64) {
	if merchantID == 0 {
		fmt.Println("错误: 商户ID不能为空")
		return
	}

	success, msg, err := manager.TestConnection(ctx, merchantID)
	if err != nil {
		fmt.Printf("测试连接失败: %v\n", err)
		return
	}

	fmt.Printf("测试结果: %s\n", msg)
	if success {
		fmt.Println("状态: ✅ 成功")
	} else {
		fmt.Println("状态: ❌ 失败")
	}
}
