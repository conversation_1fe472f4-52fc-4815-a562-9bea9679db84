[app]
AppName=blind_box
Env=test
JwtSecret=WL67cGEraN
JwtTimeout=604800
HttpPort=8083
LogPath=./data/logs
ReadTimeout=60
WriteTimeout=60

[mysql]
Host=rm-uf6c4b862289hqju56o.mysql.rds.aliyuncs.com
Port=3306
Database=blind_box
Username=wsd_main
Password=alg%jo2df@w3

[mysql_read]
Host=rm-uf6c4b862289hqju56o.mysql.rds.aliyuncs.com
Port=3306
Database=blind_box
Username=wsd_main
Password=alg%jo2df@w3

[redis]
Host=r-uf6v0l8bghf10klr65pd.redis.rds.aliyuncs.com:6379
Username=wsd_main
Password=jxk$3KsJU%3w
Db=4
MaxIdle=8
MaxActive=32
IdleTimeout=300
Prefix=
AsynqDB=48

[aliyun]
RegionID="cn-shanghai"
AccessKeyID="LTAI5tLFvg8TKobmCoGJmeah"
AccessKeySecret="******************************"
RoleArn="acs:ram::1093221814228683:role/oss"
RoleSessionName="test"
Bucket="test"

[wechat]
AppletID="wxfb60a9ba2ca2142c"
AppletSecret="8660b825a44dd5ec16c2e24fde7fce1d"
MchID="1638883957" # 商户ID 或者服务商模式的 sp_mchid
SerialNo="" # 商户API证书的证书序列号
ApiV3Key="********************************" # APIv3Key, 商户平台获取
PrivateKey="" # 商户API证书下载后,私钥 apiclient_key.pem读取后的字符串内容
NotifyUrl="https://mptry-vision.mowantoy.com/vision_box/api/box/wechatNotify"


