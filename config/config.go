package config

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	"gopkg.in/ini.v1"
)

const (
	ENT_TEST = "test"
	ENT_DEV  = "dev"
	ENT_PROD = "prod"
)

type App struct {
	AppName      string
	Env          string
	JwtSecret    string
	JwtTimeout   int
	HttpPort     int
	LogPath      string
	LogLevel     string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

var AppCfg = &App{}

type Database struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	// TablePrefix string
}

var DbCfg = &Database{}
var DbReadCfg = &Database{}

type Redis struct {
	Host        string
	Username    string
	Password    string
	Prefix      string
	Db          int
	MaxIdle     int
	MaxActive   int
	IdleTimeout time.Duration
	AsynqDB     int
}

var RedisCfg = &Redis{}

type Kafka struct {
	Version  string
	Producer KafkaProducer
	Consumer KafkaConsumer
	Maxwell  KafkaConsumer
}

type KafkaProducer struct {
	Address          []string
	SecurityProtocol string
	SaslMechanism    string
	SaslUsername     string
	SaslPassword     string
	CaPath           string
}

type KafkaConsumer struct {
	Assignor         string
	Brokers          []string
	Group            string
	Topics           []string
	Oldest           bool
	SecurityProtocol string
	SaslMechanism    string
	SaslUsername     string
	SaslPassword     string
	CaPath           string
}

var KafkaCfg = &Kafka{}

type ElasticSearch struct {
	Host     []string
	Username string
	Password string
}

var ElasticSearchCfg = &ElasticSearch{}

var AliyunCfg = &Aliyun{}

type Aliyun struct {
	RegionID        string
	OssRegionID     string
	VodRegionID     string
	AccessKeyID     string
	AccessKeySecret string
	RoleArn         string
	RamUID          string
	BucketImage     string
}

var WechatCfg = &Wechat{}

type Wechat struct {
	AppletID     string
	AppletSecret string
	MchID        string // 商户ID 或者服务商模式的 sp_mchid
	MchKey       string // 商户平台获取
	SerialNo     string // 商户API证书的证书序列号
	ApiV3Key     string // APIv3Key, 商户平台获取
	NotifyURL    string
}

var cfgPath = "config/test.ini"

func SetCfgPath(path string) {
	cfgPath = path
}

func Setup() {
	if cfg := os.Getenv("config"); cfg != "" {
		cfgPath = cfg
	}
	log.Printf("config path:%s\n", cfgPath)
	cfg, err := ini.Load(cfgPath)
	if err != nil {
		log.Fatalf("config load error:%v\n", err)
	}
	err = ini.MapTo(AppCfg, cfgPath)
	if err != nil {
		log.Fatalf("config map app error:%v\n", err)
	}

	mapConfig(cfg, "app", AppCfg)
	mapConfig(cfg, "mysql", DbCfg)
	mapConfig(cfg, "mysql_read", DbReadCfg)
	mapConfig(cfg, "redis", RedisCfg)
	mapConfig(cfg, "kafka", KafkaCfg)
	mapConfig(cfg, "kafka.producer", &KafkaCfg.Producer)
	mapConfig(cfg, "kafka.consumer", &KafkaCfg.Consumer)
	mapConfig(cfg, "kafka.maxwell", &KafkaCfg.Maxwell)
	mapConfig(cfg, "elasticsearch", ElasticSearchCfg)
	mapConfig(cfg, "aliyun", AliyunCfg)
	mapConfig(cfg, "wechat", WechatCfg)

	AppCfg.ReadTimeout = AppCfg.ReadTimeout * time.Second
	AppCfg.WriteTimeout = AppCfg.WriteTimeout * time.Second
	RedisCfg.IdleTimeout = RedisCfg.IdleTimeout * time.Second
}

func mapConfig(cfg *ini.File, section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("config map %s error:%v\n", section, err)
	}
}

func LoadWxPrivateKey() ([]byte, error) {
	keyData, err := os.ReadFile("config/apiclient_key.pem")
	if err != nil {
		return nil, fmt.Errorf("read private key err: %v", err)
	}

	// 验证PEM格式（可选但建议）
	block, _ := pem.Decode(keyData)
	if block == nil || block.Type != "PRIVATE KEY" {
		return nil, errors.New("private key invalid")
	}

	return keyData, nil
}

func LoadWxPrivateKeyRsa() (privateKey *rsa.PrivateKey, err error) {
	data, err := os.ReadFile("config/apiclient_key.pem")
	if err != nil {
		return nil, fmt.Errorf("read private key err: %v", err)
	}

	return LoadPrivateKey(data)
}

func LoadPrivateKey(byteKey []byte) (privateKey *rsa.PrivateKey, err error) {
	block, _ := pem.Decode(byteKey)
	if block == nil {
		return nil, fmt.Errorf("decode private key err")
	}
	if block.Type != "PRIVATE KEY" {
		return nil, fmt.Errorf("the kind of PEM should be PRVATE KEY")
	}
	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("parse private key err:%s", err.Error())
	}
	privateKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("not a RSA private key")
	}
	return privateKey, nil
}
