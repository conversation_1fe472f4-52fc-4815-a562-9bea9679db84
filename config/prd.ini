[app]
AppName=blind_box
Env=test
JwtSecret=WL67cGEraN
JwtTimeout=604800
HttpPort=8083
LogPath=./data/logs
ReadTimeout=60
WriteTimeout=60

[mysql]
Host=rm-uf6c52ya1md797fd41o.mysql.rds.aliyuncs.com
Port=3306
Database=blind_box
Username=wsd_main
Password=alg%jo2df@w3

[mysql_read]
Host=rm-uf6c52ya1md797fd41o.mysql.rds.aliyuncs.com
Port=3306
Database=blind_box
Username=wsd_main
Password=alg%jo2df@w3

[redis]
Host=r-bp1wsicyvdfhb98ujspd.redis.rds.aliyuncs.com:6379
Username=wsd_main
Password=jxk$3KsJU%3w
Db=4
MaxIdle=8
MaxActive=32
IdleTimeout=300
Prefix=

RegionID="cn-shanghai"
OssRegionID="oss-cn-shanghai"
VodRegionID="vod.cn-shanghai"
AccessKeyID="LTAI5tLFvg8TKobmCoGJmeah"
AccessKeySecret="******************************"
RoleArn="acs:ram::1093221814228683:role/oss"
RamUID="1093221814228683"
BucketImage="box-prod-image"

[wechat]
AppletID="wx71f93230ad1c6856"
AppletSecret="931826e19dcef4fdb1c6f049de537b9f"
MchID="1711947764" # 商户ID 或者服务商模式的 sp_mchid
SerialNo="48EC083506033642BD99076E623D8B2B487360C9" # 商户API证书的证书序列号
ApiV3Key="IV1Dyo2rECKog6ttOye9FeRt5Ws1c8Vy" # APIv3Key, 商户平台获取
NotifyUrl=""


