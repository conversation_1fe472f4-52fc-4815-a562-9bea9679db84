# config 目录规范文档

本文档记录 `config` 目录下配置管理的代码规范和最佳实践。

## 目录结构

```
config/
├── config.go            # 配置加载和管理核心文件
├── local.ini           # 本地开发环境配置
├── test.ini            # 测试环境配置
├── prd.ini             # 生产环境配置
├── test_byte.ini       # 字节测试环境配置（备用）
└── apiclient_key.pem   # 微信支付私钥文件
```

## 配置文件格式

### INI 文件规范

配置文件采用 INI 格式，使用 `gopkg.in/ini.v1` 库进行解析。

#### 基本结构
```ini
[section_name]
key=value
key2=value2

[another_section]
key=value
```

#### 配置节（Sections）

标准配置文件包含以下节：

1. **[app]** - 应用基础配置
2. **[mysql]** - MySQL主库配置
3. **[mysql_read]** - MySQL从库配置（读写分离）
4. **[redis]** - Redis配置
5. **[aliyun]** - 阿里云服务配置
6. **[wechat]** - 微信相关配置
7. **[kafka]** - Kafka配置（可选）
8. **[elasticsearch]** - ElasticSearch配置（可选）

### 配置示例

#### [app] 应用配置
```ini
[app]
AppName=blind_box           # 应用名称，用作路由前缀
Env=test                    # 环境标识: test/dev/prod
JwtSecret=WL67cGEraN        # JWT密钥
JwtTimeout=604800           # JWT超时时间（秒）
HttpPort=8083               # HTTP服务端口
LogPath=./data/logs         # 日志路径
LogLevel=info               # 日志级别: debug/info/warn/error
ReadTimeout=60              # 读超时（秒）
WriteTimeout=60             # 写超时（秒）
```

#### [mysql] 数据库配置
```ini
[mysql]
Host=mysql-host.com         # 数据库主机
Port=3306                   # 端口
Database=blind_box          # 数据库名
Username=user               # 用户名
Password=password           # 密码

[mysql_read]
Host=mysql-read-host.com    # 只读实例主机
Port=3306
Database=blind_box
Username=user
Password=password
```

#### [redis] 缓存配置
```ini
[redis]
Host=redis-host.com:6379    # Redis地址（含端口）
Username=user               # 用户名（可选）
Password=password           # 密码
Db=4                       # 数据库编号
MaxIdle=8                  # 最大空闲连接数
MaxActive=32               # 最大活跃连接数
IdleTimeout=300            # 空闲超时（秒）
Prefix=                    # 键前缀（可选）
AsynqDB=48                 # Asynq任务队列DB
```

#### [aliyun] 阿里云配置
```ini
[aliyun]
RegionID=cn-shanghai                        # 区域ID
OssRegionID=oss-cn-shanghai                # OSS区域ID
VodRegionID=vod.cn-shanghai                # VOD区域ID
AccessKeyID=LTAI5tLFvg8TKobmCoGJmeah      # AccessKey ID
AccessKeySecret=SECRET                      # AccessKey Secret
RoleArn=acs:ram::1234:role/oss            # RAM角色ARN
RamUID=1093221814228683                    # RAM用户ID
BucketImage=box-test-image                 # 图片存储桶
```

#### [wechat] 微信配置
```ini
[wechat]
AppletID=wx71f93230ad1c6856                        # 小程序AppID
AppletSecret=931826e19dcef4fdb1c6f049de537b9f     # 小程序Secret
MchID=1716542253                                   # 商户ID
SerialNo=48EC083506033642BD99076E623D8B2B487360C9 # 证书序列号
ApiV3Key=Usdfjoljsddf4sg5aasf524sdf4654s6         # APIv3密钥
NotifyURL=https://api.example.com/wechat/notify    # 支付回调URL
```

## 配置结构体定义

### App 应用配置
```go
type App struct {
    AppName      string        // 应用名称
    Env          string        // 环境标识
    JwtSecret    string        // JWT密钥
    JwtTimeout   int           // JWT超时（秒）
    HttpPort     int           // HTTP端口
    LogPath      string        // 日志路径
    LogLevel     string        // 日志级别
    ReadTimeout  time.Duration // 读超时
    WriteTimeout time.Duration // 写超时
}
```

### Database 数据库配置
```go
type Database struct {
    Host     string  // 主机地址
    Port     int     // 端口
    Database string  // 数据库名
    Username string  // 用户名
    Password string  // 密码
}
```

### Redis 缓存配置
```go
type Redis struct {
    Host        string        // 主机地址
    Username    string        // 用户名
    Password    string        // 密码
    Prefix      string        // 键前缀
    Db          int          // 数据库编号
    MaxIdle     int          // 最大空闲连接
    MaxActive   int          // 最大活跃连接
    IdleTimeout time.Duration // 空闲超时
    AsynqDB     int          // Asynq数据库
}
```

### Aliyun 阿里云配置
```go
type Aliyun struct {
    RegionID        string  // 默认区域
    OssRegionID     string  // OSS区域
    VodRegionID     string  // VOD区域
    AccessKeyID     string  // AK ID
    AccessKeySecret string  // AK Secret
    RoleArn         string  // RAM角色
    RamUID          string  // RAM用户ID
    BucketImage     string  // 图片桶
}
```

### Wechat 微信配置
```go
type Wechat struct {
    AppletID     string  // 小程序ID
    AppletSecret string  // 小程序密钥
    MchID        string  // 商户ID
    MchKey       string  // 商户密钥（V2）
    SerialNo     string  // 证书序列号
    ApiV3Key     string  // APIv3密钥
    NotifyURL    string  // 回调URL
}
```

## 配置加载机制

### 初始化流程

```go
func init() {
    config.Setup()  // 加载配置
    dbs.Setup()     // 初始化数据库
    redis.Setup()   // 初始化Redis
    helper.Setup()  // 初始化辅助工具
}
```

### Setup 函数实现

```go
func Setup() {
    // 1. 确定配置文件路径
    if cfg := os.Getenv("config"); cfg != "" {
        cfgPath = cfg  // 优先使用环境变量
    }
    
    // 2. 加载INI文件
    cfg, err := ini.Load(cfgPath)
    if err != nil {
        log.Fatalf("config load error:%v\n", err)
    }
    
    // 3. 映射配置到结构体
    mapConfig(cfg, "app", AppCfg)
    mapConfig(cfg, "mysql", DbCfg)
    mapConfig(cfg, "mysql_read", DbReadCfg)
    mapConfig(cfg, "redis", RedisCfg)
    mapConfig(cfg, "aliyun", AliyunCfg)
    mapConfig(cfg, "wechat", WechatCfg)
    
    // 4. 时间单位转换
    AppCfg.ReadTimeout = AppCfg.ReadTimeout * time.Second
    AppCfg.WriteTimeout = AppCfg.WriteTimeout * time.Second
    RedisCfg.IdleTimeout = RedisCfg.IdleTimeout * time.Second
}
```

### 配置映射

```go
func mapConfig(cfg *ini.File, section string, v interface{}) {
    err := cfg.Section(section).MapTo(v)
    if err != nil {
        log.Fatalf("config map %s error:%v\n", section, err)
    }
}
```

## 全局配置变量

配置加载后存储在全局变量中，供整个应用使用：

```go
var (
    AppCfg           = &App{}            // 应用配置
    DbCfg            = &Database{}       // 主库配置
    DbReadCfg        = &Database{}       // 从库配置
    RedisCfg         = &Redis{}          // Redis配置
    AliyunCfg        = &Aliyun{}         // 阿里云配置
    WechatCfg        = &Wechat{}         // 微信配置
    KafkaCfg         = &Kafka{}          // Kafka配置
    ElasticSearchCfg = &ElasticSearch{}  // ES配置
)
```

### 使用示例

```go
// 获取应用名称
appName := config.AppCfg.AppName

// 获取数据库配置
host := config.DbCfg.Host
port := config.DbCfg.Port

// 获取Redis配置
redisHost := config.RedisCfg.Host
redisDB := config.RedisCfg.Db
```

## 环境管理

### 环境标识

```go
const (
    ENT_TEST = "test"  // 测试环境
    ENT_DEV  = "dev"   // 开发环境
    ENT_PROD = "prod"  // 生产环境
)
```

### 配置文件选择

1. **默认配置**: `config/test.ini`
2. **环境变量覆盖**: 
   ```bash
   export config=config/prd.ini
   ./app
   ```
3. **代码指定**:
   ```go
   config.SetCfgPath("config/local.ini")
   config.Setup()
   ```

### 环境判断

```go
// 根据环境执行不同逻辑
switch config.AppCfg.Env {
case config.ENT_PROD:
    // 生产环境逻辑
case config.ENT_TEST:
    // 测试环境逻辑
default:
    // 开发环境逻辑
}
```

## 敏感信息管理

### 私钥文件加载

```go
// 加载微信支付私钥（字节数组）
func LoadWxPrivateKey() ([]byte, error) {
    keyData, err := os.ReadFile("config/apiclient_key.pem")
    if err != nil {
        return nil, fmt.Errorf("read private key err: %v", err)
    }
    
    // 验证PEM格式
    block, _ := pem.Decode(keyData)
    if block == nil || block.Type != "PRIVATE KEY" {
        return nil, errors.New("private key invalid")
    }
    
    return keyData, nil
}

// 加载微信支付私钥（RSA格式）
func LoadWxPrivateKeyRsa() (*rsa.PrivateKey, error) {
    data, err := os.ReadFile("config/apiclient_key.pem")
    if err != nil {
        return nil, fmt.Errorf("read private key err: %v", err)
    }
    
    return LoadPrivateKey(data)
}
```

### 私钥解析

```go
func LoadPrivateKey(byteKey []byte) (*rsa.PrivateKey, error) {
    block, _ := pem.Decode(byteKey)
    if block == nil {
        return nil, fmt.Errorf("decode private key err")
    }
    
    if block.Type != "PRIVATE KEY" {
        return nil, fmt.Errorf("the kind of PEM should be PRIVATE KEY")
    }
    
    key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
    if err != nil {
        return nil, fmt.Errorf("parse private key err:%s", err.Error())
    }
    
    privateKey, ok := key.(*rsa.PrivateKey)
    if !ok {
        return nil, fmt.Errorf("not a RSA private key")
    }
    
    return privateKey, nil
}
```

## 最佳实践

### 1. 配置文件管理

- **版本控制**: 配置模板入库，实际配置通过环境变量或配置中心管理
- **环境隔离**: 不同环境使用不同配置文件
- **敏感信息**: 密码、密钥等敏感信息不要提交到代码库
- **配置备份**: 生产环境配置定期备份

### 2. 配置加载

```go
// 推荐在main函数的init中加载
func init() {
    config.Setup()
}

// 或在main函数开始处加载
func main() {
    config.Setup()
    // 其他初始化...
}
```

### 3. 配置验证

```go
// 验证必要配置
func validateConfig() error {
    if config.AppCfg.JwtSecret == "" {
        return errors.New("JwtSecret is required")
    }
    
    if config.DbCfg.Host == "" {
        return errors.New("Database host is required")
    }
    
    return nil
}
```

### 4. 动态配置

对于需要动态更新的配置，考虑：
- 使用配置中心（如Apollo、Nacos）
- 监听配置文件变化并重载
- 提供配置更新API

### 5. 配置模板

创建配置模板文件 `config.ini.example`：

```ini
[app]
AppName=your_app_name
Env=dev
JwtSecret=your_jwt_secret
# ... 其他配置项

[mysql]
Host=localhost
Port=3306
Database=your_database
Username=your_username
Password=your_password
```

## 扩展配置

### 添加新的配置节

1. **定义结构体**
```go
type NewService struct {
    Host     string
    Port     int
    ApiKey   string
    Timeout  time.Duration
}

var NewServiceCfg = &NewService{}
```

2. **添加配置映射**
```go
func Setup() {
    // ... 现有配置
    mapConfig(cfg, "new_service", NewServiceCfg)
    
    // 时间单位转换
    NewServiceCfg.Timeout = NewServiceCfg.Timeout * time.Second
}
```

3. **配置文件添加节**
```ini
[new_service]
Host=service.example.com
Port=8080
ApiKey=your_api_key
Timeout=30
```

### 嵌套配置

对于复杂配置，支持嵌套结构：

```go
// Kafka配置示例
mapConfig(cfg, "kafka", KafkaCfg)
mapConfig(cfg, "kafka.producer", &KafkaCfg.Producer)
mapConfig(cfg, "kafka.consumer", &KafkaCfg.Consumer)
```

配置文件：
```ini
[kafka]
Version=2.6.0

[kafka.producer]
Address=kafka1:9092,kafka2:9092
SecurityProtocol=SASL_SSL

[kafka.consumer]
Group=consumer-group
Topics=topic1,topic2
```

## 常见问题

### Q: 如何在测试中使用不同配置？
A: 
```go
func TestSomething(t *testing.T) {
    config.SetCfgPath("config/test.ini")
    config.Setup()
    // 测试代码
}
```

### Q: 如何实现配置热更新？
A: 可以使用文件监听或配置中心，监听变化后重新调用Setup()并通知相关组件。

### Q: 如何处理数组类型配置？
A: INI不直接支持数组，可以用逗号分隔：
```ini
Hosts=host1,host2,host3
```
在代码中解析：
```go
hosts := strings.Split(cfg.Hosts, ",")
```

### Q: 如何加密敏感配置？
A: 
1. 使用环境变量存储敏感信息
2. 使用密钥管理服务（KMS）
3. 配置文件加密，运行时解密

## 注意事项

1. **配置文件权限**: 确保配置文件权限设置正确（如600）
2. **路径问题**: 使用绝对路径或基于工作目录的相对路径
3. **默认值**: 为配置项设置合理的默认值
4. **类型转换**: 注意时间单位等类型转换
5. **配置校验**: 启动时验证关键配置的有效性
6. **环境区分**: 明确区分不同环境的配置
7. **备份策略**: 生产配置需要备份和版本管理