package main

import (
	"blind_box/app/common/dbs"
	"blind_box/app/common/srv"
	"blind_box/app/job"
	"blind_box/config"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"blind_box/pkg/util/ctxUtil"
	"blind_box/router"
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func init() {
	config.Setup()
	dbs.Setup()
	redis.Setup()
	helper.Setup()
}

func main() {
	ctx, cancel := context.WithCancel(ctxUtil.NewRequestID(context.Background()))
	defer cancel()

	ginRouter := gin.Default()
	router.LoadRouter(ginRouter)

	ginSrv := &helper.GinSrv{HttpSrv: &http.Server{
		Addr:           fmt.Sprintf(":%v", config.AppCfg.HttpPort),
		Handler:        ginRouter,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   30 * time.Second,
		MaxHeaderBytes: 1 << 20, // 1MB
	}}
	logSrv, jobSrv := log.GetAsyncLogger(), job.GetJob()

	appSrvMgr := helper.GetAppSrvMgr(ctx)
	appSrvMgr.Register(logSrv, ginSrv, jobSrv)

	if err := appSrvMgr.Start(ctx); err != nil {
		fmt.Printf("Failed to start services: %v\n", err)
		return
	}

	srv.GracefullyShutdown(ctx, appSrvMgr)
}
