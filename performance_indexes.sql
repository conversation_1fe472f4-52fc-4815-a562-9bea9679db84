-- 定时任务性能优化索引
-- 用于提升定时任务查询性能，避免全表扫描

-- 1. 订单超时检查索引
-- 覆盖查询条件：order_status + pay_expire_time
CREATE INDEX idx_order_status_expire ON box_order_info(order_status, pay_expire_time) 
COMMENT '订单状态和支付过期时间复合索引，用于超时订单检查';

-- 2. 订单状态和更新时间索引（用于增量处理）
CREATE INDEX idx_order_status_updated ON box_order_info(order_status, updated_at, id) 
COMMENT '订单状态、更新时间和ID复合索引，用于增量扫描';

-- 3. 积分过期处理索引
CREATE INDEX idx_points_status_expire ON user_points(status, expire_time) 
COMMENT '积分状态和过期时间复合索引，用于积分过期检查';

-- 4. 卡券过期处理索引
CREATE INDEX idx_card_status_expire ON card_user(status, expire_time) 
COMMENT '卡券状态和过期时间复合索引，用于卡券过期检查';

-- 5. 用户订阅通知索引
CREATE INDEX idx_subscribe_status_time ON user_subscribe(status, notify_time) 
COMMENT '订阅状态和通知时间复合索引，用于订阅通知任务';

-- 6. 盲盒商品库存索引（用于库存释放操作）
CREATE INDEX idx_box_goods_lock_stock ON box_goods(lock_stock, updated_at) 
COMMENT '锁定库存和更新时间索引，用于库存释放监控';
