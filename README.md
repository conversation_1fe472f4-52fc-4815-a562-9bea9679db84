# 盲盒电商系统

这是一个基于 Go 语言开发的盲盒电商系统，采用分层架构设计，支持完整的电商业务流程。

## 核心功能

- 🎁 **盲盒系统**: 支持多种盲盒类型、概率配置、开盒记录
- 👤 **用户管理**: 注册登录、个人中心、积分系统、签到奖励
- 🛒 **订单管理**: 下单、支付、发货、退款等完整流程
- 🎫 **优惠券系统**: 发放、使用、范围配置、营销活动
- 💳 **支付集成**: 微信支付、支付宝支付
- 📦 **商品管理**: 商品信息、SKU管理、库存控制
- 🎯 **营销活动**: 活动配置、销售日历、订阅通知
- 🏪 **管理后台**: 系统配置、数据统计、权限管理
- 🔌 **OpenAPI**: 第三方接入、扫码支付、安全认证

## 技术栈

- **后端框架**: Gin Web Framework
- **数据库**: MySQL + GORM ORM
- **缓存**: Redis + go-redis
- **消息队列**: Kafka + Sarama
- **搜索引擎**: Elasticsearch
- **云服务**: 阿里云 OSS、短信服务
- **任务调度**: Asynq、Cron
- **性能监控**: Pyroscope、pprof

## 项目结构

```
blind_box/
├── app/                    # 应用层
│   ├── api/               # 第三方服务集成
│   ├── handler/           # HTTP 处理层
│   ├── service/           # 业务逻辑层
│   ├── dao/              # 数据访问层
│   ├── dto/              # 数据传输对象
│   ├── middleware/        # 中间件
│   ├── consumer/          # 消息消费者
│   └── job/              # 定时任务
├── config/                 # 配置文件
├── internal/              # 内部包
├── pkg/                   # 公共工具包
├── router/                # 路由定义
└── dbs/                   # 数据库脚本
```

## 快速开始

### 环境要求

- Go 1.23+
- MySQL 8.0+
- Redis 6.0+
- Kafka 2.8+ (可选)
- Elasticsearch 8.0+ (可选)

### 安装依赖

```bash
go mod download
```

### 配置文件

复制配置模板并修改配置：

```bash
cp config/local.ini config/test.ini
```

配置数据库、Redis、支付等信息：

```ini
[app]
AppName = blind_box
Env = test
HttpPort = 8080

[mysql]
Host = localhost
Port = 3306
Database = blind_box
Username = root
Password = your_password

[redis]
Host = localhost:6379
Password = 
Db = 0
```

### 数据库初始化

```bash
# 导入数据库结构
mysql -u root -p < dbs/build.sql
```

### 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动

## 开发指南

### 新功能开发流程

1. **数据模型设计**: 在 `dao/` 目录定义数据模型
2. **DTO 定义**: 在 `dto/` 目录定义请求响应结构
3. **数据访问层**: 实现 DAO 层数据操作
4. **业务逻辑层**: 在 Service 层实现核心逻辑
5. **接口处理层**: 在 Handler 层处理 HTTP 请求
6. **路由配置**: 在 `router/` 目录配置路由

### 代码规范

- 使用 Go 标准命名规范
- 按业务模块组织代码
- 统一错误处理和日志记录
- 编写必要的单元测试

## API 文档

### 用户相关接口

- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `POST /api/user/signin` - 用户签到

### 盲盒相关接口

- `GET /api/box/list` - 获取盲盒列表
- `POST /api/box/open` - 开启盲盒
- `GET /api/box/record` - 开盒记录

### 订单相关接口

- `POST /api/order/create` - 创建订单
- `POST /api/order/pay` - 订单支付
- `GET /api/order/list` - 订单列表

### OpenAPI 接口

- `POST /openapi/v1/scan-orders` - 创建扫码支付订单
- `POST /openapi/v1/callbacks/payment` - 支付回调通知

详细的 OpenAPI 文档请参见 [OpenAPI 集成指南](docs/openapi-guide.md)

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t blind_box .

# 运行容器
docker run -d -p 8080:8080 \
  -e config=config/prd.ini \
  blind_box
```

### 生产环境配置

- 修改 `config/prd.ini` 生产环境配置
- 配置 HTTPS 证书
- 设置监控和日志收集
- 配置负载均衡和高可用

## 监控和运维

- **性能监控**: Pyroscope 火焰图分析
- **日志管理**: 结构化日志输出
- **健康检查**: `/health` 接口
- **指标监控**: pprof 性能分析

## 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

MIT License

## 联系方式

如有问题请提交 Issue 或联系开发团队。
