SHELL:=/bin/sh
.PHONY: build start stop restart clean version reload

# 服务配置
SERVICE_NAME := blind_box
BINARY_NAME := $(SERVICE_NAME)
MAIN_FILE := main.go
LOG_FILE := $(SERVICE_NAME)_$$(date +%m%d%H%M%S).log
PID_FILE := $(SERVICE_NAME).pid
HEALTH_URL := http://127.0.0.1:8083/blind_box/version

# 简化的内存检查
ifeq ($(shell uname), Darwin)
    GOGC := 20  # macOS默认使用较小的GOGC
else
    GOGC :=     # Linux使用默认GOGC
endif

version:
	@echo "VERSION_INFO: $$(date +%Y-%m-%dT%H:%M:%S%z)"

# 构建
build: version
	@echo "Building $(SERVICE_NAME)..."
	@VERSION_INFO=$$(date +%Y-%m-%dT%H:%M:%S%z) && GIT_COMMIT=$$(git rev-parse --short HEAD) && \
	$(if $(GOGC),GOGC=$(GOGC)) go build -ldflags "-X blind_box/router.buildTime=$$VERSION_INFO -X blind_box/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE)

# 启动
start: build
	@echo "Starting $(SERVICE_NAME)..."
	@nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(PID_FILE)
	@echo "$(SERVICE_NAME) started with PID $$(cat $(PID_FILE))"

# 强制停止
stop:
	@echo "Stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		kill -9 `cat $(PID_FILE)` && rm $(PID_FILE) && echo "$(SERVICE_NAME) stopped."; \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 优雅停止
graceful-stop:
	@echo "Gracefully stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		PID=`cat $(PID_FILE)` && kill -TERM $$PID; \
		for i in 1 2 3 4 5 6 7 8 9 10; do \
			ps -p $$PID >/dev/null 2>&1 || { echo "$(SERVICE_NAME) stopped gracefully."; rm -f $(PID_FILE); exit 0; }; \
			sleep 1; \
		done; \
		echo "Timeout, force killing..." && kill -9 $$PID && rm -f $(PID_FILE); \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 传统重启
restart: stop start

# 平滑重启（简化版）
reload: build
	@echo "Reloading $(SERVICE_NAME)..."
	@if [ ! -f $(PID_FILE) ]; then \
		echo "Service not running, starting normally..."; \
		$(MAKE) start; \
	else \
		OLD_PID=`cat $(PID_FILE)`; \
		echo "Old PID: $$OLD_PID"; \
		echo "Starting new instance..."; \
		nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & \
		NEW_PID=$$!; \
		echo "New PID: $$NEW_PID"; \
		sleep 2; \
		if curl -sSf $(HEALTH_URL) >/dev/null 2>&1; then \
			echo "New instance ready, stopping old..."; \
			kill -TERM $$OLD_PID; \
			echo $$NEW_PID > $(PID_FILE); \
			echo "Reload completed!"; \
		else \
			echo "New instance failed, rolling back..."; \
			kill -9 $$NEW_PID; \
			echo "Old instance still running."; \
		fi; \
	fi

clean:
	@rm -rf ./data/logs
	@rm -f $(BINARY_NAME) $(PID_FILE) *.log
	@echo "Cleanup done."