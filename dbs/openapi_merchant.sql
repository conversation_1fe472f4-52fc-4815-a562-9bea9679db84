-- OpenAPI商户配置表
CREATE TABLE IF NOT EXISTS `openapi_merchant` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '软删除：0-未删除，1-已删除',
  
  -- 基础信息
  `app_id` varchar(64) NOT NULL COMMENT '应用ID（AppKey）',
  `app_secret` varchar(128) NOT NULL COMMENT '应用密钥',
  `merchant_name` varchar(128) NOT NULL COMMENT '商户名称',
  `merchant_code` varchar(64) NOT NULL COMMENT '商户编码',
  
  -- 联系信息
  `contact_name` varchar(64) DEFAULT NULL COMMENT '联系人姓名',
  `contact_mobile` varchar(20) DEFAULT NULL COMMENT '联系人手机',
  `contact_email` varchar(128) DEFAULT NULL COMMENT '联系人邮箱',
  
  -- 配置信息
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，2-禁用',
  `callback_url` varchar(512) DEFAULT NULL COMMENT '默认回调地址',
  `ip_whitelist` text COMMENT 'IP白名单，多个用逗号分隔，支持CIDR',
  
  -- 限制配置
  `daily_limit` bigint(20) NOT NULL DEFAULT '100000' COMMENT '每日调用限制',
  `qps_limit` int(11) NOT NULL DEFAULT '100' COMMENT 'QPS限制',
  `single_amount_limit` bigint(20) NOT NULL DEFAULT '10000000' COMMENT '单笔金额限制（分）',
  `daily_amount_limit` bigint(20) NOT NULL DEFAULT '100000000' COMMENT '每日金额限制（分）',
  
  -- SKU配置
  `allowed_skus` text COMMENT '允许的SKU列表，JSON格式',
  
  -- 其他
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id` (`app_id`),
  UNIQUE KEY `uk_merchant_code` (`merchant_code`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OpenAPI商户配置表';

-- 插入测试商户数据
INSERT INTO `openapi_merchant` (
  `app_id`, 
  `app_secret`, 
  `merchant_name`, 
  `merchant_code`,
  `contact_name`,
  `contact_mobile`,
  `contact_email`,
  `status`,
  `ip_whitelist`,
  `daily_limit`,
  `qps_limit`,
  `single_amount_limit`,
  `daily_amount_limit`,
  `allowed_skus`,
  `remark`
) VALUES (
  'APP_TEST_20250811_001',
  'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6',
  '测试商户',
  'TEST_MERCHANT_001',
  '张三',
  '13800138000',
  '<EMAIL>',
  1,
  '127.0.0.1,***********/24,10.0.0.0/8',
  100000,
  100,
  1000000,
  10000000,
  '[100001,100002,100003]',
  '测试商户，用于开发环境'
);

-- 商户访问日志表（用于审计和限流）
CREATE TABLE IF NOT EXISTS `openapi_merchant_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `api_path` varchar(128) NOT NULL COMMENT 'API路径',
  `request_time` datetime NOT NULL COMMENT '请求时间',
  `request_ip` varchar(45) NOT NULL COMMENT '请求IP',
  `request_body` text COMMENT '请求内容',
  `response_code` int(11) NOT NULL COMMENT '响应码',
  `response_body` text COMMENT '响应内容',
  `response_time` int(11) NOT NULL COMMENT '响应时间（毫秒）',
  `signature` varchar(128) DEFAULT NULL COMMENT '请求签名',
  `nonce` varchar(64) DEFAULT NULL COMMENT 'Nonce值',
  `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_request_ip` (`request_ip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OpenAPI商户访问日志表';

-- 商户限流统计表
CREATE TABLE IF NOT EXISTS `openapi_merchant_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_requests` bigint(20) NOT NULL DEFAULT '0' COMMENT '总请求数',
  `success_requests` bigint(20) NOT NULL DEFAULT '0' COMMENT '成功请求数',
  `failed_requests` bigint(20) NOT NULL DEFAULT '0' COMMENT '失败请求数',
  `total_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '总交易金额（分）',
  `max_qps` int(11) NOT NULL DEFAULT '0' COMMENT '最大QPS',
  `avg_response_time` int(11) NOT NULL DEFAULT '0' COMMENT '平均响应时间（毫秒）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_merchant_date` (`merchant_id`, `stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OpenAPI商户统计表';