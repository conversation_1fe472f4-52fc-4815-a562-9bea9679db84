{"permissions": {"allow": ["Bash(grep -n \"OrderTimeoutQueue\" /Users/<USER>/Repositories/WORK/blind_box/app/service/box/box.go)", "Bash(grep -n \"GeneratePickupCode\" /Users/<USER>/Repositories/WORK/blind_box/app/service/order/order.go)", "Bash(grep -n -A 10 \"PickupCodeGenerateRes\" /Users/<USER>/Repositories/WORK/blind_box/app/dto/order/order.go)", "Bash(grep -n -A 5 \"PickupCodeGenerateReq\" /Users/<USER>/Repositories/WORK/blind_box/app/dto/order/order.go)", "mcp__sequentialthinking__sequentialthinking", "Bash(go build:*)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(make:*)", "mcp__mcp-feedback-enhanced__interactive_feedback", "Bash(grep:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}