SHELL:=/bin/sh
.PHONY: build start stop restart clean version

# 服务名
SERVICE_NAME := blind_box
# 二进制文件名
BINARY_NAME := $(SERVICE_NAME)
# 主程序文件
MAIN_FILE := main.go
# nohup运行日志文件
LOG_FILE := $(SERVICE_NAME)_$$(date +%m%d%H%M%S).log
# PID文件
PID_FILE := $(SERVICE_NAME).pid
# 健康检查URL
HEALTH_CHECK_URL := http://127.0.0.1:8080/blind_box/version

# 检查可用内存并设置GOGC
MEM_AVAILABLE := $(shell free -g | awk '/^Mem:/ {print $$7}')

ifeq ($(shell [ $(MEM_AVAILABLE) -lt 2 ] && echo true), true)
    GOGC = 5
else
    GOGC =
endif

version:
	@echo "VERSION_INFO: $$(date +%Y-%m-%dT%H:%M:%S%z)"

# 构建二进制文件
build: version
	@echo "Building $(SERVICE_NAME)..."
	@echo "mem available: $(MEM_AVAILABLE)G"
	@echo "GOGC=$(GOGC)"
	@VERSION_INFO=$$(date +%Y-%m-%dT%H:%M:%S%z) && GIT_COMMIT=$$(git rev-parse --short HEAD) && \
	if [ -n "$(GOGC)" ]; then \
		GOGC=$(GOGC) go build -ldflags "-X blind_box/router.buildTime=$$VERSION_INFO -X blind_box/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	else \
		go build -ldflags "-X blind_box/router.buildTime=$$VERSION_INFO -X blind_box/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	fi
# 启动服务
start: build
	@echo "Starting $(SERVICE_NAME)..."
	@nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(PID_FILE)
	@echo "$(SERVICE_NAME) started with PID $$(cat $(PID_FILE))"

# @sleep 20
# @if ! curl -sSf $(HEALTH_CHECK_URL) > /dev/null; then \
# 	echo "$(SERVICE_NAME) health check failed. Stopping service..."; \
# 	$(MAKE) stop; \
# 	exit 1; \
# else \
# 	echo "$(SERVICE_NAME) is active."; \
# fi

# 停止服务
stop:
	@echo "Stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		kill -9 `cat $(PID_FILE)`; \
		rm $(PID_FILE); \
		echo "$(SERVICE_NAME) stopped."; \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 重启服务
restart: stop start

clean:
	@pwd
	@rm -rf ./data/logs
	@rm -f $(BINARY_NAME) $(LOG_FILE) $(PID_FILE)
	@echo "Cleanup done."




