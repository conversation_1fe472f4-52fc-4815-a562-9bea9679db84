# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Build and Run
- **Build**: `make build` - Compiles Go binary with version info and Git commit
- **Run**: `go run main.go` - Start development server  
- **Start**: `make start` - Build and start as daemon with PID file
- **Stop**: `make stop` - Stop running service
- **Restart**: `make restart` - Stop and start service
- **Version**: `make version` - Show build timestamp
- **Clean**: `make clean` - Remove build artifacts and logs

### Database
- **Initialize DB**: `mysql -u root -p < dbs/build.sql` - Import database schema
- **OpenAPI merchant schema**: `mysql -u root -p < dbs/openapi_merchant.sql` - Import OpenAPI merchant tables
- **Schema reference**: Check DAO models in `app/dao/` directories for table structures

### Testing
- **Run all tests**: `go test -v ./tests/...`
- **Run specific test**: `go test -v tests/direct_purchase_unit_test.go tests/test_constants.go`
- **Direct purchase tests**: `bash tests/run_direct_purchase_tests.sh`
- **Points system tests**: `bash tests/run_points_tests.sh`
- **Performance tests**: `go test -bench=. -benchmem ./tests/...`
- **Integration tests**: `go test -v -run TestDirectPurchaseIntegrationTestSuite tests/`
- **Unit test pattern**: `go test -v tests/*_unit_test.go tests/test_constants.go`
- **Test with coverage**: `go test -v -cover ./tests/...`
- **Service level tests**: `go test -v ./app/service/box/`

### Code Quality  
- **Compile check**: `go build -o /tmp/verify_build ./app/service/...`
- **Format code**: `go fmt ./...`
- **Vet code**: `go vet ./...`

## Project Architecture

### Core Structure
```
app/
├── handler/     # HTTP request handlers (controller layer)
├── service/     # Business logic layer
├── dao/         # Data access layer (models + repositories)
├── dto/         # Data transfer objects (request/response structures)
├── middleware/  # HTTP middleware (auth, logging, recovery)
├── api/         # Third-party service integrations (payment, cloud services)
├── consumer/    # Message queue consumers
└── job/         # Scheduled tasks and background jobs

pkg/             # Shared utilities and common packages
router/          # Route definitions by module
config/          # Configuration management
```

### Request Flow
`HTTP Request → Router → Middleware → Handler → Service → DAO → Database`

### Key Business Modules
- **User**: Registration, login, profile, points system, sign-in rewards
- **Box**: Blind box management, opening records, inventory, direct purchase
- **Order**: Order creation, payment, status management, pickup codes  
- **Goods**: SKU/SPU management, inventory control, batch operations
- **Coupon**: Coupon issuance, usage, range configuration
- **Activity**: Marketing campaigns, sale calendar, subscription notifications

## Database Layer

- **ORM**: GORM with MySQL
- **Connection**: Managed in `app/common/dbs/`
- **Models**: Defined in `app/dao/[module]/model.go`
- **Repositories**: Business logic in `app/dao/[module]/repo.go`
- **Schema**: Main schema in `dbs/build.sql`, OpenAPI merchant in `dbs/openapi_merchant.sql`

### Key Data Concepts
- **SKU as purchase unit**: Users buy specific SKUs, not SPUs
- **Box strategies**: Multiple purchase strategies (blind box, item card, direct purchase)
- **Points system**: FIFO-based points with Redis optimization
- **Pickup codes**: Dual-cache system for in-store pickup

## Configuration

- **Format**: INI configuration files
- **Environments**: `config/local.ini`, `config/test.ini`, `config/prd.ini`
- **Loading**: Via `config/config.go`
- **Key sections**: `[app]`, `[mysql]`, `[redis]`, `[wechat]`, `[alipay]`

## Authentication & Authorization

### User Authentication (`app/middleware/user_auth.go`)
- `CheckUser()` - Optional user authentication
- `CheckUserLogin()` - Required user authentication  
- JWT tokens with Redis validation

### Admin Authentication (`app/middleware/account_auth.go`)
- `CheckAccountLogin()` - Admin login verification
- `CheckAccountAuth()` - Role-based permission control

### OpenAPI Authentication (`app/middleware/openapi_auth.go`)
- Signature verification: `SHA256(JSON_BODY + "&key=" + APP_SECRET)`
- IP whitelist with CIDR support
- Rate limiting (QPS and daily limits)

## Business Logic Patterns

### Layered Architecture
- **Handler**: Parameter validation, response formatting
- **Service**: Core business rules, no HTTP dependencies
- **DAO**: Data operations only, no business logic
- **DTO**: Data transfer between layers

### Error Handling
- Unified error codes in `pkg/ecode/`
- Service layer returns business errors
- Handler layer converts to HTTP status codes

### Common Patterns
- Interface-driven design with dependency injection
- Redis caching for frequently accessed data
- Message queue for async processing
- Background jobs for scheduled tasks

## Development Workflow

1. **Analyze requirements** - Define business functionality
2. **Design data model** - Create/modify DAO models
3. **Define DTOs** - Request/response structures
4. **Implement DAO** - Database operations
5. **Implement Service** - Business logic
6. **Implement Handler** - HTTP interface
7. **Configure routes** - Add to `router/` modules
8. **Write tests** - Unit and integration tests

## Testing Strategy

- **Unit tests**: Business logic validation (`*_unit_test.go`)
- **Integration tests**: Full flow testing (`*_integration_test.go`) 
- **API tests**: HTTP interface testing (`*.http` files)
- **Performance tests**: Benchmark critical paths
- **Test constants**: Shared in `tests/test_constants.go`
- **Test helpers**: Common utilities in `tests/test_helper.go`

## Important Technical Notes

### Dependency Injection Pattern
Services use interface-driven design with repository injection. When adding new dependencies:
1. Add the import in the service file
2. Add the field to the Entry struct in interface.go
3. Initialize in newEntry() function

Example:
```go
// In interface.go
type Entry struct {
    StockLogRepo *stockLogDao.Entry  // Add field
}

// In newEntry()
StockLogRepo: stockLogDao.GetRepo(),  // Initialize
```

### Stock Management
- **Inventory tracking**: Total, LockNum, UsedNum, RefundNum
- **Stock consistency rule**: `Total + RefundNum >= LockNum + UsedNum`
- **Stock logs**: Use stockLogDao for audit trail
- **Redis locks**: Use `RetryLock` for atomic operations on SKU stock

### Order Status Constants
Always use constants from orderDao package:
- `orderDao.OrderStatusPayOk` instead of hardcoded `3`
- `orderDao.OrderStatusDone` instead of hardcoded `7`

### Error Handling
- **Use pkg/ecode package**: Never hardcode error codes, use or generate from `pkg/ecode/errorCode.go`
- **Service layer**: Return business errors using ecode package
- **Non-critical operations**: Log error but don't fail main flow (e.g., logging, monitoring)
- **Critical operations**: Return error to halt transaction

### Code Style Conventions
- **JSON handling**: Always use project's jsoniter instead of standard json package
- **Comments**: Maintain minimal comments, focus on complex business logic only
- **Error responses**: Simple error handling in handlers, no verbose error wrapping
- **Constants**: Use package-level constants, never hardcode business values

### Recent Improvements (2025-08)
- Enhanced stock consistency validation with `validateStockConsistency`
- Improved stock change logging with proper database records
- Added detailed monitoring for stock inconsistency detection
- Fixed syntax errors in import statements
- Simplified OpenAPI signature algorithm from complex JSON flattening to direct JSON string signing

## OpenAPI Integration

### Overview
The system provides OpenAPI for third-party integrations, particularly for offline POS systems:
- **Scan-and-pay**: POS scans products → API returns QR code → Customer pays via mini-program
- **Simplified authentication**: Direct JSON body signing with SHA256
- **Enhanced debugging**: Detailed error information in development environments

### Key Components
- **Authentication**: `app/middleware/openapi_auth.go`
  - Signature verification: `SHA256(JSON_BODY + "&key=" + APP_SECRET)`
  - IP whitelist with CIDR support
  - Rate limiting (QPS and daily limits)
  - Nonce-based replay prevention

- **Merchant Model**: `app/dao/openapi/merchant/`
  - Configuration for API access
  - SKU whitelist support
  - Amount limits

- **Service Layer**: `app/service/openapi/`
  - Scan order creation
  - Redis-based order caching
  - Mini-program URL generation

### Recent Improvements (2025-08)
- **Simplified signature algorithm**: Changed from complex JSON flattening to direct JSON string signing
- **Enhanced debugging**: Added `debug` field in error responses with detailed information
- **Extra parameters support**: Added flexible `extra` field for third-party custom data

### Development Notes
- Always use project's jsoniter instead of standard json package
- Maintain consistent code style with existing handlers (minimal comments, simple error handling)
- Test signature verification thoroughly - it's the most common integration issue
- Remember to handle both timestamp formats (int64 and float64) from different clients

## Known Issues & TODOs

Reference `docs/TODO.md` for current technical debt:
- **Critical**: Alipay/Balance refund implementation missing - only TODOs in `app/service/order/refund.go`
- **Resolved**: Stock consistency monitoring, order status constants, fee calculation deduplication

## Critical Business Rules

### Payment & Refund Flow
- **Supported payment methods**: WeChat Pay, Alipay, Balance
- **Refund status**: WeChat refund fully implemented, Alipay/Balance are TODO
- **Order timeout**: Configurable via Redis queue (15-30 minutes typical)

### Pickup Code System  
- **Dual-cache system**: Redis primary, local memory backup
- **Code generation**: `app/service/order/order.go` - GeneratePickupCode
- **Store verification**: In-store pickup validation flow

### Points System
- **FIFO-based**: First-in-first-out point consumption
- **Redis optimized**: High-performance point calculations
- **Expiration handling**: Automatic point expiration management

### Direct Purchase Feature
- **Strategy constant**: `STRATEGY_DIRECT = 3` 
- **SKU-based**: Direct purchase of specific SKUs without box_goods table
- **Cart support**: Multiple SKU purchases in single transaction
- **No randomness**: Deterministic purchase flow (buy what you see)

## Key Dependencies

### Core Framework
- **Gin Web Framework**: HTTP routing and middleware
- **GORM**: ORM for MySQL database operations
- **go-redis**: Redis client for caching and queues

### Third-party Integrations
- **gopay**: Payment gateway integration (WeChat, Alipay)
- **sarama**: Kafka client for message queue
- **elasticsearch**: Search engine integration
- **aliyun SDK**: OSS storage, SMS services

### Utilities
- **jsoniter**: High-performance JSON processing
- **jwt**: Authentication token handling
- **validator**: Request parameter validation
- **carbon**: Date/time manipulation