{"merchant": {"merchant_id": "MERC_QIANHU", "merchant_name": "千呼POS"}, "authentication": {"app_key": "MERC_QIANHU_20250811141013_05C61CF1", "app_secret": "85b9f1bd92a6bd6bc3854b8205b5d644"}, "endpoints": {"base_url": "https://api.example.com", "scan_order": "/api/v1/openapi/order/scan", "payment_callback": "/api/v1/openapi/payment/callback"}, "restrictions": {"qps_limit": 100, "daily_limit": 100000, "amount_limits": {"single_max": 10000, "daily_max": 100000}}, "signature": {"algorithm": "SHA256", "format": "JSON_BODY + '&key=' + APP_SECRET", "example": "See integration documentation for details"}, "created_at": "2025-08-11 14:10:13"}