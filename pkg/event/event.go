package event

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	redigo "github.com/gomodule/redigo/redis"
)

// EventType 事件类型
type EventType string

const (
	// 订单相关事件
	EventOrderCreated  EventType = "order.created"
	EventOrderPaid     EventType = "order.paid"
	EventOrderCanceled EventType = "order.canceled"
	EventOrderExpired  EventType = "order.expired"

	// 库存相关事件
	EventStockLocked   EventType = "stock.locked"
	EventStockReleased EventType = "stock.released"
	EventStockUpdated  EventType = "stock.updated"

	// 用户相关事件
	EventUserRegistered EventType = "user.registered"
	EventPointsChanged  EventType = "points.changed"
)

// Event 事件结构
type Event struct {
	ID        string                 `json:"id"`
	Type      EventType              `json:"type"`
	Source    string                 `json:"source"`
	Data      map[string]interface{} `json:"data"`
	Timestamp int64                  `json:"timestamp"`
	TraceID   string                 `json:"trace_id,omitempty"`
}

// EventHandler 事件处理器接口
type EventHandler interface {
	Handle(ctx context.Context, event *Event) error
	EventTypes() []EventType
}

// EventBus 事件总线
type EventBus struct {
	handlers  map[EventType][]EventHandler
	mu        sync.RWMutex
	streamKey string
}

// NewEventBus 创建事件总线
func NewEventBus() *EventBus {
	return &EventBus{
		handlers:  make(map[EventType][]EventHandler),
		streamKey: redis.GetPrefixKey("event:stream"),
	}
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(handler EventHandler) {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	for _, eventType := range handler.EventTypes() {
		eb.handlers[eventType] = append(eb.handlers[eventType], handler)
	}
}

// Publish 发布事件
func (eb *EventBus) Publish(ctx context.Context, event *Event) error {
	// 设置事件ID和时间戳
	if event.ID == "" {
		event.ID = fmt.Sprintf("%d_%s", time.Now().UnixNano(), event.Type)
	}
	if event.Timestamp == 0 {
		event.Timestamp = time.Now().Unix()
	}

	// 序列化事件
	eventData, err := json.Marshal(event)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("event marshal failed", "eventType", event.Type)
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	// 发布到Redis Stream
	if err := eb.publishToStream(ctx, event, eventData); err != nil {
		log.WithContext(ctx).WithError(err).Error("event publish to stream failed", "eventType", event.Type)
		return err
	}

	// 同步处理本地订阅者
	eb.handleLocalSubscribers(ctx, event)

	log.WithContext(ctx).Info("event published",
		"eventID", event.ID,
		"eventType", event.Type,
		"source", event.Source)

	return nil
}

// publishToStream 发布事件到Redis Stream
func (eb *EventBus) publishToStream(ctx context.Context, event *Event, eventData []byte) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	// 使用XADD命令添加到stream
	_, err := conn.Do("XADD", eb.streamKey, "*",
		"event_id", event.ID,
		"event_type", event.Type,
		"source", event.Source,
		"data", string(eventData),
		"timestamp", event.Timestamp)

	if err != nil {
		return fmt.Errorf("failed to publish to stream: %w", err)
	}

	return nil
}

// handleLocalSubscribers 处理本地订阅者
func (eb *EventBus) handleLocalSubscribers(ctx context.Context, event *Event) {
	eb.mu.RLock()
	handlers := eb.handlers[event.Type]
	eb.mu.RUnlock()

	for _, handler := range handlers {
		go func(h EventHandler) {
			defer func() {
				if r := recover(); r != nil {
					log.WithContext(ctx).Error("event handler panic",
						"eventType", event.Type,
						"panic", r)
				}
			}()

			if err := h.Handle(ctx, event); err != nil {
				log.WithContext(ctx).WithError(err).Error("event handler failed",
					"eventType", event.Type)
			}
		}(handler)
	}
}

// ConsumeFromStream 从Redis Stream消费事件
func (eb *EventBus) ConsumeFromStream(ctx context.Context, consumerGroup, consumerName string) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	// 创建消费者组（如果不存在）
	_, err := conn.Do("XGROUP", "CREATE", eb.streamKey, consumerGroup, "0", "MKSTREAM")
	if err != nil {
		// 忽略组已存在的错误
		if !contains(err.Error(), "BUSYGROUP") {
			log.WithContext(ctx).WithError(err).Error("failed to create consumer group")
			return err
		}
	}

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 读取消息
			reply, err := redigo.Values(conn.Do("XREADGROUP", "GROUP", consumerGroup, consumerName,
				"COUNT", 10, "BLOCK", 1000, "STREAMS", eb.streamKey, ">"))
			if err != nil {
				if err == redigo.ErrNil {
					continue // 超时，继续下一次读取
				}
				log.WithContext(ctx).WithError(err).Error("failed to read from stream")
				time.Sleep(time.Second)
				continue
			}

			// 处理消息
			if err := eb.processStreamMessages(ctx, conn, consumerGroup, reply); err != nil {
				log.WithContext(ctx).WithError(err).Error("failed to process stream messages")
			}
		}
	}
}

// processStreamMessages 处理Stream消息
func (eb *EventBus) processStreamMessages(ctx context.Context, conn redigo.Conn, consumerGroup string, reply []interface{}) error {
	for _, streamData := range reply {
		streamInfo, ok := streamData.([]interface{})
		if !ok || len(streamInfo) != 2 {
			continue
		}

		messages, ok := streamInfo[1].([]interface{})
		if !ok {
			continue
		}

		for _, msgData := range messages {
			msgInfo, ok := msgData.([]interface{})
			if !ok || len(msgInfo) != 2 {
				continue
			}

			msgID, ok := msgInfo[0].([]byte)
			if !ok {
				continue
			}

			fields, ok := msgInfo[1].([]interface{})
			if !ok {
				continue
			}

			// 解析消息
			event, err := eb.parseStreamMessage(fields)
			if err != nil {
				log.WithContext(ctx).WithError(err).Error("failed to parse stream message")
				// ACK消息以避免重复处理
				conn.Do("XACK", eb.streamKey, consumerGroup, string(msgID))
				continue
			}

			// 处理事件
			eb.handleLocalSubscribers(ctx, event)

			// ACK消息
			_, err = conn.Do("XACK", eb.streamKey, consumerGroup, string(msgID))
			if err != nil {
				log.WithContext(ctx).WithError(err).Error("failed to ack message")
			}
		}
	}

	return nil
}

// parseStreamMessage 解析Stream消息
func (eb *EventBus) parseStreamMessage(fields []interface{}) (*Event, error) {
	fieldMap := make(map[string]string)

	for i := 0; i < len(fields); i += 2 {
		if i+1 < len(fields) {
			key, ok1 := fields[i].([]byte)
			value, ok2 := fields[i+1].([]byte)
			if ok1 && ok2 {
				fieldMap[string(key)] = string(value)
			}
		}
	}

	eventDataStr, exists := fieldMap["data"]
	if !exists {
		return nil, fmt.Errorf("missing event data")
	}

	var event Event
	if err := json.Unmarshal([]byte(eventDataStr), &event); err != nil {
		return nil, fmt.Errorf("failed to unmarshal event: %w", err)
	}

	return &event, nil
}

// 工具函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr
}

// 全局事件总线实例
var DefaultEventBus *EventBus

// InitEventBus 初始化事件总线
func InitEventBus() {
	DefaultEventBus = NewEventBus()
}

// PublishOrderCreated 发布订单创建事件
func PublishOrderCreated(ctx context.Context, orderID uint64, userID uint64, totalAmount uint64) error {
	event := &Event{
		Type:   EventOrderCreated,
		Source: "order_service",
		Data: map[string]interface{}{
			"order_id":     orderID,
			"user_id":      userID,
			"total_amount": totalAmount,
		},
	}
	return DefaultEventBus.Publish(ctx, event)
}

// PublishOrderPaid 发布订单支付成功事件
func PublishOrderPaid(ctx context.Context, orderID uint64, userID uint64, paidAmount uint64) error {
	event := &Event{
		Type:   EventOrderPaid,
		Source: "order_service",
		Data: map[string]interface{}{
			"order_id":    orderID,
			"user_id":     userID,
			"paid_amount": paidAmount,
		},
	}
	return DefaultEventBus.Publish(ctx, event)
}

// PublishOrderCanceled 发布订单取消事件
func PublishOrderCanceled(ctx context.Context, orderID uint64, userID uint64, reason string) error {
	event := &Event{
		Type:   EventOrderCanceled,
		Source: "order_service",
		Data: map[string]interface{}{
			"order_id": orderID,
			"user_id":  userID,
			"reason":   reason,
		},
	}
	return DefaultEventBus.Publish(ctx, event)
}

// PublishStockLocked 发布库存锁定事件
func PublishStockLocked(ctx context.Context, skuID uint64, quantity uint32, orderID uint64) error {
	event := &Event{
		Type:   EventStockLocked,
		Source: "stock_service",
		Data: map[string]interface{}{
			"sku_id":   skuID,
			"quantity": quantity,
			"order_id": orderID,
		},
	}
	return DefaultEventBus.Publish(ctx, event)
}

// PublishStockReleased 发布库存释放事件
func PublishStockReleased(ctx context.Context, skuID uint64, quantity uint32, orderID uint64) error {
	event := &Event{
		Type:   EventStockReleased,
		Source: "stock_service",
		Data: map[string]interface{}{
			"sku_id":   skuID,
			"quantity": quantity,
			"order_id": orderID,
		},
	}
	return DefaultEventBus.Publish(ctx, event)
}
