package mq

import (
	"context"
	"crypto/sha256"
	"crypto/sha512"
	"crypto/tls"
	"crypto/x509"
	"hash"
	"hash/fnv"
	"io/ioutil"
	"os"
	"path/filepath"
	"time"

	"blind_box/config"
	"blind_box/pkg/log"
	"blind_box/pkg/util/retry_util"

	"github.com/IBM/sarama"
	"github.com/xdg/scram"
)

var _ Producer = new(KafkaProducer)

type KafkaProducer struct {
	pro     sarama.AsyncProducer
	address []string
}

func newKafkaProducer(address []string) *KafkaProducer {
	k := &KafkaProducer{
		address: address,
		pro:     createKafkaProducer(address),
	}
	return k
}

func getFullPath(file string) string {
	workPath, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	configPath := filepath.Join(workPath, "config")
	fullPath := filepath.Join(configPath, file)
	return fullPath
}

func GenerateKafkaConf(cfg *config.Kafka) (*sarama.Config, error) {
	var (
		err     error
		ctx     = context.Background()
		version sarama.KafkaVersion
	)

	kafkaConf := sarama.NewConfig()
	version, err = sarama.ParseKafkaVersion(config.KafkaCfg.Version)
	if err != nil {
		log.WithError(ctx, err).Error("invalid kafka version")
		return nil, err
	}
	kafkaConf.Version = version
	// set to newest in production environment to avoid large amount of duplication
	kafkaConf.Metadata.RefreshFrequency = 5 * time.Minute
	kafkaConf.Consumer.Offsets.Initial = sarama.OffsetNewest
	//Set to 5 minutes, align with jvm consumer
	kafkaConf.Consumer.Group.Rebalance.Timeout = 5 * 60 * time.Second

	kafkaConf.Producer.RequiredAcks = sarama.WaitForAll
	kafkaConf.Producer.Partitioner = sarama.NewRandomPartitioner
	kafkaConf.Producer.Return.Successes = true
	kafkaConf.Producer.Return.Errors = true
	//设置使用的kafka版本,如果低于V0_10_0_0版本,消息中的timestamp没有作用.需要消费和生产同时配置
	kafkaConf.ClientID = "mall-producer"

	switch cfg.Producer.SecurityProtocol {
	case "PLAINTEXT":
		//do nothing
	case "SASL_SSL":
		kafkaConf.Net.SASL.Enable = true
		kafkaConf.Net.SASL.User = cfg.Producer.SaslUsername
		kafkaConf.Net.SASL.Password = cfg.Producer.SaslPassword
		kafkaConf.Net.SASL.Handshake = true

		certBytes, err := ioutil.ReadFile(getFullPath(cfg.Producer.CaPath))
		if err != nil {
			log.WithError(ctx, err).Error("kafka client read cert file failed")
			return nil, err
		}
		clientCertPool := x509.NewCertPool()
		ok := clientCertPool.AppendCertsFromPEM(certBytes)
		if !ok {
			log.WithError(ctx, err).Error("kafka client failed to parse root certificate")
			return nil, err
		}
		kafkaConf.Net.TLS.Config = &tls.Config{
			RootCAs:            clientCertPool,
			InsecureSkipVerify: true,
		}
		kafkaConf.Net.TLS.Enable = true
	case "SASL_PLAINTEXT":
		kafkaConf.Net.SASL.Enable = true
		kafkaConf.Net.SASL.User = cfg.Producer.SaslUsername
		kafkaConf.Net.SASL.Password = cfg.Producer.SaslPassword
		kafkaConf.Net.SASL.Handshake = true
	default:
		log.WithContext(ctx).WithField(
			"cfg.SecurityProtocol", cfg.Producer.SecurityProtocol,
		).Error("unknown protocol")
		return nil, err
	}

	switch cfg.Producer.SaslMechanism {
	case "PLAIN":
		//do nothing
	case "SCRAM-SHA-256":
		kafkaConf.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient { return &XDGSCRAMClient{HashGeneratorFcn: SHA256} }
		kafkaConf.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
	case "SCRAM-SHA-512":
		kafkaConf.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient { return &XDGSCRAMClient{HashGeneratorFcn: SHA512} }
		kafkaConf.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
	default:
		log.WithContext(ctx).WithField(
			"cfg.SaslMechanism", cfg.Producer.SaslMechanism,
		).Error("invalid SHA algorithm")
		return nil, err
	}

	if err := kafkaConf.Validate(); err != nil {
		log.WithError(ctx, err).Error("Kafka producer config invalidate")
		return nil, err
	}
	return kafkaConf, nil
}

func createKafkaProducer(address []string) sarama.AsyncProducer {
	var (
		producer   sarama.AsyncProducer
		err        error
		retryTimes uint = 3
	)

	conf, err := GenerateKafkaConf(config.KafkaCfg)
	if err != nil {
		log.WithError(context.Background(), err).Error("generate kafka config error")
		return nil
	}

	//使用配置, 新建一个异步生产者
	err = retry_util.Do(func() error {
		producer, err = sarama.NewAsyncProducer(address, conf)
		if err != nil {
			return err
		}

		return nil
	}, retry_util.Attempts(retryTimes),
		retry_util.Delay(1*time.Second),
	)
	if err != nil {
		log.WithError(context.Background(), err).Error("create kafka producer error")
		return nil
	}

	//defer producer.AsyncClose()
	// 判断哪个通道发送过来数据.
	go func(p sarama.AsyncProducer) {
		for {
			select {
			case suc := <-p.Successes():
				if suc == nil {
					break
				}
			case fail := <-p.Errors():
				if fail != nil {
					log.WithError(context.Background(), fail.Err).Error("kafka producer error")
				}
			}
		}
	}(producer)

	log.WithContext(context.Background()).Info("Kafka Sarama producer up and running!...")

	return producer
}

func (k *KafkaProducer) Push(topic string, message string, key ...string) error {
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(message),
	}

	hostInfo, err := os.Hostname()
	if err != nil {
		log.WithError(context.Background(), err).Error("get host info error")
	}

	msg.Headers = []sarama.RecordHeader{
		// 记录一些服务信息、环境信息等
		{
			Key:   []byte("timestamp"),
			Value: []byte(time.Now().String()),
		},
		{
			Key:   []byte("env"),
			Value: []byte(config.AppCfg.Env),
		},
		{
			Key:   []byte("hostname"),
			Value: []byte(hostInfo),
		},

		{
			Key:   []byte("pod_ip"),
			Value: []byte(os.Getenv("POD_IP")),
		},
		{
			Key:   []byte("pass_namespace"),
			Value: []byte(os.Getenv("PAAS_NAMESPACE")),
		},
	}

	if len(key) > 0 {
		msg.Key = sarama.ByteEncoder(key[0])
	}
	//使用通道发送
	k.pro.Input() <- msg
	return nil
}

func (k *KafkaProducer) Close() {
	k.pro.AsyncClose()
}

var SHA256 scram.HashGeneratorFcn = func() hash.Hash { return sha256.New() }
var SHA512 scram.HashGeneratorFcn = func() hash.Hash { return sha512.New() }

type XDGSCRAMClient struct {
	*scram.Client
	*scram.ClientConversation
	scram.HashGeneratorFcn
}

func (x *XDGSCRAMClient) Begin(userName, password, authzID string) (err error) {
	x.Client, err = x.HashGeneratorFcn.NewClient(userName, password, authzID)
	if err != nil {
		return err
	}
	x.ClientConversation = x.Client.NewConversation()
	return nil
}

func (x *XDGSCRAMClient) Step(challenge string) (response string, err error) {
	response, err = x.ClientConversation.Step(challenge)
	return
}

func (x *XDGSCRAMClient) Done() bool {
	return x.ClientConversation.Done()
}

// implement the function pointer
// type PartitionerConstructor func(topic string) Partitioner
func NewStickyPartitioner(topic string) sarama.Partitioner {
	p := new(stickyPartitioner)
	p.hasher = fnv.New32a()
	p.referenceAbs = false
	p.stickSize = 32 * 1024
	p.totalSize = 0
	return p
}

type stickyPartitioner struct {
	hasher       hash.Hash32
	referenceAbs bool
	stickSize    int32
	totalSize    int32
}

func (p *stickyPartitioner) Partition(message *sarama.ProducerMessage, numPartitions int32) (int32, error) {
	if message.Key == nil {
		if message.Value != nil {
			p.totalSize += int32(message.Value.Length())
		}
		//prevent overflow
		if p.totalSize > p.stickSize*numPartitions {
			p.totalSize = p.totalSize - p.stickSize*numPartitions
		}
		//double check overflow
		if p.totalSize < 0 {
			p.totalSize = 0
		}
		return (p.totalSize / p.stickSize) % numPartitions, nil
	}
	bytes, err := message.Key.Encode()
	if err != nil {
		return -1, err
	}
	p.hasher.Reset()
	_, err = p.hasher.Write(bytes)
	if err != nil {
		return -1, err
	}
	var partition int32
	// Turns out we were doing our absolute value in a subtly different way from the upstream
	// implementation, but now we need to maintain backwards compat for people who started using
	// the old version; if referenceAbs is set we are compatible with the reference java client
	// but not past Sarama versions
	if p.referenceAbs {
		partition = (int32(p.hasher.Sum32()) & 0x7fffffff) % numPartitions
	} else {
		partition = int32(p.hasher.Sum32()) % numPartitions
		if partition < 0 {
			partition = -partition
		}
	}
	return partition, nil
}

func (p *stickyPartitioner) RequiresConsistency() bool {
	return true
}

func (p *stickyPartitioner) MessageRequiresConsistency(message *sarama.ProducerMessage) bool {
	return message.Key != nil
}
