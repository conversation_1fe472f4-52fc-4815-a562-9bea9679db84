package mq

import (
	"sync"
	"time"
)

var _ Producer = new(ChanProducer)

type LogModel struct {
}
type ChanProducer struct {
	logChan  chan *LogModel
	done     chan bool
	mu       sync.Mutex
	isClosed bool
}

func newChanProducer() *ChanProducer {
	return &ChanProducer{
		logChan: make(chan *LogModel, 1000),
		done:    make(chan bool),
	}
}

func (l *ChanProducer) Push(topic string, message string, key ...string) error {
	return nil
}

func (l *ChanProducer) Close() {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
}
