package ecode

/***********************************************************
* 100 100 6位错误码(前三位定义项目功能, 后三位定义错误)
* 前3位,如:
* 100:系统 101: 后台管理 102:用户相关 103:商品相关 104:订单相关
* 105:收款单 106:订单
* 107: 营销 1071xx: 公告
* 108: 仓库 1081xx: 出库 1082xx: 入库
* 后期功能各自占位即可
************************************************************/
var (
	OK                 = New("100000", "成功")
	CommonErrCode      = "100001"
	SystemErr          = New("100002", "系统异常，请联系客服")
	SystemBusyErr      = New("100003", "系统繁忙，稍后重试")
	ParamErr           = New("100004", "参数错误")
	TokenErr           = New("100005", "token已失效")
	EmptyDataErr       = New("100006", "数据为空")
	ParamInvalidErr    = New("100007", "参数无效")
	VisitorOnlyReadErr = New("100008", "游客无法使用此功能")
	FrequentOperateErr = New("100009", "操作频繁,请稍后再试")
	TooManyRequestsErr = New("100429", "请求过于频繁，请稍后重试")
	JsonMarshalErr     = New("100010", "Json Error")
	HttpErr            = New("100011", "Http Error")
	HttpRespErr        = New("100012", "Http Resp Error")
	SqlParamErr        = New("100013", "sql参数错误")
	SwitchCaseErr      = New("100014", "暂不支持此方式")
	NameExistErr       = New("100101", "该名称已存在，不可重复")
	IdsEmptyErr        = New("100102", "请选择要操作的数据或操作的数据无效")
	NoAuthErr          = New("100103", "无权进行此操作")
	TimeInvalidErr     = New("100104", "时间参数无效")
	ActBusyLockErr     = New("100105", "活动火爆进行中, 请稍后重试")

	RedisBatchCreateKeyNotExistErr = New("100201", "内容已过期, 请重新上传")

	// 后台管理相关(101):【0】角色 【1】菜单 【2】用户 [5] 资源管理
	AccountNeedRootAuthErr = New("101001", "仅限超级管理员可进行此操作")
	RoleExistErr           = New("101002", "角色名称不可重复")
	RoleDisableErr         = New("101003", "该角色已禁用")
	RoleHasAccountErr      = New("101004", "该角色下存在账号,不可删除")

	MenuPidSelfErr        = New("101101", "父级菜单不可为自身")
	MenuPidIsSelfChildErr = New("101102", "父级菜单不可为自己的子级菜单")
	MenuPidButtonErr      = New("101103", "父级菜单不可为按钮类型")
	MenuNotExistErr       = New("101104", "当前节点不存在,请先添加")
	MenuHasNotAuthErr     = New("101105", "无此权限,请联系管理员添加")

	MenuDelSystemMenuErr = New("101110", "系统菜单不可删除")
	MenuDelHasChildErr   = New("101111", "该菜单下存在子级菜单,请先删除子级菜单")

	AccountExistErr              = New("101201", "账号不可重复")
	AccountDisableErr            = New("101202", "无法禁用该账号，请先移除管理的客户账号")
	AdminAccountNotExistErr      = New("101203", "该账号不存在")
	AdminAccountDisableErr       = New("101204", "该账号已禁用")
	AdminAccountPwdErr           = New("101205", "该账号密码错误")
	AdminAccountNotOwnerErr      = New("101206", "仅限负责人可进行此操作")
	AdminBatchAccountNotOwnerErr = New("101207", "仅限负责人可进行此操作")
	AccountDelErr                = New("101208", "无法删除该账号，请先移除管理的客户账号")

	ResourceShopInUseErr = New("101501", "该店铺已被使用,不可进行此操作")

	// 用户相关(102): 【0】用户管理 【1】前台用户 【2】公司&财务 【3】地址
	UserTypeExistErr                 = New("102001", "该名称已存在，不可重复")
	UserDisableErr                   = New("102010", "该用户已禁用")
	UserExistErr                     = New("102011", "登录账号名不可重复")
	UserAssistMgrRedoErr             = New("102012", "负责员工与辅助支持人员不可重复设置")
	UserCreditTypeNoAuthErr          = New("102013", "该授信模式仅限超级管理员可编辑")
	UserCreditTypeNoRepayErr         = New("102014", "该用户授信支付欠款尚未结清,请先结清后再修改为其他模式")
	UserCreditAmountLessThanErr      = New("102015", "总额度不可少于已用额度")
	UserCreditRepayMoreThanErr       = New("102016", "还款金额不可大于冻结金额")
	UserCreditRepayBusyErr           = New("102017", "系统繁忙,请稍后再试")
	UserCreditAmountNeedSetTypeErr   = New("102018", "请先设置用户授信模式")
	UserCreditEarnestMoneySetTypeErr = New("102019", "仅限保证金模式下可修改")
	UserCreditNotOpenErr             = New("102020", "对应授信模式尚未开启")
	UserCreditNoFrozenAmountErr      = New("102021", "该用户授信支付尚无欠款")
	UserCreditDoneCountErr           = New("102022", "该用户授信支付数据有误")
	UserNotExistErr                  = New("102023", "该用户不存在")

	UserLoginAuthTypeErr = New("102102", "暂不支持此方式登录")

	// 用户签到&潮气值相关(103)
	UserTodaySignedErr           = New("103001", "今日已签到")
	UserGetedTaskRewardErr       = New("103002", "任务奖励已领取")
	UserTaskNotFinishErr         = New("103003", "请先完成任务再领取奖励")
	UserTideExchangeCardLimitErr = New("103004", "该道具卡已达今日兑换上限")
	UserTideCardTypeNotExistErr  = New("103005", "不存在的道具卡类型")

	UserTideNotEnoughErr = New("103011", "潮气值不足")
	UserTideIsMaxErr     = New("103012", "潮气值已达上限")

	// 活动相关(104)
	ActNotExistErr = New("104001", "活动不存在")
	ActFinishedErr = New("104002", "活动已结束")

	ActNotSubscribeErr     = New("104020", "未订阅该活动, 请先订阅")
	ActAleardySubscribeErr = New("104021", "已订阅该活动, 请勿重复订阅")

	ActSaleCalendarActExistErr = New("104030", "该活动已添加发售日历")
	ActSaleCalendarNotExistErr = New("104031", "该发售日历不存在")

	CouponSourceNotExistErr         = New("105001", "优惠券来源无效")
	CouponNotExistErr               = New("105002", "该优惠券不存在")
	CouponCodeExistErr              = New("105003", "优惠券code不可重复")
	CouponHasUserBillErr            = New("105004", "该优惠券已有产出,请先停用再编辑")
	CouponRangeActInvalidErr        = New("105005", "请输入有效活动ID")
	CouponUserAbandonUsingErr       = New("105006", "使用中,不可作废")
	CouponUserAbandonUsedErr        = New("105007", "已使用,不可作废")
	CouponUserAbandonedErr          = New("105008", "已作废,不可重复操作")
	CouponStatusInvalidErr          = New("105009", "优惠券已失效,请重新选择")
	CouponIssueNotExistErr          = New("105010", "该发放不存在")
	CouponIssueUploadEmptyErr       = New("105011", "未上传名单")
	CouponIssueImportOutErr         = New("105012", "单次至多上传1000条数据")
	CouponIssueImportUserInvalidErr = New("105013", "请上传有效名单")
	CouponIssueImportFileInvalidErr = New("105014", "该文件已失效,请重新上传")
	CouponIssueImportTypeErr        = New("105015", "该发放类型无需导入")
	CouponUserGetedErr              = New("105016", "您已领取过,请勿重复领取")
	CouponUserUsedErr               = New("105017", "优惠券已使用,请重新选择")
	CouponUserUsingErr              = New("105018", "优惠券已被占用,请重新选择")
	CouponNoUseConditionErr         = New("105019", "不满足优惠券使用条件")
	CouponHasUserEditRangeErr       = New("105020", "优惠券已有明细产出,无法修改使用范围")
	CouponIssueStartForbidTaegetErr = New("105021", "发放正在进行中,请先关闭再编辑")

	BoxLevelNotExistErr                = New("106200", "盲盒等级不存在")
	BoxLevelNameErr                    = New("106201", "名称已存在")
	BoxActiveTypeErr                   = New("106202", "非盲盒活动")
	BoxActiveGoodsNotEnoughErr         = New("106203", "累计库存不足")
	BoxActiveGoodsCurStockNotEnoughErr = New("106204", "可用库存不足")
	BoxActiveConfigExistErr            = New("106205", "配置已存在,不可重复添加")
	BoxActiveConfigNotExistErr         = New("106206", "箱子抽数和样式配置不存在")
	BoxActiveMustConfigLevelExistErr   = New("106207", "同一款式类型无法添加2条限制")
	BoxCardTypeNotExistErr             = New("106208", "不存在的道具卡类型")
	BoxCardNotExistErr                 = New("106209", "道具卡不存在")
	BoxCardDelStatusErr                = New("106210", "道具卡状态不可删除")
	BoxCardDeliveryStatusErr           = New("106211", "未发货无法确认收货")
	BoxActiveMustConfigGoodsNumErr     = New("106212", "赏品数量不可小于6大于16")
	BoxCardCodeExistErr                = New("106213", "道具卡code已存在")
	BoxCardSourceNotExistErr           = New("106214", "道具卡来源不存在")
	BoxSignExistErr                    = New("106250", "今日已签到")
	BoxRegTideIsGetErr                 = New("106251", "新人注册奖励已领取")
	BoxTaskTideIsGetErr                = New("106252", "任务奖励已领取")
	BoxTideTaskNotFinishErr            = New("106253", "请先完成任务再领取奖励")
	BoxTideExchangeCardLimitErr        = New("106254", "该道具卡已达今日兑换上限")
	BoxUserTideValNotExchangeErr       = New("106255", "潮气值不足")
	BoxUserTideValMaxErr               = New("106256", "潮气值已达上限")
	BoxActiveNoExistGoodsErr           = New("106257", "活动中不存在商品")
	BoxActiveRecommendGoodsErr         = New("106258", "普通款的数量必须小于等于箱内商品数量")
	BoxActiveTitleLongErr              = New("106259", "活动名称太长,50个字以内")
	BoxGoodsAddExistErr                = New("106260", "相同的商品不能重复添加")
	CardSourceAlreadyExist             = New("106261", "道具卡来源已存在")
	BoxGoodsNotExistErr                = New("106262", "盲盒商品不存在")
	BoxActiveNotExistErr               = New("106263", "盲盒活动不存在")
	BoxActiveErr                       = New("106264", "盲盒活动配置错误")
	BoxBagNotExistErr                  = New("106265", "盲盒中盒不存在")
	BoxSlotNotSelectErr                = New("106266", "请选择盲盒位置")

	BoxSourceCodeErr      = New("107000", "父赏品暂无条码，请生成")
	BoxDelGoodsErr        = New("107001", "赏品已有产出，不可删除")
	BoxActiveConfigErr    = New("107002", "无结束时间，不允许修改箱内赏品数量")
	BoxActiveConfigEndErr = New("107003", "未到结束时间，不允许修改箱内赏品数量")
	BoxActiveStartTimeErr = New("107004", "活动时间暂未开启")
	BoxActiveEndTimeErr   = New("107005", "活动时间已结束")

	// 商品相关 108
	SpuNotExistErr = New("108001", "spu不存在")
	SkuNotExistErr = New("108002", "sku不存在")
	SpuCodeExist   = New("108003", "商品编号不可重复")
	SkuCodeExist   = New("108004", "商品规格编号不可重复")

	SpuParamError        = New("108010", "spu参数错误")
	SkuParamError        = New("108011", "sku参数错误")
	SkuStockNotEnoughErr = New("108012", "sku库存不足")

	TagNameExist        = New("108050", "标签名称不可重复")
	TagNotExistErr      = New("108051", "标签不存在")
	TagInUseErr         = New("108052", "标签已被使用,不可进行此操作")
	TagGroupNotExistErr = New("108053", "标签组不存在")

	BatchTitleExist = New("108060", "批次标题不可重复")
	BatchNotExist   = New("108061", "批次不存在")
	BatchInUseErr   = New("108062", "批次已被使用,不可进程此操作")

	MarketBannerNotExistErr = New("109001", "该banner不存在")

	// 111
	BoxOrderAgainCreateOrder      = New("111001", "订单已创建,请勿重复创建")
	BoxOrderNotExistErr           = New("111002", "订单不存在")
	BoxOrderPayTimeoutErr         = New("111003", "订单支付超时")
	BoxOrderNotAllowPayErr        = New("111004", "该订单暂不能支付")
	BoxOrderPayMethodErr          = New("111005", "支付方式异常")
	BoxOrderCantCancelErr         = New("111006", "当前订单状态不可取消")
	BoxOrderStatusErr             = New("111007", "订单状态异常,请联系客服处理")
	BoxOrderPickupCodeErr         = New("111008", "该订单不是到店取货订单，无法生成取货码")
	BoxOrderPickupCodeGenErr      = New("111009", "取货码生成失败，请稍后再试")
	BoxOrderRefundErr             = New("111010", "当前订单状态不支持退款")
	BoxOrderRefundAlreadyErr      = New("111011", "该订单已有进行中的退款申请")
	BoxOrderRefundFeeErr          = New("111012", "退款金额不能超过订单实付金额")
	BoxOrderRefundNotExistErr     = New("111013", "退款记录不存在")
	BoxOrderRefundCantErr         = New("111014", "无权限查看该退款记录")
	BoxOrderUnsupportPayMethodErr = New("111015", "不支持的支付方式，请选择其他支付方式")
	BoxOrderCompleteFailErr       = New("111016", "订单完成失败")
	BoxOrderUpdateStatusErr       = New("111017", "订单状态更新失败")
	BoxOrderPickupCodeSaveErr     = New("111018", "取货码保存失败")
	BoxOrderTypeStoreErr          = New("111019", "该订单不是到店取货订单")
	BoxOrderTypeDeliveryErr       = New("111020", "该订单不是快递配送订单")
	BoxOrderRefundCancelErr       = New("111021", "只有进行中的退款才能取消")
	BoxOrderRefundRetryErr        = New("111022", "只有失败的退款才能重试")
)
