# pkg 目录规范文档

本文档记录 `pkg` 目录下公共工具包的代码规范和最佳实践。

## 目录结构

```
pkg/
├── ecode/              # 错误码定义和处理
│   ├── error.go       # 错误处理核心
│   └── errorCode.go   # 业务错误码定义
├── es/                # ElasticSearch 客户端
├── event/             # 事件系统
├── helper/            # 辅助工具函数
│   ├── app.go        # 应用辅助函数
│   ├── encode.go     # 编码相关
│   ├── excel.go      # Excel 处理
│   ├── function.go   # 通用函数
│   ├── gin.go        # Gin 框架辅助
│   ├── resp.go       # 响应处理
│   ├── srv.go        # 服务管理
│   └── validate.go   # 参数验证
├── job/              # 任务调度
│   ├── cursor.go     # 游标处理
│   ├── distributed_lock.go  # 分布式锁
│   └── metrics.go    # 指标收集
├── log/              # 日志系统
│   ├── log.go        # 日志核心
│   ├── logFormat.go  # 日志格式化
│   └── hook/         # 日志钩子
├── mq/               # 消息队列
│   ├── kafka.go      # Kafka 客户端
│   ├── eventbus.go   # 事件总线
│   └── chan.go       # Channel 实现
├── profile/          # 性能分析
├── qrcode/           # 二维码生成
├── redis/            # Redis 客户端和工具
│   ├── redis.go      # Redis 客户端
│   ├── keys_cache.go # 缓存键定义
│   ├── keys_lock.go  # 锁键定义
│   └── delay_queue.go # 延时队列
├── util/             # 通用工具
│   ├── ctxUtil/      # 上下文工具
│   ├── decimalUtil/  # 小数处理
│   ├── jwt.go        # JWT 认证
│   ├── retry_util/   # 重试机制
│   ├── safe_random/  # 安全随机数
│   ├── shard_util/   # 分片工具
│   ├── sliceUtil/    # 切片工具
│   ├── str_util/     # 字符串工具
│   ├── timeUtil/     # 时间工具
│   └── xlsxUtil/     # Excel 工具
└── utils/            # 其他工具
    ├── nonce.go      # Nonce 生成
    └── openapi_config.go  # OpenAPI 配置
```

## 核心组件规范

### 错误处理 (ecode/)

#### 错误码定义
```go
// 6位错误码规范
// 前3位: 功能模块
// 后3位: 具体错误
var (
    OK                 = New("100000", "成功")
    SystemErr          = New("100002", "系统异常，请联系客服")
    ParamErr           = New("100004", "参数错误")
    TokenErr           = New("100005", "token已失效")
    FrequentOperateErr = New("100009", "操作频繁,请稍后再试")
)
```

#### 错误码分类
- **100xxx**: 系统级错误
- **101xxx**: 后台管理相关
- **102xxx**: 用户相关
- **103xxx**: 签到/潮气值相关
- **104xxx**: 活动相关
- **105xxx**: 收款单相关
- **106xxx**: 订单相关
- **107xxx**: 营销相关
- **108xxx**: 仓库相关

#### 错误接口定义
```go
type Codes interface {
    Error() string
    Code() string
    Message() string
    FormatMsg(...any) string
}
```

#### 错误处理方法
```go
// 获取错误原因
func Cause(e error) Codes {
    if e == nil {
        return OK
    }
    ec, ok := errors.Cause(e).(Codes)
    if ok {
        return ec
    }
    return Code(e.Error())
}

// 格式化错误消息
func (c Code) FormatMsg(a ...any) string {
    if msg, ok := _messages.Load(c.Code()); ok {
        msg := msg.(string)
        if len(a) > 0 {
            return fmt.Sprintf(msg, a...)
        }
        return msg
    }
    return c.Error()
}
```

### 响应处理 (helper/resp.go)

#### 统一响应格式
```go
type Resp struct {
    Code string      `json:"code"`
    Msg  string      `json:"msg"`
    Data interface{} `json:"data"`
    Time float64     `json:"time"`  // 响应耗时（毫秒）
}
```

#### 响应方法
```go
// 仅返回状态
func AppResp(ctx *gin.Context, code, msg string)

// 返回数据
func AppWithDataResp(ctx *gin.Context, code, msg string, data interface{})

// 导出 Excel
func ExportExcelResp(ctx *gin.Context, buf []byte, filename string)
```

### Redis 客户端 (redis/)

#### 单例模式实现
```go
var (
    defaultEntry         *RedisClient
    defaultEntryInitOnce sync.Once
)

func GetRedisClient() *RedisClient {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newRedis(config.RedisCfg)
        })
    }
    return defaultEntry
}
```

#### 连接池配置
```go
redis.NewClient(&redis.Options{
    Addr:         c.Host,
    Username:     c.Username,
    Password:     c.Password,
    DB:           c.Db,
    PoolSize:     50,               // 连接池大小
    MinIdleConns: 5,                // 最小空闲连接数
    IdleTimeout:  30 * time.Second, // 空闲连接超时
})
```

#### 分布式锁
```go
// 锁时间常量
var (
    DefaultLockTime      = 5 * time.Second
    LockTimeTwentySecond = 20 * time.Second
    LockTimeHalfMinute   = 30 * time.Second
    LockTimeOneMinute    = 1 * time.Minute
    LockTimeTwoMinute    = 2 * time.Minute
)

// 重试锁
func (c *RedisClient) RetryLock(ginC *gin.Context, key string, expiry time.Duration) error
func (c *RedisClient) Unlock(key string) error
```

#### 缓存键规范
```go
// 缓存键命名规范: box:cache:类型:标识
const (
    // 列表缓存
    MenuListKey     = "box:cache:list:menu"
    RoleListKey     = "box:cache:list:role"
    
    // Token 缓存（带参数）
    AccountTokenKey = "box:cache:token:account:%d"
    UserTokenKey    = "box:cache:token:user:%d"
    
    // 信息缓存
    UserInfoKey     = "box:cache:info:user:%d"
    AccountInfoKey  = "box:cache:accountInfo:%d"
)

// 获取格式化的键
func GetInfoKey(id uint64) string {
    return fmt.Sprintf(UserInfoKey, id)
}
```

### 日志系统 (log/)

#### 异步日志器
```go
type AsyncLogger struct {
    logger   *logrus.Logger
    logChan  chan LogMsg     // 日志消息通道
    done     chan bool       // 关闭信号
    mu       sync.Mutex
    isClosed bool
}
```

#### 日志级别设置
```go
func setLogLevel() logrus.Level {
    switch config.LogCfg.Level {
    case "debug":
        return logrus.DebugLevel
    case "info":
        return logrus.InfoLevel
    case "warn":
        return logrus.WarnLevel
    case "error":
        return logrus.ErrorLevel
    default:
        return logrus.InfoLevel
    }
}
```

#### 日志使用
```go
// 带上下文的日志
log.Ctx(ctx).WithField("user_id", uid).Info("用户登录")

// 错误日志
log.Ctx(ctx).WithError(err).Error("操作失败")

// 字段日志
log.Ctx(ctx).WithFields(logrus.Fields{
    "order_id": orderID,
    "amount": amount,
}).Info("订单创建")
```

### 参数验证 (helper/validate.go)

#### 自定义验证器注册
```go
func LoadAppValidate() {
    vld, _ := binding.Validator.Engine().(*validator.Validate)
    zhs.RegisterDefaultTranslations(vld, trans)
    
    // 注册自定义验证
    vld.RegisterValidation("checkPhone", checkPhone)
    vld.RegisterValidation("requiredIF", requiredIF)
    vld.RegisterValidation("checkArrayRequired", checkArrayRequired)
    vld.RegisterValidation("checkStringLength", checkStringLength)
    vld.RegisterValidation("checkCodeValid", checkCodeValid)
    vld.RegisterValidation("default", setDefault)
}
```

#### 自定义验证器示例
```go
// 手机号验证
var checkPhone validator.Func = func(fl validator.FieldLevel) bool {
    if fl.Field().String() != "" {
        ok, _ := regexp.MatchString(`^1[3-9][0-9]{9}$`, fl.Field().String())
        return ok
    }
    return true
}

// 条件必填
var requiredIF validator.Func = func(fl validator.FieldLevel) bool {
    params := strings.Split(fl.Param(), " ")
    confirmField := fl.Parent().FieldByName(params[0])
    // 根据条件判断是否必填
}
```

#### 错误翻译
```go
// 自定义错误消息翻译
func CustomTranslate(err error, obj interface{}) string {
    // 优先使用 msg 标签
    if f, exist := getObj.Elem().FieldByName(e.Field()); exist {
        msg := f.Tag.Get("msg")
        if msg != "" {
            return msg
        }
    }
    // 使用默认翻译
    return e.Translate(trans)
}
```

### 安全随机数 (util/safe_random/)

#### 线程安全的随机数生成器
```go
type SafeRandom struct {
    rand *rand.Rand
    mu   sync.Mutex
}

// 全局实例（单例）
var globalRandom *SafeRandom
var once sync.Once

func GetGlobalRandom() *SafeRandom {
    once.Do(func() {
        globalRandom = New()
    })
    return globalRandom
}
```

#### 使用方法
```go
// 生成 [0,n) 范围的随机数
n := safe_random.Intn(100)

// 生成 [min,max) 范围的随机数
n := safe_random.IntnRange(10, 20)

// 生成随机浮点数
f := safe_random.Float64()

// 随机打乱切片
safe_random.Shuffle(len(slice), func(i, j int) {
    slice[i], slice[j] = slice[j], slice[i]
})
```

### 上下文管理 (helper/app.go)

#### 用户上下文
```go
type CtxUser struct {
    UID uint64
}

// 获取用户信息（失败则返回错误）
func GetCtxUser(ctx *gin.Context) (*CtxUser, error)

// 尝试获取用户信息（不报错）
func TryGetCtxUser(ctx *gin.Context) (*CtxUser, error)
```

#### 管理员上下文
```go
type CtxAccount struct {
    AccountID uint64
    RoleID    uint64
}

// 获取管理员信息
func GetCtxAccount(ctx *gin.Context) (*CtxAccount, error)
```

### 服务管理 (helper/srv.go)

#### 服务接口
```go
type Service interface {
    Start(ctx context.Context) error
    Close(ctx context.Context) error
    Name() string
}
```

#### 服务管理器
```go
type AppSrvMgr struct {
    services []Service
}

// 启动所有服务
func (a *AppSrvMgr) Start(ctx context.Context) error

// 关闭所有服务
func (a *AppSrvMgr) Close(ctx context.Context) error
```

## 工具函数规范

### 时间工具 (util/timeUtil/)
```go
// 获取当天开始时间
func GetDayStart(t time.Time) time.Time

// 获取当天结束时间
func GetDayEnd(t time.Time) time.Time

// 时间格式化
func Format(t time.Time, layout string) string
```

### 字符串工具 (util/str_util/)
```go
// 驼峰转下划线
func CamelToSnake(s string) string

// 下划线转驼峰
func SnakeToCamel(s string) string

// 生成随机字符串
func RandomString(length int) string
```

### 切片工具 (util/sliceUtil/)
```go
// 去重
func Unique[T comparable](slice []T) []T

// 包含判断
func Contains[T comparable](slice []T, item T) bool

// 差集
func Difference[T comparable](a, b []T) []T
```

### JWT 工具 (util/jwt.go)
```go
// 生成 Token
func GenerateToken(claims jwt.MapClaims, secret string) (string, error)

// 解析 Token
func ParseToken(tokenString, secret string) (jwt.MapClaims, error)

// 验证 Token
func ValidateToken(tokenString, secret string) bool
```

## 消息队列 (mq/)

### Kafka 客户端
```go
// 生产者
type Producer interface {
    Send(topic string, key, value []byte) error
    Close() error
}

// 消费者
type Consumer interface {
    Subscribe(topics []string) error
    Consume(handler func(message *Message)) error
    Close() error
}
```

### 事件总线
```go
// 发布事件
func Publish(event string, data interface{})

// 订阅事件
func Subscribe(event string, handler func(data interface{}))
```

## 最佳实践

### 1. 单例模式
```go
var (
    instance     *Type
    instanceOnce sync.Once
)

func GetInstance() *Type {
    if instance == nil {
        instanceOnce.Do(func() {
            instance = newInstance()
        })
    }
    return instance
}
```

### 2. 错误处理
```go
// Service 层返回业务错误
if err != nil {
    return ecode.SystemErr
}

// Handler 层使用 Cause 获取错误码
if err != nil {
    helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
}
```

### 3. 日志记录
```go
// 始终带上下文
log.Ctx(ctx).Info("message")

// 结构化日志
log.Ctx(ctx).WithFields(logrus.Fields{
    "key1": value1,
    "key2": value2,
}).Info("message")
```

### 4. Redis 操作
```go
// 使用统一的键前缀
key := fmt.Sprintf("box:cache:type:%d", id)

// 设置过期时间
expiry := 24 * time.Hour
client.Set(ctx, key, value, expiry)
```

### 5. 并发安全
```go
// 使用互斥锁
type SafeType struct {
    mu   sync.Mutex
    data map[string]interface{}
}

func (s *SafeType) Set(key string, value interface{}) {
    s.mu.Lock()
    defer s.mu.Unlock()
    s.data[key] = value
}
```

### 6. 资源管理
```go
// 使用 defer 确保资源释放
func process() error {
    resource, err := acquire()
    if err != nil {
        return err
    }
    defer resource.Close()
    
    // 使用资源
    return nil
}
```

## 性能优化

### 连接池复用
- Redis: 配置合理的连接池大小
- MySQL: 使用连接池避免频繁创建连接
- HTTP: 复用 HTTP 客户端

### 异步处理
- 日志: 使用异步日志避免 I/O 阻塞
- 消息: 使用消息队列解耦和异步处理

### 缓存策略
- 热点数据使用 Redis 缓存
- 合理设置缓存过期时间
- 使用分布式锁避免缓存击穿

## 安全规范

### 敏感信息
- 不记录密码、密钥等敏感信息
- 使用环境变量管理配置
- 日志脱敏处理

### 输入验证
- 所有外部输入必须验证
- 使用参数化查询防止 SQL 注入
- HTML 输出转义防止 XSS

### 随机数生成
- 使用 safe_random 包生成安全随机数
- 避免使用 math/rand 生成安全相关的随机数

## 扩展指南

### 添加新的工具包

1. 在 `pkg/` 下创建目录
2. 实现核心功能
3. 提供单例获取方法（如需要）
4. 编写单元测试
5. 更新本文档

### 添加新的错误码

1. 在 `errorCode.go` 中定义
2. 遵循错误码分类规范
3. 提供清晰的错误描述
4. 在相关模块使用

### 添加新的验证器

1. 在 `validate.go` 中实现验证函数
2. 在 `LoadAppValidate()` 中注册
3. 添加错误消息映射
4. 在 DTO 中使用

## 注意事项

1. **并发安全**: 全局变量必须考虑并发安全
2. **资源泄露**: 注意关闭文件、连接等资源
3. **错误处理**: 不要忽略错误，合理处理和记录
4. **性能影响**: 避免在热点路径做重操作
5. **代码复用**: 优先使用已有工具，避免重复造轮子