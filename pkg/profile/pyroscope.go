package profile

import (
	"os"
	"runtime"

	"blind_box/config"

	pyroscope "github.com/grafana/pyroscope-go"
	logging "gorm.io/plugin/opentelemetry/logging/logrus"
)

func InitPyroscope(appName string) {

	runtime.SetMutexProfileFraction(5)
	runtime.SetBlockProfileRate(5)

	hostname, _ := os.Hostname()

	_, err := pyroscope.Start(pyroscope.Config{
		ApplicationName: appName,
		Tags: map[string]string{
			"hostname":       hostname,
			"env":            config.AppCfg.Env,
			"pod_id":         os.Getenv("POD_IP"),
			"pass_namespace": os.Getenv("PAAS_NAMESPACE"),
		},
		ServerAddress: "http://***************:4040/",
		Logger:        logging.NewWriter(),
		ProfileTypes: []pyroscope.ProfileType{
			pyroscope.ProfileCPU,
			pyroscope.ProfileAllocObjects,
			pyroscope.ProfileAllocSpace,
			pyroscope.ProfileInuseObjects,
			pyroscope.ProfileInuseSpace,
			pyroscope.ProfileGoroutines,
			pyroscope.ProfileMutexCount,
			pyroscope.ProfileMutexDuration,
			pyroscope.ProfileBlockCount,
			pyroscope.ProfileBlockDuration,
		},
	})

	if err != nil {
		return
	}
}
