package redis

import (
	"blind_box/config"
	"blind_box/pkg/log"
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
)

var (
	LockMaxRetryTime     = 3 * time.Second
	LockRetryInterval    = 100 * time.Millisecond
	LockMaxInterval      = 10 * time.Second
	DefaultLockTime      = 5 * time.Second
	LockTimeTwentySecond = 20 * time.Second
	LockTimeHalfMinute   = 30 * time.Second
	LockTimeOneMinute    = 1 * time.Minute
	LockTimeTwoMinute    = 2 * time.Minute
)

type RedisConfig struct {
	Addr     string
	Password string
	Db       int
}

type RedisClient struct {
	*redis.Client
	redsyncClient *redsync.Redsync
	mu            sync.Mutex
	redsyncMap    map[string]*redsync.Mutex
}

var (
	defaultEntry         *RedisClient
	defaultEntryInitOnce sync.Once
)

// GetRedisClient .
func GetRedisClient() *RedisClient {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newRedis(config.RedisCfg)
		})
	}
	return defaultEntry

}

func newRedis(c *config.Redis) *RedisClient {
	client := redis.NewClient(&redis.Options{
		Addr:         c.Host,
		Username:     c.Username,
		Password:     c.Password,
		DB:           c.Db,
		PoolSize:     50,               // 连接池大小
		MinIdleConns: 5,                // 最小空闲连接数
		IdleTimeout:  30 * time.Second, // 空闲连接的超时时间
	})
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		panic(fmt.Sprintf("redis connect err %s", err.Error()))
	}

	return &RedisClient{
		Client:        client,
		redsyncMap:    map[string]*redsync.Mutex{},
		redsyncClient: redsync.New(goredis.NewPool(client)),
	}
}

// RetryLock .
func (c *RedisClient) RetryLock(ginC *gin.Context, key string, expiry time.Duration) error {
	ctx, cancel := context.WithTimeout(ginC.Request.Context(), expiry)
	defer cancel()

	b := backoff.NewExponentialBackOff()
	b.MaxInterval = LockMaxInterval
	if err := backoff.Retry(func() error {
		mutex := c.redsyncClient.NewMutex(key, redsync.WithExpiry(expiry))
		if err := mutex.Lock(); err != nil {
			return err
		}
		c.mu.Lock()
		c.redsyncMap[key] = mutex
		c.mu.Unlock()
		return nil
	}, backoff.WithContext(b, ctx)); err != nil {
		return err
	}
	return nil
}

// Lock .
func (c *RedisClient) Lock(ginC *gin.Context, key string, expiry time.Duration) error {
	mutex := c.redsyncClient.NewMutex(key,
		redsync.WithExpiry(expiry),
		redsync.WithTries(1), // 只尝试一次，不会重试
	)
	if err := mutex.Lock(); err != nil {
		return err
	}

	c.mu.Lock()
	c.redsyncMap[key] = mutex
	c.mu.Unlock()
	return nil
}

// Unlock .
func (c *RedisClient) Unlock(ctx *gin.Context, key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if mutex, ok := c.redsyncMap[key]; ok {
		if _, err := mutex.Unlock(); err != nil {
			log.Ctx(ctx).WithError(err).WithField("key", key).Error("redsync unlock err")
		}
		delete(c.redsyncMap, key)
	}
}

func (c *RedisClient) DoHSet(ctx context.Context, key string, data map[string]interface{}, timeout int) (err error) {
	var list = make([]interface{}, 0)

	for k, v := range data {
		list = append(list, k, v)
	}

	if err = c.HSet(ctx, key, list...).Err(); err != nil {
		return err
	}

	if timeout > 0 {
		// timeout 参数以秒为单位，需要乘以 time.Second 转换为正确的过期时间
		if err = c.Expire(ctx, key, time.Duration(timeout)*time.Second).Err(); err != nil {
			return err
		}
	}

	return nil
}

func (c *RedisClient) DoSAdd(ctx context.Context, key string, data interface{}) (err error) {
	if err = c.SAdd(ctx, key, data).Err(); err != nil {
		return err
	}
	return nil
}
