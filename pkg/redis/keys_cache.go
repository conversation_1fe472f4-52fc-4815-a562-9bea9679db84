package redis

import "fmt"

const (
	MenuListKey     = "box:cache:list:menu"
	RoleListKey     = "box:cache:list:role"
	AccountListKey  = "box:cache:list:account"
	AuthMenuListKey = "box:cache:list:authMenu"
	UserTypeListKey = "box:cache:list:userType"
	AreaListKey     = "box:cache:list:area"
	ConfigListKey   = "box:cache:list:config"
	LogBlackListKey = "box:cache:list:logBlack"
	ShopListKey     = "box:cache:list:shop"
	BrandListKey    = "box:cache:list:brand"
	SupplierListKey = "box:cache:list:supplier"
	VendorListKey   = "box:cache:list:vendor"

	AccountTokenKey = "box:cache:token:account:%d"
	UserTokenKey    = "box:cache:token:user:%d"
	UserInfoKey     = "box:cache:info:user:%d"

	RoleMenuIdsKey     = "box:cache:releMenuIds:%d"
	AccountInfoKey     = "box:cache:accountInfo:%d"
	AccountAuthMenuKey = "box:cache:accountAuthMenu:%d"

	SpuGroupTagListKey = "box:cache:list:tag:group:%d"

	SignConfigListKey          = "box:cache:user:signConfigList"
	UserSignInfoKey            = "box:cache:user:sign:%d"
	TideTaskConfigListKey      = "box:cache:tide:taskConfigList"
	TideCardConfigListKey      = "box:cache:tide:exchangeCardConfigList"
	UserTideTaskStatusKey      = "box:cache:tide:userTaskStatus:%d"
	UserGetRegTideKey          = "box:cache:tide:userGetRegTide:%d"
	UserGetTaskTideKey         = "box:cache:tide:userGetTaskTide:%d"
	UserTideExchangeCardKey    = "box:cache:tide:userExchangeCard:%d"
	CouponSourceListKey        = "box:cache:coupon:SourceList"
	ActivityTagListKey         = "box:cache:activityTagList"
	CouponIssueImportTargetKey = "box:cache:coupon:issueImportTarget:%v"

	WxpayOrderInfoKey = "box:cache:wxpay:order:info:%s"
	UserCollectKey    = "box:cache:userCollect:%d_%d"

	// Page Config Cache Keys - 页面配置缓存键
	PageConfigListKey   = "box:cache:list:pageConfig"
	PageConfigKey       = "box:cache:pageConfig:%s"
	PageConfigAppletKey = "box:cache:pageConfig:applet"

	BoxOrderPrepayIdKey = "box:cache:order:prepayid:%s"

	// 取货码相关Redis键
	PickupCodeKey = "box:cache:pickup:code:%s" // 订单号 -> 取货码
)

func GetRoleMenuIdsKey(rid uint64) string {
	return fmt.Sprintf(RoleMenuIdsKey, rid)
}

func GetAccountTokenKey(aid uint64) string {
	return fmt.Sprintf(AccountTokenKey, aid)
}

func GetAccountInfoKey(aid uint64) string {
	return fmt.Sprintf(AccountInfoKey, aid)
}

func GetSpuGroupTagListKey(groupId uint32) string {
	return fmt.Sprintf(SpuGroupTagListKey, groupId)
}

func GetAccountAuthMenuKey(aid uint64) string {
	return fmt.Sprintf(AccountAuthMenuKey, aid)
}

func GetUserTokenKey(uid uint64) string {
	return fmt.Sprintf(UserTokenKey, uid)
}

func GetUserInfoKey(uid uint64) string {
	return fmt.Sprintf(UserInfoKey, uid)
}

func GetUserSignInfoKey(uid uint64) string {
	return fmt.Sprintf(UserSignInfoKey, uid)
}

func GetUserTideTaskStatusKey(uid uint64) string {
	return fmt.Sprintf(UserTideTaskStatusKey, uid)
}

func GetUserGetRegTideKey(uid uint64) string {
	return fmt.Sprintf(UserGetRegTideKey, uid)
}
func GetUserGetTaskTideKey(uid uint64) string {
	return fmt.Sprintf(UserGetTaskTideKey, uid)
}
func GetUserTideExchangeCardKey(uid uint64) string {
	return fmt.Sprintf(UserTideExchangeCardKey, uid)
}
func GetCouponIssueImportTargetKey(key string) string {
	return fmt.Sprintf(CouponIssueImportTargetKey, key)
}

func GetWxpayOrderInfoKey(billNo string) string {
	return fmt.Sprintf(WxpayOrderInfoKey, billNo)
}

func GetUserCollectKey(uid uint64, entityType uint32) string {
	return fmt.Sprintf(UserCollectKey, uid, entityType)
}

// GetPageConfigKey 获取单个页面配置缓存键
func GetPageConfigKey(key string) string {
	return fmt.Sprintf(PageConfigKey, key)
}

func GetBoxOrderPrepayIdKey(orderNo string) string {
	return fmt.Sprintf(BoxOrderPrepayIdKey, orderNo)
}

// GetPickupCodeKey 获取订单取货码缓存键
func GetPickupCodeKey(orderNo string) string {
	return fmt.Sprintf(PickupCodeKey, orderNo)
}
