package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"blind_box/pkg/log"
)

// DelayQueueFallback 延迟队列降级机制
type DelayQueueFallback struct {
	backupKey string
}

// NewDelayQueueFallback 创建延迟队列降级机制
func NewDelayQueueFallback(queueName string) *DelayQueueFallback {
	return &DelayQueueFallback{
		backupKey: GetPrefixKey("delay_queue_backup:" + queueName),
	}
}

// BackupItem 备份队列项到Redis Hash（当ZSet操作失败时使用）
func (dqf *DelayQueueFallback) BackupItem(ctx context.Context, item *DelayQueueItem) error {
	client := GetRedisClient()

	// 序列化数据
	data, err := json.Marshal(item)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("marshal backup item failed")
		return err
	}

	// 使用Hash存储，field为item.ID，value为序列化数据
	field := fmt.Sprintf("%s:%d", item.ID, item.ExpireAt)
	err = client.HSet(ctx, dqf.backupKey, field, string(data)).Err()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("backup delay queue item failed: %s", item.ID)
		return err
	}

	// 设置Hash的过期时间为48小时（比延迟队列长一倍）
	client.Expire(ctx, dqf.backupKey, 48*time.Hour)

	log.WithContext(ctx).Info("delay queue item backed up: %s", item.ID)
	return nil
}

// RecoverExpiredItems 从备份中恢复过期项
func (dqf *DelayQueueFallback) RecoverExpiredItems(ctx context.Context) ([]*DelayQueueItem, error) {
	client := GetRedisClient()

	// 获取所有备份项
	fields, err := client.HGetAll(ctx, dqf.backupKey).Result()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("get backup items failed")
		return nil, err
	}

	now := time.Now().Unix()
	expiredItems := make([]*DelayQueueItem, 0)
	expiredFields := make([]string, 0)

	for field, value := range fields {
		var item DelayQueueItem
		if err := json.Unmarshal([]byte(value), &item); err != nil {
			log.WithContext(ctx).WithError(err).Error("unmarshal backup item failed")
			continue
		}

		// 检查是否过期
		if item.ExpireAt <= now {
			expiredItems = append(expiredItems, &item)
			expiredFields = append(expiredFields, field)
		}
	}

	// 删除已过期的备份项
	if len(expiredFields) > 0 {
		err = client.HDel(ctx, dqf.backupKey, expiredFields...).Err()
		if err != nil {
			log.WithContext(ctx).WithError(err).Error("delete expired backup items failed")
		} else {
			log.WithContext(ctx).Info("removed expired backup items: %d", len(expiredFields))
		}
	}

	return expiredItems, nil
}

// CleanBackupItems 清理备份项
func (dqf *DelayQueueFallback) CleanBackupItems(ctx context.Context, beforeTime time.Time) error {
	client := GetRedisClient()

	// 获取所有备份项
	fields, err := client.HGetAll(ctx, dqf.backupKey).Result()
	if err != nil {
		return err
	}

	expiredFields := make([]string, 0)
	for field, value := range fields {
		var item DelayQueueItem
		if err := json.Unmarshal([]byte(value), &item); err != nil {
			continue
		}

		if item.ExpireAt < beforeTime.Unix() {
			expiredFields = append(expiredFields, field)
		}
	}

	if len(expiredFields) > 0 {
		count, err := client.HDel(ctx, dqf.backupKey, expiredFields...).Result()
		if err != nil {
			return err
		}
		log.WithContext(ctx).Info("cleaned backup items: %d", count)
	}

	return nil
}

// 全局备份实例
var (
	OrderTimeoutFallback *DelayQueueFallback
)

// InitDelayQueueFallbacks 初始化延迟队列降级机制
func InitDelayQueueFallbacks() {
	OrderTimeoutFallback = NewDelayQueueFallback("order_timeout")
}
