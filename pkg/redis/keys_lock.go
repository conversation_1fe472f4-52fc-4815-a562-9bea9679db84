package redis

import "fmt"

var (
	LockAdminUserBalance     = "box:lock:adminUserBalance:%v"
	LockAdminUserRepayCredit = "box:lock:adminUserRepayCredit:%v"

	LockUserTideExchangeCard = "box:lock:userTideExchangeCard:%v"
	LockUserRegCouponReward  = "box:lock:userRegCouponReward:%v"
	LockHomepageCouponReward = "box:lock:homepageCouponReward:%v"
	LockNewUserReward        = "box:lock:newUserReward:%v"

	LockUserItemCard = "box:lock:userItemCard:%v"

	LockLoginSendCard = "box:lock:loginSendCard:%v"

	LockBoxOrderTradeNo = "box:lock:orderTradeNo:%v"
	LockBoxOrderBoxID   = "box:lock:orderBoxID:%v"
	LockBoxOrderUserID  = "box:lock:orderUserID:%v"
	LockBoxOrderGoodsID = "box:lock:orderGoodsID:%v"

	// BoxLock_CronEveryDayDegradeTide = "boxLock:CronEveryDayDegradeTide"
	// BoxLock_CronActSubscribeNotify  = "boxLock:CronActSubscribeNotify"
	// BoxLock_UserTideExchangeCard    = "boxLock:UserTideExchangeCard:%d"
	// BoxLock_UserGetRegCoupon        = "boxLock:UserGetRegCoupon:%d"
	// BoxLock_UserGetHomepageCoupon   = "boxLock:UserGetHomepageCoupon:%d"
)

func GetUserBalanceLockKey(userID uint64) string {
	return fmt.Sprintf(LockAdminUserBalance, userID)
}

func GetUserRepayCreditLockKey(userID uint64) string {
	return fmt.Sprintf(LockAdminUserRepayCredit, userID)
}

func GetUserTideExchangeCardLockKey(uid uint64) string {
	return fmt.Sprintf(LockUserTideExchangeCard, uid)
}

func GetUserRegCouponRewardLockKey(uid uint64) string {
	return fmt.Sprintf(LockUserRegCouponReward, uid)
}

func GetHomePageCouponRewardLockKey(uid uint64) string {
	return fmt.Sprintf(LockHomepageCouponReward, uid)
}

func GetNewUserRewardLockKey(uid uint64) string {
	return fmt.Sprintf(LockNewUserReward, uid)
}

func GetUserItemCardLockKey(uid uint64) string {
	return fmt.Sprintf(LockUserItemCard, uid)
}

func GetLoginSendCardLockKey(uid uint64) string {
	return fmt.Sprintf(LockLoginSendCard, uid)
}

func GetBoxOrderTradeNoLockKey(tradeNo string) string {
	return fmt.Sprintf(LockBoxOrderTradeNo, tradeNo)
}

func GetBoxOrderBoxIDLockKey(boxID uint64) string {
	return fmt.Sprintf(LockBoxOrderBoxID, boxID)
}

func GetBoxOrderUserIDLockKey(userID uint64) string {
	return fmt.Sprintf(LockBoxOrderUserID, userID)
}

func GetBoxOrderGoodsIDLockKey(goodsID uint64) string {
	return fmt.Sprintf(LockBoxOrderGoodsID, goodsID)
}
