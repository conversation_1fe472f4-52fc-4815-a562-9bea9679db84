package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"blind_box/pkg/log"

	"github.com/go-redis/redis/v8"
)

// DelayQueueItem 延迟队列项
type DelayQueueItem struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Data     map[string]interface{} `json:"data"`
	ExpireAt int64                  `json:"expire_at"`
}

// DelayQueue 延迟队列管理器
type DelayQueue struct {
	queueKey string
}

// NewDelayQueue 创建延迟队列
func NewDelayQueue(queueName string) *DelayQueue {
	return &DelayQueue{
		queueKey: GetPrefixKey("delay_queue:" + queueName),
	}
}

// AddItem 添加延迟队列项（带重试和降级机制）
func (dq *DelayQueue) AddItem(ctx context.Context, item *DelayQueueItem) error {
	// 序列化数据
	data, err := json.Marshal(item)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("marshal delay queue item failed")
		return err
	}

	// 使用 ZADD 添加到有序集合，score 为过期时间戳
	client := GetRedisClient()

	// 重试机制：最多重试3次
	for attempt := 1; attempt <= 3; attempt++ {
		err = client.ZAdd(ctx, dq.queueKey, &redis.Z{
			Score:  float64(item.ExpireAt),
			Member: string(data),
		}).Err()

		if err == nil {
			log.WithContext(ctx).Info("delay queue item added: %s, type: %s, expireAt: %d",
				item.ID, item.Type, item.ExpireAt)
			return nil
		}

		log.WithContext(ctx).WithError(err).Warn("add delay queue item failed, attempt %d/3: %s", attempt, item.ID)

		// 短暂等待后重试
		if attempt < 3 {
			time.Sleep(time.Duration(attempt*100) * time.Millisecond)
		}
	}

	// 所有重试都失败，启用降级机制
	log.WithContext(ctx).WithError(err).Error("delay queue add failed after all retries, using fallback: %s", item.ID)

	// 根据队列名确定降级实例
	var fallback *DelayQueueFallback
	switch {
	case dq.queueKey == GetPrefixKey("delay_queue:order_timeout"):
		fallback = OrderTimeoutFallback
	default:
		// 创建临时降级实例
		fallback = NewDelayQueueFallback("generic")
	}

	if fallbackErr := fallback.BackupItem(ctx, item); fallbackErr != nil {
		log.WithContext(ctx).WithError(fallbackErr).Error("fallback backup also failed: %s", item.ID)
		return fmt.Errorf("both delay queue and fallback failed: %w", err)
	}

	return nil // 降级成功，不返回错误
}

// AddOrderTimeout 添加订单超时项（便捷方法）
func (dq *DelayQueue) AddOrderTimeout(ctx context.Context, orderID uint64, timeoutMinutes int) error {
	expireAt := time.Now().Add(time.Duration(timeoutMinutes) * time.Minute).Unix()

	item := &DelayQueueItem{
		ID:   fmt.Sprintf("order:%d", orderID),
		Type: "order_timeout",
		Data: map[string]interface{}{
			"order_id": orderID,
			"action":   "cancel_timeout",
		},
		ExpireAt: expireAt,
	}

	return dq.AddItem(ctx, item)
}

// RemoveItem 移除延迟队列项
func (dq *DelayQueue) RemoveItem(ctx context.Context, itemID string) error {
	client := GetRedisClient()

	// 由于 ZREM 需要完整的 member，我们需要先找到匹配的项
	items, err := dq.GetExpiredItems(ctx, time.Now().Add(24*time.Hour).Unix(), 1000)
	if err != nil {
		return err
	}

	for _, item := range items {
		if item.ID == itemID {
			err = client.ZRem(ctx, dq.queueKey, string(mustMarshal(item))).Err()
			if err != nil {
				log.WithContext(ctx).WithError(err).Error("remove delay queue item failed: %s", itemID)
				return err
			}
			log.WithContext(ctx).Info("delay queue item removed: %s", itemID)
			return nil
		}
	}

	return nil
}

// RemoveOrderTimeout 移除订单超时项（便捷方法）
func (dq *DelayQueue) RemoveOrderTimeout(ctx context.Context, orderID uint64) error {
	return dq.RemoveItem(ctx, fmt.Sprintf("order:%d", orderID))
}

// GetExpiredItems 获取已过期的项
func (dq *DelayQueue) GetExpiredItems(ctx context.Context, maxScore int64, limit int) ([]*DelayQueueItem, error) {
	client := GetRedisClient()

	// 使用 ZRANGEBYSCORE 获取过期项
	opt := &redis.ZRangeBy{
		Min: "0",
		Max: fmt.Sprintf("%d", maxScore),
	}
	if limit > 0 {
		opt.Count = int64(limit)
	}

	values, err := client.ZRangeByScore(ctx, dq.queueKey, opt).Result()
	if err != nil {
		if err == redis.Nil {
			return []*DelayQueueItem{}, nil
		}
		log.WithContext(ctx).WithError(err).Error("get expired items failed")
		return nil, err
	}

	items := make([]*DelayQueueItem, 0, len(values))
	for _, value := range values {
		var item DelayQueueItem
		if err := json.Unmarshal([]byte(value), &item); err != nil {
			log.WithContext(ctx).WithError(err).Error("unmarshal delay queue item failed")
			continue
		}
		items = append(items, &item)
	}

	return items, nil
}

// ProcessExpiredItems 处理过期项并移除（包含降级恢复）
func (dq *DelayQueue) ProcessExpiredItems(ctx context.Context, processor func(*DelayQueueItem) error) error {
	now := time.Now().Unix()
	items, err := dq.GetExpiredItems(ctx, now, 100) // 每次处理100个
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("get expired items from delay queue failed")
		// 尝试从降级备份中恢复
		return dq.processFromFallback(ctx, processor)
	}

	client := GetRedisClient()

	processedCount := 0
	failedCount := 0
	for _, item := range items {
		// 处理项
		if err := processor(item); err != nil {
			log.WithContext(ctx).WithError(err).Error("process delay queue item failed: %s", item.ID)
			failedCount++
			// 处理失败的项目暂不移除，下次继续处理
			continue
		}

		// 移除已处理的项
		data := mustMarshal(item)
		err = client.ZRem(ctx, dq.queueKey, string(data)).Err()
		if err != nil {
			log.WithContext(ctx).WithError(err).Error("remove processed item failed: %s", item.ID)
			continue
		}

		processedCount++
	}

	// 同时检查降级备份中的过期项
	fallbackProcessed := 0
	if err := dq.processFromFallback(ctx, processor); err == nil {
		fallbackProcessed = 1 // 简化统计，实际可以返回具体数量
	}

	if processedCount > 0 || failedCount > 0 || fallbackProcessed > 0 {
		log.WithContext(ctx).Info("processed delay queue items: %d success, %d failed, %d from fallback",
			processedCount, failedCount, fallbackProcessed)
	}

	return nil
}

// GetQueueSize 获取队列大小
func (dq *DelayQueue) GetQueueSize(ctx context.Context) (int64, error) {
	client := GetRedisClient()

	size, err := client.ZCard(ctx, dq.queueKey).Result()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("get queue size failed")
		return 0, err
	}

	return size, nil
}

// GetQueueStats 获取队列统计信息
func (dq *DelayQueue) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	client := GetRedisClient()

	// 总数量
	totalSize, err := client.ZCard(ctx, dq.queueKey).Result()
	if err != nil {
		return nil, err
	}

	// 已过期数量
	now := time.Now().Unix()
	expiredSize, err := client.ZCount(ctx, dq.queueKey, "0", fmt.Sprintf("%d", now)).Result()
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"queue_name":   dq.queueKey,
		"total_size":   totalSize,
		"expired_size": expiredSize,
		"pending_size": totalSize - expiredSize,
		"check_time":   time.Now().Format(time.RFC3339),
	}

	return stats, nil
}

// CleanExpiredItems 清理过期项（维护任务）
func (dq *DelayQueue) CleanExpiredItems(ctx context.Context, beforeTime time.Time) error {
	client := GetRedisClient()

	count, err := client.ZRemRangeByScore(ctx, dq.queueKey, "0", fmt.Sprintf("%d", beforeTime.Unix())).Result()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("clean expired items failed")
		return err
	}

	if count > 0 {
		log.WithContext(ctx).Info("cleaned expired items: %d", count)
	}

	return nil
}

// mustMarshal 辅助函数，Marshal失败时panic
func mustMarshal(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		panic(fmt.Sprintf("marshal failed: %v", err))
	}
	return data
}

// processFromFallback 从降级备份中处理过期项
func (dq *DelayQueue) processFromFallback(ctx context.Context, processor func(*DelayQueueItem) error) error {
	// 根据队列名确定降级实例
	var fallback *DelayQueueFallback
	switch {
	case dq.queueKey == GetPrefixKey("delay_queue:order_timeout"):
		fallback = OrderTimeoutFallback
	default:
		return nil // 其他队列暂不支持降级恢复
	}

	if fallback == nil {
		return nil
	}

	// 从备份中获取过期项
	expiredItems, err := fallback.RecoverExpiredItems(ctx)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("recover from fallback failed")
		return err
	}

	if len(expiredItems) == 0 {
		return nil
	}

	log.WithContext(ctx).Info("recovered %d items from fallback", len(expiredItems))

	// 处理恢复的项
	for _, item := range expiredItems {
		if err := processor(item); err != nil {
			log.WithContext(ctx).WithError(err).Error("process fallback item failed: %s", item.ID)
			// 处理失败，重新备份
			fallback.BackupItem(ctx, item)
		}
	}

	return nil
}

// 全局延迟队列实例
var (
	OrderTimeoutQueue *DelayQueue
)

// InitDelayQueues 初始化延迟队列
func InitDelayQueues() {
	OrderTimeoutQueue = NewDelayQueue("order_timeout")
	InitDelayQueueFallbacks()
}
