package job

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// JobMetrics 定时任务监控指标
type JobMetrics struct {
	// 任务执行次数
	TaskExecutions *prometheus.CounterVec
	// 任务执行耗时
	TaskDuration *prometheus.HistogramVec
	// 任务执行错误次数
	TaskErrors *prometheus.CounterVec
	// 任务执行成功次数
	TaskSuccess *prometheus.CounterVec
	// 当前正在执行的任务数
	TasksInProgress prometheus.Gauge
	// 延迟队列大小
	DelayQueueSize *prometheus.GaugeVec
	// 分布式锁获取情况
	LockAcquisitions *prometheus.CounterVec
	// 游标延迟（当前时间 - 游标最后更新时间）
	CursorLag *prometheus.GaugeVec
}

var (
	// 全局指标实例
	Metrics *JobMetrics
)

// InitMetrics 初始化监控指标
func InitMetrics() {
	Metrics = &JobMetrics{
		TaskExecutions: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "job_task_executions_total",
				Help: "Total number of job task executions",
			},
			[]string{"task_name", "status"}, // status: started, completed
		),

		TaskDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "job_task_duration_seconds",
				Help:    "Job task execution duration in seconds",
				Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120, 300}, // 0.1s到5分钟
			},
			[]string{"task_name"},
		),

		TaskErrors: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "job_task_errors_total",
				Help: "Total number of job task errors",
			},
			[]string{"task_name", "error_type"},
		),

		TaskSuccess: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "job_task_success_total",
				Help: "Total number of successful job task executions",
			},
			[]string{"task_name"},
		),

		TasksInProgress: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "job_tasks_in_progress",
				Help: "Number of job tasks currently in progress",
			},
		),

		DelayQueueSize: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "job_delay_queue_size",
				Help: "Number of items in delay queue",
			},
			[]string{"queue_name", "status"}, // status: total, expired, pending
		),

		LockAcquisitions: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "job_lock_acquisitions_total",
				Help: "Total number of distributed lock acquisition attempts",
			},
			[]string{"job_name", "result"}, // result: acquired, failed, skipped
		),

		CursorLag: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "job_cursor_lag_minutes",
				Help: "Job cursor lag in minutes (current time - last update time)",
			},
			[]string{"job_name"},
		),
	}
}

// RecordTaskStart 记录任务开始
func (jm *JobMetrics) RecordTaskStart(taskName string) {
	jm.TaskExecutions.WithLabelValues(taskName, "started").Inc()
	jm.TasksInProgress.Inc()
}

// RecordTaskCompletion 记录任务完成
func (jm *JobMetrics) RecordTaskCompletion(taskName string, duration time.Duration, err error) {
	jm.TasksInProgress.Dec()
	jm.TaskExecutions.WithLabelValues(taskName, "completed").Inc()
	jm.TaskDuration.WithLabelValues(taskName).Observe(duration.Seconds())

	if err != nil {
		// 根据错误类型分类
		errorType := "unknown"
		switch {
		case err.Error() == "context deadline exceeded":
			errorType = "timeout"
		case err.Error() == "connection refused":
			errorType = "connection"
		default:
			errorType = "business"
		}
		jm.TaskErrors.WithLabelValues(taskName, errorType).Inc()
	} else {
		jm.TaskSuccess.WithLabelValues(taskName).Inc()
	}
}

// RecordLockAcquisition 记录分布式锁获取情况
func (jm *JobMetrics) RecordLockAcquisition(jobName string, result string) {
	jm.LockAcquisitions.WithLabelValues(jobName, result).Inc()
}

// UpdateDelayQueueSize 更新延迟队列大小
func (jm *JobMetrics) UpdateDelayQueueSize(queueName string, total, expired, pending int64) {
	jm.DelayQueueSize.WithLabelValues(queueName, "total").Set(float64(total))
	jm.DelayQueueSize.WithLabelValues(queueName, "expired").Set(float64(expired))
	jm.DelayQueueSize.WithLabelValues(queueName, "pending").Set(float64(pending))
}

// UpdateCursorLag 更新游标延迟
func (jm *JobMetrics) UpdateCursorLag(jobName string, lagMinutes float64) {
	jm.CursorLag.WithLabelValues(jobName).Set(lagMinutes)
}

// TaskWrapper 任务包装器，自动记录指标
func TaskWrapper(taskName string, task func() error) func() {
	return func() {
		if Metrics == nil {
			// 如果指标未初始化，直接执行任务
			task()
			return
		}

		start := time.Now()
		Metrics.RecordTaskStart(taskName)

		err := task()
		duration := time.Since(start)

		Metrics.RecordTaskCompletion(taskName, duration, err)
	}
}
