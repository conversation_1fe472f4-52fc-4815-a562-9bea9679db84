package job

import (
	"context"
	"encoding/json"
	"time"

	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	goredis "github.com/go-redis/redis/v8"
)

// JobCursor 任务游标，用于记录增量处理进度
type JobCursor struct {
	LastProcessedID uint64    `json:"last_processed_id"`
	LastUpdateTime  time.Time `json:"last_update_time"`
	JobName         string    `json:"job_name"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// CursorManager 游标管理器
type CursorManager struct {
	keyPrefix string
}

// NewCursorManager 创建游标管理器
func NewCursorManager() *CursorManager {
	return &CursorManager{
		keyPrefix: "job:cursor:",
	}
}

// GetCursor 获取任务游标
func (cm *CursorManager) GetCursor(ctx context.Context, jobName string) (*JobCursor, error) {
	key := redis.GetPrefixKey(cm.keyPrefix + jobName)

	client := redis.GetRedisClient()
	data, err := client.Get(ctx, key).Bytes()
	if err != nil {
		if err == goredis.Nil {
			// 如果不存在，返回默认游标
			return &JobCursor{
				LastProcessedID: 0,
				LastUpdateTime:  time.Now().Add(-24 * time.Hour), // 默认处理24小时内的数据
				JobName:         jobName,
				UpdatedAt:       time.Now(),
			}, nil
		}
		log.WithContext(ctx).WithError(err).Error("get job cursor failed: %s", jobName)
		return nil, err
	}

	var cursor JobCursor
	if err := json.Unmarshal(data, &cursor); err != nil {
		log.WithContext(ctx).WithError(err).Error("unmarshal job cursor failed: %s", jobName)
		return nil, err
	}

	return &cursor, nil
}

// UpdateCursor 更新任务游标
func (cm *CursorManager) UpdateCursor(ctx context.Context, cursor *JobCursor) error {
	key := redis.GetPrefixKey(cm.keyPrefix + cursor.JobName)
	cursor.UpdatedAt = time.Now()

	data, err := json.Marshal(cursor)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("marshal job cursor failed: %s", cursor.JobName)
		return err
	}

	// 设置7天过期时间，避免Redis内存泄漏
	expireDuration := 7 * 24 * time.Hour
	client := redis.GetRedisClient()
	if err := client.Set(ctx, key, data, expireDuration).Err(); err != nil {
		log.WithContext(ctx).WithError(err).Error("update job cursor failed: %s", cursor.JobName)
		return err
	}

	log.WithContext(ctx).Info("job cursor updated: %s, lastProcessedID: %d, lastUpdateTime: %s",
		cursor.JobName,
		cursor.LastProcessedID,
		cursor.LastUpdateTime.Format(time.RFC3339))

	return nil
}

// ResetCursor 重置任务游标（用于故障恢复）
func (cm *CursorManager) ResetCursor(ctx context.Context, jobName string, resetHours int) error {
	cursor := &JobCursor{
		LastProcessedID: 0,
		LastUpdateTime:  time.Now().Add(-time.Duration(resetHours) * time.Hour),
		JobName:         jobName,
		UpdatedAt:       time.Now(),
	}

	return cm.UpdateCursor(ctx, cursor)
}

// DeleteCursor 删除任务游标
func (cm *CursorManager) DeleteCursor(ctx context.Context, jobName string) error {
	key := redis.GetPrefixKey(cm.keyPrefix + jobName)
	client := redis.GetRedisClient()
	return client.Del(ctx, key).Err()
}

// ListCursors 列出指定任务游标（用于监控）
func (cm *CursorManager) ListCursors(ctx context.Context, jobNames []string) (map[string]*JobCursor, error) {
	cursors := make(map[string]*JobCursor)
	for _, jobName := range jobNames {
		cursor, err := cm.GetCursor(ctx, jobName)
		if err != nil {
			log.WithContext(ctx).WithError(err).Warn("get cursor failed: %s", jobName)
			continue
		}
		cursors[jobName] = cursor
	}

	return cursors, nil
}

// GetCursorStats 获取游标统计信息
func (cm *CursorManager) GetCursorStats(ctx context.Context, jobName string) (map[string]interface{}, error) {
	cursor, err := cm.GetCursor(ctx, jobName)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"job_name":          cursor.JobName,
		"last_processed_id": cursor.LastProcessedID,
		"last_update_time":  cursor.LastUpdateTime.Format(time.RFC3339),
		"cursor_updated_at": cursor.UpdatedAt.Format(time.RFC3339),
		"lag_minutes":       time.Since(cursor.LastUpdateTime).Minutes(),
		"is_healthy":        time.Since(cursor.UpdatedAt) < 10*time.Minute, // 10分钟内更新过认为健康
	}

	return stats, nil
}
