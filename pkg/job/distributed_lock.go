package job

import (
	"context"
	"fmt"
	"time"

	"blind_box/pkg/log"
	"blind_box/pkg/redis"
)

// DistributedLock 分布式锁管理器
type DistributedLock struct {
	keyPrefix  string
	defaultTTL time.Duration
}

// NewDistributedLock 创建分布式锁管理器
func NewDistributedLock() *DistributedLock {
	return &DistributedLock{
		keyPrefix:  "job:lock:",
		defaultTTL: 5 * time.Minute, // 默认5分钟锁定时间
	}
}

// TryLock 尝试获取分布式锁
func (dl *DistributedLock) TryLock(ctx context.Context, jobName string, ttl time.Duration) (bool, error) {
	if ttl == 0 {
		ttl = dl.defaultTTL
	}

	key := redis.GetPrefixKey(dl.keyPrefix + jobName)
	value := fmt.Sprintf("%d", time.Now().UnixNano()) // 使用时间戳作为唯一标识

	client := redis.GetRedisClient()

	// 使用 SET NX EX 命令实现分布式锁
	reply, err := client.SetNX(ctx, key, value, ttl).Result()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("distributed lock try lock failed: %s", jobName)
		return false, err
	}

	if reply {
		log.WithContext(ctx).Info("distributed lock acquired: %s, ttl: %v", jobName, ttl)
		return true, nil
	}

	// 锁已被其他实例持有
	log.WithContext(ctx).Info("distributed lock already held: %s", jobName)
	return false, nil
}

// Unlock 释放分布式锁
func (dl *DistributedLock) Unlock(ctx context.Context, jobName string) error {
	key := redis.GetPrefixKey(dl.keyPrefix + jobName)

	client := redis.GetRedisClient()

	err := client.Del(ctx, key).Err()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("distributed lock unlock failed: %s", jobName)
		return err
	}

	log.WithContext(ctx).Info("distributed lock released: %s", jobName)
	return nil
}

// ExtendLock 延长锁的过期时间
func (dl *DistributedLock) ExtendLock(ctx context.Context, jobName string, ttl time.Duration) error {
	if ttl == 0 {
		ttl = dl.defaultTTL
	}

	key := redis.GetPrefixKey(dl.keyPrefix + jobName)

	client := redis.GetRedisClient()

	err := client.Expire(ctx, key, ttl).Err()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("distributed lock extend failed: %s", jobName)
		return err
	}

	log.WithContext(ctx).Info("distributed lock extended: %s, ttl: %v", jobName, ttl)
	return nil
}

// IsLocked 检查锁是否存在
func (dl *DistributedLock) IsLocked(ctx context.Context, jobName string) (bool, error) {
	key := redis.GetPrefixKey(dl.keyPrefix + jobName)

	client := redis.GetRedisClient()

	exists, err := client.Exists(ctx, key).Result()
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("distributed lock check failed: %s", jobName)
		return false, err
	}

	return exists > 0, nil
}

// WithLock 使用分布式锁执行函数
func (dl *DistributedLock) WithLock(ctx context.Context, jobName string, ttl time.Duration, fn func() error) error {
	// 尝试获取锁
	acquired, err := dl.TryLock(ctx, jobName, ttl)
	if err != nil {
		// 记录锁获取失败指标
		if Metrics != nil {
			Metrics.RecordLockAcquisition(jobName, "failed")
		}
		return fmt.Errorf("failed to acquire lock: %w", err)
	}

	if !acquired {
		// 记录锁跳过指标
		if Metrics != nil {
			Metrics.RecordLockAcquisition(jobName, "skipped")
		}
		log.WithContext(ctx).Info("job skipped due to lock: %s", jobName)
		return nil // 锁被其他实例持有，跳过执行
	}

	// 记录锁获取成功指标
	if Metrics != nil {
		Metrics.RecordLockAcquisition(jobName, "acquired")
	}

	// 确保释放锁
	defer func() {
		if unlockErr := dl.Unlock(ctx, jobName); unlockErr != nil {
			log.WithContext(ctx).WithError(unlockErr).Error("failed to unlock: %s", jobName)
		}
	}()

	// 执行任务
	log.WithContext(ctx).Info("job execution started with lock: %s", jobName)
	startTime := time.Now()

	err = fn()

	duration := time.Since(startTime)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("job execution failed: %s, duration: %v", jobName, duration)
		return fmt.Errorf("job execution failed: %w", err)
	}

	log.WithContext(ctx).Info("job execution completed: %s, duration: %v", jobName, duration)
	return nil
}

// 全局分布式锁实例
var DefaultDistributedLock *DistributedLock

// InitDistributedLock 初始化分布式锁
func InitDistributedLock() {
	DefaultDistributedLock = NewDistributedLock()
}
