package log

import (
	"fmt"
	"os"
	"runtime"
	"strings"

	"blind_box/config"
	"blind_box/pkg/log/hook"

	"github.com/sirupsen/logrus"
)

// CustomHook for log the call context
type CustomHook struct {
	Field  string
	Skip   int
	levels []logrus.Level
}

// NewContextHook use to make an hook
// 根据上面的推断, 我们递归深度可以设置到5即可.
func NewCustomHook(levels ...logrus.Level) logrus.Hook {
	hook := CustomHook{
		Field:  "line",
		Skip:   5,
		levels: levels,
	}
	if len(hook.levels) == 0 {
		hook.levels = logrus.AllLevels
	}
	return &hook
}

// Levels implement levels
func (h CustomHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// Fire implement fire
func (h CustomHook) Fire(entry *logrus.Entry) error {
	// logFilePath := fmt.Sprintf("%s/%s/%s.log", config.AppCfg.LogPath, config.AppCfg.AppName, carbon.Now().ToDateString())
	logFilePath := fmt.Sprintf("%s/%s", config.AppCfg.LogPath, config.AppCfg.AppName)
	logFileName := fmt.Sprintf("%s.log", config.AppCfg.AppName)
	// if err := EnsureDirExists(filepath.Dir(logFilePath)); err != nil {
	// 	return err
	// }
	// logFile, err := os.OpenFile(logFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0666)
	// if err != nil {
	// 	return err
	// }
	// defer logFile.Close()
	// GetAsyncLogger().logger.SetOutput(logFile)
	GetAsyncLogger().logger.SetOutput(&hook.Logger{
		Filepath:   logFilePath,
		Filename:   logFileName,
		MaxSize:    10,   // 每个日志文件的最大大小（以MB为单位）
		MaxBackups: 100,  // 保留的旧日志文件的最大数量
		MaxAge:     30,   // 保留旧日志文件的最大天数
		Compress:   true, // 是否压缩/归档旧日志文件
		NamyByDate: true,
	})
	return nil
}

// 对caller进行递归查询, 直到找到非logrus包产生的第一个调用.
// 因为filename我获取到了上层目录名, 因此所有logrus包的调用的文件名都是 logrus/...
// 因此通过排除logrus开头的文件名, 就可以排除所有logrus包的自己的函数调用
func findCaller(skip int) string {
	file := ""
	line := 0
	for i := 0; i < 10; i++ {
		pc, file, line, ok := runtime.Caller(skip + i)
		if !ok || !strings.Contains(file, "app/") {
			continue
		}
		fnName := runtime.FuncForPC(pc).Name()
		if strings.Contains(fnName, "CustomGormLogger") {
			continue
		}
		n := 0
		for i := len(file) - 1; i > 0; i-- {
			if file[i] == '/' {
				n++
				if n >= 3 {
					file = file[i+1:]
					return fmt.Sprintf("%s:%d", file, line)
				}
			}
		}
	}
	return fmt.Sprintf("%s:%d", file, line)
}

// 这里其实可以获取函数名称的: fnName := runtime.FuncForPC(pc).Name()
// 但是我觉得有 文件名和行号就够定位问题, 因此忽略了caller返回的第一个值:pc
// 在标准库log里面我们可以选择记录文件的全路径或者文件名, 但是在使用过程成并发最合适的,
// 因为文件的全路径往往很长, 而文件名在多个包中往往有重复, 因此这里选择多取一层, 取到文件所在的上层目录那层.
func getCaller(skip int) (string, int) {
	_, file, line, ok := runtime.Caller(skip)
	if !ok {
		return "", 0
	}
	fmt.Printf("getCaller: %s:%d\n", file, line)
	n := 0
	for i := len(file) - 1; i > 0; i-- {
		if file[i] == '/' {
			n++
			if n >= 2 {
				file = file[i+1:]
				break
			}
		}
	}
	return file, line
}

func EnsureDirExists(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}
