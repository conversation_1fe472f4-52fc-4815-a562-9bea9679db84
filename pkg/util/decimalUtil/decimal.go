package decimalUtil

import (
	"errors"

	"github.com/shopspring/decimal"
)

func Div(a, b uint64) uint64 {
	if b == 0 {
		return 0
	}
	return uint64(decimal.NewFromUint64(a).Div(decimal.NewFromUint64(b)).Round(0).IntPart())
}

func AmountToFloat(amount uint64) float64 {
	var ret float64
	if amount == 0 {
		return ret
	}
	ret, _ = decimal.NewFromUint64(amount).Div(decimal.NewFromFloat(100)).Round(2).Float64()
	return ret
}

// === 新增的安全价格计算函数 ===

// SafeMultiply 安全的乘法运算（分 × 数量）
// 返回结果（分）和可能的错误
func SafeMultiply(price uint64, quantity uint32) (uint64, error) {
	if price == 0 || quantity == 0 {
		return 0, nil
	}

	priceDecimal := decimal.NewFromUint64(price)
	quantityDecimal := decimal.NewFromUint64(uint64(quantity))
	result := priceDecimal.Mul(quantityDecimal)

	// 检查是否超过 uint64 范围
	maxUint64 := decimal.NewFromUint64(^uint64(0))
	if result.Cmp(maxUint64) > 0 {
		return 0, errors.New("multiplication overflow: result exceeds uint64 range")
	}

	return uint64(result.IntPart()), nil
}

// SafeAdd 安全的加法运算
func SafeAdd(amounts ...uint64) (uint64, error) {
	result := decimal.Zero
	maxUint64 := decimal.NewFromUint64(^uint64(0))

	for _, amount := range amounts {
		result = result.Add(decimal.NewFromUint64(amount))
		if result.Cmp(maxUint64) > 0 {
			return 0, errors.New("addition overflow: result exceeds uint64 range")
		}
	}

	return uint64(result.IntPart()), nil
}

// SafeSubtract 安全的减法运算（确保不会产生负数）
func SafeSubtract(minuend uint64, subtrahends ...uint64) (uint64, error) {
	result := decimal.NewFromUint64(minuend)

	for _, subtrahend := range subtrahends {
		subtrahendDecimal := decimal.NewFromUint64(subtrahend)
		result = result.Sub(subtrahendDecimal)

		if result.IsNegative() {
			return 0, errors.New("subtraction would result in negative value")
		}
	}

	return uint64(result.IntPart()), nil
}

// CalculatePercentage 计算百分比（用于优惠券折扣等）
// amount: 金额（分），percentage: 百分比（如 85 表示 85%）
func CalculatePercentage(amount uint64, percentage uint32) (uint64, error) {
	if amount == 0 || percentage == 0 {
		return 0, nil
	}

	amountDecimal := decimal.NewFromUint64(amount)
	percentageDecimal := decimal.NewFromUint64(uint64(percentage))

	// 计算百分比：amount * percentage / 100
	result := amountDecimal.Mul(percentageDecimal).Div(decimal.NewFromInt(100))

	// 四舍五入到分
	return uint64(result.Round(0).IntPart()), nil
}

// CalculateDiscount 计算折扣金额
// originalAmount: 原价（分），discountAmount: 折扣金额（分），discountPercentage: 折扣百分比
func CalculateDiscount(originalAmount uint64, discountAmount uint64, discountPercentage uint32) (finalAmount uint64, actualDiscount uint64, err error) {
	original := decimal.NewFromUint64(originalAmount)

	var discount decimal.Decimal

	// 优先使用折扣金额，其次使用折扣百分比
	if discountAmount > 0 {
		discount = decimal.NewFromUint64(discountAmount)
	} else if discountPercentage > 0 {
		// 计算百分比折扣
		discount = original.Mul(decimal.NewFromUint64(uint64(discountPercentage))).Div(decimal.NewFromInt(100))
	}

	// 确保折扣不超过原价
	if discount.Cmp(original) > 0 {
		discount = original
	}

	finalAmount = uint64(original.Sub(discount).Round(0).IntPart())
	actualDiscount = uint64(discount.Round(0).IntPart())

	return finalAmount, actualDiscount, nil
}

// FloatToAmount 安全地将浮点数金额（元）转为分
func FloatToAmount(yuan float64) (uint64, error) {
	if yuan < 0 {
		return 0, errors.New("amount cannot be negative")
	}

	yuanDecimal := decimal.NewFromFloat(yuan)
	fenDecimal := yuanDecimal.Mul(decimal.NewFromInt(100))

	// 检查是否超过 uint64 范围
	maxUint64 := decimal.NewFromUint64(^uint64(0))
	if fenDecimal.Cmp(maxUint64) > 0 {
		return 0, errors.New("amount too large: exceeds uint64 range")
	}

	return uint64(fenDecimal.Round(0).IntPart()), nil
}

// AmountToYuan 安全地将分转为元（使用 decimal 避免精度损失）
func AmountToYuan(amount uint64) decimal.Decimal {
	return decimal.NewFromUint64(amount).Div(decimal.NewFromInt(100))
}

// CompareAmounts 比较两个金额
// 返回: -1 (a < b), 0 (a == b), 1 (a > b)
func CompareAmounts(a, b uint64) int {
	aDecimal := decimal.NewFromUint64(a)
	bDecimal := decimal.NewFromUint64(b)
	return aDecimal.Cmp(bDecimal)
}

// ValidateAmount 验证金额是否在有效范围内
func ValidateAmount(amount uint64, maxAmount uint64) error {
	if amount > maxAmount {
		return errors.New("amount exceeds maximum allowed value")
	}
	return nil
}
