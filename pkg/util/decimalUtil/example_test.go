package decimalUtil

import (
	"fmt"
	"testing"
	"time"
)

// TestRealBusinessScenario 测试真实业务场景
func TestRealBusinessScenario(t *testing.T) {
	calculator := NewPriceCalculator()

	// 模拟一个真实的购物场景：用户购买盲盒
	fmt.Println("=== 盲盒电商价格计算测试 ===")

	// 1. 用户添加商品到购物车
	cartItems := []CartItem{
		{SkuID: 1001, UnitPrice: 29900, Quantity: 2}, // 泡泡玛特盲盒 299元 × 2个
		{SkuID: 1002, UnitPrice: 8900, Quantity: 3},  // 限定款盲盒 89元 × 3个
		{SkuID: 1003, UnitPrice: 15800, Quantity: 1}, // 联名款盲盒 158元 × 1个
	}

	fmt.Printf("📦 购物车商品：\n")
	for i, item := range cartItems {
		fmt.Printf("  %d. SKU_%d: %s × %d件\n",
			i+1, item.SkuID, AmountToYuan(item.UnitPrice).String()+"元", item.Quantity)
	}

	// 2. 计算购物车总价
	result, err := calculator.CalculateCartTotal(cartItems)
	if err != nil {
		t.Fatalf("计算购物车总价失败: %v", err)
	}

	fmt.Printf("\n💰 价格明细：\n")
	fmt.Printf("  商品小计: %s元\n", AmountToYuan(result.SubTotal).String())

	// 验证计算结果：299*2 + 89*3 + 158*1 = 598 + 267 + 158 = 1023元
	expectedSubTotal := uint64(102300) // 1023元
	if result.SubTotal != expectedSubTotal {
		t.Errorf("小计计算错误，期望 %d，实际 %d", expectedSubTotal, result.SubTotal)
	}

	// 3. 应用满减优惠券
	coupon := &CouponInfo{
		ID:              12345,
		DiscountAmount:  5000,   // 50元立减
		ThresholdAmount: 100000, // 满1000元
		CouponType:      1,      // 立减
	}

	err = calculator.ApplyCoupon(result, coupon)
	if err != nil {
		t.Fatalf("应用优惠券失败: %v", err)
	}

	fmt.Printf("  优惠券立减: -%s元\n", AmountToYuan(result.CouponDiscount).String())
	fmt.Printf("  优惠后金额: %s元\n", AmountToYuan(result.FinalAmount).String())

	// 4. 应用积分抵扣（用户有1000积分，1积分=1分）
	pointsAmount := uint64(1000) // 10元积分
	err = calculator.ApplyPointsDiscount(result, pointsAmount)
	if err != nil {
		t.Fatalf("应用积分抵扣失败: %v", err)
	}

	fmt.Printf("  积分抵扣: -%s元\n", AmountToYuan(result.PointsDiscount).String())
	fmt.Printf("  最终支付: %s元\n", AmountToYuan(result.FinalAmount).String())
	fmt.Printf("  总节省: %s元\n", AmountToYuan(result.TotalSaved).String())

	// 验证最终结果：1023 - 50 - 10 = 963元
	expectedFinalAmount := uint64(96300) // 963元
	if result.FinalAmount != expectedFinalAmount {
		t.Errorf("最终金额计算错误，期望 %d，实际 %d", expectedFinalAmount, result.FinalAmount)
	}

	// 5. 模拟部分退款场景（退掉1个泡泡玛特盲盒）
	fmt.Printf("\n🔄 退款场景测试：\n")

	refundItems := []CartItem{
		{SkuID: 1001, UnitPrice: 29900, Quantity: 1}, // 退1个泡泡玛特盲盒
	}

	refundResult, err := calculator.CalculateRefund(
		result.SubTotal,       // 原订单金额
		refundItems,           // 退款商品
		result.CouponDiscount, // 原优惠券折扣
		result.PointsDiscount, // 原积分抵扣
	)
	if err != nil {
		t.Fatalf("计算退款失败: %v", err)
	}

	fmt.Printf("  退款商品价值: %s元\n", AmountToYuan(refundResult.SubTotal).String())
	fmt.Printf("  退回优惠券: %s元\n", AmountToYuan(refundResult.CouponDiscount).String())
	fmt.Printf("  退回积分: %s元\n", AmountToYuan(refundResult.PointsDiscount).String())
	fmt.Printf("  实际退款: %s元\n", AmountToYuan(refundResult.FinalAmount).String())

	fmt.Printf("\n✅ 所有测试通过！价格计算精确无误。\n")
}

// TestEdgeCases 测试边界情况
func TestEdgeCases(t *testing.T) {
	calculator := NewPriceCalculator()

	fmt.Println("\n=== 边界情况测试 ===")

	// 1. 测试高价商品大量购买
	t.Run("高价商品大量购买", func(t *testing.T) {
		items := []CartItem{
			{SkuID: 9001, UnitPrice: 1000000, Quantity: 10000}, // 1万元商品买1万件
		}

		result, err := calculator.CalculateCartTotal(items)
		if err != nil {
			t.Fatalf("高价商品计算失败: %v", err)
		}

		fmt.Printf("  高价商品总价: %s元\n", AmountToYuan(result.SubTotal).String())

		// 验证：10000元 × 10000件 = 1亿元
		expected := uint64(10000000000) // 1亿元
		if result.SubTotal != expected {
			t.Errorf("高价商品计算错误，期望 %d，实际 %d", expected, result.SubTotal)
		}
	})

	// 2. 测试溢出保护
	t.Run("溢出保护测试", func(t *testing.T) {
		_, err := SafeMultiply(^uint64(0), 2) // 最大值乘以2
		if err == nil {
			t.Error("应该检测到溢出错误")
		}
		fmt.Printf("  ✅ 正确检测到溢出: %v\n", err)
	})

	// 3. 测试精度计算
	t.Run("精度计算测试", func(t *testing.T) {
		// 模拟复杂的折扣计算
		originalAmount := uint64(99999) // 999.99元

		// 85折
		discountedAmount, err := CalculatePercentage(originalAmount, 85)
		if err != nil {
			t.Fatalf("折扣计算失败: %v", err)
		}

		fmt.Printf("  原价: %s元\n", AmountToYuan(originalAmount).String())
		fmt.Printf("  85折后: %s元\n", AmountToYuan(discountedAmount).String())

		// 验证：999.99 * 0.85 = 849.9915，四舍五入到85000分
		expected := uint64(84999) // 849.99元
		if discountedAmount != expected {
			t.Errorf("折扣计算错误，期望 %d，实际 %d", expected, discountedAmount)
		}
	})

	fmt.Printf("✅ 边界情况测试全部通过！\n")
}

// TestPerformanceComparison 性能对比测试
func TestPerformanceComparison(t *testing.T) {
	fmt.Println("\n=== 性能对比分析 ===")

	// 模拟1000次订单计算
	calculator := NewPriceCalculator()
	items := []CartItem{
		{SkuID: 1, UnitPrice: 9999, Quantity: 5},
		{SkuID: 2, UnitPrice: 15800, Quantity: 2},
	}

	// 使用我们的安全计算器
	start := time.Now()
	for i := 0; i < 1000; i++ {
		_, err := calculator.CalculateCartTotal(items)
		if err != nil {
			t.Fatalf("计算失败: %v", err)
		}
	}
	safeCalculationTime := time.Since(start)

	// 直接uint64计算（不安全版本）
	start = time.Now()
	for i := 0; i < 1000; i++ {
		total := items[0].UnitPrice*uint64(items[0].Quantity) +
			items[1].UnitPrice*uint64(items[1].Quantity)
		_ = total
	}
	directCalculationTime := time.Since(start)

	fmt.Printf("  安全计算器 1000次: %v\n", safeCalculationTime)
	fmt.Printf("  直接计算 1000次: %v\n", directCalculationTime)
	fmt.Printf("  性能开销: %.2fx\n", float64(safeCalculationTime)/float64(directCalculationTime))
	fmt.Printf("  💡 换取精确性和安全性，性能开销是可以接受的\n")
}
