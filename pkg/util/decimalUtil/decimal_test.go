package decimalUtil

import (
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSafeMultiply(t *testing.T) {
	tests := []struct {
		name        string
		price       uint64
		quantity    uint32
		expected    uint64
		expectError bool
	}{
		{
			name:        "正常乘法",
			price:       9999, // 99.99元
			quantity:    10,
			expected:    99990, // 999.90元
			expectError: false,
		},
		{
			name:        "零价格",
			price:       0,
			quantity:    100,
			expected:    0,
			expectError: false,
		},
		{
			name:        "零数量",
			price:       10000,
			quantity:    0,
			expected:    0,
			expectError: false,
		},
		{
			name:        "大数溢出测试",
			price:       ^uint64(0), // 最大uint64
			quantity:    2,
			expected:    0,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := SafeMultiply(tt.price, tt.quantity)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSafeAdd(t *testing.T) {
	tests := []struct {
		name        string
		amounts     []uint64
		expected    uint64
		expectError bool
	}{
		{
			name:        "正常加法",
			amounts:     []uint64{1000, 2000, 3000},
			expected:    6000,
			expectError: false,
		},
		{
			name:        "包含零值",
			amounts:     []uint64{1000, 0, 3000},
			expected:    4000,
			expectError: false,
		},
		{
			name:        "单个值",
			amounts:     []uint64{5000},
			expected:    5000,
			expectError: false,
		},
		{
			name:        "溢出测试",
			amounts:     []uint64{^uint64(0), 1}, // 最大值+1
			expected:    0,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := SafeAdd(tt.amounts...)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSafeSubtract(t *testing.T) {
	tests := []struct {
		name        string
		minuend     uint64
		subtrahends []uint64
		expected    uint64
		expectError bool
	}{
		{
			name:        "正常减法",
			minuend:     10000,
			subtrahends: []uint64{1000, 2000},
			expected:    7000,
			expectError: false,
		},
		{
			name:        "减去零",
			minuend:     5000,
			subtrahends: []uint64{0, 1000},
			expected:    4000,
			expectError: false,
		},
		{
			name:        "结果为零",
			minuend:     5000,
			subtrahends: []uint64{5000},
			expected:    0,
			expectError: false,
		},
		{
			name:        "负数结果",
			minuend:     1000,
			subtrahends: []uint64{2000},
			expected:    0,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := SafeSubtract(tt.minuend, tt.subtrahends...)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestCalculatePercentage(t *testing.T) {
	tests := []struct {
		name       string
		amount     uint64
		percentage uint32
		expected   uint64
	}{
		{
			name:       "85%折扣",
			amount:     10000, // 100元
			percentage: 85,
			expected:   8500, // 85元
		},
		{
			name:       "50%折扣",
			amount:     20000, // 200元
			percentage: 50,
			expected:   10000, // 100元
		},
		{
			name:       "零金额",
			amount:     0,
			percentage: 85,
			expected:   0,
		},
		{
			name:       "零百分比",
			amount:     10000,
			percentage: 0,
			expected:   0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CalculatePercentage(tt.amount, tt.percentage)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFloatToAmount(t *testing.T) {
	tests := []struct {
		name        string
		yuan        float64
		expected    uint64
		expectError bool
	}{
		{
			name:        "正常转换",
			yuan:        99.99,
			expected:    9999,
			expectError: false,
		},
		{
			name:        "整数转换",
			yuan:        100.0,
			expected:    10000,
			expectError: false,
		},
		{
			name:        "零值",
			yuan:        0.0,
			expected:    0,
			expectError: false,
		},
		{
			name:        "负数",
			yuan:        -10.0,
			expected:    0,
			expectError: true,
		},
		{
			name:        "小数精度",
			yuan:        1.235, // 应该四舍五入到1.24
			expected:    124,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := FloatToAmount(tt.yuan)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestAmountToYuan(t *testing.T) {
	tests := []struct {
		name     string
		amount   uint64
		expected string
	}{
		{
			name:     "正常转换",
			amount:   9999,
			expected: "99.99",
		},
		{
			name:     "整数金额",
			amount:   10000,
			expected: "100",
		},
		{
			name:     "零值",
			amount:   0,
			expected: "0",
		},
		{
			name:     "小金额",
			amount:   1,
			expected: "0.01",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AmountToYuan(tt.amount)
			assert.Equal(t, tt.expected, result.String())
		})
	}
}

func TestPriceCalculator(t *testing.T) {
	calculator := NewPriceCalculator()

	t.Run("计算购物车总价", func(t *testing.T) {
		items := []CartItem{
			{SkuID: 1, UnitPrice: 9999, Quantity: 2}, // 99.99元 × 2 = 199.98元
			{SkuID: 2, UnitPrice: 5000, Quantity: 1}, // 50.00元 × 1 = 50.00元
		}

		result, err := calculator.CalculateCartTotal(items)
		assert.NoError(t, err)
		assert.Equal(t, uint64(24998), result.SubTotal) // 249.98元
		assert.Equal(t, uint64(24998), result.FinalAmount)
	})

	t.Run("应用立减优惠券", func(t *testing.T) {
		result := &PriceResult{
			SubTotal:    20000, // 200元
			FinalAmount: 20000,
		}

		coupon := &CouponInfo{
			ID:              1,
			DiscountAmount:  1000,  // 10元立减
			ThresholdAmount: 10000, // 满100元
			CouponType:      1,     // 立减
		}

		err := calculator.ApplyCoupon(result, coupon)
		assert.NoError(t, err)
		assert.Equal(t, uint64(1000), result.CouponDiscount)
		assert.Equal(t, uint64(19000), result.FinalAmount) // 190元
	})

	t.Run("优惠券门槛不满足", func(t *testing.T) {
		result := &PriceResult{
			SubTotal:    5000, // 50元
			FinalAmount: 5000,
		}

		coupon := &CouponInfo{
			ID:              1,
			DiscountAmount:  1000,  // 10元立减
			ThresholdAmount: 10000, // 满100元
			CouponType:      1,     // 立减
		}

		err := calculator.ApplyCoupon(result, coupon)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "未达到优惠券使用门槛")
	})

	t.Run("应用积分抵扣", func(t *testing.T) {
		result := &PriceResult{
			SubTotal:    10000, // 100元
			FinalAmount: 9000,  // 已经减去10元优惠券
		}

		err := calculator.ApplyPointsDiscount(result, 500) // 5元积分
		assert.NoError(t, err)
		assert.Equal(t, uint64(500), result.PointsDiscount)
		assert.Equal(t, uint64(8500), result.FinalAmount) // 85元
	})

	t.Run("积分抵扣超过应付金额", func(t *testing.T) {
		result := &PriceResult{
			SubTotal:    10000,
			FinalAmount: 1000, // 只剩10元
		}

		err := calculator.ApplyPointsDiscount(result, 2000) // 想用20元积分
		assert.NoError(t, err)
		assert.Equal(t, uint64(1000), result.PointsDiscount) // 只能抵扣10元
		assert.Equal(t, uint64(0), result.FinalAmount)
	})
}

func TestCalculateDiscount(t *testing.T) {
	tests := []struct {
		name               string
		originalAmount     uint64
		discountAmount     uint64
		discountPercentage uint32
		expectedFinal      uint64
		expectedDiscount   uint64
	}{
		{
			name:             "立减优惠",
			originalAmount:   10000, // 100元
			discountAmount:   1000,  // 10元立减
			expectedFinal:    9000,  // 90元
			expectedDiscount: 1000,  // 10元
		},
		{
			name:               "百分比折扣",
			originalAmount:     10000, // 100元
			discountPercentage: 20,    // 20%折扣
			expectedFinal:      8000,  // 80元
			expectedDiscount:   2000,  // 20元
		},
		{
			name:             "折扣超过原价",
			originalAmount:   5000,  // 50元
			discountAmount:   10000, // 100元立减
			expectedFinal:    0,     // 0元
			expectedDiscount: 5000,  // 实际只减50元
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			finalAmount, actualDiscount, err := CalculateDiscount(
				tt.originalAmount,
				tt.discountAmount,
				tt.discountPercentage,
			)

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedFinal, finalAmount)
			assert.Equal(t, tt.expectedDiscount, actualDiscount)
		})
	}
}

// 性能测试
func BenchmarkSafeMultiply(b *testing.B) {
	for i := 0; i < b.N; i++ {
		SafeMultiply(9999, 100)
	}
}

func BenchmarkDirectMultiply(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = uint64(9999) * uint64(100)
	}
}

func BenchmarkDecimalMultiply(b *testing.B) {
	for i := 0; i < b.N; i++ {
		a := decimal.NewFromUint64(9999)
		b := decimal.NewFromUint64(100)
		result := a.Mul(b)
		_ = uint64(result.IntPart())
	}
}
