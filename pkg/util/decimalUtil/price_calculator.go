package decimalUtil

import (
	"errors"
	"fmt"

	"github.com/shopspring/decimal"
)

// PriceCalculator 价格计算器
type PriceCalculator struct {
	// 可以在这里添加配置参数，如最大订单金额等
	MaxOrderAmount uint64
}

// NewPriceCalculator 创建新的价格计算器
func NewPriceCalculator() *PriceCalculator {
	return &PriceCalculator{
		MaxOrderAmount: 99999999999, // 999亿分 = 9.99亿元，可根据业务需求调整
	}
}

// CartItem 购物车项目
type CartItem struct {
	SkuID     uint64 `json:"skuId"`
	UnitPrice uint64 `json:"unitPrice"` // 单价（分）
	Quantity  uint32 `json:"quantity"`  // 数量
}

// CouponInfo 优惠券信息
type CouponInfo struct {
	ID              uint64 `json:"id"`
	DiscountAmount  uint64 `json:"discountAmount"`  // 立减金额（分）
	ThresholdAmount uint64 `json:"thresholdAmount"` // 门槛金额（分）
	DiscountPercent uint32 `json:"discountPercent"` // 折扣百分比（如85表示85%）
	CouponType      uint32 `json:"couponType"`      // 优惠券类型：1-立减，2-折扣
}

// PriceResult 价格计算结果
type PriceResult struct {
	SubTotal       uint64 `json:"subTotal"`       // 小计（分）
	CouponDiscount uint64 `json:"couponDiscount"` // 优惠券折扣（分）
	PointsDiscount uint64 `json:"pointsDiscount"` // 积分抵扣（分）
	FinalAmount    uint64 `json:"finalAmount"`    // 最终金额（分）
	TotalSaved     uint64 `json:"totalSaved"`     // 总节省（分）
}

// CalculateCartTotal 计算购物车总价
func (pc *PriceCalculator) CalculateCartTotal(items []CartItem) (*PriceResult, error) {
	if len(items) == 0 {
		return &PriceResult{}, nil
	}

	var totalPrice uint64

	// 逐项计算小计
	for i, item := range items {
		if item.UnitPrice == 0 || item.Quantity == 0 {
			continue
		}

		itemTotal, err := SafeMultiply(item.UnitPrice, item.Quantity)
		if err != nil {
			return nil, fmt.Errorf("计算第%d项商品总价失败: %w", i+1, err)
		}

		totalPrice, err = SafeAdd(totalPrice, itemTotal)
		if err != nil {
			return nil, fmt.Errorf("累加商品总价失败: %w", err)
		}
	}

	// 验证订单金额不超过限制
	if err := ValidateAmount(totalPrice, pc.MaxOrderAmount); err != nil {
		return nil, fmt.Errorf("订单金额超过限制: %w", err)
	}

	return &PriceResult{
		SubTotal:    totalPrice,
		FinalAmount: totalPrice,
	}, nil
}

// ApplyCoupon 应用优惠券
func (pc *PriceCalculator) ApplyCoupon(result *PriceResult, coupon *CouponInfo) error {
	if result == nil || coupon == nil {
		return errors.New("价格结果或优惠券信息不能为空")
	}

	// 检查是否满足门槛
	if coupon.ThresholdAmount > 0 && result.SubTotal < coupon.ThresholdAmount {
		return fmt.Errorf("订单金额 %s 未达到优惠券使用门槛 %s",
			AmountToYuan(result.SubTotal).String(),
			AmountToYuan(coupon.ThresholdAmount).String())
	}

	var discount uint64
	var err error

	switch coupon.CouponType {
	case 1: // 立减
		discount = coupon.DiscountAmount
	case 2: // 折扣
		if coupon.DiscountPercent > 100 {
			return errors.New("折扣百分比不能大于100%")
		}
		// 计算折扣金额 = 原价 * (100 - 折扣百分比) / 100
		discountPercent := 100 - coupon.DiscountPercent
		discount, err = CalculatePercentage(result.SubTotal, discountPercent)
		if err != nil {
			return fmt.Errorf("计算折扣金额失败: %w", err)
		}
	default:
		return errors.New("不支持的优惠券类型")
	}

	// 确保折扣不超过商品金额
	if discount > result.SubTotal {
		discount = result.SubTotal
	}

	// 计算最终金额
	finalAmount, err := SafeSubtract(result.SubTotal, discount)
	if err != nil {
		return fmt.Errorf("计算最终金额失败: %w", err)
	}

	result.CouponDiscount = discount
	result.FinalAmount = finalAmount
	result.TotalSaved, _ = SafeAdd(result.TotalSaved, discount)

	return nil
}

// ApplyPointsDiscount 应用积分抵扣
func (pc *PriceCalculator) ApplyPointsDiscount(result *PriceResult, pointsAmount uint64) error {
	if result == nil {
		return errors.New("价格结果不能为空")
	}

	if pointsAmount == 0 {
		return nil
	}

	// 积分抵扣不能超过当前应付金额
	currentAmount := result.FinalAmount
	if pointsAmount > currentAmount {
		pointsAmount = currentAmount
	}

	finalAmount, err := SafeSubtract(result.FinalAmount, pointsAmount)
	if err != nil {
		return fmt.Errorf("计算积分抵扣后金额失败: %w", err)
	}

	result.PointsDiscount = pointsAmount
	result.FinalAmount = finalAmount
	result.TotalSaved, _ = SafeAdd(result.TotalSaved, pointsAmount)

	return nil
}

// CalculateRefund 计算退款金额
func (pc *PriceCalculator) CalculateRefund(originalAmount uint64, refundItems []CartItem,
	originalCouponDiscount uint64, originalPointsDiscount uint64) (*PriceResult, error) {

	// 计算退款商品的原价小计
	refundSubTotal, err := pc.CalculateCartTotal(refundItems)
	if err != nil {
		return nil, fmt.Errorf("计算退款商品小计失败: %w", err)
	}

	if refundSubTotal.SubTotal > originalAmount {
		return nil, errors.New("退款金额不能超过原订单金额")
	}

	// 按比例计算优惠券和积分的退款
	var couponRefund, pointsRefund uint64

	if originalAmount > 0 {
		// 计算退款比例
		ratio := decimal.NewFromUint64(refundSubTotal.SubTotal).Div(decimal.NewFromUint64(originalAmount))

		// 按比例退回优惠券折扣
		if originalCouponDiscount > 0 {
			couponRefundDecimal := decimal.NewFromUint64(originalCouponDiscount).Mul(ratio)
			couponRefund = uint64(couponRefundDecimal.Round(0).IntPart())
		}

		// 按比例退回积分抵扣
		if originalPointsDiscount > 0 {
			pointsRefundDecimal := decimal.NewFromUint64(originalPointsDiscount).Mul(ratio)
			pointsRefund = uint64(pointsRefundDecimal.Round(0).IntPart())
		}
	}

	// 计算实际退款金额
	actualRefund, err := SafeSubtract(refundSubTotal.SubTotal, couponRefund, pointsRefund)
	if err != nil {
		return nil, fmt.Errorf("计算实际退款金额失败: %w", err)
	}

	return &PriceResult{
		SubTotal:       refundSubTotal.SubTotal,
		CouponDiscount: couponRefund,
		PointsDiscount: pointsRefund,
		FinalAmount:    actualRefund,
		TotalSaved:     couponRefund + pointsRefund,
	}, nil
}

// ValidatePrice 验证价格合理性
func (pc *PriceCalculator) ValidatePrice(amount uint64) error {
	if amount == 0 {
		return errors.New("金额不能为0")
	}

	if amount > pc.MaxOrderAmount {
		return fmt.Errorf("金额 %s 超过系统限制 %s",
			AmountToYuan(amount).String(),
			AmountToYuan(pc.MaxOrderAmount).String())
	}

	return nil
}

// FormatPriceResult 格式化价格结果为人类可读格式
func (pc *PriceCalculator) FormatPriceResult(result *PriceResult) map[string]string {
	return map[string]string{
		"subTotal":       AmountToYuan(result.SubTotal).String() + "元",
		"couponDiscount": AmountToYuan(result.CouponDiscount).String() + "元",
		"pointsDiscount": AmountToYuan(result.PointsDiscount).String() + "元",
		"finalAmount":    AmountToYuan(result.FinalAmount).String() + "元",
		"totalSaved":     AmountToYuan(result.TotalSaved).String() + "元",
	}
}
