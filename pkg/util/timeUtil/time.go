package timeUtil

import (
	"time"

	"blind_box/app/common/dbs"
)

type LadderNum uint32
type OverdueLevel uint32

const (
	InvervalOne   = 60 * 60 * 24
	InvervalThree = 60 * 60 * 24 * 3
	InvervalSeven = 60 * 60 * 24 * 7

	OverdueLevelDefault OverdueLevel = 0
	OverdueLevelOne     OverdueLevel = 1
	OverdueLevelTwo     OverdueLevel = 2
	OverdueLevelThree   OverdueLevel = 3
	OverdueLevelFour    OverdueLevel = 4
)

// GetLadderTime .
func GetLadderTime(n LadderNum) time.Duration {
	if 0 < n && n <= 1000 {
		return time.Millisecond * 10
	} else if 1000 < n && n <= 3000 {
		return time.Millisecond * 20
	} else if 3000 < n && n <= 6000 {
		return time.Millisecond * 30
	} else if 6000 < n && n <= 10000 {
		return time.Millisecond * 50
	} else {
		return time.Millisecond * 100
	}
}

// GetOverdueLevel .
func GetOverdueLevel(endTime int64) OverdueLevel {
	nowTime := time.Now().Unix()
	if nowTime <= endTime {
		return OverdueLevelDefault
	}
	inverval := nowTime - endTime
	if inverval <= InvervalOne {
		return OverdueLevelOne
	} else if InvervalOne < inverval && inverval <= InvervalThree {
		return OverdueLevelTwo
	} else if InvervalThree < inverval && inverval <= InvervalSeven {
		return OverdueLevelThree
	} else {
		return OverdueLevelFour
	}
}

// JudgeValidStartEndTime .
func JudgeValidStartEndTime(startTime, endTime int64, inverval dbs.ExportTimeInterval) bool {
	if startTime == dbs.False || endTime == dbs.False || endTime-startTime > int64(inverval) || startTime >= endTime {
		return false
	}
	return true
}

// 当天开始时间
func TruncateToDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// 当天结束时间
func TruncateToDayEnd(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
}
