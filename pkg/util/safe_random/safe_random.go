package safe_random

import (
	"math/rand"
	"sync"
	"time"
)

// SafeRandom 线程安全的随机数生成器
type SafeRandom struct {
	rand *rand.Rand
	mu   sync.Mutex
}

// 全局实例
var globalRandom *SafeRandom
var once sync.Once

// GetGlobalRandom 获取全局随机数生成器实例
func GetGlobalRandom() *SafeRandom {
	once.Do(func() {
		globalRandom = New()
	})
	return globalRandom
}

// New 创建新的安全随机数生成器
func New() *SafeRandom {
	return &SafeRandom{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// Intn 生成 [0,n) 范围内的随机整数
func (s *SafeRandom) Intn(n int) int {
	if n <= 0 {
		return 0
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.rand.Intn(n)
}

// IntnRange 生成 [min, max) 范围内的随机整数
func (s *SafeRandom) IntnRange(min, max int) int {
	if min >= max {
		return min
	}
	return min + s.Intn(max-min)
}

// Int63n 生成 [0,n) 范围内的随机int64
func (s *SafeRandom) Int63n(n int64) int64 {
	if n <= 0 {
		return 0
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.rand.Int63n(n)
}

// Float64 生成 [0.0,1.0) 范围内的随机浮点数
func (s *SafeRandom) Float64() float64 {
	s.mu.Lock()
	defer s.mu.Unlock()
	return s.rand.Float64()
}

// Shuffle 随机打乱切片
func (s *SafeRandom) Shuffle(n int, swap func(i, j int)) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.rand.Shuffle(n, swap)
}

// 便捷方法，直接使用全局实例

// Intn 使用全局实例生成随机数
func Intn(n int) int {
	return GetGlobalRandom().Intn(n)
}

// IntnRange 使用全局实例生成范围随机数
func IntnRange(min, max int) int {
	return GetGlobalRandom().IntnRange(min, max)
}

// Int63n 使用全局实例生成int64随机数
func Int63n(n int64) int64 {
	return GetGlobalRandom().Int63n(n)
}

// Float64 使用全局实例生成浮点随机数
func Float64() float64 {
	return GetGlobalRandom().Float64()
}

// Shuffle 使用全局实例随机打乱
func Shuffle(n int, swap func(i, j int)) {
	GetGlobalRandom().Shuffle(n, swap)
}

// 这个文件用于查看现有的随机数生成实现
