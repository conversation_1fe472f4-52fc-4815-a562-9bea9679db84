package strUtil

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"strings"

	"blind_box/pkg/log"

	"github.com/sirupsen/logrus"
)

// ToCamelInitCase .
func ToCamelInitCase(s string, initCase bool) string {
	s = strings.TrimSpace(s)
	if s == "" {
		return s
	}
	a, hasAcronym := uppercaseAcronym.Load(s)
	if hasAcronym {
		s = a.(string)
	}

	n := strings.Builder{}
	n.Grow(len(s))
	capNext := initCase
	prevIsCap := false
	for i, v := range []byte(s) {
		vIsCap := v >= 'A' && v <= 'Z'
		vIsLow := v >= 'a' && v <= 'z'
		if capNext {
			if vIsLow {
				v -= 'a' - 'A' // 转换为大写字母
			}
		} else if i == 0 {
			if initCase && (vIsLow || !vIsCap || v == '/') {
				if vIsLow {
					v -= 'a' - 'A' // 转换为大写字母
				}
			}
		} else if prevIsCap && vIsCap && !hasAcronym {
			v += 'a' - 'A' // 转换为小写字母
		}
		prevIsCap = vIsCap

		if v == '/' {
			n.WriteByte(v)
			capNext = false
		} else if vIsCap || vIsLow {
			n.WriteByte(v)
			capNext = false
		} else if vIsNum := v >= '0' && v <= '9'; vIsNum {
			n.WriteByte(v)
			capNext = true
		} else {
			capNext = v == '_' || v == ' ' || v == '-' || v == '.'
		}
	}
	return n.String()
}

// CommaStrToIdSlice 逗号分割的字符串转换为ID切片
func CommaStrToIdSlice(commaStr string) []uint64 {
	var (
		ctx = context.Background()
	)

	if commaStr == "" {
		log.WithContext(ctx).WithField("commaStr", commaStr).Warn("CommaStrToIdSlice empty")
		return make([]uint64, 0)
	}
	slice := strings.Split(commaStr, ",")
	idSlice := make([]uint64, 0, len(slice))
	for _, v := range slice {
		u, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			log.WithContext(ctx).WithError(err).
				WithField("commaStr", commaStr).
				Error("strconv.ParseUint error")
			continue
		}
		if u <= 0 {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"commaStr": commaStr,
				"u":        u,
			}).Warn("CommaStrToIdSlice u <= 0")
			continue
		}
		idSlice = append(idSlice, u)
	}
	return idSlice
}

func IntMapJoin(intMap map[int]struct{}, seq string) string {
	intArr := make([]int, len(intMap))

	// 将整数转换为字符串
	i := 0
	for num := range intMap {
		intArr[i] = num
		i++
	}

	// 对整数数组进行排序
	sort.Ints(intArr)

	// 将排序后的整数数组转换为字符串数组
	strArr := make([]string, len(intArr))
	for i, num := range intArr {
		strArr[i] = fmt.Sprintf("%d", num)
	}

	// 使用 strings.Join 将字符串切片连接为一个字符串，以逗号分割
	result := strings.Join(strArr, seq)

	return result
}

func UintMapJoin(uintMap map[uint32]struct{}, seq string) string {
	uintArr := make([]uint32, len(uintMap))

	// 将整数转换为字符串
	i := 0
	for num := range uintMap {
		uintArr[i] = num
		i++
	}

	// 对整数数组进行排序
	slices.Sort(uintArr)

	// 将排序后的整数数组转换为字符串数组
	strArr := make([]string, len(uintArr))
	for i, num := range uintArr {
		strArr[i] = fmt.Sprintf("%d", num)
	}

	// 使用 strings.Join 将字符串切片连接为一个字符串，以逗号分割
	result := strings.Join(strArr, seq)

	return result
}

func SplitStrToIntSli(str string, seq string) []int {
	if str == "" {
		return []int{}
	}
	strArr := strings.Split(str, seq)
	intArr := make([]int, len(strArr))
	for i, num := range strArr {
		intArr[i], _ = strconv.Atoi(num)
	}
	return intArr
}

func SplitStrToUintSli(str string, seq string) []uint32 {
	if str == "" {
		return []uint32{}
	}
	strArr := strings.Split(str, seq)
	intArr := make([]uint32, len(strArr))
	for i, num := range strArr {
		t, _ := strconv.Atoi(num)
		intArr[i] = uint32(t)
	}
	return intArr
}

func SplitStrToUint64Sli(str string, seq string) []uint64 {
	if str == "" {
		return []uint64{}
	}
	strArr := strings.Split(str, seq)
	intArr := make([]uint64, len(strArr))
	for i, num := range strArr {
		t, _ := strconv.Atoi(num)
		intArr[i] = uint64(t)
	}
	return intArr
}
