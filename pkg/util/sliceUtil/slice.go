package sliceUtil

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

// SliceNotContainerUint64 计算slice1中不包含slice2中的元素
func SliceNotContainer[T comparable](parentSlice, childSlice []T) []T {
	if len(childSlice) == 0 {
		return parentSlice
	}

	cMap := make(map[T]struct{})
	for _, val := range childSlice {
		cMap[val] = struct{}{}
	}

	ret := []T{}
	for _, val := range parentSlice {
		if _, ok := cMap[val]; !ok {
			ret = append(ret, val)
		}
	}
	return ret
}

// SliceToEmptyMap .
func SliceToEmptyMap[T comparable](slice []T) map[T]struct{} {
	retMap := make(map[T]struct{}, len(slice))
	for _, val := range slice {
		if _, ok := retMap[val]; !ok {
			retMap[val] = struct{}{}
		}
	}
	return retMap
}

// StringsToUint64s .
func StringsToUint64s(strs []string) []uint64 {
	ret := []uint64{}
	for _, str := range strs {
		if str == "" {
			continue
		}
		val, _ := strconv.ParseUint(str, 10, 64)
		ret = append(ret, val)
	}
	return ret
}

// Uint64sToStrings .
func Uint64sToStrings(nums []uint64) []string {
	ret := []string{}
	for _, num := range nums {
		if num == 0 {
			continue
		}
		ret = append(ret, strconv.FormatUint(num, 10))
	}
	return ret
}

// GetUintsIntersect .
func GetUintsIntersect(source, target []uint64) []uint64 {
	if len(target) == 0 {
		return source
	}
	if len(source) == 0 {
		return target
	}
	return lo.Intersect[uint64](source, target)
}

// Chunk .
func Chunk[T comparable](slice []T, size int) [][]T {
	var chunks [][]T
	for i := 0; i < len(slice); i += size {
		end := i + size
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func UintSliJoin(intSli []uint32, seq string) string {
	sort.Slice(intSli, func(i, j int) bool {
		return intSli[i] < intSli[j]
	})
	strArr := make([]string, len(intSli))

	// 将整数转换为字符串
	for i, num := range intSli {
		strArr[i] = fmt.Sprintf("%d", num)
	}

	// 使用 strings.Join 将字符串切片连接为一个字符串，以逗号分割
	result := strings.Join(strArr, seq)

	return result
}

func SplitStrToUintSli(str string, seq string) []uint32 {
	if str == "" {
		return []uint32{}
	}
	strArr := strings.Split(str, seq)
	intArr := make([]uint32, len(strArr))
	for i, num := range strArr {
		t, _ := strconv.Atoi(num)
		intArr[i] = uint32(t)
	}
	return intArr
}

func RemoveValue(source []uint32, value uint32) []uint32 {
	result := make([]uint32, 0)
	if len(source) == 0 {
		return result
	}
	for _, v := range source {
		if v != value {
			result = append(result, v)
		}
	}
	return result
}
