package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net"
	"strings"
	"time"
)

// GenerateAppID 生成唯一的AppID
func GenerateAppID(prefix string) string {
	timestamp := time.Now().Format("20060102150405")
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)
	randomCode := hex.EncodeToString(randomBytes)

	if prefix == "" {
		prefix = "APP"
	}

	return fmt.Sprintf("%s_%s_%s", strings.ToUpper(prefix), timestamp, strings.ToUpper(randomCode))
}

// GenerateAppSecret 生成安全的AppSecret (32位)
func GenerateAppSecret() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机生成失败，使用时间戳和SHA256作为后备
		fallback := fmt.Sprintf("%d_fallback_secret", time.Now().UnixNano())
		hash := sha256.Sum256([]byte(fallback))
		return hex.EncodeToString(hash[:16])
	}
	return hex.EncodeToString(bytes)
}

// GenerateSignature 生成签名示例
func GenerateSignature(data interface{}, appSecret string) (string, error) {
	// 将数据转换为JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("marshal data failed: %w", err)
	}

	// 构建签名字符串
	signStr := string(jsonBytes) + "&key=" + appSecret

	// 计算SHA256
	hash := sha256.New()
	hash.Write([]byte(signStr))

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// ValidateIPWhitelist 验证IP是否在白名单中
func ValidateIPWhitelist(clientIP string, whitelist []string) bool {
	// 如果白名单为空，默认允许所有
	if len(whitelist) == 0 {
		return true
	}

	// 解析客户端IP
	ip := net.ParseIP(clientIP)
	if ip == nil {
		return false
	}

	// 检查每个白名单项
	for _, item := range whitelist {
		item = strings.TrimSpace(item)
		if item == "" {
			continue
		}

		// 检查是否是CIDR格式
		if strings.Contains(item, "/") {
			_, cidr, err := net.ParseCIDR(item)
			if err == nil && cidr.Contains(ip) {
				return true
			}
		} else {
			// 单个IP地址
			if item == clientIP {
				return true
			}
		}
	}

	return false
}

// MerchantConfig 商户配置结构
type MerchantConfig struct {
	Merchant       MerchantInfo    `json:"merchant"`
	Authentication AuthInfo        `json:"authentication"`
	Endpoints      EndpointInfo    `json:"endpoints"`
	Restrictions   RestrictionInfo `json:"restrictions"`
	Signature      SignatureInfo   `json:"signature"`
	CreatedAt      string          `json:"created_at"`
}

// MerchantInfo 商户信息
type MerchantInfo struct {
	MerchantID   string `json:"merchant_id"`
	MerchantName string `json:"merchant_name"`
	ContactName  string `json:"contact_name,omitempty"`
	ContactPhone string `json:"contact_phone,omitempty"`
	ContactEmail string `json:"contact_email,omitempty"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

// EndpointInfo 端点信息
type EndpointInfo struct {
	BaseURL         string `json:"base_url"`
	ScanOrder       string `json:"scan_order"`
	PaymentCallback string `json:"payment_callback"`
	CallbackURL     string `json:"callback_url,omitempty"`
}

// RestrictionInfo 限制信息
type RestrictionInfo struct {
	IPWhitelist  []string     `json:"ip_whitelist,omitempty"`
	SKUWhitelist []uint64     `json:"sku_whitelist,omitempty"`
	QPSLimit     int          `json:"qps_limit"`
	DailyLimit   int          `json:"daily_limit"`
	AmountLimits AmountLimits `json:"amount_limits"`
}

// AmountLimits 金额限制
type AmountLimits struct {
	SingleMax float64 `json:"single_max"`
	DailyMax  float64 `json:"daily_max"`
}

// SignatureInfo 签名信息
type SignatureInfo struct {
	Algorithm string `json:"algorithm"`
	Format    string `json:"format"`
	Example   string `json:"example,omitempty"`
}

// GenerateMerchantConfig 生成商户配置
func GenerateMerchantConfig(
	merchantCode string,
	merchantName string,
	baseURL string,
	ipWhitelist []string,
	skuWhitelist []uint64,
) *MerchantConfig {
	appID := GenerateAppID(merchantCode)
	appSecret := GenerateAppSecret()

	config := &MerchantConfig{
		Merchant: MerchantInfo{
			MerchantID:   merchantCode,
			MerchantName: merchantName,
		},
		Authentication: AuthInfo{
			AppID:     appID,
			AppSecret: appSecret,
		},
		Endpoints: EndpointInfo{
			BaseURL:         baseURL,
			ScanOrder:       "/api/v1/openapi/order/scan",
			PaymentCallback: "/api/v1/openapi/payment/callback",
		},
		Restrictions: RestrictionInfo{
			IPWhitelist:  ipWhitelist,
			SKUWhitelist: skuWhitelist,
			QPSLimit:     100,
			DailyLimit:   100000,
			AmountLimits: AmountLimits{
				SingleMax: 10000.00,
				DailyMax:  100000.00,
			},
		},
		Signature: SignatureInfo{
			Algorithm: "SHA256",
			Format:    "JSON_BODY + '&key=' + APP_SECRET",
			Example:   "See integration documentation for details",
		},
		CreatedAt: time.Now().Format("2006-01-02 15:04:05"),
	}

	return config
}

// ExportConfigToJSON 导出配置为JSON格式
func ExportConfigToJSON(config *MerchantConfig) (string, error) {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return "", fmt.Errorf("marshal config failed: %w", err)
	}
	return string(data), nil
}

// ParseIPWhitelist 解析IP白名单字符串
func ParseIPWhitelist(whitelist string) []string {
	if whitelist == "" {
		return []string{}
	}

	ips := strings.Split(whitelist, ",")
	result := make([]string, 0, len(ips))

	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			result = append(result, ip)
		}
	}

	return result
}

// ParseSKUWhitelist 解析SKU白名单JSON字符串
func ParseSKUWhitelist(skuJSON string) ([]uint64, error) {
	if skuJSON == "" {
		return []uint64{}, nil
	}

	var skus []uint64
	err := json.Unmarshal([]byte(skuJSON), &skus)
	if err != nil {
		return nil, fmt.Errorf("unmarshal sku list failed: %w", err)
	}

	return skus, nil
}

// FormatSKUWhitelist 格式化SKU白名单为JSON字符串
func FormatSKUWhitelist(skus []uint64) string {
	if len(skus) == 0 {
		return ""
	}

	data, _ := json.Marshal(skus)
	return string(data)
}
