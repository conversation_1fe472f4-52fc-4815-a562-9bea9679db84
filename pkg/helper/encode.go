package helper

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"io"

	"golang.org/x/crypto/bcrypt"
)

const (
	AesKey = "196b9a0f7x3c6d9e"
)

// EncodeMd5 .
func EncodeMd5(str string) string {
	strByte := []byte(str)
	ret := md5.New()
	ret.Write(strByte)
	return hex.EncodeToString(ret.Sum(nil))
}

// EncodeBcrypt .
func EncodeBcrypt(str string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(str), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// AesCbcEncrypt .
func AesCbcEncrypt(str string) (string, error) {
	block, err := aes.NewCipher([]byte(AesKey))
	if err != nil {
		return "", err
	}

	ret := PKCS7Padding([]byte(str), block.BlockSize())
	iv := make([]byte, aes.BlockSize)
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	ciphertext := make([]byte, len(ret))
	mode.CryptBlocks(ciphertext, ret)

	ciphertext = append(iv, ciphertext...)
	encodeRet := base64.StdEncoding.EncodeToString(ciphertext)
	return encodeRet, nil
}

// AesCbcDecrypt .
func AesCbcDecrypt(str string) (string, error) {
	decodeRet, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(AesKey))
	if err != nil {
		return "", err
	}

	iv := decodeRet[:aes.BlockSize]
	decodeRet = decodeRet[aes.BlockSize:]

	mode := cipher.NewCBCDecrypter(block, iv)
	plainStr := make([]byte, len(decodeRet))
	mode.CryptBlocks(plainStr, decodeRet)

	plainStr = PKCS7UnPadding(plainStr)
	return string(plainStr), nil
}

// PKCS7Padding 使用PKCS7填充原始数据
func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// PKCS7UnPadding 移除PKCS7填充
func PKCS7UnPadding(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	return data[:(length - unpadding)]
}
