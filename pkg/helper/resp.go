package helper

import (
	"fmt"
	"net/http"
	"net/url"
	"time"

	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

type Resp struct {
	Code string      `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
	Time float64     `json:"time"`
}

func AppResp(ctx *gin.Context, code, msg string) {
	ctx.Header("trace-id", GetGinRequestID(ctx))
	ctx.JSON(http.StatusOK, Resp{
		Code: code,
		Msg:  msg,
		Time: GetElapsedTime(ctx),
	})
	ctx.Set("rspCode", code)
}

func AppWithDataResp(ctx *gin.Context, code, msg string, data interface{}) {
	ctx.Header("trace-id", GetGinRequestID(ctx))
	ctx.JSON(http.StatusOK, Resp{
		Code: code,
		Msg:  msg,
		Data: data,
		Time: GetElapsedTime(ctx),
	})
	ctx.Set("rspCode", code)
}

// ExportExcelResp .
func ExportExcelResp(ctx *gin.Context, buf []byte, filename string) {
	ctx.Header("Content-Type", "application/octet-stream")

	encodedFilename := url.QueryEscape(filename) + ".xlsx"
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"; filename*=utf-8''%s", encodedFilename, encodedFilename))
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Cache-Control", "no-cache")
	ctx.Header("Pragma", "no-cache")

	ctx.Writer.WriteHeader(http.StatusOK)
	_, err := ctx.Writer.Write(buf)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ExportExcelResp err")
	}
}

func GetElapsedTime(ctx *gin.Context) float64 {
	openTime, _ := ctx.Get("reqTime")
	return float64(time.Since(openTime.(time.Time))) / float64(time.Millisecond)
}

func GetGinRequestID(ctx *gin.Context) string {
	requestId := ctx.Value("request_id")
	return requestId.(string)
}
