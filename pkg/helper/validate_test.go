package helper

import (
	"errors"
	"io"
	"reflect"
	"testing"

	"github.com/go-playground/validator/v10"
)

// 测试用的结构体定义
type TestUser struct {
	Name     string `validate:"required" msg:"用户名是必填项"`
	Email    string `validate:"required,email" msg:"邮箱格式不正确"`
	Phone    string `validate:"checkPhone" msg:"手机号码格式错误"`
	Age      int    `validate:"min=1,max=120" msg:"年龄必须在1-120之间"`
	Password string `validate:"required"`
}

type TestNestedStruct struct {
	User   TestUser `validate:"required"`
	UserId uint64   `validate:"required" msg:"用户ID不能为空"`
}

type TestSliceStruct struct {
	Users []TestUser `validate:"required,dive"`
	Title string     `validate:"required" msg:"标题不能为空"`
}

func TestString2AsciiLen(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				str: "BUSHIROAD Creative 盒蛋 灵能百分百 100 III  集换式 方形亚克力立牌 Vol.2 再版 1盒10个(BOX:4571598656284)",
			},
			want: 103,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := String2AsciiLen(tt.args.str); got != tt.want {
				t.Errorf("String2AsciiLen() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTestTranslate(t *testing.T) {
	// 初始化验证器
	LoadAppValidate()

	// 设置测试用的错误消息映射
	errMsgMap["Phone.checkPhone"] = "手机号格式不正确"

	// 创建一个新的验证器实例并注册自定义验证函数
	v := validator.New()
	v.RegisterValidation("checkPhone", checkPhone)

	type args struct {
		err error
		obj interface{}
	}
	tests := []struct {
		name     string
		args     args
		want     string
		setupErr func() error
	}{
		{
			name: "测试字段msg标签优先级",
			args: args{
				obj: &TestUser{},
			},
			want: "用户名是必填项",
			setupErr: func() error {
				user := &TestUser{
					// Name 为空，应该触发 required 验证错误
					Email:    "<EMAIL>",
					Phone:    "13812345678",
					Age:      25,
					Password: "password",
				}
				return v.Struct(user)
			},
		},
		{
			name: "测试errMsgMap优先级",
			args: args{
				obj: &TestUser{Phone: "invalid"},
			},
			want: "手机号格式不正确",
			setupErr: func() error {
				user := &TestUser{
					Name:     "test",
					Email:    "<EMAIL>",
					Phone:    "invalid", // 无效手机号
					Age:      25,
					Password: "password",
				}
				return v.Struct(user)
			},
		},
		{
			name: "测试嵌套结构体msg标签",
			args: args{
				obj: &TestNestedStruct{},
			},
			want: "用户ID不能为空",
			setupErr: func() error {
				nested := &TestNestedStruct{
					User: TestUser{
						Name:     "test",
						Email:    "<EMAIL>",
						Phone:    "13812345678",
						Age:      25,
						Password: "password",
					},
					// UserId 为空，应该触发验证错误
				}
				return v.Struct(nested)
			},
		},
		{
			name: "测试切片中结构体的msg标签",
			args: args{
				obj: &TestSliceStruct{},
			},
			want: "标题不能为空",
			setupErr: func() error {
				slice := &TestSliceStruct{
					Users: []TestUser{
						{
							Name:     "test",
							Email:    "<EMAIL>",
							Phone:    "13812345678",
							Age:      25,
							Password: "password",
						},
					},
					// Title 为空，应该触发验证错误
				}
				return v.Struct(slice)
			},
		},
		{
			name: "测试默认翻译（无自定义msg）",
			args: args{
				obj: &TestUser{},
			},
			want: "Key: 'TestUser.Password' Error:Field validation for 'Password' failed on the 'required' tag", // 这个字段没有msg标签，会使用默认翻译
			setupErr: func() error {
				user := &TestUser{
					Name:  "test",
					Email: "<EMAIL>",
					Phone: "13812345678",
					Age:   25,
					// Password 为空，且没有msg标签
				}
				return v.Struct(user)
			},
		},
		{
			name: "测试EOF错误",
			args: args{
				err: io.EOF,
				obj: &TestUser{},
			},
			want: "参数错误", // ecode.ParamErr.Message() 的返回值
		},
		{
			name: "测试普通错误",
			args: args{
				err: errors.New("普通错误消息"),
				obj: &TestUser{},
			},
			want: "普通错误消息",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var err error
			if tt.setupErr != nil {
				err = tt.setupErr()
				// 确保 setupErr 确实产生了错误
				if err == nil {
					t.Fatalf("setupErr should return an error, but got nil")
				}
			} else {
				err = tt.args.err
			}

			got := FindTranslate(err, tt.args.obj)
			if got != tt.want {
				t.Errorf("TestTranslate() = %v, want %v", got, tt.want)
			}
		})
	}
}

// 测试 findFieldMsg 函数
func Test_findFieldMsg(t *testing.T) {
	tests := []struct {
		name      string
		obj       interface{}
		fieldName string
		want      string
	}{
		{
			name:      "查找普通字段的msg标签",
			obj:       &TestUser{},
			fieldName: "Name",
			want:      "用户名是必填项",
		},
		{
			name:      "查找不存在的字段",
			obj:       &TestUser{},
			fieldName: "NonExistField",
			want:      "",
		},
		{
			name:      "查找没有msg标签的字段",
			obj:       &TestUser{},
			fieldName: "Password",
			want:      "",
		},
		{
			name:      "查找嵌套结构体中的字段",
			obj:       &TestNestedStruct{},
			fieldName: "UserId",
			want:      "用户ID不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := findFieldMsg(reflect.ValueOf(tt.obj), tt.fieldName)
			if got != tt.want {
				t.Errorf("findFieldMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}
