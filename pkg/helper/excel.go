package helper

import (
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/tealeg/xlsx"
	"io"
	"net/http"
	"time"
)

func ToExcel(title []string, dataList []interface{}) (content io.ReadSeeker) {
	file := xlsx.NewFile()
	sheet, _ := file.AddSheet("sheet1")

	titleRow := sheet.AddRow()
	for _, v := range title {
		cell := titleRow.AddCell()
		cell.Value = v
	}

	for _, v := range dataList {
		row := sheet.AddRow()
		row.WriteStruct(v, -1)
	}

	var buffer bytes.Buffer
	_ = file.Write(&buffer)
	return bytes.NewReader(buffer.Bytes())
}

func ResponseXls(c *gin.Context, content io.ReadSeeker, fileTag string) {
	fileName := fmt.Sprintf("%s%s%d.xlsx", fileTag, `-`, time.Now().Unix())
	c.<PERSON>.Header().Add("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
	c.Writer.Header().Add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	http.ServeContent(c.Writer, c.Request, fileName, time.Now(), content)
}
