package helper

import (
	"context"
	"net/http"
	"strconv"

	"blind_box/app/common/dbs"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
)

func Setup() {
	LoadAppValidate()
}

type CtxAccount struct {
	AccountID uint64
	RoleID    uint64
}

// GetCtxAccount .
func GetCtxAccount(ctx *gin.Context) (*CtxAccount, error) {
	if val, ok := ctx.Get("ctxAccount"); ok && val != nil {
		if ctxAccount, OK := val.(*CtxAccount); OK {
			return ctxAccount, nil
		}
	}
	return &CtxAccount{}, ecode.TokenErr
}

type CtxUser struct {
	UID uint64
}

// GetCtxUser .
func GetCtxUser(ctx *gin.Context) (*CtxUser, error) {
	if val, ok := ctx.Get("ctxUser"); ok && val != nil {
		if ctxUser, OK := val.(*CtxUser); OK {
			return ctxUser, nil
		}
	}
	return &CtxUser{}, ecode.TokenErr
}

func TryGetCtxUser(ctx *gin.Context) (*CtxUser, error) {
	if val, ok := ctx.Get("ctxUser"); ok && val != nil {
		if ctxUser, OK := val.(*CtxUser); OK {
			return ctxUser, nil
		}
	}
	return &CtxUser{}, nil
}

func GetCtxClientType(ctx *gin.Context) dbs.ClientType {
	val := ctx.GetHeader("Client-Type")
	if len(val) <= 0 {
		log.Ctx(ctx).Warn("GetCtxClientType from ctx Err")
		return dbs.ClientTypeUnknown
	}

	clientType, err := strconv.Atoi(val)
	if err != nil {
		log.Ctx(ctx).WithField("val", val).Warn("GetCtxClientType from ctx Err")
		return dbs.ClientTypeUnknown
	}

	switch clientType {
	case int(dbs.ClientTypeAdmin):
		return dbs.ClientTypeAdmin
	case int(dbs.ClientTypeWeb):
		return dbs.ClientTypeWeb
	default:
		return dbs.ClientTypeUnknown
	}
}

func GenGinCtx() *gin.Context {
	var (
		ginCtx = &gin.Context{
			Request: &http.Request{},
		}
		requestID = ctxUtil.GenRequestID(ctxUtil.ReqTypeS)
		newCtx    = ctxUtil.WithRequestID(context.TODO(), requestID)
	)
	ginCtx.Request = ginCtx.Request.WithContext(newCtx)
	return ginCtx
}
