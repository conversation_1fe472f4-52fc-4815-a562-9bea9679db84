package helper

import (
	"fmt"
	"strings"
	"time"

	"blind_box/app/common/dbs"
	adminConfig "blind_box/app/dao/admin/config"
	"blind_box/pkg/util/safe_random"

	"github.com/gin-gonic/gin"
)

type AppNoType string

const (
	AppNoReceipt       AppNoType = "receipt"        // 收款单
	AppNoReceiptBill   AppNoType = "receipt_bill"   // 收款单明细号
	AppNoCreditBill    AppNoType = "credit_bill"    // 授信明细号
	AppNoOrder         AppNoType = "order"          // 订单
	AppNoSubFullPay    AppNoType = "sub_full_pay"   // 全款子订单号
	AppNoSubReserve    AppNoType = "sub_reserve"    // 预订子订单号
	AppNoDeposit       AppNoType = "deposit"        // 定金单
	AppNoFinal         AppNoType = "final"          // 尾款单
	AppNoPC            AppNoType = "pc"             // 支付批次
	AppNoPay           AppNoType = "pay"            // 支付
	AppNoApply         AppNoType = "apply"          // 请款单
	AppNoStoreIn       AppNoType = "store_in"       // 入库单
	AppNoStoreOut      AppNoType = "store_out"      // 出库单
	AppNoStorePurchase AppNoType = "store_purchase" // 采购单
	AppNoStoreRefund   AppNoType = "store_refund"   // 退货单

	AppNoGoodsSpu AppNoType = "goods_spu" // 商品spu
	AppNoGoodsSku AppNoType = "goods_sku" // 商品sku

	AppNoBox       AppNoType = "box"              // 盲盒
	AppNoBoxOrder  AppNoType = "box_order"        // 盲盒订单
	AppNoBoxTrade  AppNoType = "box_trade"        // 盲盒交易
	AppNoBoxRefund AppNoType = "box_trade_refund" // 盲盒交易退款
)

// RandCode 生成指定长度的随机字符串
func RandCode(leng int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	ret := []byte{}
	for i := 0; i < leng; i++ {
		ret = append(ret, byte(str[safe_random.Intn(len(str))]))
	}
	return string(ret)
}

func GetRandCodeByTime(leng int) string {
	return fmt.Sprintf("%s%v", RandCode(leng), time.Now().Unix())
}

// GetAppNo .
func GetAppNo(t AppNoType) string {
	noStrs := getAppNo(t)
	if len(noStrs) <= 0 {
		return ""
	}

	randNum := dbs.RandomRangeTime(1000, 9999)
	return fmt.Sprintf("%v-%v-%v%v", noStrs[0], noStrs[1], randNum, noStrs[2])
}

// GetAppNo .
func GetAppNoByNum(t AppNoType, num uint32) string {
	noStrs := getAppNo(t)
	var randStr string
	if num > 99 {
		randNum := dbs.RandomRangeTime(1000, 9999)
		randStr = fmt.Sprintf("%v", randNum)
	} else {
		randNum := dbs.RandomRangeTime(10, 99)
		randStr = fmt.Sprintf("%v%0*d", randNum, 2, num)
	}

	return fmt.Sprintf("%v-%v-%v%v", noStrs[0], noStrs[1], randStr, noStrs[2])
}

func getAppNo(t AppNoType) []string {
	ret := []string{}
	switch t {
	case AppNoReceipt:
		ret = append(ret, "SK") // 收款单
	case AppNoReceiptBill:
		ret = append(ret, "RB") // 收款单明细
	case AppNoCreditBill:
		ret = append(ret, "CB") // 授信明细
	case AppNoOrder:
		ret = append(ret, "DH") // 订单-单号
	case AppNoSubFullPay:
		ret = append(ret, "QK") // 全款订单-子单号
	case AppNoSubReserve:
		ret = append(ret, "YD") // 预定订单-子单号
	case AppNoDeposit:
		ret = append(ret, "YDD") // 预定-定金单
	case AppNoFinal:
		ret = append(ret, "YDW") // 预定-尾款单
	case AppNoPC:
		ret = append(ret, "PC") // 支付批次
	case AppNoPay:
		ret = append(ret, "FK") // 付款
	case AppNoApply:
		ret = append(ret, "AK") // 请款
	case AppNoStoreIn:
		ret = append(ret, "RK") // 入库
	case AppNoStoreOut:
		ret = append(ret, "CK") // 出库
	case AppNoStorePurchase:
		ret = append(ret, "CG") // 采购
	case AppNoStoreRefund:
		ret = append(ret, "TH") // 退货
	case AppNoGoodsSpu:
		ret = append(ret, "GP") // 商品spu
	case AppNoGoodsSku:
		ret = append(ret, "GK") // 商品sku
	case AppNoBox:
		ret = append(ret, "BB") // 盲盒
	case AppNoBoxOrder:
		ret = append(ret, "BO") // 盲盒订单
	case AppNoBoxTrade:
		ret = append(ret, "BT") // 盲盒交易
	case AppNoBoxRefund:
		ret = append(ret, "BTR") // 盲盒交易退款
	}
	if len(ret) <= 0 {
		return ret
	}
	nowTime := time.Now().Format(dbs.TimeDateFormatAppress)
	nowTimeSlice := strings.Split(nowTime, "-")
	return append(ret, nowTimeSlice...)
}

func GetImageCdnUrl(ctx *gin.Context, url dbs.CdnImg) string {
	if url == "" {
		return ""
	}
	cdnDomain := adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyImageCdnDomain)
	if cdnDomain == "" {
		return string(url)
	}
	if strings.HasPrefix(string(url), "http") || strings.HasPrefix(string(url), "https") {
		return string(url)
	}
	return cdnDomain + string(url)
}

type MyInt interface {
	int | int8 | int16 | int32 | int64 |
		uint | uint8 | uint16 | uint32 | uint64
}

func Max[T MyInt](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Min[T MyInt](a, b T) T {
	if a < b {
		return a
	}
	return b
}

func FindMinSlice[T MyInt](slice []T) T {
	if len(slice) == 0 {
		return 0
	}
	min := slice[0]
	for _, val := range slice[1:] {
		if val < min {
			min = val
		}
	}

	return min
}
