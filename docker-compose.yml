version: '3.8'

services:
  blind_box:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blind_box
    ports:
      - "8083:8083"
    environment:
      - config=./config/test.ini
    volumes:
      - ./data/logs:/data/tsf/cmd/data/logs  # 挂载日志目录
      - ./config:/data/tsf/cmd/config
    restart: unless-stopped
    networks:
      - blind_box_network

networks:
  blind_box_network:
    driver: bridge 