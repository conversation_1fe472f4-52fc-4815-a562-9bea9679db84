package applet

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/admin/config"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2/miniprogram/subscribe"
)

func (e *Entry) SendMsg(ctx *gin.Context, subTemplate *SubTemplate) error {
	env := config.RedisGetConfig[string](ctx, config.KeyCurrentEnv)
	subClient := e.InitAppletClient(ctx).GetSubscribe()

	msg := &subscribe.Message{
		ToUser:     subTemplate.Openid,
		TemplateID: subTemplate.TempId,
		Page:       subTemplate.Page,
		Data:       subTemplate.Data,
		Lang:       "zh_CN",
	}

	switch env {
	case string(dbs.EnvDevelop):
		msg.MiniprogramState = "developer"
	case string(dbs.EnvTest):
		msg.MiniprogramState = "trial"
	default:
		msg.MiniprogramState = "formal"
	}

	if err := subClient.Send(msg); err != nil {
		log.Ctx(ctx).WithError(err).WithField("msg", msg).Error("SendMsg Send error")
		return err
	}

	return nil
}
