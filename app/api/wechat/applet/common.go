package applet

import (
	userSub "blind_box/app/dao/user/subscribe"
	"blind_box/config"
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"github.com/silenceper/wechat/v2/miniprogram/subscribe"
)

type SubTemplate struct {
	Openid string
	TempId string
	Page   string
	Data   map[string]*subscribe.DataItem
}

// InitAppletClient .
func (e *Entry) InitAppletClient(ctx *gin.Context) *miniprogram.MiniProgram {
	wc := wechat.NewWechat()
	miniCfg := &miniConfig.Config{
		AppID:     config.WechatCfg.AppletID,
		AppSecret: config.WechatCfg.AppletSecret,
		Cache:     cache.NewMemory(),
	}
	return wc.GetMiniProgram(miniCfg)
}

func (e *Entry) GetSubTemplate(ctx *gin.Context, subType uint32, openid string) (*SubTemplate, error) {
	subTemplate := &SubTemplate{
		Openid: openid,
		Data:   map[string]*subscribe.DataItem{},
	}
	switch subType {
	case uint32(userSub.EntityTypeBoxSaleStart):
		subTemplate.TempId = "__QwfT1QQhme5DAdvPH-YAgXbCAk0gagBD_hm2mYi78"
		subTemplate.Page = "pages/detail/index?id=%v"
		// subTemplate.Data = map[string]*subscribe.DataItem{
		// 	"thing1": {
		// 		Value: "value1",
		// 		Color: "#173177",
		// 	},
		// }
	case uint32(userSub.EntityTypeSkuHasStock):
		subTemplate.TempId = "RCOiHjFgpFeNN4p7__cKPEXNTF0U7Vl_a4vNWH34R2k"
		subTemplate.Page = "pages/productDetail/index?spuId=%v&skuId=%v"
	case uint32(userSub.EntityTypeActStart):
		subTemplate.TempId = "__QwfT1QQhme5DAdvPH-YAgXbCAk0gagBD_hm2mYi78"
		subTemplate.Page = "pages/detail/index?id=%v"
	default:
		return nil, errors.New("subType not found")
	}

	return subTemplate, nil
}
