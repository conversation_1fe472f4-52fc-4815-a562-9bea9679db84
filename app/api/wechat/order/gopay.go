package order

import (
	"fmt"

	"blind_box/app/common/dbs"
	adminConfig "blind_box/app/dao/admin/config"
	commonDao "blind_box/app/dao/common"
	userDao "blind_box/app/dao/user"
	payLog "blind_box/app/dao/wechat/pay_log"
	"blind_box/config"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"blind_box/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat/v3"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

func (e *Entry) InitClientV3(ctx *gin.Context) (*wechat.ClientV3, error) {
	privateKey, err := config.LoadWxPrivateKey()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("LoadWxPrivateKeyFailed")
		return nil, err
	}
	log.Ctx(ctx).WithFields(logrus.Fields{
		"wechatCfg": config.WechatCfg,
	}).Debug("LoadWxPrivateKey")

	client, err := wechat.NewClientV3(
		config.WechatCfg.MchID,
		config.WechatCfg.SerialNo,
		config.WechatCfg.ApiV3Key,
		string(privateKey))
	if err != nil {
		log.Ctx(ctx).WithField("config.WechatCfg", config.WechatCfg).WithError(err).Error("NewClientV3")
		return nil, err
	}

	return client, nil
}

// GopayOrderPay .
func (e *Entry) GopayOrderPay(ctx *gin.Context, tx *gorm.DB, uAuth *userDao.UserAuth, amount uint64, payNo string, payInfo commonDao.WxpayOrderInfo) (*wechat.AppletParams, error) {
	client, err := e.InitClientV3(ctx)
	if err != nil {
		return nil, err
	}

	var (
		bm = make(gopay.BodyMap)
	)

	bm.Set("appid", config.WechatCfg.AppletID).
		Set("mchid", config.WechatCfg.MchID).
		Set("description", fmt.Sprintf("%v-%v", uAuth.ID, payNo)).
		Set("out_trade_no", payNo).
		Set("notify_url", adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyWxAppletPayNotify)). // 支付结果通知的回调地址 必须为 https 协议的 URL，不能携带查询参数
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", amount).
				Set("currency", "CNY")
		}).
		Set("payer", wechat.Payer{
			Openid: uAuth.AuthUid,
		})
	if payInfo.TimeExpire != "" {
		bm.Set("time_expire", payInfo.TimeExpire)
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"bm": bm.JsonBody(),
	}).Debug("GopayOrderPay bm")

	payResp, err := client.V3TransactionJsapi(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GopayOrderPayFailed")
		return nil, err
	}
	log.Ctx(ctx).WithField("payResp", payResp).WithField("reqBm", bm).Info("GopayOrderPayReqRespSuccess")

	if payResp.Code != wechat.Success {
		log.Ctx(ctx).WithError(err).WithFields(logrus.Fields{
			"payResp":  &payResp,
			"code":     &payResp.Code,
			"signInfo": &payResp.SignInfo,
			"error":    &payResp.Error,
		}).Error("payRsp.Code Failed")
		return nil, fmt.Errorf("pay failed: %v", payResp.Error)
	}

	var (
		entityInfo, _ = json.Marshal(payInfo)
		reqData, _    = json.Marshal(bm)
		respData, _   = json.Marshal(payResp.Response)
	)

	logData := &payLog.Model{
		UserID: uAuth.ID,
		ReqID:  ctxUtil.GetRequestID(ctx),
		Amount: amount,
		// EntityType:    uint32(dao.ActionPay),
		RequestFunc:   "V3TransactionJsapi",
		WxEntityID:    "",
		OutEntityNo:   payNo,
		Status:        dbs.False,
		OutEntityInfo: string(entityInfo),
		RequestData:   string(reqData),
		ResponseData:  string(respData),
	}

	// 返回支付参数给前端
	applet, err := client.PaySignOfApplet(config.WechatCfg.AppletID, payResp.Response.PrepayId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PaySignOfAppletFailed")
		return nil, err
	}

	if err = e.RedisSetPrePayID(ctx, payNo, payResp.Response.PrepayId); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisSetAccountTokenFailed")
		return nil, err
	}

	if err = e.WxPayLogRepo.CreateWithTx(ctx, tx, logData); err != nil {
		return nil, err
	}

	return applet, nil
}

// RedisSetAccountToken .
func (e *Entry) RedisSetPrePayID(ctx *gin.Context, payNo, prePayID string) error {
	var (
		cacheKey   = redis.GetBoxOrderPrepayIdKey(payNo)
		expireTime = dbs.GetRedisExpireTimeDefault(dbs.RedisExpireTime15Min) // 过期时间
	)

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, prePayID, expireTime).Err(); err != nil {
		return err
	}
	return nil
}

// GopayOrderRefund .
func (e *Entry) GopayOrderRefund(ctx *gin.Context, tx *gorm.DB, uInfo *userDao.Model, refundNo string, refundInfo commonDao.WxrefundOrderInfo) error {
	client, err := e.InitClientV3(ctx)
	if err != nil {
		return err
	}

	// 构建退款请求参数
	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", refundInfo.PayNo).
		Set("out_refund_no", refundNo).
		Set("notify_url", adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyWxAppletRefundNotify)). // 支付结果通知的回调地址 必须为 https 协议的 URL，不能携带查询参数
		Set("amount", wechat.RefundOrderAmount{
			Total:    int(refundInfo.TotalAmount),
			Refund:   int(refundInfo.RefundAmount),
			Currency: "CNY",
		})

	refundResp, err := client.V3Refund(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithField("reqBm", bm).WithError(err).Error("GopayOrderRefundFailed")
		return err
	}
	log.Ctx(ctx).WithField("refundResp", refundResp).WithField("reqBm", bm).Info("GopayOrderRefundReqRespSuccess")

	if refundResp.Code != wechat.Success {
		log.Ctx(ctx).WithError(err).Error("refundRsp.Code Failed")
		return fmt.Errorf("refund failed: %v", refundResp.Error)
	}

	var (
		entityInfo, _ = json.Marshal(refundInfo)
		reqData, _    = json.Marshal(bm)
		respData, _   = json.Marshal(refundResp.Response)
	)

	logData := &payLog.Model{
		UserID: uInfo.ID,
		ReqID:  helper.GetGinRequestID(ctx),
		// EntityType:    uint32(dao.ActionRefund),
		WxEntityID:    refundResp.Response.RefundId,
		RequestFunc:   "V3Refund",
		Status:        dbs.False,
		OutEntityNo:   refundNo,
		OutEntityInfo: string(entityInfo),
		RequestData:   string(reqData),
		ResponseData:  string(respData),
	}

	if err = e.WxPayLogRepo.CreateWithTx(ctx, tx, logData); err != nil {
		return err
	}

	return nil
}
