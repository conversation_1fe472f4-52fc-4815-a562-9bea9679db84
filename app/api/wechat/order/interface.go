package order

import (
	"sync"

	userDao "blind_box/app/dao/user"
	payLog "blind_box/app/dao/wechat/pay_log"
	"blind_box/pkg/redis"
	jsoniter "github.com/json-iterator/go"
)

const (
	jkyCreateOrderUrl string = "https://polyapi.jackyun.com/openapi/do/notify/STANDARD/32/198"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Api interface {
}

type Entry struct {
	UserRepo userDao.Repo
	// WxPayLogRepo payLog.Repo
	WxPayLogRepo *payLog.Entry

	RedisCli *redis.RedisClient
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:     userDao.GetRepo(),
		WxPayLogRepo: payLog.GetRepo(),
		RedisCli:     redis.GetRedisClient(),
	}
}
