package common

import (
	userDao "blind_box/app/dao/user"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

const (
	jkyCreateOrderUrl string = "https://polyapi.jackyun.com/openapi/do/notify/STANDARD/32/198"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Api interface {
}

type Entry struct {
	UserRepo userDao.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo: userDao.GetRepo(),
	}
}
