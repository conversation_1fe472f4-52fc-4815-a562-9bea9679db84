package common

import (
	"blind_box/config"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
)

// InitWechatClient .
func (e *Entry) InitWechatClient(ctx *gin.Context) (*wechat.ClientV3, error) {
	privateKey, err := config.LoadWxPrivateKey()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("LoadWxPrivateKeyFailed")
		return nil, err
	}

	client, err := wechat.NewClientV3(config.WechatCfg.MchID, config.WechatCfg.SerialNo, config.WechatCfg.ApiV3Key, string(privateKey))
	if err != nil {
		return nil, err
	}

	return client, nil
}
