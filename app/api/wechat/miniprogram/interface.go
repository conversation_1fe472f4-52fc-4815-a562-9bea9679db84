package miniprogram

import (
	"context"
	"sync"

	"blind_box/config"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Api interface {
	InitWechatMiniProgram(ctx *gin.Context) *miniprogram.MiniProgram
}

type Entry struct {
	Wechat *wechat.Wechat
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		Wechat: initWechatSDK(),
	}
}

func initWechatSDK() *wechat.Wechat {
	wc := wechat.NewWechat()
	redisOpt := &cache.RedisOpts{
		Host:     config.RedisCfg.Host,
		Password: config.RedisCfg.Password,
		Database: config.RedisCfg.Db,
	}
	redisCache := cache.NewRedis(context.Background(), redisOpt)
	wc.SetCache(redisCache)
	return wc
}
