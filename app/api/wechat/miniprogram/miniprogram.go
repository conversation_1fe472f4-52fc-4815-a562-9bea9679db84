package miniprogram

import (
	"blind_box/config"

	"github.com/gin-gonic/gin"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniConfig "github.com/silenceper/wechat/v2/miniprogram/config"
)

func (e *Entry) InitWechatMiniProgram(ctx *gin.Context) *miniprogram.MiniProgram {
	cfg := &miniConfig.Config{
		AppID:     config.WechatCfg.AppletID,
		AppSecret: config.WechatCfg.AppletSecret,
	}

	return e.Wechat.GetMiniProgram(cfg)
}
