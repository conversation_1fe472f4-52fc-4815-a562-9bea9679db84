package wechat

import (
	"net/http"

	wxOrderApi "blind_box/app/api/wechat/order"
	orderSrv "blind_box/app/service/order"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat/v3"
)

// WxOrderPayNotify .
func WxOrderPayNotify(ctx *gin.Context) {
	notifyReq, err := wechat.V3ParseNotify(ctx.Request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("WxOrderPayNotify ParseNotify error")
		ctx.JSON(http.StatusBadRequest, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "Parse Notify Failed"})
		return
	}
	log.Ctx(ctx).WithField("notifyReq", notifyReq).Info("WxOrderPayNotifyReq")

	client, err := wxOrderApi.GetApi().InitClientV3(ctx)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("WxOrderPayNotify InitClientV3 error")
		ctx.JSON(http.StatusBadRequest, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "InitClientV3 Failed"})
		return
	}

	log.Ctx(ctx).WithField("client", client).Info("WxOrderPayNotify Client Info")

	//// 获取微信平台证书
	//certMap := client.WxPublicKeyMap()
	//// 异步通知验签
	//err = notifyReq.VerifySignByPKMap(certMap)
	//if err != nil {
	//	log.Ctx(ctx).WithFields(logrus.Fields{
	//		"certMap":   certMap,
	//		"notifyReq": notifyReq,
	//	}).WithError(err).Error("WxOrderPayNotify VerifySignByPKMap error")
	//	ctx.JSON(http.StatusBadRequest, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "VerifySignByPKMap Failed"})
	//	return
	//}

	log.Ctx(ctx).WithField("notifyReq", notifyReq).Info("WxOrderPayNotify VerifySignByPKMap Success")

	if err = orderSrv.GetService().WxAppletPayNotify(ctx, notifyReq); err != nil {
		log.Ctx(ctx).WithError(err).Error("WxOrderPayNotify Logic error")
		ctx.JSON(http.StatusBadRequest, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "notify logic error"})
		return
	}
	log.Ctx(ctx).WithField("notifyReq", notifyReq).Info("WxOrderPayNotify Logic Success")

	ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.SUCCESS, Message: "Success"})
}

// WxOrderRefundNotify .
func WxOrderRefundNotify(ctx *gin.Context) {
	notifyReq, err := wechat.V3ParseNotify(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "Parse Notify Failed"})
		return
	}
	log.Ctx(ctx).WithField("notifyReq", notifyReq).Info("WxOrderRefundNotifyReq")

	client, err := wxOrderApi.GetApi().InitClientV3(ctx)
	if err != nil {
		ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "InitClientV3 Failed"})
		return
	}

	// 获取微信平台证书
	certMap := client.WxPublicKeyMap()
	// 异步通知验签
	err = notifyReq.VerifySignByPKMap(certMap)
	if err != nil {
		ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "VerifySignByPKMap Failed"})
		return
	}

	if err = orderSrv.GetService().WxAppletRefundNotify(ctx, notifyReq); err != nil {
		ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.FAIL, Message: "notify logic error"})
		return
	}

	ctx.JSON(http.StatusOK, &wechat.V3NotifyRsp{Code: gopay.SUCCESS, Message: "Success"})
}
