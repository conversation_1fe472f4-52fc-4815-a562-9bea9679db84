# app/api 目录规范文档

本文档记录 `app/api` 目录下第三方服务集成的代码规范和模式。

## 目录结构

```
app/api/
├── alipay/         # 支付宝支付集成
│   └── alipay.go   # 支付宝支付接口（待实现）
├── aliyun/         # 阿里云服务集成
│   ├── common/     # 公共常量和工具
│   │   ├── common.go  # 工具函数
│   │   └── const.go   # 常量定义
│   ├── oss/        # OSS 对象存储
│   │   ├── interface.go  # 接口定义和依赖注入
│   │   └── oss.go        # OSS 服务实现
│   └── oss.go      # OSS 入口（旧版兼容）
└── wechat/         # 微信服务集成
    ├── applet/     # 小程序服务
    │   ├── common.go      # 公共函数
    │   ├── interface.go   # 接口定义
    │   └── subscribe.go   # 订阅消息
    ├── common/     # 微信公共组件
    │   ├── common.go      # 公共函数
    │   └── interface.go   # 接口定义
    ├── miniprogram/  # 小程序 SDK
    │   ├── interface.go   # 接口定义
    │   └── miniprogram.go # 小程序实现
    ├── order/      # 微信支付
    │   ├── gopay.go       # GoPay 支付实现
    │   └── interface.go   # 接口定义
    └── applet.go   # 小程序入口（旧版兼容）
```

## 核心设计模式

### 1. 单例模式与依赖注入

所有 API 服务模块采用单例模式，通过依赖注入管理外部依赖：

```go
// interface.go 标准结构
type Api interface {
    // 接口方法定义
}

type Entry struct {
    // 依赖的 DAO 层
    UserRepo      userDao.Repo
    OrderRepo     *orderDao.Entry
    
    // 依赖的其他服务
    RedisCli      *redis.RedisClient
}

var (
    defaultEntry         *Entry
    defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}

func newEntry() *Entry {
    return &Entry{
        UserRepo:  userDao.GetRepo(),
        OrderRepo: orderDao.GetRepo(),
        RedisCli:  redis.GetRedisClient(),
    }
}
```

### 2. JSON 处理统一化

所有模块统一使用 jsoniter：

```go
import jsoniter "github.com/json-iterator/go"

var json = jsoniter.ConfigCompatibleWithStandardLibrary
```

## 各模块规范

### 阿里云服务 (aliyun/)

#### 常量管理
- **位置**: `common/const.go`
- **规范**: 使用类型化常量，避免魔法数字
```go
type BucketType string
type Resolution uint32

const (
    BucketTypeImage BucketType = "image"
    Resolution_1280 Resolution = 4  // 高清1280
)
```

#### OSS 服务
- **STS Token 生成**: 临时凭证，有效期 3600 秒
- **签名算法**: OSS4-HMAC-SHA256
- **文件大小限制**: 0-1000M
```go
func (e *Entry) GetOssToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssTokenResp, error)
func (e *Entry) GetOssAppletToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssAppletTokenResp, error)
```

### 微信服务 (wechat/)

#### SDK 初始化
- **缓存策略**: 优先使用 Redis 缓存，内存缓存作为备用
- **配置管理**: 从 config 包读取配置
```go
// 小程序初始化
func (e *Entry) InitAppletClient(ctx *gin.Context) *miniprogram.MiniProgram {
    wc := wechat.NewWechat()
    miniCfg := &miniConfig.Config{
        AppID:     config.WechatCfg.AppletID,
        AppSecret: config.WechatCfg.AppletSecret,
        Cache:     cache.NewMemory(),
    }
    return wc.GetMiniProgram(miniCfg)
}
```

#### 微信支付
- **支付版本**: 使用 V3 API
- **签名方式**: RSA with SHA256
- **GoPay 集成**: 使用 go-pay/gopay 库
```go
func (e *Entry) InitClientV3(ctx *gin.Context) (*wechat.ClientV3, error)
func (e *Entry) GopayOrderPay(ctx *gin.Context, tx *gorm.DB, uAuth *userDao.UserAuth, amount uint64, payNo string, payInfo commonDao.WxpayOrderInfo) (*wechat.AppletParams, error)
```

#### 订阅消息
- **环境区分**: 开发(developer)、测试(trial)、生产(formal)
- **模板管理**: 通过 subType 映射模板 ID
```go
func (e *Entry) SendMsg(ctx *gin.Context, subTemplate *SubTemplate) error
func (e *Entry) GetSubTemplate(ctx *gin.Context, subType uint32, openid string) (*SubTemplate, error)
```

### 支付宝服务 (alipay/)
- **状态**: 待实现 (TODO)
- **预留接口**: 保持与微信支付相似的结构

## 编码规范

### 错误处理
```go
// 记录详细错误日志
if err != nil {
    log.Ctx(ctx).WithError(err).WithField("param", param).Error("操作失败")
    return nil, err
}

// 成功日志（重要操作）
log.Ctx(ctx).WithField("response", resp).Info("操作成功")
```

### 配置读取
```go
// 从 Redis 读取动态配置
env := config.RedisGetConfig[string](ctx, config.KeyCurrentEnv)

// 从配置文件读取静态配置
appID := config.WechatCfg.AppletID
```

### 事务支持
支付等关键操作支持事务：
```go
func (e *Entry) GopayOrderPay(ctx *gin.Context, tx *gorm.DB, ...) error
```

### 日志记录
- **敏感信息**: 不记录密钥、密码等敏感信息
- **调试日志**: 使用 Debug 级别，生产环境自动过滤
- **错误日志**: 包含上下文信息，便于问题定位

## 最佳实践

### 1. SDK 复用
- 避免重复初始化 SDK 客户端
- 使用单例模式管理 SDK 实例
- 合理配置缓存策略

### 2. 超时控制
```go
// STS Token 有效期
request.DurationSeconds = "3600"

// 支付订单超时
if payInfo.TimeExpire != "" {
    bm.Set("time_expire", payInfo.TimeExpire)
}
```

### 3. 签名验证
- 微信支付: RSA 签名验证
- OSS: HMAC-SHA256 签名
- 支付宝: RSA2 签名（待实现）

### 4. 环境隔离
```go
switch env {
case string(dbs.EnvDevelop):
    msg.MiniprogramState = "developer"
case string(dbs.EnvTest):
    msg.MiniprogramState = "trial"
default:
    msg.MiniprogramState = "formal"
}
```

### 5. 批量操作优化
- 使用连接池管理外部服务连接
- 合理使用批量接口减少 API 调用

## 安全规范

### 密钥管理
- **存储**: 密钥文件独立存储，不进入代码仓库
- **加载**: 运行时从文件系统加载
```go
privateKey, err := config.LoadWxPrivateKey()
```

### 签名校验
- 所有支付回调必须验证签名
- 使用官方 SDK 的签名验证方法

### 日志脱敏
```go
// 不记录完整密钥
log.Ctx(ctx).WithFields(logrus.Fields{
    "mchID": config.WechatCfg.MchID,
    // 不记录 ApiV3Key 等敏感信息
}).Debug("初始化支付客户端")
```

## 扩展指南

### 添加新的第三方服务

1. **创建目录结构**
```
app/api/[service_name]/
├── interface.go    # 接口定义
├── [module].go     # 具体实现
└── common/         # 公共组件（可选）
```

2. **实现接口层**
- 定义 Api 接口
- 实现 Entry 结构体
- 添加单例获取方法

3. **依赖注入**
```go
func newEntry() *Entry {
    return &Entry{
        // 注入所需依赖
        XxxRepo: xxxDao.GetRepo(),
    }
}
```

4. **实现业务方法**
- 参数使用 DTO 结构
- 返回统一格式响应
- 完善错误处理和日志

### 版本升级
- 保持向后兼容
- 使用版本化 API 路径
- 逐步迁移旧版本调用

## 常见问题

### Q: 为什么使用单例模式？
A: 避免重复初始化 SDK 客户端，提高性能，统一管理依赖。

### Q: 如何处理第三方服务超时？
A: 设置合理的超时时间，使用 context 传递超时控制，实现重试机制。

### Q: 如何调试第三方 API？
A: 使用 Debug 级别日志记录请求和响应，注意脱敏处理。

## TODO 列表

1. **支付宝支付集成** - `alipay/alipay.go` 待实现
2. **视频转码服务** - 阿里云 MPS 服务集成
3. **短信服务** - 阿里云短信服务集成
4. **统一异常处理** - 第三方服务异常的统一封装
5. **监控告警** - 第三方服务调用监控