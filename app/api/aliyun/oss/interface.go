package oss

import (
	userDao "blind_box/app/dao/user"
	"sync"
)

type Api interface {
}

type Entry struct {
	UserRepo userDao.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo: userDao.GetRepo(),
	}
}
