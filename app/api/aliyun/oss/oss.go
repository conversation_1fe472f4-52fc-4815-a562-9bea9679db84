package oss

import (
	"blind_box/app/api/aliyun/common"
	"blind_box/app/common/dbs"
	aliyunDto "blind_box/app/dto/api/aliyun"
	"blind_box/config"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"hash"
	"io"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"github.com/aliyun/credentials-go/credentials"
	"github.com/gin-gonic/gin"
)

// GetOssToken .
func (e *Entry) GetOssToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssTokenResp, error) {
	var (
		bucket      = common.GetBucket(bt)
		client, err = sts.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return nil, err
	}
	// 创建AssumeRole请求
	request := sts.CreateAssumeRoleRequest()
	request.Scheme = "https"
	request.RoleArn = config.AliyunCfg.RoleArn
	request.RoleSessionName = fmt.Sprintf("%v-Account-%v", bucket, accountID)
	request.DurationSeconds = "3600" // 临时凭证有效时间，单位为秒（最多3600秒）

	// 发送请求并获取响应
	resp, err := client.AssumeRole(request)
	if err != nil {
		return nil, err
	}
	return &aliyunDto.AliyunOssTokenResp{
		Bucket:          bucket,
		RegionID:        config.AliyunCfg.OssRegionID,
		Token:           resp.Credentials.SecurityToken,
		AccessKeyId:     resp.Credentials.AccessKeyId,
		AccessKeySecret: resp.Credentials.AccessKeySecret,
	}, nil
}

// GetOssAppletToken .
func (e *Entry) GetOssAppletToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssAppletTokenResp, error) {
	bucket := common.GetBucket(bt)
	credConf := new(credentials.Config).
		SetType("ram_role_arn").                                             // 填写Credential类型，固定值为ram_role_arn
		SetAccessKeyId(config.AliyunCfg.AccessKeyID).                        // RAM用户的访问密钥AccessKeyId
		SetAccessKeySecret(config.AliyunCfg.AccessKeySecret).                // RAM用户的访问密钥AccessKeySecret
		SetRoleArn(config.AliyunCfg.RoleArn).                                // RAM角色的ARN信息，即需要扮演的角色ID。格式为acs:ram::$accountID:role/$roleName
		SetRoleSessionName(fmt.Sprintf("%v-Account-%v", bucket, accountID)). // 自定义角色会话名称，用于区分不同的令牌
		SetRoleSessionExpiration(3600)                                       // （可选）限制STS Token的有效时间
		// SetPolicy("").                                                       // （可选）限制STS Token权限

	provider, err := credentials.NewCredential(credConf) // 根据配置创建凭证提供器
	if err != nil {
		return nil, err
	}

	// 从凭证提供器获取凭证
	cred, err := provider.GetCredential()
	if err != nil {
		return nil, err
	}

	var (
		utcTime    = time.Now().UTC()
		utcDate    = utcTime.Format("********")
		expireTime = utcTime.Add(1 * time.Hour)
		hmacHash   = func() hash.Hash {
			return sha256.New()
		}
		product       = "oss"
		credentialStr = fmt.Sprintf("%v/%v/%v/%v/aliyun_v4_request", *cred.AccessKeyId, utcDate, config.AliyunCfg.RegionID, product)
		signVersion   = "OSS4-HMAC-SHA256"
	)

	// 示例policy表单域只列举了部分必填字段，如有其他需求可参考文档：https://help.aliyun.com/zh/oss/developer-reference/signature-version-4-recommend
	policyMap := map[string]any{
		"expiration": expireTime.Format(dbs.TimeDateTimeIso8601),
		"conditions": []any{
			map[string]string{"bucket": bucket},
			map[string]string{"x-oss-signature-version": signVersion},
			map[string]string{"x-oss-credential": credentialStr},
			map[string]string{"x-oss-security-token": *cred.SecurityToken},
			map[string]string{"x-oss-date": utcTime.Format("********T150405Z")},
			[]interface{}{"content-length-range", 1, 1048576000}, // 限制文件大小(0-1000M)
		},
	}

	policy, err := json.Marshal(policyMap)
	if err != nil {
		return nil, err
	}
	stringToSign := base64.StdEncoding.EncodeToString([]byte(policy))

	// 构建signing key
	signingKey := "aliyun_v4" + *cred.AccessKeySecret
	h1 := hmac.New(hmacHash, []byte(signingKey))
	io.WriteString(h1, utcDate)
	h1Key := h1.Sum(nil)

	h2 := hmac.New(hmacHash, h1Key)
	io.WriteString(h2, config.AliyunCfg.RegionID)
	h2Key := h2.Sum(nil)

	h3 := hmac.New(hmacHash, h2Key)
	io.WriteString(h3, "oss")
	h3Key := h3.Sum(nil)

	h4 := hmac.New(hmacHash, h3Key)
	io.WriteString(h4, "aliyun_v4_request")
	h4Key := h4.Sum(nil)

	// 生成签名
	h := hmac.New(hmacHash, h4Key)
	io.WriteString(h, stringToSign)
	signature := hex.EncodeToString(h.Sum(nil))

	return &aliyunDto.AliyunOssAppletTokenResp{
		Bucket:           bucket,
		RegionID:         config.AliyunCfg.RegionID,
		OssRegionID:      config.AliyunCfg.OssRegionID,
		Signature:        signature,
		SignatureVersion: signVersion,
		SecurityToken:    *cred.SecurityToken,
		Policy:           stringToSign,
		Credential:       credentialStr,
		Date:             utcTime.Format("********T150405Z"),
	}, nil
}

// // GenVideoSignedUrl .
// func (e *Entry) GenVideoSignedUrl(ctx *gin.Context, bt common.BucketType, objKey string) (*aliyunDto.GenVideoSignedUrlResp, error) {
// 	var (
// 		bucket = common.GetBucket(bt)
// 		urlTtl = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyVideoSignUrlTtl)
// 		cfg    = oss.LoadDefaultConfig().
// 			WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret, "")).
// 			WithRegion(config.AliyunCfg.RegionID)
// 		client = oss.NewClient(cfg)
// 	)

// 	result, err := client.Presign(ctx.Request.Context(),
// 		&oss.GetObjectRequest{
// 			Bucket:  oss.Ptr(bucket),
// 			Key:     oss.Ptr(objKey),
// 			Process: oss.Ptr("hls/sign"),
// 		},
// 		oss.PresignExpires(time.Duration(urlTtl)*time.Minute),
// 	)
// 	if err != nil {
// 		log.Ctx(ctx).WithError(err).Warn("GenVideoSignedUrl err")
// 		return nil, err
// 	}

// 	return &aliyunDto.GenVideoSignedUrlResp{
// 		SignedUrl:  result.URL,
// 		Expiration: result.Expiration.Format(dbs.TimeDateFormatFull),
// 	}, nil
// }

// // GenVideoPresignUrl .
// func (e *Entry) GenVideoPresignUrl(ctx *gin.Context, bt common.BucketType, url string) (*aliyunDto.GenVideoSignedUrlResp, error) {
// 	var (
// 		bucket = common.GetBucket(bt)
// 		urlTtl = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyVideoSignUrlTtl)
// 		cfg    = oss.LoadDefaultConfig().
// 			WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret, "")).
// 			WithRegion(config.AliyunCfg.RegionID)
// 		client = oss.NewClient(cfg)
// 	)

// 	result, err := client.Presign(ctx.Request.Context(),
// 		&oss.GetObjectRequest{
// 			Bucket: oss.Ptr(bucket),
// 			Key:    oss.Ptr(common.GenVideoKeyByUrl(url)),
// 			// Process: oss.Ptr("hls/sign"),
// 		},
// 		oss.PresignExpires(time.Duration(urlTtl)*time.Minute),
// 	)
// 	if err != nil {
// 		log.Ctx(ctx).WithError(err).Warn("GenVideoSignedUrl err")
// 		return nil, err
// 	}

// 	return &aliyunDto.GenVideoSignedUrlResp{
// 		SignedUrl:  result.URL,
// 		Expiration: result.Expiration.Format(dbs.TimeDateFormatFull),
// 	}, nil
// }
