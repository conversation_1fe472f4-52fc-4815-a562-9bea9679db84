package aliyun

import (
	"blind_box/app/api/aliyun/common"
	"blind_box/app/api/aliyun/oss"
	aliyunDto "blind_box/app/dto/api/aliyun"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AliyunOssToken .
func AliyunOssToken(ctx *gin.Context) {
	req := aliyunDto.AliyunOssTokenReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	ret, err := oss.GetApi().GetOssToken(ctx, ctxAccount.AccountID, common.BucketType(req.BucketType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AliyunOssAppletToken .
func AliyunOssAppletToken(ctx *gin.Context) {
	req := aliyunDto.AliyunOssTokenReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	ret, err := oss.GetApi().GetOssAppletToken(ctx, ctxAccount.AccountID, common.BucketType(req.BucketType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
