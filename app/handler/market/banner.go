package market

import (
	marketDto "blind_box/app/dto/market"
	"blind_box/app/service/market"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"github.com/gin-gonic/gin"
)

func BannerList(c *gin.Context) {
	req := &marketDto.BannerListReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := market.GetService().BannerList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)

}
