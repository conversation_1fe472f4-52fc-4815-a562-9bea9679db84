package admin

import (
	adminAccount "blind_box/app/dao/admin/account"
	adminDto "blind_box/app/dto/admin"
	"blind_box/app/service/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminAccountLogin .
func AdminAccountLogin(ctx *gin.Context) {
	req := &adminDto.AdminAccountLoginReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := admin.GetService().AdminAccountLogin(ctx, req.Account, req.Pwd)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminAccountLogout .
func AdminAccountLogout(ctx *gin.Context) {
	ctxUser, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	adminAccount.GetRepo().RedisClearAccountToken(ctx, ctxUser.AccountID)
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminAccountInfo .
func AdminAccountInfo(ctx *gin.Context) {
	ctxUser, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	ret, err := admin.GetService().AdminGetAccountInfo(ctx, ctxUser.AccountID, ctxUser.RoleID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminAccountEditPwd .
func AdminAccountEditPwd(ctx *gin.Context) {
	req := &adminDto.AdminAccountEditPwdReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	if err := admin.GetService().AdminSetAccountPwd(ctx, ctxUser.AccountID, req.Pwd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
