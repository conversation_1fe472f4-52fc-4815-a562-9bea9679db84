package admin

import (
	"blind_box/app/service/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminAreaList 列表
func AdminAreaList(c *gin.Context) {
	ret, err := admin.GetService().AdminAreaList(c)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
