package admin

import (
	adminDto "blind_box/app/dto/admin"
	"blind_box/app/service/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminAccountList .
func AdminAccountList(ctx *gin.Context) {
	req := &adminDto.AdminAccountListReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	req.NotRids = admin.GetService().GetCommonRoleFilter(ctx)
	req.NotAids = admin.GetService().GetCommonAccountFilter(ctx)

	ret, err := admin.GetService().AdminAccountList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetAccount .
func AdminSetAccount(ctx *gin.Context) {
	req := &adminDto.AdminSetAccountReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := admin.GetService().AdminSetAccount(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminDelAccount .
func AdminDelAccount(ctx *gin.Context) {
	req := &adminDto.AdminDelAccountReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := admin.GetService().AdminDelAccount(ctx, req.ID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminSetAccountPwd .
func AdminSetAccountPwd(ctx *gin.Context) {
	req := &adminDto.AdminSetAccountPwdReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := admin.GetService().AdminSetAccountPwd(ctx, req.ID, req.Pwd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminAccountAll .
func AdminAccountAll(ctx *gin.Context) {
	req := &adminDto.AdminAccountAllReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := admin.GetService().AdminAccountAll(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminJudge .
func AdminJudge(ctx *gin.Context) {
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), admin.GetService().JudgeIsAdmin(ctxAccount.RoleID))
}
