package admin

import (
	adminDto "blind_box/app/dto/admin"
	"blind_box/app/service/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminRoleList .
func AdminRoleList(ctx *gin.Context) {
	req := &adminDto.AdminRoleListReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	req.NotRids = admin.GetService().GetCommonRoleFilter(ctx)
	ret, err := admin.GetService().AdminRoleList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetRole .
func AdminSetRole(ctx *gin.Context) {
	req := &adminDto.AdminSetRoleReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := admin.GetService().AdminSetRole(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetRoleAuth .
func AdminSetRoleAuth(ctx *gin.Context) {
	req := &adminDto.AdminSetRoleAuthReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := admin.GetService().AdminSetRoleAuth(ctx, req.ID, req.MenuIDS); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminDelRole .
func AdminDelRole(ctx *gin.Context) {
	req := &adminDto.AdminDelRoleReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := admin.GetService().AdminDelRole(ctx, req.ID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())

}

// AdminRoleAll .
func AdminRoleAll(ctx *gin.Context) {
	req := &adminDto.AdminRoleAllReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := admin.GetService().AdminRoleAll(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
