package order

import (
	orderDto "blind_box/app/dto/order"
	orderSrv "blind_box/app/service/order"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// RefundCreate 用户申请退款
func RefundCreate(ctx *gin.Context) {
	req := &orderDto.RefundCreateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().RefundCreate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// RefundDetail 退款详情
func RefundDetail(ctx *gin.Context) {
	req := &orderDto.RefundDetailReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().RefundDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// RefundList 退款列表
func RefundList(ctx *gin.Context) {
	req := &orderDto.RefundListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().RefundList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// RefundCancel 取消退款申请
func RefundCancel(ctx *gin.Context) {
	req := &orderDto.RefundCancelReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().RefundCancel(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminRefundCreate 管理后台创建退款
func AdminRefundCreate(ctx *gin.Context) {
	req := &orderDto.AdminRefundCreateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前管理员信息
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.AccountID = ctxAccount.AccountID

	res, err := orderSrv.GetService().AdminRefundCreate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminRefundList 管理后台退款列表
func AdminRefundList(ctx *gin.Context) {
	req := &orderDto.AdminRefundListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	res, err := orderSrv.GetService().AdminRefundList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminRefundDetail 管理后台退款详情
func AdminRefundDetail(ctx *gin.Context) {
	req := &orderDto.AdminRefundDetailReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	res, err := orderSrv.GetService().AdminRefundDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminRefundRetry 管理后台重试退款
func AdminRefundRetry(ctx *gin.Context) {
	req := &orderDto.AdminRefundRetryReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前管理员信息
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.AccountID = ctxAccount.AccountID

	res, err := orderSrv.GetService().AdminRefundRetry(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}
