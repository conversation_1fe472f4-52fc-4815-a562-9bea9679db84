package order

import (
	orderDto "blind_box/app/dto/order"
	orderSrv "blind_box/app/service/order"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

func OrderStatus(ctx *gin.Context) {
	req := &orderDto.OrderStatusReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	res, err := orderSrv.GetService().OrderStatus(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

func OrderDetail(ctx *gin.Context) {
	req := &orderDto.OrderDetailReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	t, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = t.UID

	res, err := orderSrv.GetService().OrderDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

func OrderList(ctx *gin.Context) {
	req := &orderDto.OrderListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	t, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = t.UID

	res, err := orderSrv.GetService().OrderList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// OrderCancel 订单取消（小程序端）
func OrderCancel(ctx *gin.Context) {
	req := &orderDto.OrderCancelReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().OrderCancel(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderCancel 管理后台订单取消
func AdminOrderCancel(ctx *gin.Context) {
	req := &orderDto.AdminOrderCancelReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.AccountID = ctxAccount.AccountID

	res, err := orderSrv.GetService().AdminOrderCancel(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// GeneratePickupCode 生成取货码（小程序端）
// @Summary 生成取货码
// @Description 为已支付的到店取货订单生成取货码，支持强制刷新功能。限流策略：1分钟内最多3次，10分钟内最多10次
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param orderId formData int false "订单ID"
// @Param orderNo formData string false "订单号"
// @Param forceRefresh formData bool false "是否强制刷新取货码，默认false"
// @Success 200 {object} orderDto.PickupCodeGenerateRes "生成成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Failure 429 {object} helper.Response "请求过于频繁"
// @Failure 500 {object} helper.Response "服务器错误"
// @Security Bearer
// @Router /order/pickup-code/generate [post]
func GeneratePickupCode(ctx *gin.Context) {
	req := &orderDto.PickupCodeGenerateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().GeneratePickupCode(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// RefreshPickupCode 刷新取货码（小程序端）
// @Summary 刷新取货码
// @Description 强制重新生成取货码，用于二维码刷新功能。限流策略：1分钟内最多3次，10分钟内最多10次
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param orderId formData int false "订单ID"
// @Param orderNo formData string false "订单号"
// @Success 200 {object} orderDto.PickupCodeGenerateRes "刷新成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Failure 429 {object} helper.Response "请求过于频繁"
// @Failure 500 {object} helper.Response "服务器错误"
// @Security Bearer
// @Router /order/pickup-code/refresh [post]
func RefreshPickupCode(ctx *gin.Context) {
	req := &orderDto.PickupCodeGenerateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID
	req.ForceRefresh = true // 强制刷新

	res, err := orderSrv.GetService().GeneratePickupCode(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// QueryPickupCode 查询取货码（小程序端）
func QueryPickupCode(ctx *gin.Context) {
	req := &orderDto.PickupCodeQueryReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取当前用户信息
	user, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = user.UID

	res, err := orderSrv.GetService().QueryPickupCode(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// VerifyPickupCode 验证取货码（门店端使用）
func VerifyPickupCode(ctx *gin.Context) {
	req := &orderDto.PickupCodeVerifyReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	res, err := orderSrv.GetService().VerifyPickupCode(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// CompleteOrder 完成订单（门店端使用）
func CompleteOrder(ctx *gin.Context) {
	req := &orderDto.OrderCompleteReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	res, err := orderSrv.GetService().CompleteOrder(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}
