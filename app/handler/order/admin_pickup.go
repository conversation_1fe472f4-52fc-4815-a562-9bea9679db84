package order

import (
	orderDto "blind_box/app/dto/order"
	orderSrv "blind_box/app/service/order"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminPickupCodeVerify 管理后台验证取货码
// @Summary 管理后台验证取货码
// @Description 用于管理后台验证取货码，专门用于到店取货场景，支持订单号双重验证，提供完整的订单和配送信息
// @Tags 管理后台-订单发货
// @Accept json
// @Produce json
// @Param pickupCode formData string true "取货码"
// @Param orderId formData int false "订单ID，可选，用于双重验证"
// @Param orderNo formData string false "订单号，可选，用于双重验证"
// @Success 200 {object} orderDto.AdminPickupCodeVerifyRes "验证成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Failure 500 {object} helper.Response "服务器错误"
// @Security Bearer
// @Router /admin/order/pickup-code/verify [post]
func AdminPickupCodeVerify(ctx *gin.Context) {
	var req orderDto.AdminPickupCodeVerifyReq
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取管理员信息
	//adminID, exists := ctx.Get("admin_id")
	//if !exists {
	//	helper.AppResp(ctx, ecode.NoAuthErr.Code(), "管理员信息不存在")
	//	return
	//}
	//adminName, _ := ctx.Get("admin_name")
	//
	//req.AccountID = adminID.(uint64)
	//if adminName != nil {
	//	req.AdminName = adminName.(string)
	//}

	srv := orderSrv.GetService()
	res, err := srv.AdminPickupCodeVerify(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderComplete 管理后台完成订单+发货状态更新
// @Summary 管理后台完成订单
// @Description 用于管理后台完成订单，可选择同时更新发货状态为已送达
// @Tags 管理后台-订单发货
// @Accept json
// @Produce json
// @Param body body orderDto.AdminOrderCompleteReq true "完成订单请求"
// @Success 200 {object} orderDto.AdminOrderCompleteRes "完成成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Failure 500 {object} helper.Response "服务器错误"
// @Security Bearer
// @Router /admin/order/complete [post]
func AdminOrderComplete(ctx *gin.Context) {
	var req orderDto.AdminOrderCompleteReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 验证参数
	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取管理员信息
	//adminID, exists := ctx.Get("admin_id")
	//if !exists {
	//	helper.AppResp(ctx, ecode.NoAuthErr.Code(), "管理员信息不存在")
	//	return
	//}
	//adminName, _ := ctx.Get("admin_name")
	//
	//req.AccountID = adminID.(uint64)
	//if adminName != nil {
	//	req.AdminName = adminName.(string)
	//}

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderComplete(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminTrackingNumberVerify 管理后台验证快递单号
// @Summary 管理后台验证快递单号
// @Description 根据快递单号查询对应的订单和发货信息，用于快递配送场景
// @Tags 管理后台-订单发货
// @Accept application/x-www-form-urlencoded
// @Produce json
// @Param trackingNumber formData string true "快递单号"
// @Param orderId formData int false "订单ID，可选"
// @Param orderNo formData string false "订单号，可选"
// @Success 200 {object} orderDto.AdminTrackingNumberVerifyRes "验证成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Failure 500 {object} helper.Response "服务器错误"
// @Security Bearer
// @Router /admin/order/tracking-number/verify [post]
func AdminTrackingNumberVerify(ctx *gin.Context) {
	var req orderDto.AdminTrackingNumberVerifyReq

	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 获取管理员信息（与现有代码保持一致的注释方式）
	//adminID, exists := ctx.Get("admin_id")
	//if !exists {
	//	helper.AppResp(ctx, ecode.NoAuthErr.Code(), "管理员信息不存在")
	//	return
	//}
	//adminName, _ := ctx.Get("admin_name")
	//
	//req.AccountID = adminID.(uint64)
	//if adminName != nil {
	//	req.AdminName = adminName.(string)
	//}

	// 参数验证
	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	srv := orderSrv.GetService()
	res, err := srv.AdminTrackingNumberVerify(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}
