package order

import (
	"strconv"

	"blind_box/app/dto/order"
	orderSrv "blind_box/app/service/order"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminOrderList 管理后台订单列表
func AdminOrderList(ctx *gin.Context) {
	var req order.AdminOrderListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderList(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderDetail 管理后台订单详情
func AdminOrderDetail(ctx *gin.Context) {
	var req order.AdminOrderDetailReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderDetail(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderDeliveryCreate 管理后台创建发货记录
func AdminOrderDeliveryCreate(ctx *gin.Context) {
	var req order.AdminOrderDeliveryCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 从上下文获取管理员信息
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	req.AccountID = ctxAccount.AccountID
	// req.AdminName 可以从数据库查询获取

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderDeliveryCreate(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderDeliveryUpdate 管理后台更新发货记录
func AdminOrderDeliveryUpdate(ctx *gin.Context) {
	var req order.AdminOrderDeliveryUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 从上下文获取管理员信息
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	req.AccountID = ctxAccount.AccountID
	// req.AdminName 可以从数据库查询获取

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderDeliveryUpdate(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderDeliveryDetail 管理后台获取发货详情
func AdminOrderDeliveryDetail(ctx *gin.Context) {
	var req order.AdminOrderDeliveryDetailReq

	// 从路径参数获取ID
	if idStr := ctx.Param("id"); idStr != "" {
		if id, err := strconv.ParseUint(idStr, 10, 64); err == nil {
			req.ID = id
		}
	}

	// 从查询参数获取订单ID
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	// 验证参数
	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderDeliveryDetail(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// AdminOrderDeliveryList 管理后台发货记录列表
func AdminOrderDeliveryList(ctx *gin.Context) {
	var req order.AdminOrderDeliveryListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	srv := orderSrv.GetService()
	res, err := srv.AdminOrderDeliveryList(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}
