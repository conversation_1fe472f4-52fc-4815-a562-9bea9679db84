package activity

import (
	actDto "blind_box/app/dto/activity"
	commonDto "blind_box/app/dto/common"
	actSrv "blind_box/app/service/activity"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// ActivitySubscribeList 用户订阅活动列表
func ActivitySubscribeList(ctx *gin.Context) {
	req := commonDto.DataListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := actSrv.GetService().ActivitySubscribeList(ctx, ctxUser.UID, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SubscribeActivity 用户订阅活动
func SubscribeActivity(ctx *gin.Context) {
	req := actDto.SubscribeActivityReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := actSrv.GetService().SubscribeActivity(ctx, ctxUser.UID, req.ActID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// DelSubscribeActivity 用户删除订阅活动
func DelSubscribeActivity(ctx *gin.Context) {
	req := actDto.SubscribeActivityReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := actSrv.GetService().DelSubscribeActivity(ctx, ctxUser.UID, req.ActID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
