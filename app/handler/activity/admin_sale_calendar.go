package activity

import (
	scDao "blind_box/app/dao/activity/sale_calendar"
	actDto "blind_box/app/dto/activity"
	actSrv "blind_box/app/service/activity"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminSaleCalendarList 发售日历列表
func AdminSaleCalendarList(ctx *gin.Context) {
	req := actDto.AdminSaleCalendarListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := actSrv.GetService().AdminSaleCalendarList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetSaleCalendar 设置发售日历
func AdminSetSaleCalendar(ctx *gin.Context) {
	req := actDto.AdminSetSaleCalendarReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := actSrv.GetService().AdminSetSaleCalendar(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminDelSaleCalendar 删除发售日历
func AdminDelSaleCalendar(ctx *gin.Context) {
	req := actDto.AdminDelSaleCalendarReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := scDao.GetRepo().DelByID(ctx, req.ID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
