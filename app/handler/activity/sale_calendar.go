package activity

import (
	scDao "blind_box/app/dao/activity/sale_calendar"
	commonDto "blind_box/app/dto/common"
	actSrv "blind_box/app/service/activity"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// SaleCalendarList .
func SaleCalendarList(ctx *gin.Context) {
	req := commonDto.DataListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := actSrv.GetService().SaleCalendarList(ctx, ctxUser.UID, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// CountSaleCalendar .
func CountSaleCalendar(ctx *gin.Context) {
	ret, err := scDao.GetRepo().CountByFilter(ctx, &scDao.Filter{})
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
