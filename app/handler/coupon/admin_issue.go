package coupon

import (
	"blind_box/app/common/dbs"
	issueDao "blind_box/app/dao/coupon/coupon_issue"
	couponDto "blind_box/app/dto/coupon"
	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminCouponIssueList 优惠券发放列表
func AdminCouponIssueList(ctx *gin.Context) {
	req := couponDto.AdminCouponIssueListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := couponSrv.GetService().AdminCouponIssueList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminCountIssueContainCoupon 统计优惠券在发放中的数量
func AdminCountIssueContainCoupon(ctx *gin.Context) {
	req := couponDto.AdminCountIssueContainCouponreq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := issueDao.GetRepo().CountByFilter(ctx, &issueDao.Filter{CouponID: req.CouponID, NotID: req.ID, IsValid: true})
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminCouponIssueDetail 优惠券发放详情
func AdminCouponIssueDetail(ctx *gin.Context) {
	req := couponDto.AdminCouponIssueDetailReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := couponSrv.GetService().AdminCouponIssueDetail(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetCouponIssue 设置优惠券发放
func AdminSetCouponIssue(ctx *gin.Context) {
	req := couponDto.AdminSetCouponIssueReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := couponSrv.GetService().AdminSetCouponIssue(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminOperateCouponIssue 操作优惠券发放
func AdminOperateCouponIssue(ctx *gin.Context) {
	req := couponDto.AdminOperateCouponIssueReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := couponSrv.GetService().AdminOperateCouponIssue(ctx, req.ID, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminCouponIssueTargetSet 设置优惠券发放目标
func AdminCouponIssueTargetSet(ctx *gin.Context) {
	req := couponDto.AdminCouponIssueTargetSetReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := couponSrv.GetService().AdminCouponIssueTargetSet(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminCouponIssueTargetUpload 上传优惠券发放附件
func AdminCouponIssueTargetUpload(ctx *gin.Context) {
	file, _, err := ctx.Request.FormFile("target")
	if err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	defer file.Close()

	ret, err := couponSrv.GetService().AdminCouponIssueTargetUpload(ctx, file)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminCouponIssueTargetExport 导出优惠券发放附件
func AdminCouponIssueTargetExport(ctx *gin.Context) {
	req := couponDto.AdminCouponIssueTargetExportReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	buf, err := couponSrv.GetService().AdminCouponIssueTargetExport(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.ExportExcelResp(ctx, buf, "发放名单")
}
