package coupon

import (
	"blind_box/app/common/dbs"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	couponDto "blind_box/app/dto/coupon"

	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GetHomepageCoupon 领取首页弹出优惠券
func GetHomepageCoupon(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	if err := couponSrv.GetService().UserGetHomepageCoupon(ctx, ctxUser.UID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserCouponList 用户优惠券列表
func UserCouponList(ctx *gin.Context) {
	req := couponDto.UserCouponListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := couponSrv.GetService().UserCouponList(ctx, ctxUser.UID, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// ActUserAvailableCoupon 查询活动-用户可使用的优惠券列表
func ActUserAvailableCoupon(ctx *gin.Context) {
	req := couponDto.ActUserAvailableCouponReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := couponSrv.GetService().ActUserAvailableCoupon(ctx, ctxUser.UID, req.ActID, req.DrawNum)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UseCoupon 使用优惠券
func UseCoupon(ctx *gin.Context) {
	req := couponDto.UseCouponReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := couponSrv.GetService().CheckWhenUseCoupon(ctx, ctxUser.UID, req.UCID, req.ActID, req.DrawNum)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err = couponSrv.GetService().UpdateUserCouponStatus(ctx, tx, ret.CUID, cUserDao.DFCUserStatus(req.Status), req.TradeNo, ret.DiscountAmount); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
