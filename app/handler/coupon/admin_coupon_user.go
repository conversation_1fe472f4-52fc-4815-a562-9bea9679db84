package coupon

import (
	"blind_box/app/common/dbs"
	couponDto "blind_box/app/dto/coupon"
	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminCouponUserList 优惠券领取列表
func AdminCouponUserList(ctx *gin.Context) {
	req := couponDto.AdminCouponUserListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := couponSrv.GetService().AdminCouponUserList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminOperateCouponUser 操作优惠券领取
func AdminOperateCouponUser(ctx *gin.Context) {
	req := couponDto.AdminOperateCouponUserReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := couponSrv.GetService().AdminOperateCouponUser(ctx, req.ID, dbs.OperateAction(req.Action), req.Remark); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
