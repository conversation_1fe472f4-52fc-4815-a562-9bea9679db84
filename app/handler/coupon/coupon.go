package coupon

import (
	couponDto "blind_box/app/dto/coupon"
	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// HomepageCouponList 首页弹出优惠券奖励
func HomepageCouponList(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	ret, err := couponSrv.GetService().HomepageCouponList(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// CouponRangeActList  优惠券使用范围-活动列表
func CouponRangeActList(ctx *gin.Context) {
	req := couponDto.CouponRangeActListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := couponSrv.GetService().CouponRangeActList(ctx, req.CouponID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
