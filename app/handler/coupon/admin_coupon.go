package coupon

import (
	"blind_box/app/common/dbs"
	couponDao "blind_box/app/dao/coupon"
	couponDto "blind_box/app/dto/coupon"
	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminCouponList 优惠券列表
func AdminCouponList(ctx *gin.Context) {
	req := couponDto.AdminCouponListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := couponSrv.GetService().AdminCouponList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminCouponDetail 优惠券详情
func AdminCouponDetail(ctx *gin.Context) {
	req := couponDto.AdminCouponDetailReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if req.ID == 0 && req.Code == "" {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := couponSrv.GetService().AdminCouponDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetCoupon 设置优惠券
func AdminSetCoupon(ctx *gin.Context) {
	req := couponDto.AdminSetCouponReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	var err error
	if req.ID > 0 {
		err = couponSrv.GetService().AdminUpdateCoupon(ctx, req)
	} else {
		err = couponSrv.GetService().AdminCreateCoupon(ctx, req)
	}
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminOperateCoupon 操作优惠券
func AdminOperateCoupon(ctx *gin.Context) {
	req := couponDto.AdminOperateCouponReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := couponSrv.GetService().AdminOperateCoupon(ctx, req.ID, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminCouponSourceList 优惠券来源列表
func AdminCouponSourceList(ctx *gin.Context) {
	ret, err := couponDao.GetRepo().RedisSourceList(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	resp := []couponDto.AdminCouponSourceItem{}
	for _, val := range ret {
		resp = append(resp, couponDto.AdminCouponSourceItem{
			ID:   val.ID,
			Name: val.Name,
		})
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// AdminSetCouponSource 设置优惠券来源名称
func AdminSetCouponSource(ctx *gin.Context) {
	req := couponDto.AdminSetCouponSourceReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := couponSrv.GetService().AdminSetCouponSource(ctx, req.ID, req.Name); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
