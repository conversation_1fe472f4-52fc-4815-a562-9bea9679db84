# app/handler 目录规范文档

本文档记录 `app/handler` 目录下 HTTP 请求处理器（控制器层）的代码规范和最佳实践。

## 目录结构

```
app/handler/
├── activity/          # 活动管理
│   ├── admin_*.go    # 管理后台接口
│   └── *.go          # 小程序端接口
├── admin/            # 管理后台基础功能
│   ├── account.go    # 账号管理
│   ├── login.go      # 登录认证
│   ├── menu.go       # 菜单管理
│   └── role.go       # 角色权限
├── box/              # 盲盒功能
│   ├── admin_*.go    # 管理后台接口
│   └── *.go          # 小程序端接口
├── card/             # 卡片管理
├── config/           # 配置管理
├── coupon/           # 优惠券管理
├── goods/            # 商品管理
├── market/           # 营销功能
├── openapi/          # 开放 API
├── order/            # 订单管理
├── resource/         # 资源管理（品牌、店铺、供应商）
└── user/             # 用户管理
```

## 核心设计规范

### Handler 函数签名

所有 Handler 函数必须遵循统一签名：

```go
func HandlerName(ctx *gin.Context) {
    // 处理逻辑
}
```

### 命名规范

#### 文件命名
- **小程序端接口**: `功能名.go` (如 `user.go`, `order.go`)
- **管理后台接口**: `admin_功能名.go` (如 `admin_user.go`, `admin_order.go`)

#### 函数命名
- **大驼峰命名**: `UserBasic`, `OrderList`
- **管理后台前缀**: `AdminUserList`, `AdminOrderCancel`
- **动作明确**: `Create`, `Update`, `Delete`, `List`, `Detail`, `Get`

### 请求处理流程

标准的 Handler 处理流程包含四个步骤：

```go
func HandlerName(ctx *gin.Context) {
    // 1. 参数绑定和验证
    req := &dto.RequestType{}
    if err := ctx.ShouldBind(req); err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
        return
    }
    
    // 2. 获取上下文信息（如需要）
    ctxUser, err := helper.GetCtxUser(ctx)
    if err != nil {
        helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
        return
    }
    
    // 3. 调用 Service 层
    ret, err := service.GetService().Method(ctx, req)
    if err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    
    // 4. 返回响应
    helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
```

## 参数绑定规范

### 绑定方法选择

根据请求类型选择合适的绑定方法：

```go
// GET 请求 - Query 参数
ctx.ShouldBindQuery(&req)

// POST/PUT - JSON Body
ctx.ShouldBind(&req)      // 自动根据 Content-Type 绑定
ctx.ShouldBindJSON(&req)  // 明确指定 JSON

// Form 表单
ctx.ShouldBindForm(&req)

// URI 路径参数
ctx.ShouldBindUri(&req)
```

### 参数验证

#### 使用 binding 标签
```go
type UserEditReq struct {
    Name  string `json:"name" binding:"required" msg:"名称不能为空"`
    Age   int    `json:"age" binding:"min=1,max=100" msg:"年龄必须在1-100之间"`
    Email string `json:"email" binding:"email" msg:"邮箱格式不正确"`
}
```

#### 自定义验证
```go
// DTO 中定义验证方法
func (req *OrderDetailReq) Validate() error {
    if req.OrderID == 0 && req.OrderNo == "" {
        return errors.New("订单ID或订单号必须提供一个")
    }
    return nil
}

// Handler 中调用
if err := req.Validate(); err != nil {
    helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
    return
}
```

### 错误消息处理

```go
// 使用自定义翻译（带 msg 标签）
helper.CustomTranslate(err, &req)

// 使用默认翻译
helper.Translate(err)

// 直接使用错误消息
err.Error()
```

## 上下文信息获取

### 用户认证信息

```go
// 必须登录 - 失败则 panic
ctxUser, _ := helper.GetCtxUser(ctx)

// 尝试获取 - 返回错误
ctxUser, err := helper.GetCtxUser(ctx)
if err != nil {
    helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
    return
}

// 可选登录 - 不报错
ctxUser, err := helper.TryGetCtxUser(ctx)
```

### 管理员认证信息

```go
// 获取管理员信息
ctxAccount, err := helper.GetCtxAccount(ctx)
if err != nil {
    helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
    return
}

// 使用管理员信息
req.AccountID = ctxAccount.AccountID
req.RoleID = ctxAccount.RoleID
```

## 响应格式规范

### 成功响应

```go
// 仅返回状态
helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())

// 返回数据
helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), data)
```

### 错误响应

```go
// 参数错误
helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))

// 业务错误
helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())

// Token 错误
helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
```

### 特殊响应（OpenAPI）

```go
// OpenAPI 回调响应
ctx.JSON(200, &openapi.CallbackResponse{
    Code: openapi.ErrorCodeSuccess,
    Msg:  openapi.GetErrorMessage(openapi.ErrorCodeSuccess),
})
```

## Service 调用规范

### 获取 Service 实例

```go
// 使用单例模式
userSrv := user.GetService()
orderSrv := orderSrv.GetService()
```

### 调用 Service 方法

```go
// 传递 gin.Context
ret, err := service.GetService().Method(ctx, param1, param2)

// 传递完整请求对象
ret, err := service.GetService().Method(ctx, req)

// 传递用户信息
ret, err := service.GetService().Method(ctx, ctxUser.UID, req)
```

## 中间件配合

### 请求中断检查

```go
func CreateScanOrder(ctx *gin.Context) {
    // 检查请求是否被中间件中断
    if ctx.IsAborted() {
        return
    }
    
    // 继续处理
}
```

### 认证中间件配合

```go
// 路由配置（router 层）
router.Group("/api/user").Use(
    middleware.CheckUserLogin(),  // 必须登录
).GET("/info", handler.UserBasic)

router.Group("/api/box").Use(
    middleware.CheckUser(),       // 可选登录
).GET("/list", handler.BoxList)

router.Group("/admin").Use(
    middleware.CheckAccountLogin(),  // 管理员登录
    middleware.CheckAccountAuth(),   // 权限验证
).GET("/users", handler.AdminUserList)
```

## 业务场景实现

### 分页查询

```go
func AdminUserList(ctx *gin.Context) {
    req := &dto.AdminUserListReq{}
    if err := ctx.ShouldBindQuery(&req); err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
        return
    }
    
    // 默认分页参数
    if req.Page == 0 {
        req.Page = 1
    }
    if req.PageSize == 0 {
        req.PageSize = 10
    }
    
    ret, err := service.GetService().AdminUserList(ctx, req)
    if err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    
    helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
```

### 批量操作

```go
func AdminSpuOperate(ctx *gin.Context) {
    req := &dto.AdminSpuOperateReq{}
    if err := ctx.ShouldBind(&req); err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
        return
    }
    
    // 批量修改状态
    if err := service.GetService().AdminSpuOperate(ctx, req.SpuIds, dbs.OperateAction(req.Action)); err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    
    helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
```

### 文件上传

```go
func Upload(ctx *gin.Context) {
    // 获取上传文件
    file, err := ctx.FormFile("file")
    if err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), "文件上传失败")
        return
    }
    
    // 验证文件
    if file.Size > 10*1024*1024 { // 10MB
        helper.AppResp(ctx, ecode.ParamErr.Code(), "文件大小超过限制")
        return
    }
    
    // 处理文件
    url, err := service.GetService().UploadFile(ctx, file)
    if err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    
    helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), map[string]string{
        "url": url,
    })
}
```

### 编号生成

```go
func GoodsCodeGen(ctx *gin.Context) {
    req := &dto.GoodsCodeGenReq{}
    if err := ctx.ShouldBind(req); err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
        return
    }
    
    ret := &dto.GoodsCodeGenResp{}
    ret.Code = helper.GetAppNo(req.CodeType)
    
    helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
```

## 错误处理规范

### 参数错误
```go
// 绑定失败
if err := ctx.ShouldBind(&req); err != nil {
    helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
    return
}

// 验证失败
if err := req.Validate(); err != nil {
    helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
    return
}
```

### 认证错误
```go
// Token 错误
if err != nil {
    helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
    return
}
```

### 业务错误
```go
// Service 层错误
if err != nil {
    helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
    return
}
```

## 日志记录

Handler 层通常不需要记录日志，日志记录应在 Service 层完成。特殊情况下：

```go
// 记录关键操作
log.Ctx(ctx).WithFields(logrus.Fields{
    "user_id": ctxUser.UID,
    "action": "delete_order",
    "order_id": req.OrderID,
}).Info("管理员删除订单")
```

## 安全规范

### SQL 注入防护
- 所有参数通过 DTO 结构体接收
- 使用参数化查询，不拼接 SQL

### XSS 防护
- 输出前进行 HTML 转义
- 使用框架提供的安全方法

### CSRF 防护
- 使用 Token 验证
- 重要操作二次确认

### 权限验证
```go
// 验证用户归属
if order.UserID != ctxUser.UID {
    helper.AppResp(ctx, ecode.PermissionErr.Code(), "无权访问该订单")
    return
}
```

## 性能优化

### 避免 N+1 查询
在 Handler 层不做数据库查询，交给 Service 层统一处理

### 响应数据精简
```go
// 只返回必要字段
type UserInfoResp struct {
    ID     uint64 `json:"id"`
    Name   string `json:"name"`
    Avatar string `json:"avatar"`
    // 不返回敏感信息如密码、Token 等
}
```

### 异步处理
```go
// 耗时操作异步处理
go func() {
    // 发送通知等非关键操作
}()
```

## 文档注释规范

### Swagger 文档
```go
// GeneratePickupCode 生成取货码（小程序端）
// @Summary 生成取货码
// @Description 为已支付的到店取货订单生成取货码
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param orderId formData int false "订单ID"
// @Success 200 {object} dto.PickupCodeGenerateRes "生成成功"
// @Failure 400 {object} helper.Response "参数错误"
// @Router /order/pickup-code/generate [post]
func GeneratePickupCode(ctx *gin.Context) {
    // ...
}
```

## 最佳实践

### 1. 职责单一
- Handler 只负责参数处理和响应格式化
- 业务逻辑放在 Service 层
- 不直接操作数据库

### 2. 统一规范
- 使用统一的响应格式
- 遵循统一的错误处理
- 保持代码风格一致

### 3. 参数验证
- 使用 binding 标签自动验证
- 复杂验证逻辑封装到 DTO
- 提供友好的错误提示

### 4. 代码复用
- 通用逻辑抽取到 helper
- 相似功能提取公共方法
- 避免重复代码

### 5. 安全第一
- 所有输入都要验证
- 敏感操作需要权限检查
- 不暴露内部错误信息

## 常见问题

### Q: 何时使用 ShouldBind vs ShouldBindJSON？
A: ShouldBind 自动识别 Content-Type，通用性更好；ShouldBindJSON 明确指定 JSON，性能略好。

### Q: 如何处理可选参数？
A: 使用指针类型或设置默认值，在 Service 层判断处理。

### Q: 如何处理文件上传？
A: 使用 ctx.FormFile() 获取文件，验证大小和类型，交给 Service 层处理。

### Q: 如何实现接口幂等性？
A: 在 Service 层实现幂等逻辑，使用唯一标识或 Token 机制。

## 扩展指南

### 添加新的 Handler

1. 在对应模块目录创建文件
2. 定义 Handler 函数
3. 遵循四步处理流程
4. 在 router 层注册路由
5. 编写接口文档

### 修改现有 Handler

1. 保持向后兼容
2. 更新 DTO 结构
3. 调整 Service 调用
4. 更新接口文档
5. 通知前端配合修改