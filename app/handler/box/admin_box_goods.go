package box

import (
	boxDto "blind_box/app/dto/box"
	boxSrv "blind_box/app/service/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"github.com/gin-gonic/gin"
)

func AdminBoxGoodsAdd(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsAddReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsAdd(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsUpdate(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsUpdateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsUpdate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsSort(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsSortReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsSort(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsDel(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsDelReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsDel(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsUpdateStock(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsUpdateStockReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsUpdateStock(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsList(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxGoodsInfo(ctx *gin.Context) {
	req := &boxDto.AdminBoxGoodsInfoReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxGoodsInfo(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
