package box

import (
	boxDto "blind_box/app/dto/box"
	boxSrv "blind_box/app/service/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"github.com/gin-gonic/gin"
)

func BoxQueue(ctx *gin.Context) {
	req := &boxDto.BoxQueueReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	t, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = t.UID

	ret, err := boxSrv.GetService().Queue(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func BoxQueuePush(ctx *gin.Context) {
	req := &boxDto.BoxQueuePushReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	t, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = t.UID

	ret, err := boxSrv.GetService().QueuePush(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
