package box

import (
	boxDto "blind_box/app/dto/box"
	boxSrv "blind_box/app/service/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"github.com/gin-gonic/gin"
)

func AdminBoxLevelAdd(ctx *gin.Context) {
	req := &boxDto.AdminBoxLevelAddReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxLevelAdd(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminBoxLevelUpdate(ctx *gin.Context) {
	req := &boxDto.AdminBoxLevelUpdateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxLevelUpdate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)

}

func AdminBoxLevelList(ctx *gin.Context) {
	req := &boxDto.AdminBoxLevelListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := boxSrv.GetService().AdminBoxLevelList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)

}
