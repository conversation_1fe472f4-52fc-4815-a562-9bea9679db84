package box

import (
	"fmt"

	boxDto "blind_box/app/dto/box"
	boxSrv "blind_box/app/service/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// 商品列表接口
func GoodsList(ctx *gin.Context) {
	var req boxDto.GoodsListReq

	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	res, err := boxSrv.GetService().GoodsList(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// 商品详情接口
func GoodsDetail(ctx *gin.Context) {
	req := &boxDto.GoodsDetailReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	res, err := boxSrv.GetService().GoodsDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// 直接购买接口（统一购物车模式）
func DirectPurchase(ctx *gin.Context) {
	var req boxDto.DirectPurchaseReq

	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if req.OrderID == 0 {
		// 购物车验证
		if len(req.CartItems) == 0 {
			helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车不能为空")
			return
		}

		if len(req.CartItems) > 50 { // 限制购物车最大条目数
			helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车商品数量不能超过50个")
			return
		}

		skuMap := make(map[uint64]bool)
		for i, item := range req.CartItems {
			if item.SkuID == 0 {
				helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的 skuId 不能为空", i+1))
				return
			}
			if item.Quantity == 0 {
				helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的 quantity 必须大于 0", i+1))
				return
			}
			if item.Quantity > 9999 { // 限制单个SKU最大数量
				helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的数量不能超过9999", i+1))
				return
			}

			// 检查SKU是否重复
			if skuMap[item.SkuID] {
				helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("购物车中存在重复的商品 SKU: %d", item.SkuID))
				return
			}
			skuMap[item.SkuID] = true
		}
	}

	// 公共参数验证
	if req.PayMethod == 0 {
		helper.AppResp(ctx, ecode.ParamErr.Code(), "支付方式不能为空")
		return
	}

	// 获取用户信息
	t, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}
	req.UserID = t.UID

	res, err := boxSrv.GetService().DirectPurchase(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// 商品库存检查接口
func GoodsStockCheck(ctx *gin.Context) {
	var req boxDto.GoodsStockCheckReq

	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	// 购物车验证
	if len(req.CartItems) == 0 {
		helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车不能为空")
		return
	}

	if len(req.CartItems) > 50 { // 限制购物车最大条目数
		helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车商品数量不能超过50个")
		return
	}

	skuMap := make(map[uint64]bool)
	for i, item := range req.CartItems {
		if item.SkuID == 0 {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的 skuId 不能为空", i+1))
			return
		}
		if item.Quantity == 0 {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的数量必须大于0", i+1))
			return
		}
		if item.Quantity > 9999 { // 限制单个SKU最大数量
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的数量不能超过9999", i+1))
			return
		}

		// 检查SKU是否重复
		if skuMap[item.SkuID] {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("商品 %d 在购物车中重复", item.SkuID))
			return
		}
		skuMap[item.SkuID] = true
	}

	res, err := boxSrv.GetService().GoodsStockCheck(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

// 商品价格计算接口（统一CartItems模式）
func GoodsPriceCalc(ctx *gin.Context) {
	var req boxDto.GoodsPriceCalcReq

	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	// 购物车验证
	if len(req.CartItems) == 0 {
		helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车不能为空")
		return
	}

	if len(req.CartItems) > 50 { // 限制购物车最大条目数
		helper.AppResp(ctx, ecode.ParamErr.Code(), "购物车商品数量不能超过50个")
		return
	}

	skuMap := make(map[uint64]bool)
	for i, item := range req.CartItems {
		if item.SkuID == 0 {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的 skuId 不能为空", i+1))
			return
		}
		if item.Quantity == 0 {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的数量必须大于0", i+1))
			return
		}
		if item.Quantity > 9999 { // 限制单个SKU最大数量
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("第%d个商品的数量不能超过9999", i+1))
			return
		}

		// 检查SKU是否重复
		if skuMap[item.SkuID] {
			helper.AppResp(ctx, ecode.ParamErr.Code(), fmt.Sprintf("商品 %d 在购物车中重复", item.SkuID))
			return
		}
		skuMap[item.SkuID] = true
	}

	// 获取用户信息（如果需要使用优惠券）
	if req.UserID == 0 && req.CouponID > 0 {
		t, err := helper.GetCtxUser(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			return
		}
		req.UserID = t.UID
	}

	res, err := boxSrv.GetService().GoodsPriceCalc(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}
