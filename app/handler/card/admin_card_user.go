package card

import (
	cardDto "blind_box/app/dto/card"
	"blind_box/app/service/card"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"github.com/gin-gonic/gin"
)

func AdminCardSend(ctx *gin.Context) {
	req := &cardDto.AdminCardSendReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
		return
	}
	ret, err := card.GetService().CardSend(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminUserCard(ctx *gin.Context) {
	req := &cardDto.AdminUserCardReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
		return
	}
	ret, err := card.GetService().UserCardList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminUserCardDel(ctx *gin.Context) {
	req := &cardDto.AdminUserCardDelReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
		return
	}
	ret, err := card.GetService().UserCardDel(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
