package goods

import (
	"blind_box/app/common/dbs"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

func AdminSpuList(c *gin.Context) {
	req := goodsDto.AdminSpuListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminSpuList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminSpuDetail(c *gin.Context) {
	req := goodsDto.AdminSpuDetailReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminSpuDetail(c, req.SpuId, true)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminSpuCreate(c *gin.Context) {
	req := goodsDto.AdminSpuCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminSpuCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminSpuUpdate(c *gin.Context) {
	req := goodsDto.AdminSpuUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	err := goods.GetService().AdminSpuUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminSpuOperate 批量修改状态
func AdminSpuOperate(c *gin.Context) {
	req := goodsDto.AdminSpuOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := goods.GetService().AdminSpuOperate(c, req.SpuIds, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

func GoodsCodeGen(c *gin.Context) {
	req := &goodsDto.GoodsCodeGenReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret := &goodsDto.GoodsCodeGenResp{}
	ret.Code = helper.GetAppNo(req.CodeType)

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
