package goods

import (
	"blind_box/app/common/dbs"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminBatchList 批次列表
func AdminBatchList(c *gin.Context) {
	req := goodsDto.AdminBatchListReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminBatchList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBatchDetail 批次详情
func AdminBatchDetail(c *gin.Context) {
	req := goodsDto.AdminBatchDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminBatchDetail(c, req.ID)

	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBatchCreate 添加
func AdminBatchCreate(c *gin.Context) {
	req := goodsDto.AdminBatchCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminBatchCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBatchUpdate 更新
func AdminBatchUpdate(c *gin.Context) {
	req := goodsDto.AdminBatchUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminBatchUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBatchDelete 删除
func AdminBatchDelete(c *gin.Context) {
	req := goodsDto.AdminBatchDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := goods.GetService().AdminBatchDelete(c, req.ID); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

func AdminBatchOperate(c *gin.Context) {
	req := goodsDto.AdminBatchOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := goods.GetService().AdminBatchOperate(c, req.ID, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}
