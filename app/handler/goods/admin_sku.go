package goods

import (
	"blind_box/app/common/dbs"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminSkuList .
func AdminSkuList(c *gin.Context) {
	req := goodsDto.AdminSkuListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := goods.GetService().AdminSkuList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)

}

// AdminSkuDetail .
func AdminSkuDetail(c *gin.Context) {
	req := &goodsDto.AdminSkuDetailReq{}
	if err := c.ShouldBindQuery(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := goods.GetService().AdminSkuDetail(c, req.SkuId)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSkuOperate 批量修改状态
func AdminSkuOperate(c *gin.Context) {
	req := goodsDto.AdminSkuOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := goods.GetService().AdminSkuOperate(c, req.SkuIds, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminSkuStockSet 批量修改状态
func AdminSkuStockSet(c *gin.Context) {
	req := goodsDto.AdminSkuStockSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := goods.GetService().AdminSkuStockSet(c, req.SkuIds, req.Val); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminSkuStockLogList 批量修改状态
func AdminSkuStockLogList(c *gin.Context) {
	req := goodsDto.AdminSkuStockLogListReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := goods.GetService().AdminSkuStockLogList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
