package goods

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/goods/tag"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/goods"

	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminTagGroupList 标签资源列表
func AdminTagGroupList(c *gin.Context) {
	ret := tag.TagGroupList
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminTagList 列表
func AdminTagList(c *gin.Context) {
	req := goodsDto.AdminTagListReq{}
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminTagList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminTagCreate 添加
func AdminTagCreate(c *gin.Context) {
	req := goodsDto.AdminTagCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminTagCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminTagUpdate 更新
func AdminTagUpdate(c *gin.Context) {
	req := goodsDto.AdminTagUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminTagUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminTagDelete 删除
func AdminTagDelete(c *gin.Context) {
	req := goodsDto.AdminTagDeleteReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := goods.GetService().AdminTagDelete(c, req.ID); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

func AdminTagOperate(c *gin.Context) {
	req := goodsDto.AdminTagOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := goods.GetService().AdminTagOperate(c, req.ID, dbs.OperateAction(req.Action)); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

func AdminTagCommonList(c *gin.Context) {
	req := goodsDto.AdminTagCommonListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := goods.GetService().AdminTagCommonList(c, req.GroupID, req.Enable)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
