package goods

import (
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GoodsSpuList .
func GoodsSpuList(c *gin.Context) {
	req := goodsDto.AdminSpuListReq{}
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := goods.GetService().AdminSpuList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GoodsSpuDetail .
func GoodsSpuDetail(c *gin.Context) {
	req := goodsDto.AdminSpuDetailReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	ret, err := goods.GetService().AdminSpuDetail(c, req.SpuId, false)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
