package config

import (
	configDto "blind_box/app/dto/config"
	configSrv "blind_box/app/service/config"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminPageConfig 管理后台 - 获取页面配置列表
func AdminPageConfig(ctx *gin.Context) {
	req := &configDto.AdminPageConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().AdminPageConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminUpdatePageConfig 管理后台 - 更新页面配置
func AdminUpdatePageConfig(ctx *gin.Context) {
	req := &configDto.AdminUpdatePageConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().AdminUpdatePageConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
