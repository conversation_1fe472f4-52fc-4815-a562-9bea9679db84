package config

import (
	configDto "blind_box/app/dto/config"
	configSrv "blind_box/app/service/config"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

func BoxConfig(ctx *gin.Context) {
	ret, err := configSrv.GetService().BoxConfig(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func PageConfig(ctx *gin.Context) {
	req := &configDto.PageConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().PageConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
