package openapi

import (
	"strconv"

	"blind_box/app/dto/openapi"
	openapiSrv "blind_box/app/service/openapi"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// CreateMerchant 创建商户
// @Summary 创建OpenAPI商户
// @Description 创建新的OpenAPI商户配置
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param request body openapi.CreateMerchantRequest true "创建商户请求"
// @Success 200 {object} openapi.CreateMerchantResponse
// @Router /admin/openapi/merchant [post]
func AdminCreateMerchant(c *gin.Context) {
	var req openapi.CreateMerchantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "参数错误: "+err.Error())
		return
	}

	resp, err := openapiSrv.GetService().CreateMerchant(c, &req)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "创建商户失败: "+err.Error())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// UpdateMerchant 更新商户
// @Summary 更新OpenAPI商户
// @Description 更新OpenAPI商户配置
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param request body openapi.UpdateMerchantRequest true "更新商户请求"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchant [put]
func AdminUpdateMerchant(c *gin.Context) {
	var req openapi.UpdateMerchantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "参数错误: "+err.Error())
		return
	}

	err := openapiSrv.GetService().UpdateMerchant(c, &req)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "更新商户失败: "+err.Error())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// ListMerchants 获取商户列表
// @Summary 获取OpenAPI商户列表
// @Description 获取OpenAPI商户列表
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param status query int false "状态：1-启用，2-禁用"
// @Param keyword query string false "关键词（商户名称）"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchants [get]
func AdminListMerchants(c *gin.Context) {
	status := uint32(0)
	if s := c.Query("status"); s != "" {
		if v, err := strconv.ParseUint(s, 10, 32); err == nil {
			status = uint32(v)
		}
	}

	keyword := c.Query("keyword")

	list, err := openapiSrv.GetService().ListMerchants(c, status, keyword)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "获取商户列表失败")
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), list)
}

// GetMerchantConfig 获取商户配置
// @Summary 获取商户配置
// @Description 获取指定商户的完整配置信息
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param id path int true "商户ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchant/{id}/config [get]
func AdminGetMerchantConfig(c *gin.Context) {
	idStr := c.Param("id")
	merchantID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "商户ID无效")
		return
	}

	config, err := openapiSrv.GetService().GetMerchantConfig(c, merchantID)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "获取配置失败: "+err.Error())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), config)
}

// ExportConfig 导出配置文件
// @Summary 导出商户配置文件
// @Description 导出商户配置文件（JSON格式）
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param id path int true "商户ID"
// @Param format query string false "格式：json/yaml，默认json"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchant/{id}/export [get]
func AdminExportConfig(c *gin.Context) {
	idStr := c.Param("id")
	merchantID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "商户ID无效")
		return
	}

	format := c.DefaultQuery("format", "json")

	configStr, err := openapiSrv.GetService().ExportConfig(c, merchantID, format)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "导出配置失败: "+err.Error())
		return
	}

	// 设置响应头，支持文件下载
	c.Header("Content-Disposition", "attachment; filename=merchant_config."+format)
	c.Header("Content-Type", "application/"+format)
	c.String(200, configStr)
}

// ResetAppSecret 重置AppSecret
// @Summary 重置商户AppSecret
// @Description 重置商户的AppSecret密钥
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param id path int true "商户ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchant/{id}/reset-secret [post]
func AdminResetAppSecret(c *gin.Context) {
	idStr := c.Param("id")
	merchantID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "商户ID无效")
		return
	}

	newSecret, err := openapiSrv.GetService().ResetAppSecret(c, merchantID)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "重置密钥失败: "+err.Error())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), gin.H{
		"app_secret": newSecret,
	})
}

// TestConnection 测试连接
// @Summary 测试商户连接
// @Description 测试商户配置是否正确
// @Tags OpenAPI管理
// @Accept json
// @Produce json
// @Param id path int true "商户ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/openapi/merchant/{id}/test [post]
func AdminTestConnection(c *gin.Context) {
	idStr := c.Param("id")
	merchantID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "商户ID无效")
		return
	}

	success, msg, err := openapiSrv.GetService().TestConnection(c, merchantID)
	if err != nil {
		helper.AppResp(c, ecode.SystemErr.Code(), "测试连接失败: "+err.Error())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), gin.H{
		"success": success,
		"message": msg,
	})
}
