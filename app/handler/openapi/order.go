package openapi

import (
	"blind_box/app/dto/openapi"
	openapiSrv "blind_box/app/service/openapi"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

func CreateScanOrder(ctx *gin.Context) {
	// Check if request was aborted by middleware
	if ctx.IsAborted() {
		return
	}

	req := &openapi.CreateScanOrderReq{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	res, err := openapiSrv.GetService().CreateScanOrder(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

func QueryScanOrder(ctx *gin.Context) {
	// Check if request was aborted by middleware
	if ctx.IsAborted() {
		return
	}

	req := &openapi.QueryScanOrderReq{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
		return
	}

	if err := req.Validate(); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	res, err := openapiSrv.GetService().QueryScanOrder(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), res)
}

func HandleCallback(ctx *gin.Context) {
	// Check if request was aborted by middleware
	if ctx.IsAborted() {
		return
	}

	req := &openapi.PaymentCallback{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		ctx.JSON(200, &openapi.CallbackResponse{
			Code: openapi.ErrorCodeParamError,
			Msg:  openapi.GetErrorMessage(openapi.ErrorCodeParamError),
		})
		return
	}

	err := openapiSrv.GetService().HandlePaymentCallback(ctx, req)
	if err != nil {
		ctx.JSON(200, &openapi.CallbackResponse{
			Code: openapi.ErrorCodeSystemError,
			Msg:  err.Error(),
		})
		return
	}

	ctx.JSON(200, &openapi.CallbackResponse{
		Code: openapi.ErrorCodeSuccess,
		Msg:  openapi.GetErrorMessage(openapi.ErrorCodeSuccess),
	})
}
