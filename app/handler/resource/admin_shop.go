package resource

import (
	"blind_box/app/common/dbs"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/app/service/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminShopList .
func AdminShopList(c *gin.Context) {
	req := resourceDto.AdminShopListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := resource.GetService().AdminShopList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShopSet .
func AdminShopSet(c *gin.Context) {
	req := resourceDto.AdminShopSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := resource.GetService().AdminShopSet(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShopOperate .
func AdminShopOperate(c *gin.Context) {
	req := resourceDto.AdminShopOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := resource.GetService().AdminShopOperate(c, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminShopAll .
func AdminShopAll(c *gin.Context) {
	req := resourceDto.AdminShopAllReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := resource.GetService().AdminShopAll(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
