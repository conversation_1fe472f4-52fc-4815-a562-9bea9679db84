package resource

import (
	"blind_box/app/common/dbs"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/app/service/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminVendorList .
func AdminVendorList(c *gin.Context) {
	req := resourceDto.AdminVendorListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := resource.GetService().AdminVendorList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminVendorSet .
func AdminVendorSet(c *gin.Context) {
	req := resourceDto.AdminVendorSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := resource.GetService().AdminVendorSet(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminVendorOperate .
func AdminVendorOperate(c *gin.Context) {
	req := resourceDto.AdminVendorOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := resource.GetService().AdminVendorOperate(c, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminVendorAll .
func AdminVendorAll(c *gin.Context) {
	req := resourceDto.AdminVendorAllReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := resource.GetService().AdminVendorAll(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
