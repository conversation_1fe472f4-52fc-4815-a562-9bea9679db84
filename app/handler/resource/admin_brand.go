package resource

import (
	"blind_box/app/common/dbs"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/app/service/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminBrandList .
func AdminBrandList(c *gin.Context) {
	req := resourceDto.AdminBrandListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := resource.GetService().AdminBrandList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBrandSet .
func AdminBrandSet(c *gin.Context) {
	req := resourceDto.AdminBrandSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := resource.GetService().AdminBrandSet(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminBrandOperate .
func AdminBrandOperate(c *gin.Context) {
	req := resourceDto.AdminBrandOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := resource.GetService().AdminBrandOperate(c, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminBrandAll .
func AdminBrandAll(c *gin.Context) {
	req := resourceDto.AdminBrandAllReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := resource.GetService().AdminBrandAll(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
