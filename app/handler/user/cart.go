package user

import (
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// UserCartList .
func UserCartList(ctx *gin.Context) {
	req := userDto.UserCartListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().UserCartList(ctx, ctxUser.UID, req, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UserCartAdd .
func UserCartAdd(ctx *gin.Context) {
	req := userDto.UserCartAddReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().UserCartAdd(ctx, ctxUser.UID, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserCartEdit .
func UserCartEdit(ctx *gin.Context) {
	req := userDto.UserCartEditReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().UserCartEdit(ctx, ctxUser.UID, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserCartDel .
func UserCartDel(ctx *gin.Context) {
	req := userDto.UserCartDelReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().UserCartDel(ctx, ctxUser.UID, req.CartIds); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserCartCount .
func UserCartCount(ctx *gin.Context) {
	req := userDto.UserCartListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().UserCartCount(ctx, ctxUser.UID, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
