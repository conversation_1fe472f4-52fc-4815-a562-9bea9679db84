package user

import (
	"blind_box/app/common/dbs"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminRoleList 列表
func AdminUserList(ctx *gin.Context) {
	req := userDto.AdminUserListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := user.GetService().AdminUserList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminOperateUser .
func AdminOperateUser(ctx *gin.Context) {
	req := &userDto.AdminOperateUserReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := user.GetService().AdminOperateUser(ctx, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminUserInfo 获取用户信息
func AdminUserInfo(ctx *gin.Context) {
	req := &userDto.AdminUserInfoReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := user.GetService().AdminUserInfo(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
