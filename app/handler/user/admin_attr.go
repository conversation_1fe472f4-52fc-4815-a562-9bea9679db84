package user

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dto/common"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminUserLoginLogList .
func AdminUserLoginLogList(ctx *gin.Context) {
	req := userDto.AdminUserLoginLogListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := user.GetService().AdminUserLoginLogList(ctx, req.UserID, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminUserAddrList 列表
func AdminUserAddrList(ctx *gin.Context) {
	req := &userDto.AdminUserAddrListReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := user.GetService().AdminUserAddrList(ctx, req.UserID, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetUserAddr 设置用户地址
func AdminSetUserAddr(ctx *gin.Context) {
	req := userDto.AdminSetUserAddrReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := user.GetService().AdminSetUserAddr(ctx, req.UserID, req.SetUserAddrReq); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminUserAddrInfo 列表
func AdminUserAddrInfo(ctx *gin.Context) {
	req := userDto.AdminUserAddrInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := user.GetService().UserAddrInfo(ctx, req.UserID, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminDelUserAddr 删除
func AdminDelUserAddr(ctx *gin.Context) {
	req := common.IdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := user.GetService().AdminOperateAddr(ctx, req.ID, dbs.ActionDel, true); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
