package user

import (
	userDto "blind_box/app/dto/user"
	userSrv "blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// SubscribeList 用户订阅列表
func SubscribeList(ctx *gin.Context) {
	req := userDto.SubscribeListReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := userSrv.GetService().SubscribeList(ctx, ctxUser.UID, req.EntityType, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SubscribeActivity 用户订阅活动
func SubscribeAdd(ctx *gin.Context) {
	req := userDto.SubscribeAddReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := userSrv.GetService().SubscribeAdd(ctx, ctxUser.UID, req.EntityId, req.EntityType); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// SubscribeDel 用户删除订阅活动
func SubscribeDel(ctx *gin.Context) {
	req := userDto.SubscribeAddReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := userSrv.GetService().SubscribeDel(ctx, ctxUser.UID, req.EntityId, req.EntityType); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
