package user

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dto/common"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// UserAddrList 列表
func UserAddrList(ctx *gin.Context) {
	req := common.DataListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().AdminUserAddrList(ctx, ctxUser.UID, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SetUserAddr 设置用户地址
func SetUserAddr(ctx *gin.Context) {
	req := userDto.SetUserAddrReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().SetUserAddr(ctx, ctxUser.UID, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserAddrInfo 列表
func UserAddrInfo(ctx *gin.Context) {
	req := common.IdReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().UserAddrInfo(ctx, ctxUser.UID, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// DelUserAddr 删除
func DelUserAddr(ctx *gin.Context) {
	req := common.IdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := user.GetService().AdminOperateAddr(ctx, req.ID, dbs.ActionDel, false); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
