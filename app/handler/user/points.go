package user

import (
	adminDto "blind_box/app/dto/admin"
	"blind_box/app/dto/common"
	"blind_box/app/service/admin"
	userSrv "blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminEditUserPoints 管理员编辑用户积分
func AdminEditUserPoints(ctx *gin.Context) {
	req := &adminDto.AdminEditUserPointsReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	// 获取当前操作员信息
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), "未获取到操作员信息")
		return
	}
	req.OperatorID = ctxAccount.AccountID

	ret, err := admin.GetService().AdminEditUserPoints(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminUserPointsLog 管理员查询用户积分记录
func AdminUserPointsLog(ctx *gin.Context) {
	req := &adminDto.AdminUserPointsLogReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := admin.GetService().AdminUserPointsLog(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminUserPointsStats 管理员查询用户积分统计
func AdminUserPointsStats(ctx *gin.Context) {
	req := &adminDto.AdminUserPointsStatsReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := admin.GetService().AdminUserPointsStats(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GetUserPointsBill 用户积分明细账单(近一年)
func GetUserPointsBill(ctx *gin.Context) {
	req := common.DataListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := userSrv.GetService().GetUserPointsBill(ctx, ctxUser.UID, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
