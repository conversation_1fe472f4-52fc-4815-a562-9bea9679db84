package user

import (
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// RewardNewUserInfo 新人奖励
func RewardNewUserInfo(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().RewardNewUserInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// RewardNewUserGet 领取新人奖励
func RewardNewUserGet(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	if err := user.GetService().RewardNewUserGet(ctx, ctxUser.UID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// RewardNewUserStatus 新人奖励状态查询
func RewardNewUserStatus(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().RewardNewUserStatus(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
