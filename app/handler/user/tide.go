package user

import (
	commonDto "blind_box/app/dto/common"
	userDto "blind_box/app/dto/user"
	userSrv "blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GetTideTaskInfo 潮气值每日任务列表
func GetTideTaskInfo(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := userSrv.GetService().GetTideTaskInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GetTideCardInfo 潮气值可兑换道具卡配置
func GetTideCardInfo(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	ret, err := userSrv.GetService().GetTideCardInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UserGetTaskTide 用户领取任务潮气值
func UserGetTaskVal(ctx *gin.Context) {
	req := userDto.UserGetTaskTideReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := userSrv.GetService().UserGetTaskVal(ctx, ctxUser.UID, req.TaskId); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserTideExchangeCard 使用潮气值兑换道具卡
func UserTideExchangeCard(ctx *gin.Context) {
	req := userDto.UserTideExchangeCardReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := userSrv.GetService().UserTideExchangeCard(ctx, ctxUser.UID, req.CardId); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// GetUserTideBill 用户潮气值明细账单(近7日)
func GetUserTideBill(ctx *gin.Context) {
	req := commonDto.DataListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := userSrv.GetService().GetUserTideBill(ctx, ctxUser.UID, req.Page, req.Limit)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
