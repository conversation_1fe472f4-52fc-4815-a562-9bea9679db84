package user

import (
	tideDao "blind_box/app/dao/user/tide"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminTideSourceList 潮气值来源列表
func AdminTideSourceList(ctx *gin.Context) {
	sourceList := tideDao.GetRepo().AdminTideSourceList()
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), sourceList)
}

// AdminTideBill 用户潮气值明细账单
func AdminTideBill(ctx *gin.Context) {
	req := userDto.AdminTideBillReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := user.GetService().AdminTideBill(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminTideSend 发放潮气值
func AdminTideSend(ctx *gin.Context) {
	req := userDto.AdminSendTideReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}
	if err := user.GetService().AdminSendTide(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
