package user

import (
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user/login"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// UserWechatLogin .
func UserWechatLogin(ctx *gin.Context) {
	req := userDto.UserLoginCommonReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := login.NewLoginManager().Login(ctx, userDao.AtWxApplet, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// ScriptUserLogin .
func ScriptUserLogin(ctx *gin.Context) {
	req := userDto.AdminUserInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := login.ScriptUserLogin(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UserLogout .
func UserLogout(ctx *gin.Context) {
	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	userDao.GetRepo().RedisClearUserToken(ctx, ctxUser.UID)
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserLogin .
func UserLoginThird(ctx *gin.Context) {
	req := userDto.UserLoginThirdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := login.NewLoginManager().Login(ctx, userDao.AuthType(req.AuthType), userDto.UserLoginCommonReq{AuthToken: req.AuthToken})
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
