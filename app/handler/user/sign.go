package user

import (
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GetUserSignConfigInfo 获取用户签到信息
func GetUserSignConfigInfo(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	ret, err := user.GetService().GetUserSignConfigInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)

}

// UserSign 用户签到
func UserSign(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)

	if err := user.GetService().UserSign(ctx, ctxUser.UID); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
