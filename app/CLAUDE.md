# app 目录代码规范

本文档记录 `/app` 目录下的代码组织结构和编码规范。

## 目录结构

```
app/
├── handler/     # HTTP 请求处理器（控制器层）
├── service/     # 业务逻辑层
├── dao/         # 数据访问层（模型 + 仓库）
├── dto/         # 数据传输对象（请求/响应结构）
├── middleware/  # HTTP 中间件（认证、日志、恢复）
├── api/         # 第三方服务集成（支付、云服务）
├── consumer/    # 消息队列消费者
├── job/         # 定时任务和后台作业
├── common/      # 公共组件（数据库连接、通用服务）
├── cmd/         # 命令行入口
└── script/      # 脚本工具
```

## 分层架构规范

### 请求处理流程
`HTTP Request → Router → Middleware → Handler → Service → DAO → Database`

### 各层职责

#### Handler 层（控制器）
- **位置**: `app/handler/[模块名]/`
- **职责**: 
  - 参数绑定和验证
  - 调用 Service 层
  - 统一响应格式化
- **命名规范**:
  - 文件名: 小写下划线 `user.go`, `admin_user.go`
  - 函数名: 大驼峰 `UserBasic`, `AdminUserList`
- **代码规范**:
  ```go
  func HandlerName(ctx *gin.Context) {
      // 1. 参数绑定
      req := &dto.RequestType{}
      if err := ctx.ShouldBind(req); err != nil {
          helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, req))
          return
      }
      
      // 2. 调用服务
      ret, err := service.GetService().Method(ctx, req)
      if err != nil {
          helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
          return
      }
      
      // 3. 响应
      helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
  }
  ```

#### Service 层（业务逻辑）
- **位置**: `app/service/[模块名]/`
- **职责**:
  - 核心业务逻辑实现
  - 事务管理
  - 跨 DAO 协调
  - 不包含 HTTP 相关内容
- **文件组织**:
  - `interface.go`: 接口定义和依赖注入
  - `[业务名].go`: 具体实现
- **依赖注入模式**:
  ```go
  // interface.go
  type Entry struct {
      UserRepo      *userDao.Entry
      OrderRepo     *orderDao.Entry
      // 其他依赖
  }
  
  func newEntry() *Entry {
      return &Entry{
          UserRepo:  userDao.GetRepo(),
          OrderRepo: orderDao.GetRepo(),
      }
  }
  ```
- **方法签名**:
  ```go
  func (e *Entry) MethodName(ctx *gin.Context, req *dto.RequestType) (res *dto.ResponseType, err error)
  ```

#### DAO 层（数据访问）
- **位置**: `app/dao/[模块名]/`
- **职责**:
  - 数据库操作
  - Redis 缓存
  - 只包含数据操作，无业务逻辑
- **文件组织**:
  - `interface.go`: 接口定义
  - `model.go`: GORM 模型定义
  - `repo.go`: 数据库操作方法
  - `redis.go`: Redis 缓存操作
- **模型定义规范**:
  ```go
  type Model struct {
      dbs.ModelWithDel  // 继承基础字段
      // 业务字段
  }
  
  func (Model) TableName() string {
      return "table_name"
  }
  ```
- **常量定义**:
  ```go
  // 使用包级常量，不要硬编码
  var (
      OrderStatusPayOk    OrderStatus = 3
      OrderStatusDone     OrderStatus = 7
      DeliveryTypeExpress uint32 = 1
  )
  ```

#### DTO 层（数据传输对象）
- **位置**: `app/dto/[模块名]/`
- **职责**:
  - 定义请求参数结构
  - 定义响应数据结构
  - 参数验证规则
- **命名规范**:
  - 请求: `XxxReq`
  - 响应: `XxxRes` 或 `XxxResp`
- **验证标签**:
  ```go
  type UserEditReq struct {
      Name string `json:"name" binding:"required" msg:"名称不能为空"`
      Age  int    `json:"age" binding:"min=1,max=100" msg:"年龄必须在1-100之间"`
  }
  ```

## 通用编码规范

### 导入规范
```go
import (
    // 标准库
    "fmt"
    "time"
    
    // 项目内部包
    "blind_box/app/dao/user"
    "blind_box/app/dto/user"
    "blind_box/pkg/ecode"
    
    // 第三方包
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
)
```

### 错误处理
- **Service 层**: 返回业务错误
  ```go
  if err != nil {
      return nil, ecode.SystemErr
  }
  ```
- **Handler 层**: 转换为 HTTP 响应
  ```go
  helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
  ```
- **非关键操作**: 记录日志但不中断流程
  ```go
  if err := logOperation(); err != nil {
      log.Ctx(ctx).WithError(err).Warn("log operation failed")
      // 继续执行
  }
  ```

### JSON 处理
```go
// 始终使用项目的 jsoniter
import jsoniter "github.com/json-iterator/go"

var json = jsoniter.ConfigCompatibleWithStandardLibrary
```

### 事务处理
```go
// Service 层事务管理
tx := dbs.MysqlEngine.UseWithGinCtx(ctx, true).Begin()
defer func() {
    if err != nil {
        tx.Rollback()
    } else {
        tx.Commit()
    }
}()
```

### Redis 操作
```go
// 使用 RetryLock 进行原子操作
redis.RetryLock(ctx, lockKey, func() error {
    // 原子操作
    return nil
})
```

## Middleware 规范

### 认证中间件
- **用户认证**: `user_auth.go`
  - `CheckUser()`: 可选认证
  - `CheckUserLogin()`: 必须认证
- **管理员认证**: `account_auth.go`
  - `CheckAccountLogin()`: 登录验证
  - `CheckAccountAuth()`: 权限验证
- **OpenAPI 认证**: `openapi_auth.go`
  - 签名验证: `SHA256(JSON_BODY + "&key=" + APP_SECRET)`
  - IP 白名单
  - 速率限制

### 中间件使用
```go
// 路由配置
router.Group("/api").Use(
    middleware.CheckUserLogin(),
    middleware.RateLimit(),
).GET("/user", handler.UserInfo)
```

## 业务模块组织

### 模块划分
- **user**: 用户管理、登录、积分
- **box**: 盲盒管理、开箱记录
- **order**: 订单创建、支付、退款
- **goods**: SKU/SPU 管理、库存
- **coupon**: 优惠券发放、使用
- **activity**: 营销活动、订阅通知

### 跨模块调用
- Service 层通过依赖注入访问其他模块
- 避免循环依赖
- 使用接口解耦

## 代码风格

### 注释规范
- 只在复杂业务逻辑处添加注释
- 保持注释简洁
- 使用中文注释

### 命名规范
- **包名**: 小写，单词不分隔
- **文件名**: 小写下划线
- **类型名**: 大驼峰
- **常量**: 大驼峰或全大写下划线
- **变量/函数**: 小驼峰（导出的用大驼峰）

### 代码简洁性
- 避免过度包装错误
- 简单直接的错误处理
- 不添加不必要的抽象层

## 测试规范

### 测试文件
- 单元测试: `*_test.go`
- 集成测试: `*_integration_test.go`

### 测试组织
```go
func TestMethodName(t *testing.T) {
    // Arrange
    // Act  
    // Assert
}
```

## 性能优化

### 批量操作
```go
// 使用批量创建而非循环单个创建
e.BatchCreate(ctx, models)
```

### 并发处理
```go
// 使用 errgroup 处理并发
g, ctx := errgroup.WithContext(ctx)
g.Go(func() error { return task1() })
g.Go(func() error { return task2() })
if err := g.Wait(); err != nil {
    return err
}
```

### 缓存策略
- 高频访问数据使用 Redis
- 合理设置过期时间
- 使用双缓存（Redis + 本地内存）提高性能

## 安全规范

### SQL 注入防护
- 始终使用参数化查询
- 不拼接 SQL 字符串

### 敏感信息
- 不记录密码、密钥等敏感信息
- 使用环境变量管理配置
- 生产环境关闭 debug 信息

### 权限控制
- Handler 层进行权限检查
- Service 层验证数据归属
- 使用中间件统一认证

## 库存管理规范

### 一致性规则
```go
// 库存一致性: Total + RefundNum >= LockNum + UsedNum
validateStockConsistency(stock)
```

### 库存日志
```go
// 所有库存变更必须记录日志
stockLogDao.Create(ctx, &StockLog{
    SkuID:     skuID,
    ChangeNum: changeNum,
    Reason:    reason,
})
```

## 重要提醒

1. **使用包常量**: 永远不要硬编码业务值
2. **依赖注入**: 新增依赖时更新 Entry 结构
3. **事务管理**: Service 层统一管理事务
4. **错误处理**: 使用 pkg/ecode 包
5. **JSON 处理**: 使用 jsoniter 而非标准库
6. **代码生成**: 优先使用现有代码模式