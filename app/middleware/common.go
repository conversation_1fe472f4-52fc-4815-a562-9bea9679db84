package middleware

import (
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util/ctxUtil"
	"bytes"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func CORSMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Credentials", "true")
		ctx.Header("Access-Control-Allow-Headers", "token, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, Client-Type, Client-Source, accept, origin, Cache-Control, X-Requested-With")
		ctx.Header("Access-Control-Allow-Methods", "POST,HEAD,PATCH,OPTIONS,GET,PUT,DELETE")

		var (
			requestID = ctxUtil.GenRequestID(ctxUtil.ReqTypeH)
			startTime = time.Now()
			newCtx    = ctxUtil.WithRequestID(ctx.Request.Context(), requestID)
		)
		ctx.Set("reqTime", startTime)
		ctx.Set("request_id", requestID)
		ctx.Request = ctx.Request.WithContext(newCtx)

		log.Ctx(ctx).WithField("requestPath", ctx.Request.URL.Path).
			WithField("HeaderInfo", ctx.Request.Header).Info("AppServerStart")

		switch ctx.Request.Method {
		case http.MethodPost, http.MethodPut:
			body, err := io.ReadAll(ctx.Request.Body)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("io.ReadAll err")
				helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
				ctx.Abort()
				return
			}
			ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
			if len(body) > 0 {
				log.Ctx(ctx).WithField("requestPath", ctx.Request.URL.Path).WithField("requestBody", string(body)).Info("appRequestBody")
			}
		case http.MethodGet:
			param := ctx.Request.URL.Query()
			if len(param) > 0 {
				log.Ctx(ctx).WithField("requestPath", ctx.Request.URL.Path).WithField("requestParam", param).Info("appRequestParam")
			}
		default:
			ctx.AbortWithStatus(204)
			return
		}

		ctx.Next()
	}
}
