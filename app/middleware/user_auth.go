package middleware

import (
	userDao "blind_box/app/dao/user"
	userSrv "blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util"
	"bytes"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
)

// CheckUser .
func CheckUser() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckUserLogin ctx.IsAborted")
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		if authToken != "" {
			appClaims, err := util.ParseToken(authToken)
			if err != nil {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				log.Ctx(ctx).WithError(err).WithField("token", authToken).Warn("util.ParseToken err")
				ctx.Abort()
				return
			}
			log.Ctx(ctx).WithField("appClaims", appClaims).Info("util.ParseToken ret")

			token, err := userDao.GetRepo().RedisGetUserToken(ctx, appClaims.UserID)
			if token == "" || err != nil {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				log.Ctx(ctx).WithField("token", token).Warn("RedisGetAccountToken err")
				ctx.Abort()
				return
			}

			if token != authToken {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				ctx.Abort()
				return
			}

			ctx.Set("ctxUser", &helper.CtxUser{UID: appClaims.UserID})
			// 兼容老版本
			ctx.Request.Header.Set("uid", fmt.Sprintf("%d", appClaims.UserID))
			ctx.Request.Header.Set("userId", fmt.Sprintf("%d", appClaims.UserID))
		}
		ctx.Next()
	}
}

// CheckUserLogin .
func CheckUserLogin() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckUserLogin ctx.IsAborted")
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		appClaims, err := util.ParseToken(authToken)
		if err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithError(err).WithField("token", authToken).Warn("util.ParseToken err")
			ctx.Abort()
			return
		}
		log.Ctx(ctx).WithField("appClaims", appClaims).Info("util.ParseToken ret")

		token, err := userDao.GetRepo().RedisGetUserToken(ctx, appClaims.UserID)
		if token == "" || err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithField("token", token).Warn("RedisGetAccountToken err")
			ctx.Abort()
			return
		}

		if token != authToken {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}

		ctx.Set("ctxUser", &helper.CtxUser{UID: appClaims.UserID})
		// 兼容老版本
		ctx.Request.Header.Set("uid", fmt.Sprintf("%d", appClaims.UserID))
		ctx.Request.Header.Set("userId", fmt.Sprintf("%d", appClaims.UserID))
		ctx.Next()
	}
}

// UserOperationLog .
func UserOperationLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.Method == http.MethodGet {
			ctx.Next()
			return
		}

		// 获取请求头与请求体
		reqHeaders := ctx.Request.Header
		headers, _ := json.Marshal(&reqHeaders)

		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		ctx.Next()

		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("AdminOperationLog ctx.IsAborted")
			return
		}

		isAbort, err := userSrv.GetUserLogEntity().Producer(ctx, headers, body)
		if isAbort {
			ctx.Abort()
			return
		}

		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
	}
}
