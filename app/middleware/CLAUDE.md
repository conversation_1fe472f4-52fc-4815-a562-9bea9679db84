# app/middleware 目录规范文档

本文档记录 `app/middleware` 目录下 HTTP 中间件的代码规范和最佳实践。

## 目录结构

```
app/middleware/
├── interface.go         # 接口定义（预留）
├── common.go           # 通用中间件（CORS、请求ID等）
├── recovery.go         # 恢复中间件（处理panic）
├── user_auth.go        # 用户认证中间件
├── account_auth.go     # 管理员认证中间件
├── script_auth.go      # 脚本权限中间件
├── openapi_auth.go     # OpenAPI认证和限流中间件
└── rate_limit.go       # 通用限流中间件
```

## 核心设计规范

### 中间件函数签名

所有中间件必须返回 `gin.HandlerFunc`：

```go
func MiddlewareName() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 前置处理
        
        // 检查是否已被中断
        if ctx.IsAborted() {
            log.Ctx(ctx).Debug("MiddlewareName ctx.IsAborted")
            return
        }
        
        // 中间件逻辑
        
        // 继续或中断
        if shouldAbort {
            helper.AppResp(ctx, code, message)
            ctx.Abort()
            return
        }
        
        ctx.Next()
        
        // 后置处理（可选）
    }
}
```

### 中间件执行流程

```
请求进入
    ↓
CORS中间件（设置跨域头、生成RequestID）
    ↓
Recovery中间件（捕获panic）
    ↓
认证中间件（用户/管理员）
    ↓
权限中间件（检查权限）
    ↓
限流中间件（请求频率控制）
    ↓
操作日志中间件（记录操作）
    ↓
业务Handler
    ↓
响应返回
```

## 通用中间件 (common.go)

### CORSMiddleware - 跨域和请求初始化

```go
func CORSMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. 设置CORS头
        ctx.Header("Access-Control-Allow-Origin", "*")
        ctx.Header("Access-Control-Allow-Credentials", "true")
        ctx.Header("Access-Control-Allow-Headers", "...")
        ctx.Header("Access-Control-Allow-Methods", "...")
        
        // 2. 生成请求ID
        requestID := ctxUtil.GenRequestID(ctxUtil.ReqTypeH)
        startTime := time.Now()
        newCtx := ctxUtil.WithRequestID(ctx.Request.Context(), requestID)
        
        // 3. 设置上下文
        ctx.Set("reqTime", startTime)
        ctx.Set("request_id", requestID)
        ctx.Request = ctx.Request.WithContext(newCtx)
        
        // 4. 记录请求日志
        log.Ctx(ctx).WithField("requestPath", ctx.Request.URL.Path).
            WithField("HeaderInfo", ctx.Request.Header).Info("AppServerStart")
        
        // 5. 处理请求体（POST/PUT）
        if ctx.Request.Method == http.MethodPost || ctx.Request.Method == http.MethodPut {
            body, err := io.ReadAll(ctx.Request.Body)
            if err != nil {
                helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
                ctx.Abort()
                return
            }
            ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
            log.Ctx(ctx).WithField("requestBody", string(body)).Info("appRequestBody")
        }
        
        ctx.Next()
    }
}
```

#### 关键功能
- **跨域处理**: 设置必要的CORS响应头
- **请求追踪**: 生成唯一请求ID用于日志追踪
- **请求记录**: 记录请求路径、头部、请求体
- **OPTIONS处理**: 处理预检请求

## 认证中间件

### 用户认证 (user_auth.go)

#### CheckUser - 可选用户认证
```go
func CheckUser() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        authToken := ctx.Request.Header.Get("Authorization")
        if authToken != "" {
            // 1. 解析Token
            appClaims, err := util.ParseToken(authToken)
            if err != nil {
                helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
                ctx.Abort()
                return
            }
            
            // 2. 验证Redis中的Token
            token, err := userDao.GetRepo().RedisGetUserToken(ctx, appClaims.UserID)
            if token != authToken {
                helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
                ctx.Abort()
                return
            }
            
            // 3. 设置用户信息
            ctx.Set("ctxUser", &helper.CtxUser{UID: appClaims.UserID})
            // 兼容老版本
            ctx.Request.Header.Set("uid", fmt.Sprintf("%d", appClaims.UserID))
        }
        ctx.Next()
    }
}
```

#### CheckUserLogin - 必须用户认证
```go
func CheckUserLogin() gin.HandlerFunc {
    // 与CheckUser类似，但Token为必须
    // 无Token或验证失败直接返回错误
}
```

#### UserOperationLog - 用户操作日志
```go
func UserOperationLog() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. GET请求跳过
        if ctx.Request.Method == http.MethodGet {
            ctx.Next()
            return
        }
        
        // 2. 获取请求信息
        reqHeaders := ctx.Request.Header
        body, err := io.ReadAll(ctx.Request.Body)
        ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
        
        ctx.Next()
        
        // 3. 异步记录操作日志
        if !ctx.IsAborted() {
            userSrv.GetUserLogEntity().Producer(ctx, headers, body)
        }
    }
}
```

### 管理员认证 (account_auth.go)

#### CheckAccountLogin - 管理员登录验证
```go
func CheckAccountLogin() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. 解析Token
        authToken := ctx.Request.Header.Get("Authorization")
        appClaims, err := util.ParseToken(authToken)
        
        // 2. 验证Redis中的Token
        token, err := accountDao.GetRepo().RedisGetAccountToken(ctx, appClaims.UserID)
        if token != authToken {
            helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
            ctx.Abort()
            return
        }
        
        // 3. 设置管理员信息
        ctx.Set("ctxAccount", &helper.CtxAccount{
            AccountID: appClaims.UserID,
            RoleID:    appClaims.RoleID,
        })
        
        ctx.Next()
    }
}
```

#### CheckAccountAuth - 管理员权限验证
```go
func CheckAccountAuth() gin.HandlerFunc {
    // 权限验证逻辑（当前已注释，预留扩展）
    // 可实现基于角色的权限控制(RBAC)
}
```

#### AdminOperationLog - 管理员操作日志
```go
func AdminOperationLog() gin.HandlerFunc {
    // 与UserOperationLog类似
    // 记录管理员的操作日志
}
```

### 脚本权限 (script_auth.go)

#### CheckScriptAuth - 脚本权限验证
```go
func CheckScriptAuth() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 获取管理员信息
        ctxAccount, err := helper.GetCtxAccount(ctx)
        if err != nil {
            helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
            ctx.Abort()
            return
        }
        
        // 只允许Root和Admin角色
        if ctxAccount.RoleID != dbs.RoleRootID && ctxAccount.RoleID != dbs.RoleAdminID {
            helper.AppResp(ctx, ecode.NoAuthErr.Code(), ecode.NoAuthErr.Message())
            ctx.Abort()
            return
        }
        
        ctx.Next()
    }
}
```

## OpenAPI 认证 (openapi_auth.go)

### 认证流程

```
1. 验证请求头（AppID、Signature、Nonce）
2. 查询商户信息并验证状态
3. 检查IP白名单
4. 验证时间戳（防重放）
5. 验证Nonce（防重复）
6. 验证签名
7. 设置Nonce缓存
```

### OpenAPIAuth - API认证中间件

```go
func OpenAPIAuth() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. 获取认证头
        appID := ctx.GetHeader(HeaderAppID)
        signature := ctx.GetHeader(HeaderSignature)
        nonce := ctx.GetHeader(HeaderNonce)
        
        // 2. 查询商户信息
        merchant, err := merchantDao.GetRepo().FetchByAppID(ctx, appID)
        if err != nil || !merchant.IsEnabled() {
            respondOpenAPIError(ctx, openapi.ErrorCodeInvalidAPIKey, "")
            ctx.Abort()
            return
        }
        
        // 3. IP白名单检查
        clientIP := ctx.ClientIP()
        if !checkIPWhitelist(merchant, clientIP) {
            respondOpenAPIError(ctx, openapi.ErrorCodeIPNotAllowed, "")
            ctx.Abort()
            return
        }
        
        // 4. 读取请求体
        bodyBytes, err := io.ReadAll(ctx.Request.Body)
        ctx.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))
        
        // 5. 验证时间戳（5分钟有效期）
        timestamp := parseTimestamp(requestData["timestamp"])
        now := time.Now().Unix()
        if timestamp < now-TimestampMaxDiff || timestamp > now+60 {
            respondOpenAPIError(ctx, openapi.ErrorCodeTimestampExpired, "")
            ctx.Abort()
            return
        }
        
        // 6. 验证Nonce防重放
        nonceKey := fmt.Sprintf("openapi:nonce:%s", nonce)
        if redis.GetRedisClient().Exists(ctx, nonceKey).Val() > 0 {
            respondOpenAPIError(ctx, openapi.ErrorCodeNonceDuplicate, "")
            ctx.Abort()
            return
        }
        
        // 7. 验证签名
        // 签名算法: SHA256(JSON_BODY + "&key=" + APP_SECRET)
        calculatedSign := calculateSimpleSignature(string(bodyBytes), merchant.AppSecret)
        if calculatedSign != signature {
            respondOpenAPIError(ctx, openapi.ErrorCodeSignError, "")
            ctx.Abort()
            return
        }
        
        // 8. 设置Nonce缓存（10分钟）
        redis.GetRedisClient().Set(ctx, nonceKey, "1", 600*time.Second)
        
        // 9. 设置商户信息到上下文
        ctx.Set("merchant", merchant)
        ctx.Set("app_id", appID)
        
        ctx.Next()
    }
}
```

### 签名算法

```go
func calculateSimpleSignature(jsonBody string, appSecret string) string {
    // 拼接签名字符串
    signStr := jsonBody + "&key=" + appSecret
    
    // SHA256加密
    hash := sha256.Sum256([]byte(signStr))
    signature := hex.EncodeToString(hash[:])
    
    return strings.ToUpper(signature)
}
```

### IP白名单检查

```go
func checkIPWhitelist(merchant *merchantDao.Model, clientIP string) (bool, error) {
    // 未设置白名单时默认放行
    if merchant.IpWhitelist == "" {
        return true, nil
    }
    
    whitelist := strings.Split(merchant.IpWhitelist, ",")
    for _, ipRule := range whitelist {
        // 1. 直接IP匹配
        if ipRule == clientIP {
            return true, nil
        }
        
        // 2. CIDR网段匹配
        if strings.Contains(ipRule, "/") {
            _, ipNet, err := net.ParseCIDR(ipRule)
            if err == nil {
                clientIPAddr := net.ParseIP(clientIP)
                if ipNet.Contains(clientIPAddr) {
                    return true, nil
                }
            }
        }
    }
    
    return false, nil
}
```

### OpenAPIRateLimit - API限流中间件

```go
func OpenAPIRateLimit() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        appID := ctx.GetString("app_id")
        merchant := ctx.Value("merchant").(*merchantDao.Model)
        
        // 1. QPS限流（每秒请求数）
        qpsKey := fmt.Sprintf("openapi:qps:%s:%d", appID, time.Now().Unix())
        count := redis.GetRedisClient().Incr(ctx, qpsKey).Result()
        if count > int64(merchant.QpsLimit) {
            respondOpenAPIError(ctx, openapi.ErrorCodeRateLimitExceed, "")
            ctx.Abort()
            return
        }
        
        // 2. 每日调用次数限制
        dailyKey := fmt.Sprintf("openapi:daily:%s:%s", appID, time.Now().Format("20060102"))
        dailyCount := redis.GetRedisClient().Incr(ctx, dailyKey).Result()
        if dailyCount > int64(merchant.DailyLimit) {
            respondOpenAPIError(ctx, openapi.ErrorCodeQuotaExceed, "")
            ctx.Abort()
            return
        }
        
        ctx.Next()
    }
}
```

## 限流中间件 (rate_limit.go)

### 限流配置结构

```go
type RateLimitConfig struct {
    // 短窗口限流
    ShortWindow time.Duration  // 如1分钟
    ShortLimit  int           // 如3次
    
    // 长窗口限流
    LongWindow  time.Duration  // 如10分钟
    LongLimit   int           // 如10次
    
    // 冷却时间
    CooldownTime time.Duration // 如30秒
    
    // 友好提示
    FriendlyMessage string
}
```

### 通用限流中间件

```go
func RateLimit(keyPrefix string, config *RateLimitConfig) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 1. 获取用户ID
        user, err := helper.GetCtxUser(ctx)
        if err != nil {
            ctx.Next()
            return
        }
        
        // 2. 检查冷却期
        cooldownKey := fmt.Sprintf("rate_limit:%s:cooldown:%d", keyPrefix, user.UID)
        if redis.GetRedisClient().Exists(ctx, cooldownKey).Val() > 0 {
            helper.AppResp(ctx, ecode.TooManyRequestsErr.Code(), config.FriendlyMessage)
            ctx.Abort()
            return
        }
        
        // 3. 短窗口限流检查
        shortKey := fmt.Sprintf("rate_limit:%s:short:%d", keyPrefix, user.UID)
        shortCount := incrementAndExpire(ctx, client, shortKey, config.ShortWindow)
        
        // 4. 长窗口限流检查
        longKey := fmt.Sprintf("rate_limit:%s:long:%d", keyPrefix, user.UID)
        longCount := incrementAndExpire(ctx, client, longKey, config.LongWindow)
        
        // 5. 判断是否触发限流
        if shortCount > config.ShortLimit || longCount > config.LongLimit {
            // 设置冷却时间
            redis.GetRedisClient().Set(ctx, cooldownKey, "1", config.CooldownTime)
            helper.AppResp(ctx, ecode.TooManyRequestsErr.Code(), config.FriendlyMessage)
            ctx.Abort()
            return
        }
        
        ctx.Next()
    }
}
```

### 原子性计数器

使用Lua脚本保证原子性：

```go
func incrementAndExpire(ctx *gin.Context, client *redis.RedisClient, key string, expiration time.Duration) (int, error) {
    luaScript := `
        local count = redis.call('INCR', KEYS[1])
        if count == 1 then
            redis.call('EXPIRE', KEYS[1], ARGV[1])
        end
        return count
    `
    
    result := client.Eval(ctx, luaScript, []string{key}, int(expiration.Seconds())).Result()
    return int(result.(int64)), nil
}
```

### 预定义限流配置

```go
// 取货码生成限流
var DefaultPickupCodeRateLimit = &RateLimitConfig{
    ShortWindow:     1 * time.Minute,  // 1分钟内
    ShortLimit:      3,                // 最多3次
    LongWindow:      10 * time.Minute, // 10分钟内
    LongLimit:       10,               // 最多10次
    CooldownTime:    30 * time.Second, // 冷却30秒
    FriendlyMessage: "为了您的账户安全，请稍后再试。系统检测到您刷新取货码过于频繁，请30秒后重试",
}

func PickupCodeRateLimit() gin.HandlerFunc {
    return RateLimit("pickup_code", DefaultPickupCodeRateLimit)
}
```

## 恢复中间件 (recovery.go)

### RecoveryWithWriter - Panic恢复

```go
func RecoveryWithWriter() gin.HandlerFunc {
    return func(c *gin.Context) {
        httpRequest, _ := httputil.DumpRequest(c.Request, true)
        
        defer func() {
            if err := recover(); err != nil {
                // 1. 检查是否是连接断开
                var brokenPipe bool
                if ne, ok := err.(*net.OpError); ok {
                    if se, ok := ne.Err.(*os.SyscallError); ok {
                        if strings.Contains(strings.ToLower(se.Error()), "broken pipe") {
                            brokenPipe = true
                        }
                    }
                }
                
                // 2. 获取调用栈
                stack := stack(3)
                
                // 3. 记录错误日志
                fields := logrus.Fields{
                    "error": err,
                    "stack": string(stack),
                    "now":   carbon.Now().ToDateTimeString(),
                }
                log.Ctx(c).WithFields(fields).Error("RecoveryWithWriter")
                
                // 4. 返回系统错误
                helper.AppResp(c, ecode.SystemErr.Code(), ecode.SystemErr.Message())
                
                if brokenPipe {
                    c.Abort()
                    return
                }
            }
        }()
        
        c.Next()
        
        // 5. 性能监控（响应时间>5秒告警）
        consumeTime := helper.GetElapsedTime(c)
        if consumeTime >= 5000 {
            log.Ctx(c).Warn("elapsed >= %d", 5000)
        }
    }
}
```

## 中间件开发规范

### 1. 基本结构

```go
func MiddlewareName(params ...interface{}) gin.HandlerFunc {
    // 初始化（只执行一次）
    // 例如：编译正则、加载配置等
    
    return func(ctx *gin.Context) {
        // 1. 检查是否已中断
        if ctx.IsAborted() {
            return
        }
        
        // 2. 前置处理
        // 例如：验证、记录等
        
        // 3. 决定是否继续
        if shouldAbort {
            helper.AppResp(ctx, code, message)
            ctx.Abort()
            return
        }
        
        // 4. 设置上下文信息
        ctx.Set("key", value)
        
        // 5. 继续处理
        ctx.Next()
        
        // 6. 后置处理（可选）
        // 例如：记录响应、清理资源等
    }
}
```

### 2. 错误处理

```go
// 统一使用helper.AppResp返回错误
helper.AppResp(ctx, ecode.ErrorCode.Code(), ecode.ErrorCode.Message())

// 中断请求链
ctx.Abort()

// 记录错误日志
log.Ctx(ctx).WithError(err).Error("MiddlewareName error")
```

### 3. 上下文传递

```go
// 设置值到上下文
ctx.Set("ctxUser", &helper.CtxUser{UID: userID})
ctx.Set("ctxAccount", &helper.CtxAccount{AccountID: accountID, RoleID: roleID})

// 从上下文获取值
user, _ := helper.GetCtxUser(ctx)
account, _ := helper.GetCtxAccount(ctx)
```

### 4. 请求体处理

```go
// 读取请求体时需要重新设置
body, err := io.ReadAll(ctx.Request.Body)
if err != nil {
    // 错误处理
}

// 重新设置请求体供后续使用
ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
```

### 5. 性能考虑

```go
// 异步处理非关键操作
go func() {
    // 记录日志、发送通知等
}()

// 使用缓存减少数据库访问
if cached := getFromCache(key); cached != nil {
    return cached
}

// 避免在中间件中做重操作
```

## 最佳实践

### 1. 中间件顺序

按照以下顺序组织中间件：

1. **全局中间件**: CORS、Recovery
2. **认证中间件**: 用户/管理员认证
3. **权限中间件**: 权限验证
4. **限流中间件**: 请求频率控制
5. **日志中间件**: 操作日志记录
6. **业务中间件**: 特定业务逻辑

### 2. 认证层级

- **公开接口**: 无需认证
- **可选认证**: `CheckUser()` - 登录用户有更多功能
- **必须认证**: `CheckUserLogin()` - 必须登录
- **权限认证**: `CheckAccountAuth()` - 需要特定权限

### 3. 错误响应

```go
// 统一错误格式
type Response struct {
    Code string      `json:"code"`
    Msg  string      `json:"msg"`
    Data interface{} `json:"data"`
    Time float64     `json:"time"`
}

// 使用ecode包定义的错误码
helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
```

### 4. 日志记录

```go
// 请求开始
log.Ctx(ctx).Info("AppServerStart")

// 请求结束
log.Ctx(ctx).Info("AppServerEnd")

// 错误日志
log.Ctx(ctx).WithError(err).Error("Error message")

// 带字段的日志
log.Ctx(ctx).WithFields(logrus.Fields{
    "userID": userID,
    "action": action,
}).Info("Action performed")
```

### 5. 安全考虑

- **Token验证**: 双重验证（解析+Redis校验）
- **防重放攻击**: 时间戳+Nonce机制
- **IP白名单**: 支持单个IP和CIDR网段
- **签名验证**: SHA256签名算法
- **限流保护**: 多级限流机制

## 扩展指南

### 添加新的中间件

1. **创建中间件文件**
```go
// app/middleware/new_middleware.go
package middleware

import "github.com/gin-gonic/gin"

func NewMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 实现逻辑
        ctx.Next()
    }
}
```

2. **注册到路由**
```go
// router/router.go
router.Use(middleware.NewMiddleware())
```

3. **配置中间件参数**
```go
func NewMiddleware(config *MiddlewareConfig) gin.HandlerFunc {
    // 使用配置
}
```

### 自定义认证中间件

```go
func CustomAuth(validator func(token string) (interface{}, error)) gin.HandlerFunc {
    return func(ctx *gin.Context) {
        token := ctx.GetHeader("Authorization")
        
        userInfo, err := validator(token)
        if err != nil {
            helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
            ctx.Abort()
            return
        }
        
        ctx.Set("userInfo", userInfo)
        ctx.Next()
    }
}
```

### 组合中间件

```go
// 组合多个中间件
func CombinedMiddleware() gin.HandlerFunc {
    return func(ctx *gin.Context) {
        // 依次执行多个中间件逻辑
        CheckUserLogin()(ctx)
        if ctx.IsAborted() {
            return
        }
        
        RateLimit("api", config)(ctx)
        if ctx.IsAborted() {
            return
        }
        
        ctx.Next()
    }
}
```

## 常见问题

### Q: 中间件执行顺序如何控制？
A: 按照注册顺序执行，Use()注册的先执行，路由组的中间件后执行。

### Q: 如何在中间件间传递数据？
A: 使用`ctx.Set()`和`ctx.Get()`或`ctx.Value()`方法。

### Q: 中间件中如何正确处理请求体？
A: 读取后必须重新设置：`ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))`

### Q: 如何实现条件中间件？
A: 在中间件内部判断条件，或使用闭包返回不同的HandlerFunc。

### Q: 中间件性能优化建议？
A: 
- 缓存验证结果
- 异步处理非关键操作
- 避免重复的数据库查询
- 使用连接池

## 注意事项

1. **检查Abort状态**: 始终检查`ctx.IsAborted()`避免重复处理
2. **错误处理统一**: 使用统一的错误响应格式
3. **日志记录规范**: 包含必要的上下文信息
4. **资源清理**: 在defer中清理资源
5. **并发安全**: 注意共享数据的并发访问
6. **性能影响**: 避免在中间件中做耗时操作
7. **安全验证**: 不要相信客户端数据，始终验证