package middleware

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net"
	"sort"
	"strconv"
	"strings"
	"time"

	merchantDao "blind_box/app/dao/openapi/merchant"
	"blind_box/app/dto/openapi"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
)

const (
	HeaderAppID     = "X-App-Id"
	HeaderSignature = "X-Signature"
	HeaderNonce     = "X-Nonce"

	TimestampMaxDiff = 300 // 5分钟
	NonceExpireTime  = 600 // 10分钟
)

// OpenAPIAuth OpenAPI认证中间件
func OpenAPIAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取请求头
		appID := ctx.GetHeader(HeaderAppID)
		signature := ctx.GetHeader(HeaderSignature)
		nonce := ctx.GetHeader(HeaderNonce)

		// 验证请求头
		if appID == "" || signature == "" || nonce == "" {
			log.Ctx(ctx).Error("OpenAPIAuth missing required headers")
			respondOpenAPIError(ctx, openapi.ErrorCodeInvalidAPIKey, "")
			ctx.Abort()
			return
		}

		// 查询商户信息
		merchant, err := merchantDao.GetRepo().FetchByAppID(ctx, appID)
		if err != nil || merchant == nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth fetch merchant failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeInvalidAPIKey, "")
			ctx.Abort()
			return
		}

		// 检查商户状态
		if !merchant.IsEnabled() {
			log.Ctx(ctx).Error("OpenAPIAuth merchant is disabled")
			respondOpenAPIError(ctx, openapi.ErrorCodeInvalidAPIKey, "")
			ctx.Abort()
			return
		}

		// 检查IP白名单（未设置白名单时默认放行所有IP）
		clientIP := ctx.ClientIP()
		allowed, err := checkIPWhitelist(merchant, clientIP)
		if err != nil || !allowed {
			log.Ctx(ctx).Error("OpenAPIAuth IP not allowed: %s", clientIP)
			respondOpenAPIError(ctx, openapi.ErrorCodeIPNotAllowed, "")
			ctx.Abort()
			return
		}

		// 读取请求体
		bodyBytes, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth read body failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeParamError, "")
			ctx.Abort()
			return
		}

		// 重新设置请求体，以便后续处理器可以读取
		ctx.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))

		// 解析请求参数获取timestamp和nonce用于验证
		var requestData map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &requestData); err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth parse JSON failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeParamError, "")
			ctx.Abort()
			return
		}

		// 验证时间戳
		timestampValue, exists := requestData["timestamp"]
		if !exists {
			log.Ctx(ctx).Error("OpenAPIAuth timestamp missing")
			debugInfo := map[string]interface{}{
				"error": "timestamp field missing in request body",
			}
			respondOpenAPIErrorWithDebug(ctx, openapi.ErrorCodeTimestampExpired, "", debugInfo)
			ctx.Abort()
			return
		}

		timestamp, err := parseTimestamp(timestampValue)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth parse timestamp failed")
			debugInfo := map[string]interface{}{
				"error":           "invalid timestamp format",
				"timestamp_value": timestampValue,
				"timestamp_type":  fmt.Sprintf("%T", timestampValue),
			}
			respondOpenAPIErrorWithDebug(ctx, openapi.ErrorCodeTimestampExpired, "", debugInfo)
			ctx.Abort()
			return
		}

		now := time.Now().Unix()
		if timestamp < now-TimestampMaxDiff || timestamp > now+60 {
			log.Ctx(ctx).Error("OpenAPIAuth timestamp expired: %d", timestamp)
			debugInfo := map[string]interface{}{
				"error":             "timestamp expired",
				"request_timestamp": timestamp,
				"server_timestamp":  now,
				"valid_range":       fmt.Sprintf("[%d, %d]", now-TimestampMaxDiff, now+60),
			}
			respondOpenAPIErrorWithDebug(ctx, openapi.ErrorCodeTimestampExpired, "", debugInfo)
			ctx.Abort()
			return
		}

		// 验证nonce
		nonceValue, exists := requestData["nonce"]
		if !exists {
			log.Ctx(ctx).Error("OpenAPIAuth nonce missing")
			respondOpenAPIError(ctx, openapi.ErrorCodeParamError, "")
			ctx.Abort()
			return
		}

		requestNonce, ok := nonceValue.(string)
		if !ok {
			log.Ctx(ctx).Error("OpenAPIAuth invalid nonce type")
			respondOpenAPIError(ctx, openapi.ErrorCodeParamError, "")
			ctx.Abort()
			return
		}

		if requestNonce != nonce {
			log.Ctx(ctx).Error("OpenAPIAuth nonce mismatch: header=%s, body=%s", nonce, requestNonce)
			respondOpenAPIError(ctx, openapi.ErrorCodeParamError, "")
			ctx.Abort()
			return
		}

		// 验证nonce是否重复
		nonceKey := fmt.Sprintf("openapi:nonce:%s", nonce)
		existsCount, err := redis.GetRedisClient().Exists(ctx, nonceKey).Result()
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth check nonce failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeSystemError, "")
			ctx.Abort()
			return
		}

		if existsCount > 0 {
			log.Ctx(ctx).Error("OpenAPIAuth nonce duplicate: %s", nonce)
			respondOpenAPIError(ctx, openapi.ErrorCodeNonceDuplicate, "")
			ctx.Abort()
			return
		}

		// 验证签名 - 使用简化的算法
		fmt.Println("Calculating signature for body:", string(bodyBytes))
		calculatedSign := calculateSimpleSignature(string(bodyBytes), merchant.AppSecret)
		if calculatedSign != signature {
			log.Ctx(ctx).Error("OpenAPIAuth signature mismatch: expected=%s, actual=%s", calculatedSign, signature)

			// 提供调试信息
			debugInfo := map[string]interface{}{
				"expected_signature":  calculatedSign,
				"actual_signature":    signature,
				"request_body_length": len(bodyBytes),
				"signature_algorithm": "SHA256(JSON_BODY + '&key=' + APP_SECRET)",
			}

			respondOpenAPIErrorWithDebug(ctx, openapi.ErrorCodeSignError, "", debugInfo)
			ctx.Abort()
			return
		}

		// 设置nonce缓存，防止重放
		err = redis.GetRedisClient().Set(ctx, nonceKey, "1", time.Duration(NonceExpireTime)*time.Second).Err()
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIAuth set nonce cache failed")
		}

		// 将商户信息存入上下文
		ctx.Set("merchant", merchant)
		ctx.Set("app_id", appID)

		ctx.Next()
	}
}

// calculateSimpleSignature 简化的签名算法，直接对JSON字符串签名
func calculateSimpleSignature(jsonBody string, appSecret string) string {
	// 直接对JSON字符串 + 密钥进行签名
	signStr := jsonBody + "&key=" + appSecret

	// SHA256加密
	hash := sha256.Sum256([]byte(signStr))
	signature := hex.EncodeToString(hash[:])

	return strings.ToUpper(signature)
}

// parseTimestamp 解析时间戳，支持多种类型
func parseTimestamp(value interface{}) (int64, error) {
	switch v := value.(type) {
	case float64:
		return int64(v), nil
	case int64:
		return v, nil
	case int:
		return int64(v), nil
	case string:
		return strconv.ParseInt(v, 10, 64)
	default:
		return 0, fmt.Errorf("invalid timestamp type: %T", value)
	}
}

// calculateSignature 原有的复杂签名算法，保留用于向后兼容
func calculateSignature(params map[string]string, appSecret string) string {
	// 参数排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 拼接参数
	var pairs []string
	for _, k := range keys {
		pairs = append(pairs, fmt.Sprintf("%s=%s", k, params[k]))
	}
	signStr := strings.Join(pairs, "&") + "&key=" + appSecret

	// SHA256加密
	hash := sha256.Sum256([]byte(signStr))
	signature := hex.EncodeToString(hash[:])

	return strings.ToUpper(signature)
}

// parseRequestParams 解析请求参数，将JSON展平为key=value形式用于签名验证
func parseRequestParams(body string) (map[string]string, error) {
	params := make(map[string]string)

	if body == "" {
		return params, nil
	}

	// 解析JSON到interface{}
	var data interface{}
	if err := json.Unmarshal([]byte(body), &data); err != nil {
		return nil, fmt.Errorf("invalid JSON: %w", err)
	}

	// 递归展平JSON结构
	flattenJSON(data, "", params)

	return params, nil
}

// flattenJSON 递归展平JSON结构
func flattenJSON(data interface{}, prefix string, result map[string]string) {
	switch v := data.(type) {
	case map[string]interface{}:
		for key, value := range v {
			newKey := key
			if prefix != "" {
				newKey = prefix + "." + key
			}
			flattenJSON(value, newKey, result)
		}
	case []interface{}:
		for i, value := range v {
			newKey := fmt.Sprintf("%s[%d]", prefix, i)
			flattenJSON(value, newKey, result)
		}
	case nil:
		result[prefix] = ""
	default:
		// 将所有基础类型转换为字符串
		result[prefix] = fmt.Sprintf("%v", v)
	}
}

// checkIPWhitelist 检查IP白名单
func checkIPWhitelist(merchant *merchantDao.Model, clientIP string) (bool, error) {
	// 如果没有设置白名单，默认允许所有IP
	if merchant.IpWhitelist == "" {
		return true, nil
	}

	// 解析白名单
	whitelist := strings.Split(merchant.IpWhitelist, ",")
	for _, ipRule := range whitelist {
		ipRule = strings.TrimSpace(ipRule)
		if ipRule == "" {
			continue
		}

		// 直接IP匹配
		if ipRule == clientIP {
			return true, nil
		}

		// CIDR网段匹配
		if strings.Contains(ipRule, "/") {
			_, ipNet, err := net.ParseCIDR(ipRule)
			if err != nil {
				continue // 跳过无效的CIDR格式
			}
			clientIPAddr := net.ParseIP(clientIP)
			if clientIPAddr != nil && ipNet.Contains(clientIPAddr) {
				return true, nil
			}
		}
	}

	return false, nil
}

// OpenAPIRateLimit OpenAPI限流中间件
func OpenAPIRateLimit() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		appID := ctx.GetString("app_id")
		if appID == "" {
			ctx.Next()
			return
		}

		// 获取商户信息
		merchant, exists := ctx.Get("merchant")
		if !exists {
			ctx.Next()
			return
		}

		merchantModel := merchant.(*merchantDao.Model)

		// QPS限流
		qpsKey := fmt.Sprintf("openapi:qps:%s:%d", appID, time.Now().Unix())
		count, err := redis.GetRedisClient().Incr(ctx, qpsKey).Result()
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIRateLimit incr qps failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeSystemError, "")
			ctx.Abort()
			return
		}

		// 设置过期时间
		if count == 1 {
			redis.GetRedisClient().Expire(ctx, qpsKey, 2*time.Second)
		}

		// 检查QPS限制
		if count > int64(merchantModel.QpsLimit) {
			log.Ctx(ctx).Error("OpenAPIRateLimit QPS exceed: %d > %d", count, merchantModel.QpsLimit)
			respondOpenAPIError(ctx, openapi.ErrorCodeRateLimitExceed, "")
			ctx.Abort()
			return
		}

		// 每日调用次数限制
		dailyKey := fmt.Sprintf("openapi:daily:%s:%s", appID, time.Now().Format("20060102"))
		dailyCount, err := redis.GetRedisClient().Incr(ctx, dailyKey).Result()
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OpenAPIRateLimit incr daily failed")
			respondOpenAPIError(ctx, openapi.ErrorCodeSystemError, "")
			ctx.Abort()
			return
		}

		// 设置过期时间
		if dailyCount == 1 {
			redis.GetRedisClient().Expire(ctx, dailyKey, 25*time.Hour)
		}

		// 检查每日限制
		if dailyCount > int64(merchantModel.DailyLimit) {
			log.Ctx(ctx).Error("OpenAPIRateLimit daily limit exceed: %d > %d", dailyCount, merchantModel.DailyLimit)
			respondOpenAPIError(ctx, openapi.ErrorCodeQuotaExceed, "")
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}

// GetMerchantFromContext 从上下文获取商户信息
func GetMerchantFromContext(ctx *gin.Context) (*merchantDao.Model, error) {
	merchant, exists := ctx.Get("merchant")
	if !exists {
		return nil, fmt.Errorf("merchant not found in context")
	}

	merchantModel, ok := merchant.(*merchantDao.Model)
	if !ok {
		return nil, fmt.Errorf("invalid merchant type in context")
	}

	return merchantModel, nil
}

// respondOpenAPIError 统一错误响应格式
func respondOpenAPIError(ctx *gin.Context, code int, msg string) {
	respondOpenAPIErrorWithDebug(ctx, code, msg, nil)
}

// respondOpenAPIErrorWithDebug 带调试信息的错误响应
func respondOpenAPIErrorWithDebug(ctx *gin.Context, code int, msg string, debugInfo map[string]interface{}) {
	if msg == "" {
		msg = openapi.GetErrorMessage(code)
	}

	response := gin.H{
		"code":      code,
		"msg":       msg,
		"data":      nil,
		"timestamp": time.Now().Unix(),
	}

	// 在开发环境或测试环境提供调试信息
	if debugInfo != nil && len(debugInfo) > 0 {
		response["debug"] = debugInfo
	}

	ctx.JSON(200, response)
}
