package middleware

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

func CheckScriptAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountAuth ctx.IsAborted")
			return
		}

		ctxAccount, err := helper.GetCtxAccount(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
		if ctxAccount.RoleID != dbs.RoleRootID && ctxAccount.RoleID != dbs.RoleAdminID {
			helper.AppResp(ctx, ecode.NoAuthErr.Code(), ecode.NoAuthErr.Message())
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}
