package middleware

import (
	accountDao "blind_box/app/dao/admin/account"
	// adminMenu "blind_box/app/dao/admin_menu"
	// adminRole "blind_box/app/dao/admin_role"
	// "blind_box/app/service/admin"
	"blind_box/app/service/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util"
	"bytes"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

// CheckAccountLogin .
func CheckAccountLogin() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountLogin ctx.IsAborted")
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		appClaims, err := util.ParseToken(authToken)
		if err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithError(err).WithField("token", authToken).Error("util.ParseToken err")
			ctx.Abort()
			return
		}
		log.WithField(ctx.Request.Context(), "appClaims", appClaims).Info("util.ParseToken ret")

		token, err := accountDao.GetRepo().RedisGetAccountToken(ctx, appClaims.UserID)
		if token == "" || err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithField("token", token).Warn("RedisGetAccountToken err")
			ctx.Abort()
			return
		}

		if token != authToken {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}

		// if !adminRole.GetRepo().JudgeRoleIsValid(ctx, appClaims.RoleID) {
		// 	adminAccount.GetRepo().RedisClearAccountToken(ctx, appClaims.UserID)
		// 	helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		// 	ctx.Abort()
		// 	return
		// }

		ctx.Set("ctxAccount", &helper.CtxAccount{
			AccountID: appClaims.UserID,
			RoleID:    appClaims.RoleID,
		})

		ctx.Next()
	}
}

// CheckAccountAuth .
func CheckAccountAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountAuth ctx.IsAborted")
			return
		}

		// TODO 调用接口修改, 由接口校验权限

		// ctxAccount, err := helper.GetCtxAccount(ctx)
		// if err != nil {
		// 	helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		// 	ctx.Abort()
		// 	return
		// }

		// if admin.GetService().JudgeIsAdmin(ctxAccount.RoleID) {
		// 	return
		// }
		// var (
		// 	path               = strings.TrimPrefix(ctx.Request.URL.Path, "/")
		// 	matchMenuList      = adminMenu.ModelList{}
		// 	matchMenuIds       = []uint64{}
		// 	menuList           = adminMenu.ModelList{}
		// 	roleMenuIds        = []uint64{}
		// 	hasAuth       bool = false
		// )

		// if menuList, err = adminMenu.GetRepo().RedisMenuList(ctx); err != nil {
		// 	helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		// 	ctx.Abort()
		// 	return
		// }

		// for _, menu := range menuList {
		// 	beAuth := strings.TrimPrefix(menu.BeAuth, "/")
		// 	if fmt.Sprintf("%s/%s", config.AppCfg.AppName, beAuth) == path {
		// 		matchMenuList = append(matchMenuList, menu)
		// 		matchMenuIds = append(matchMenuIds, menu.ID)
		// 		if menu.IsRoot == dbs.True {
		// 			helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		// 			ctx.Abort()
		// 			return
		// 		}
		// 	}
		// }

		// if len(matchMenuList) == 0 {
		// 	helper.AppResp(ctx, ecode.MenuNotExistErr.Code(), ecode.MenuNotExistErr.Message())
		// 	ctx.Abort()
		// 	return
		// }

		// if roleMenuIds, err = adminRole.GetRepo().RedisRoleMenuIds(ctx, ctxAccount.RoleID); err != nil {
		// 	helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		// 	ctx.Abort()
		// 	return
		// }

		// for _, roleMenuId := range roleMenuIds {
		// 	if lo.Contains[uint64](matchMenuIds, roleMenuId) {
		// 		hasAuth = true
		// 		break
		// 	}
		// }

		// if !hasAuth {
		// 	helper.AppResp(ctx, ecode.MenuHasNotAuthErr.Code(), ecode.MenuHasNotAuthErr.Message())
		// 	ctx.Abort()
		// 	return
		// }

		ctx.Next()
	}
}

// AdminOperationLog .
func AdminOperationLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.Method == http.MethodGet {
			ctx.Next()
			return
		}

		// 获取请求头与请求体
		reqHeaders := ctx.Request.Header
		headers, _ := json.Marshal(&reqHeaders)

		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		ctx.Next()

		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("AdminOperationLog ctx.IsAborted")
			return
		}

		isAbort, err := admin.GetAdminLogEntity().Producer(ctx, headers, body)
		if isAbort {
			ctx.Abort()
			return
		}

		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
	}
}
