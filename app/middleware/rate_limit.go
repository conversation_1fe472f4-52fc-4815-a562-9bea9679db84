package middleware

import (
	"fmt"
	"strconv"
	"time"

	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
)

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	// 基础限流：短时间内允许的请求次数
	ShortWindow time.Duration // 短窗口时间，如1分钟
	ShortLimit  int           // 短窗口内允许的请求次数，如5次

	// 长期限流：较长时间内允许的请求次数
	LongWindow time.Duration // 长窗口时间，如10分钟
	LongLimit  int           // 长窗口内允许的请求次数，如15次

	// 冷却时间：触发限流后的等待时间
	CooldownTime time.Duration // 冷却时间，如30秒

	// 用户友好提示
	FriendlyMessage string // 触发限流时的友好提示
}

// DefaultPickupCodeRateLimit 取货码生成的默认限流配置
var DefaultPickupCodeRateLimit = &RateLimitConfig{
	ShortWindow:     1 * time.Minute,  // 1分钟内
	ShortLimit:      3,                // 最多3次
	LongWindow:      10 * time.Minute, // 10分钟内
	LongLimit:       10,               // 最多10次
	CooldownTime:    30 * time.Second, // 冷却30秒
	FriendlyMessage: "为了您的账户安全，请稍后再试。系统检测到您刷新取货码过于频繁，请30秒后重试",
}

// PickupCodeRateLimit 取货码生成限流中间件
func PickupCodeRateLimit() gin.HandlerFunc {
	return RateLimit("pickup_code", DefaultPickupCodeRateLimit)
}

// RateLimit 通用限流中间件
func RateLimit(keyPrefix string, config *RateLimitConfig) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取用户ID
		user, err := helper.GetCtxUser(ctx)
		if err != nil {
			// 如果无法获取用户ID，跳过限流（让后续处理）
			ctx.Next()
			return
		}

		userID := user.UID
		if err := checkRateLimit(ctx, keyPrefix, userID, config); err != nil {
			// 触发限流，返回友好错误信息
			helper.AppResp(ctx, ecode.TooManyRequestsErr.Code(), config.FriendlyMessage)
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}

// checkRateLimit 检查用户是否触发限流
func checkRateLimit(ctx *gin.Context, keyPrefix string, userID uint64, config *RateLimitConfig) error {
	client := redis.GetRedisClient()

	// 检查是否在冷却期
	cooldownKey := fmt.Sprintf("rate_limit:%s:cooldown:%d", keyPrefix, userID)
	if client.Exists(ctx, cooldownKey).Val() > 0 {
		log.Ctx(ctx).Warn("User in cooldown period", "userID", userID, "keyPrefix", keyPrefix)
		return fmt.Errorf("rate limit cooldown")
	}

	// 短窗口限流检查
	shortKey := fmt.Sprintf("rate_limit:%s:short:%d", keyPrefix, userID)
	shortCount, err := incrementAndExpire(ctx, client, shortKey, config.ShortWindow)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to check short window rate limit")
		// 限流检查失败，为了安全起见，允许请求通过
		return nil
	}

	// 长窗口限流检查
	longKey := fmt.Sprintf("rate_limit:%s:long:%d", keyPrefix, userID)
	longCount, err := incrementAndExpire(ctx, client, longKey, config.LongWindow)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to check long window rate limit")
		return nil
	}

	// 判断是否触发限流
	if shortCount > config.ShortLimit {
		// 短时间内请求过多，设置冷却时间
		client.Set(ctx, cooldownKey, "1", config.CooldownTime)
		log.Ctx(ctx).Warn("User triggered short window rate limit",
			"userID", userID,
			"shortCount", shortCount,
			"shortLimit", config.ShortLimit,
			"cooldownTime", config.CooldownTime)
		return fmt.Errorf("short window rate limit exceeded")
	}

	if longCount > config.LongLimit {
		// 长时间内请求过多，设置冷却时间
		client.Set(ctx, cooldownKey, "1", config.CooldownTime)
		log.Ctx(ctx).Warn("User triggered long window rate limit",
			"userID", userID,
			"longCount", longCount,
			"longLimit", config.LongLimit,
			"cooldownTime", config.CooldownTime)
		return fmt.Errorf("long window rate limit exceeded")
	}

	// 记录正常请求
	log.Ctx(ctx).Info("Rate limit check passed",
		"userID", userID,
		"shortCount", shortCount,
		"longCount", longCount,
		"keyPrefix", keyPrefix)

	return nil
}

// incrementAndExpire 原子性地增加计数器并设置过期时间
func incrementAndExpire(ctx *gin.Context, client *redis.RedisClient, key string, expiration time.Duration) (int, error) {
	// 使用 Lua 脚本确保原子性
	luaScript := `
		local count = redis.call('INCR', KEYS[1])
		if count == 1 then
			redis.call('EXPIRE', KEYS[1], ARGV[1])
		end
		return count
	`

	result, err := client.Eval(ctx, luaScript, []string{key}, int(expiration.Seconds())).Result()
	if err != nil {
		return 0, err
	}

	count, ok := result.(int64)
	if !ok {
		// 尝试转换字符串
		if str, isStr := result.(string); isStr {
			if parsed, parseErr := strconv.ParseInt(str, 10, 64); parseErr == nil {
				count = parsed
			} else {
				return 0, fmt.Errorf("unexpected result type: %T", result)
			}
		} else {
			return 0, fmt.Errorf("unexpected result type: %T", result)
		}
	}

	return int(count), nil
}

// GetRateLimitStatus 获取用户当前限流状态（用于前端显示）
func GetRateLimitStatus(ctx *gin.Context, keyPrefix string, userID uint64, config *RateLimitConfig) map[string]interface{} {
	client := redis.GetRedisClient()

	// 检查冷却状态
	cooldownKey := fmt.Sprintf("rate_limit:%s:cooldown:%d", keyPrefix, userID)
	cooldownTTL := client.TTL(ctx, cooldownKey).Val()

	// 获取当前计数
	shortKey := fmt.Sprintf("rate_limit:%s:short:%d", keyPrefix, userID)
	longKey := fmt.Sprintf("rate_limit:%s:long:%d", keyPrefix, userID)

	shortCount, _ := strconv.Atoi(client.Get(ctx, shortKey).Val())
	longCount, _ := strconv.Atoi(client.Get(ctx, longKey).Val())

	return map[string]interface{}{
		"shortCount":      shortCount,
		"shortLimit":      config.ShortLimit,
		"shortRemaining":  max(0, config.ShortLimit-shortCount),
		"longCount":       longCount,
		"longLimit":       config.LongLimit,
		"longRemaining":   max(0, config.LongLimit-longCount),
		"inCooldown":      cooldownTTL > 0,
		"cooldownSeconds": max(0, int(cooldownTTL.Seconds())),
		"canRequest":      cooldownTTL <= 0 && shortCount < config.ShortLimit && longCount < config.LongLimit,
	}
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
