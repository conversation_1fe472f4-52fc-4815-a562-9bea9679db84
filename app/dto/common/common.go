package common

const (
	OperateNothing = "nothing"
	OperateSuccess = "success"
	OperateError   = "error"
)

type DataListReq struct {
	Page  int `form:"page,default=1" json:"page,default=1" binding:"default=1"`
	Limit int `form:"limit,default=20" json:"limit,default=20" binding:"default=20"`
}

type IdReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type BaseTimeRangeReq struct {
	StartTime int64 `form:"start_time" json:"start_time" binding:"required" msg:"开始时间不能为空"`
	EndTime   int64 `form:"end_time" json:"end_time" binding:"required,gtefield=StartTime,withinOneYear" msg:"结束时间必须大于开始时间且时间跨度不超过1年"`
}
