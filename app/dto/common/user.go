package common

type UserIDReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
}

type CommonUserInfo struct {
	ID            uint64 `json:"id"`
	Account       string `json:"account"`
	Name          string `json:"name"`
	Mobile        string `json:"mobile"`
	Balance       uint64 `json:"balance"`
	CompanyID     uint64 `json:"company_id"`
	CompanyName   string `json:"company_name"`
	UserTypeID    uint64 `json:"user_type_id"`
	UserTypeName  string `json:"user_type_name"`
	ManagerID     uint64 `json:"manager_id"`
	ManagerName   string `json:"manager_name"`
	BuyLevelID    uint64 `json:"buy_level_id"`
	BuyLevelName  string `json:"buy_level_name"`
	CreditType    uint32 `json:"credit_type"`
	Status        uint32 `json:"status"`
	IdType        uint32 `json:"id_type"`
	UserType      uint32 `json:"user_type"`
	Deposit       uint64 `json:"deposit"`
	LastLoginTime string `json:"last_login_time"`
	CreatedAt     string `json:"created_at"`
}

type CommonUserAddrInfo struct {
	ID        uint64 `json:"id"`
	UserID    uint64 `json:"user_id"`
	Consignee string `json:"consignee"`
	Mobile    string `json:"mobile"`
	Area      string `json:"area"`
	Address   string `json:"address"`
	IsDefault uint32 `json:"is_default"`
	CreatedAt string `json:"created_at"`
}
