package common

type CommonSpuSkuFilterReq struct {
	SpuID      uint64   `form:"spu_id" json:"spu_id"`
	SpuIds     []uint64 `form:"spu_ids" json:"spu_ids"`
	SpuCode    string   `form:"spu_code" json:"spu_code"`
	SpuTitle   string   `form:"spu_title" json:"spu_title"`
	SkuID      uint64   `form:"sku_id" json:"sku_id"`
	SkuIds     []uint64 `form:"sku_ids" json:"sku_ids"`
	SkuCode    string   `form:"sku_code" json:"sku_code"`
	SkuTitle   string   `form:"sku_title" json:"sku_title"`
	CategoryID uint64   `form:"category_id" json:"category_id"`
}

type GoodsTagResp struct {
	ID      uint64 `json:"id"`
	Name    string `json:"name"`
	GroupID uint32 `json:"group_id"`
}
