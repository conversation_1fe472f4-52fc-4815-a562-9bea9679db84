package openapi

import (
	"errors"
	"regexp"
	"time"
)

// 订单状态常量定义在 app/dao/order/order/model.go
// OrderStatusCreated  = 1 // 订单生成（待支付）
// OrderStatusPayIng   = 2 // 支付中
// OrderStatusPayOk    = 3 // 支付成功
// OrderStatusPayFail  = 4 // 支付失败
// OrderStatusRefund   = 5 // 已退款
// OrderStatusCancel   = 6 // 订单取消/关闭
// OrderStatusDone     = 7 // 订单完成
// OrderStatusDelivery = 8 // 订单发货

// GoodsItem 商品项
type GoodsItem struct {
	Code string `json:"code" binding:"required,min=1,max=64"` // 商品条码
	Num  int    `json:"num" binding:"required,min=1,max=999"` // 商品数量
}

// CreateScanOrderReq 扫码下单请求
type CreateScanOrderReq struct {
	Timestamp int64                  `json:"timestamp" binding:"required"`             // 请求时间戳
	Nonce     string                 `json:"nonce" binding:"required,len=32"`          // 随机字符串，32位
	OrderNo   string                 `json:"order_no" binding:"required,min=1,max=64"` // POS机订单号
	ShopID    uint64                 `json:"shop_id" binding:"required"`               // 店铺ID
	Goods     []GoodsItem            `json:"goods" binding:"required,min=1,max=50"`    // 商品列表
	Extra     map[string]interface{} `json:"extra,omitempty"`                          // 额外参数，第三方可传入相关字段
}

// Validate 自定义验证
func (req *CreateScanOrderReq) Validate() error {
	// 验证时间戳（5分钟内有效）
	now := time.Now().Unix()
	if req.Timestamp < now-300 || req.Timestamp > now+60 {
		return errors.New("timestamp is invalid or expired")
	}

	// 验证nonce格式（字母数字）
	if !regexp.MustCompile("^[a-zA-Z0-9]{32}$").MatchString(req.Nonce) {
		return errors.New("nonce must be 32 alphanumeric characters")
	}

	// 验证订单号格式
	if !regexp.MustCompile("^[a-zA-Z0-9_-]+$").MatchString(req.OrderNo) {
		return errors.New("order_no contains invalid characters")
	}

	// 验证商品列表
	for _, item := range req.Goods {
		if item.Code == "" {
			return errors.New("goods code cannot be empty")
		}
		if len(item.Code) > 64 {
			return errors.New("goods code too long")
		}
		if item.Num <= 0 {
			return errors.New("goods quantity must be greater than 0")
		}
		if item.Num > 999 {
			return errors.New("goods quantity exceeds maximum limit")
		}
	}

	return nil
}

// CreateScanOrderRes 扫码下单响应
type CreateScanOrderRes struct {
	Code      int                  `json:"code"`      // 响应码
	Msg       string               `json:"msg"`       // 响应消息
	Data      *CreateScanOrderData `json:"data"`      // 响应数据
	Timestamp int64                `json:"timestamp"` // 响应时间戳
	Sign      string               `json:"sign"`      // 响应签名
}

// CreateScanOrderData 扫码下单响应数据
type CreateScanOrderData struct {
	OrderID        string `json:"order_id"`        // 系统订单号
	PosOrderNo     string `json:"pos_order_no"`    // POS机订单号
	MiniProgramUrl string `json:"miniprogram_url"` // 小程序支付页面链接
	QrCode         string `json:"qr_code"`         // 二维码内容（用户扫码跳转）
	TotalAmount    uint64 `json:"total_amount"`    // 订单总金额（分）
	ExpireTime     int64  `json:"expire_time"`     // 支付过期时间戳
	CreatedAt      int64  `json:"created_at"`      // 订单创建时间戳
}

// QueryScanOrderReq 查询扫码订单请求
type QueryScanOrderReq struct {
	Timestamp int64  `json:"timestamp" binding:"required"`             // 请求时间戳
	Nonce     string `json:"nonce" binding:"required,len=32"`          // 随机字符串，32位
	OrderNo   string `json:"order_no" binding:"required,min=1,max=64"` // POS机订单号
}

// Validate 自定义验证
func (req *QueryScanOrderReq) Validate() error {
	// 验证时间戳（5分钟内有效）
	now := time.Now().Unix()
	if req.Timestamp < now-300 || req.Timestamp > now+60 {
		return errors.New("timestamp is invalid or expired")
	}

	// 验证nonce格式（字母数字）
	if !regexp.MustCompile("^[a-zA-Z0-9]{32}$").MatchString(req.Nonce) {
		return errors.New("nonce must be 32 alphanumeric characters")
	}

	// 验证订单号格式
	if !regexp.MustCompile("^[a-zA-Z0-9_-]+$").MatchString(req.OrderNo) {
		return errors.New("order_no contains invalid characters")
	}

	return nil
}

// QueryScanOrderRes 查询扫码订单响应
type QueryScanOrderRes struct {
	Code      int                 `json:"code"`      // 响应码
	Msg       string              `json:"msg"`       // 响应消息
	Data      *QueryScanOrderData `json:"data"`      // 响应数据
	Timestamp int64               `json:"timestamp"` // 响应时间戳
	Sign      string              `json:"sign"`      // 响应签名
}

// QueryScanOrderData 查询扫码订单响应数据
type QueryScanOrderData struct {
	OrderID       string `json:"order_id"`                 // 系统订单号
	PosOrderNo    string `json:"pos_order_no"`             // POS机订单号
	Status        uint32 `json:"status"`                   // 订单状态：1-订单生成,2-支付中,3-支付成功,4-支付失败,5-已退款,6-订单取消/关闭,7-订单完成,8-订单发货
	TotalAmount   uint64 `json:"total_amount"`             // 订单总金额（分）
	PaidAmount    uint64 `json:"paid_amount,omitempty"`    // 实际支付金额（分）
	PaidTime      int64  `json:"paid_time,omitempty"`      // 支付时间戳
	PaymentMethod string `json:"payment_method,omitempty"` // 支付方式
	TransactionID string `json:"transaction_id,omitempty"` // 第三方支付流水号
	ExpireTime    int64  `json:"expire_time"`              // 支付过期时间戳
	CreatedAt     int64  `json:"created_at"`               // 订单创建时间戳
}
