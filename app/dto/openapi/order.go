package openapi

import (
	"errors"
	"fmt"
	"regexp"
	"time"
)

// CreateOrderReq 创建订单请求
type CreateOrderReq struct {
	Timestamp       int64             `json:"timestamp" binding:"required"`                      // 请求时间戳
	Nonce           string            `json:"nonce" binding:"required,len=32"`                   // 随机字符串，32位
	MerchantOrderNo string            `json:"merchant_order_no" binding:"required,min=1,max=64"` // 第三方订单号
	CallbackUrl     string            `json:"callback_url" binding:"required,url"`               // 支付回调地址
	ReturnUrl       string            `json:"return_url" binding:"required,url"`                 // 支付完成跳转地址
	OrderInfo       *OrderInfo        `json:"order_info" binding:"required"`                     // 订单信息
	UserInfo        *UserInfo         `json:"user_info" binding:"required"`                      // 用户信息
	Payment         *PaymentInfo      `json:"payment" binding:"required"`                        // 支付信息
	Delivery        *DeliveryInfo     `json:"delivery"`                                          // 配送信息
	Extra           map[string]string `json:"extra"`                                             // 扩展信息
}

// OrderInfo 订单信息
type OrderInfo struct {
	Items       []*OrderItem `json:"items" binding:"required,min=1,max=50,dive"`            // 商品列表
	TotalAmount uint64       `json:"total_amount" binding:"required,min=1,max=10000000000"` // 订单总金额（分）
	Currency    string       `json:"currency" binding:"required,eq=CNY"`                    // 币种
	Subject     string       `json:"subject" binding:"required,min=1,max=32"`               // 订单标题
	Description string       `json:"description" binding:"max=128"`                         // 订单描述
	ExpireTime  int32        `json:"expire_time" binding:"min=300,max=3600"`                // 过期时间（秒）
}

// OrderItem 订单商品项
type OrderItem struct {
	SkuID    uint64 `json:"sku_id" binding:"required"`                  // SKU ID
	Quantity uint32 `json:"quantity" binding:"required,min=1,max=9999"` // 购买数量
	Price    uint64 `json:"price" binding:"required,min=1"`             // 单价（分）
	Name     string `json:"name" binding:"required,min=1,max=64"`       // 商品名称
}

// UserInfo 用户信息
type UserInfo struct {
	OpenID   string `json:"openid" binding:"required,min=1,max=128"` // 第三方用户标识
	Mobile   string `json:"mobile" binding:"omitempty,mobile"`       // 手机号
	Nickname string `json:"nickname" binding:"max=32"`               // 用户昵称
}

// PaymentInfo 支付信息
type PaymentInfo struct {
	Method    string                 `json:"method" binding:"required,oneof=wechat_h5 wechat_native wechat_jsapi alipay_h5 alipay_qr"` // 支付方式
	Channel   string                 `json:"channel" binding:"required"`                                                               // 支付渠道
	SceneInfo map[string]interface{} `json:"scene_info"`                                                                               // 场景信息
}

// DeliveryInfo 配送信息
type DeliveryInfo struct {
	Type      uint32        `json:"type" binding:"omitempty,oneof=1 2"` // 配送方式：1-快递，2-到店自取
	AddressID uint64        `json:"address_id"`                         // 收货地址ID
	ShopID    uint64        `json:"shop_id"`                            // 自取店铺ID
	Receiver  *ReceiverInfo `json:"receiver"`                           // 收货人信息
}

// ReceiverInfo 收货人信息
type ReceiverInfo struct {
	Name     string `json:"name" binding:"required,min=1,max=32"`     // 收件人姓名
	Mobile   string `json:"mobile" binding:"required,mobile"`         // 手机号
	Province string `json:"province" binding:"required,min=1,max=32"` // 省份
	City     string `json:"city" binding:"required,min=1,max=32"`     // 城市
	District string `json:"district" binding:"required,min=1,max=32"` // 区县
	Address  string `json:"address" binding:"required,min=1,max=128"` // 详细地址
}

// Validate 自定义验证
func (req *CreateOrderReq) Validate() error {
	// 验证时间戳（5分钟内有效）
	now := time.Now().Unix()
	if req.Timestamp < now-300 || req.Timestamp > now+60 {
		return errors.New("timestamp is invalid or expired")
	}

	// 验证nonce格式（字母数字）
	if !regexp.MustCompile("^[a-zA-Z0-9]{32}$").MatchString(req.Nonce) {
		return errors.New("nonce must be 32 alphanumeric characters")
	}

	// 验证商户订单号格式
	if !regexp.MustCompile("^[a-zA-Z0-9_-]+$").MatchString(req.MerchantOrderNo) {
		return errors.New("merchant_order_no contains invalid characters")
	}

	// 验证订单金额
	totalAmount := uint64(0)
	for i, item := range req.OrderInfo.Items {
		itemTotal := item.Price * uint64(item.Quantity)
		if itemTotal < item.Price { // 溢出检查
			return fmt.Errorf("item %d quantity too large", i)
		}
		totalAmount += itemTotal
	}

	if totalAmount != req.OrderInfo.TotalAmount {
		return fmt.Errorf("total_amount mismatch: calculated=%d, provided=%d", totalAmount, req.OrderInfo.TotalAmount)
	}

	// 验证配送信息
	if req.Delivery != nil {
		if req.Delivery.Type == 1 && req.Delivery.AddressID == 0 && req.Delivery.Receiver == nil {
			return errors.New("express delivery requires address_id or receiver info")
		}
		if req.Delivery.Type == 2 && req.Delivery.ShopID == 0 {
			return errors.New("store pickup requires shop_id")
		}
	}

	// 设置默认过期时间
	if req.OrderInfo.ExpireTime == 0 {
		req.OrderInfo.ExpireTime = 900 // 默认15分钟
	}

	return nil
}

// CreateOrderRes 创建订单响应
type CreateOrderRes struct {
	Code      int              `json:"code"`      // 响应码
	Msg       string           `json:"msg"`       // 响应消息
	Data      *CreateOrderData `json:"data"`      // 响应数据
	Timestamp int64            `json:"timestamp"` // 响应时间戳
	Sign      string           `json:"sign"`      // 响应签名
}

// CreateOrderData 订单创建响应数据
type CreateOrderData struct {
	OrderID         string      `json:"order_id"`                 // 系统订单号
	MerchantOrderNo string      `json:"merchant_order_no"`        // 第三方订单号
	PaymentUrl      string      `json:"payment_url"`              // 支付跳转链接（H5支付）
	QrCode          string      `json:"qr_code"`                  // 二维码内容（扫码支付）
	PaymentParams   interface{} `json:"payment_params,omitempty"` // 支付参数（JSAPI支付）
	ExpireTime      int64       `json:"expire_time"`              // 支付过期时间戳
	TotalAmount     uint64      `json:"total_amount"`             // 订单总金额（分）
	ActualAmount    uint64      `json:"actual_amount"`            // 实际应付金额（分）
	Status          string      `json:"status"`                   // 订单状态
	CreatedAt       int64       `json:"created_at"`               // 订单创建时间戳
}

// PaymentCallback 支付回调通知
type PaymentCallback struct {
	EventType       string `json:"event_type"`        // 事件类型
	EventID         string `json:"event_id"`          // 事件ID
	OrderID         string `json:"order_id"`          // 系统订单号
	MerchantOrderNo string `json:"merchant_order_no"` // 商户订单号
	Status          string `json:"status"`            // 订单状态
	PaidAmount      uint64 `json:"paid_amount"`       // 实际支付金额（分）
	PaidTime        int64  `json:"paid_time"`         // 支付时间戳
	PaymentMethod   string `json:"payment_method"`    // 支付方式
	TransactionID   string `json:"transaction_id"`    // 第三方支付流水号
	Timestamp       int64  `json:"timestamp"`         // 通知时间戳
	Sign            string `json:"sign"`              // 通知签名
}

// CallbackResponse 回调响应
type CallbackResponse struct {
	Code int    `json:"code"` // 响应码，0表示成功
	Msg  string `json:"msg"`  // 响应消息
}

// OrderStatus 订单状态枚举
const (
	OrderStatusWaitPay   = "WAIT_PAY"  // 待支付
	OrderStatusPaying    = "PAYING"    // 支付中
	OrderStatusPaid      = "PAID"      // 已支付
	OrderStatusCancelled = "CANCELLED" // 已取消
	OrderStatusExpired   = "EXPIRED"   // 已过期
	OrderStatusRefunding = "REFUNDING" // 退款中
	OrderStatusRefunded  = "REFUNDED"  // 已退款
	OrderStatusFailed    = "FAILED"    // 失败
)

// EventType 事件类型枚举
const (
	EventTypePaymentSucceeded = "payment.succeeded" // 支付成功
	EventTypePaymentFailed    = "payment.failed"    // 支付失败
	EventTypeOrderCancelled   = "order.cancelled"   // 订单取消
	EventTypeOrderExpired     = "order.expired"     // 订单过期
	EventTypeOrderRefunded    = "order.refunded"    // 订单退款
)

// ErrorCode 错误码定义
const (
	ErrorCodeSuccess           = 0     // 成功
	ErrorCodeSignError         = 40001 // 签名错误
	ErrorCodeInvalidAPIKey     = 40002 // API Key无效
	ErrorCodeTimestampExpired  = 40003 // 时间戳过期
	ErrorCodeParamError        = 40004 // 参数错误
	ErrorCodeNonceDuplicate    = 40005 // Nonce重复
	ErrorCodeRateLimitExceed   = 42001 // 请求频率过高
	ErrorCodeIPNotAllowed      = 42002 // IP不在白名单
	ErrorCodeQuotaExceed       = 42003 // 超出配额限制
	ErrorCodeSystemError       = 50001 // 系统内部错误
	ErrorCodeDBError           = 50002 // 数据库错误
	ErrorCodeThirdPartyError   = 50003 // 第三方服务错误
	ErrorCodeGoodsNotExist     = 60001 // 商品不存在
	ErrorCodeGoodsOffline      = 60002 // 商品已下架
	ErrorCodeStockNotEnough    = 60003 // 库存不足
	ErrorCodePriceError        = 60004 // 价格校验失败
	ErrorCodeOrderExists       = 60005 // 订单已存在
	ErrorCodeUserInfoError     = 60006 // 用户信息无效
	ErrorCodePaymentNotSupport = 60007 // 支付方式不支持
	ErrorCodeAmountExceed      = 60008 // 订单金额超限
	ErrorCodeOrderNotExist     = 60009 // 订单不存在
)

// GetErrorMessage 获取错误信息
func GetErrorMessage(code int) string {
	messages := map[int]string{
		ErrorCodeSuccess:           "成功",
		ErrorCodeSignError:         "签名验证失败",
		ErrorCodeInvalidAPIKey:     "API Key无效",
		ErrorCodeTimestampExpired:  "请求已过期",
		ErrorCodeParamError:        "参数错误",
		ErrorCodeNonceDuplicate:    "Nonce重复",
		ErrorCodeRateLimitExceed:   "请求频率过高",
		ErrorCodeIPNotAllowed:      "IP不在白名单",
		ErrorCodeQuotaExceed:       "超出配额限制",
		ErrorCodeSystemError:       "系统内部错误",
		ErrorCodeDBError:           "数据库错误",
		ErrorCodeThirdPartyError:   "第三方服务错误",
		ErrorCodeGoodsNotExist:     "商品不存在",
		ErrorCodeGoodsOffline:      "商品已下架",
		ErrorCodeStockNotEnough:    "库存不足",
		ErrorCodePriceError:        "价格校验失败",
		ErrorCodeOrderExists:       "订单已存在",
		ErrorCodeUserInfoError:     "用户信息无效",
		ErrorCodePaymentNotSupport: "支付方式不支持",
		ErrorCodeAmountExceed:      "订单金额超限",
		ErrorCodeOrderNotExist:     "订单不存在",
	}

	if msg, ok := messages[code]; ok {
		return msg
	}
	return "未知错误"
}
