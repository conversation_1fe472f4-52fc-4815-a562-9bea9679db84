package openapi

import (
	"blind_box/pkg/utils"
)

// CreateMerchantRequest 创建商户请求
type CreateMerchantRequest struct {
	MerchantName    string   `json:"merchant_name" binding:"required"`
	MerchantCode    string   `json:"merchant_code" binding:"required"`
	ContactName     string   `json:"contact_name"`
	ContactMobile   string   `json:"contact_mobile"`
	ContactEmail    string   `json:"contact_email"`
	CallbackURL     string   `json:"callback_url"`
	IPWhitelist     []string `json:"ip_whitelist"`
	SKUWhitelist    []uint64 `json:"sku_whitelist"`
	QPSLimit        uint32   `json:"qps_limit"`
	DailyLimit      uint64   `json:"daily_limit"`
	SingleAmountMax float64  `json:"single_amount_max"` // 单位：元
	DailyAmountMax  float64  `json:"daily_amount_max"`  // 单位：元
	Remark          string   `json:"remark"`
}

// CreateMerchantResponse 创建商户响应
type CreateMerchantResponse struct {
	MerchantID uint64                `json:"merchant_id"`
	AppID      string                `json:"app_id"`
	AppSecret  string                `json:"app_secret"`
	Config     *utils.MerchantConfig `json:"config"`
}

type UpdateMerchantRequest struct {
	MerchantID      uint64   `json:"merchant_id" binding:"required"`
	MerchantName    string   `json:"merchant_name"`
	ContactName     string   `json:"contact_name"`
	ContactMobile   string   `json:"contact_mobile"`
	ContactEmail    string   `json:"contact_email"`
	CallbackURL     string   `json:"callback_url"`
	IPWhitelist     []string `json:"ip_whitelist"`
	SKUWhitelist    []uint64 `json:"sku_whitelist"`
	QPSLimit        uint32   `json:"qps_limit"`
	DailyLimit      uint64   `json:"daily_limit"`
	SingleAmountMax float64  `json:"single_amount_max"`
	DailyAmountMax  float64  `json:"daily_amount_max"`
	Status          uint32   `json:"status"`
	Remark          string   `json:"remark"`
}
