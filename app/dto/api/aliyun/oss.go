package aliyun

type AliyunOssTokenReq struct {
	BucketType string `form:"bucket_type" json:"bucket_type" binding:"oneof=image video video_mps"`
}

type AliyunOssTokenResp struct {
	Bucket          string `json:"bucket"`
	Token           string `json:"token"`
	RegionID        string `json:"regionID"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
}

type AliyunOssAppletTokenResp struct {
	Bucket           string `json:"bucket"`
	RegionID         string `json:"region_id"`
	OssRegionID      string `json:"oss_region_id"`
	Signature        string `json:"signature"`
	SignatureVersion string `json:"signature_version"`
	SecurityToken    string `json:"security_token"`
	Policy           string `json:"policy"`
	Credential       string `json:"credential"`
	Date             string `json:"date"`
}
