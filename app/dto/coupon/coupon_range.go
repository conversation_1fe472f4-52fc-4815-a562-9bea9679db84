package coupon

type CouponRangeActListReq struct {
	CouponID uint64 `form:"coupon_id" json:"coupon_id" binding:"required,omitempty"`
}

type CouponRangeActListItem struct {
	ActID       uint64   `json:"act_id"`       // 活动id
	ActTitle    string   `json:"act_title"`    // 活动标题
	Cover       string   `json:"cover"`        // 活动缩略图
	ActPrice    uint64   `json:"act_price"`    // 活动价格
	CreatedTime string   `json:"created_time"` // 创建日期
	// TagNames    []string `json:"tag_names"`    // 活动标签
}

type CouponRangeActListResp struct {
	List  []*CouponRangeActListItem `json:"list"`
	Total int                       `json:"total"`
}

type ActUserAvailableCouponReq struct {
	ActID   uint64 `form:"act_id" json:"act_id" binding:"required,omitempty"`
	DrawNum uint32 `form:"draw_num" json:"draw_num" binding:"required,omitempty"`
}

type ActUserAvailableCouponItem struct {
	CUID           uint64  `json:"cu_id"`
	CouponID       uint64  `json:"coupon_id"`
	CouponCode     string  `json:"coupon_code"`     // 优惠券code
	CouponName     string  `json:"coupon_name"`     // 优惠券名称
	DiscountAmount uint64  `json:"discount_amount"` // 优惠金额
	ExpireTime     string  `json:"expire_time"`     // 过期时间
	RangeType      uint32  `json:"range_type"`      // 使用范围
	Extra          *CExtra `json:"extra"`           // 文字展示配置
	Status         uint32  `json:"status"`          // 状态
	CreatedTime    string  `json:"created_time"`    // 创建时间
}

type ActUserAvailableCouponItemSort []*ActUserAvailableCouponItem

func (x ActUserAvailableCouponItemSort) Len() int {
	return len(x)
}
func (x ActUserAvailableCouponItemSort) Less(i, j int) bool {
	if x[i].DiscountAmount == x[j].DiscountAmount {
		return x[i].CUID < x[j].CUID
	} else {
		return x[i].DiscountAmount > x[j].DiscountAmount
	}
}
func (x ActUserAvailableCouponItemSort) Swap(i, j int) {
	x[i], x[j] = x[j], x[i]
}

type ActUserAvailableCouponResp struct {
	AvailableList    ActUserAvailableCouponItemSort `json:"available_list"`
	AvailableTotal   int                            `json:"available_total"`
	UnavailableList  ActUserAvailableCouponItemSort `json:"unavailable_list"`
	UnavailableTotal int                            `json:"unavailable_total"`
}
