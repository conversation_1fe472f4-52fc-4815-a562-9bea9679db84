package coupon

import "blind_box/app/dto/common"

type AdminCouponListReq struct {
	Name string `form:"name" json:"name"`
	Code string `form:"code" json:"code"`
	common.DataListReq
}

type AdminCouponListItem struct {
	ID              uint64  `json:"id"`
	Code            string  `json:"code"`             // 优惠券code
	Name            string  `json:"name"`             // 名称
	DiscountType    uint32  `json:"discount_type"`    // 折扣类型 1整单折扣
	DiscountMethod  uint32  `json:"discount_method"`  // 优惠方式 1现金立减
	DiscountAmount  float64 `json:"discount_amount"`  // 优惠金额
	ThresholdType   uint32  `json:"threshold_type"`   // 门槛类型 0无门槛 1实付满减
	ThresholdAmount float64 `json:"threshold_amount"` // 门槛金额
	ExpireDays      uint32  `json:"expire_days"`      // 领取有效天数
	RangeType       uint32  `json:"range_type"`       // 使用范围 0全场无限制 1指定活动id
	Status          uint32  `json:"status"`           // 状态
	CreatedTime     string  `json:"created_time"`     // 创建时间
}
type AdminCouponListResp struct {
	List  []*AdminCouponListItem `json:"list"`
	Total int64                  `json:"total"`
}

type AdminCouponDetailReq struct {
	ID   uint64 `form:"id" json:"id"`
	Code string `form:"code" json:"code"`
}

type AdminCouponDetailResp struct {
	ID              uint64         `json:"id"`
	Code            string         `json:"code"`             // 优惠券code
	Name            string         `json:"name"`             // 名称
	DiscountType    uint32         `json:"discount_type"`    // 折扣类型 1整单折扣
	DiscountMethod  uint32         `json:"discount_method"`  // 优惠方式 1现金立减
	DiscountAmount  float64        `json:"discount_amount"`  // 优惠金额
	ThresholdType   uint32         `json:"threshold_type"`   // 门槛类型 0无门槛 1实付满减
	ThresholdAmount float64        `json:"threshold_amount"` // 门槛金额
	ExpireDays      uint32         `json:"expire_days"`      // 领取有效天数
	RangeType       uint32         `json:"range_type"`       // 使用范围 0全场无限制 1指定活动id
	RangeEntity     []*RangeEntity `json:"range_entity"`     // 使用范围实体
	Extra           *CExtra        `json:"extra"`            // 文字展示配置
	Status          uint32         `json:"status"`           // 状态
	HasUserGet      uint32         `json:"has_user_get"`     // 是否已有用户领取
	CreatedTime     string         `json:"created_time"`     // 创建时间
}

type RangeEntity struct {
	EntityID   uint64 `json:"entity_id"`   // 实体id
	EntityName string `json:"entity_name"` // 实体名称
}

type CExtra struct {
	NameDesc      string `form:"name_desc" json:"name_desc" binding:"required,omitempty,max=50"`           // 名称说明
	DetailDesc    string `form:"detail_desc" json:"detail_desc" binding:"required,omitempty,max=50"`       // 详情说明
	DiscountDesc  string `form:"discount_desc" json:"discount_desc" binding:"required,omitempty,max=50"`   // 折扣说明
	ThresholdDesc string `form:"threshold_desc" json:"threshold_desc" binding:"required,omitempty,max=50"` // 门槛说明
}

type AdminSetCouponReq struct {
	ID              uint64   `form:"id" json:"id" binding:"omitempty"`
	Name            string   `form:"name" json:"name" binding:"required,omitempty,max=64"`
	Code            string   `form:"code" json:"code" binding:"required,omitempty,max=64"`
	DiscountType    uint32   `form:"discount_type" json:"discount_type" binding:"oneof=0 1"`                                                // 折扣类型 1整单折扣
	DiscountMethod  uint32   `form:"discount_method" json:"discount_method" binding:"oneof=0 1"`                                            // 优惠方式 1现金立减
	DiscountAmount  float64  `form:"discount_amount" json:"discount_amount" binding:"required,omitempty,max=100000"`                        // 优惠金额
	ThresholdType   uint32   `form:"threshold_type" json:"threshold_type" binding:"oneof=0 1"`                                              // 门槛类型 0无门槛 1实付满减
	ThresholdAmount float64  `form:"threshold_amount" json:"threshold_amount" binding:"max=100000,requiredIF=ThresholdType 1" msg:"门槛金额无效"` // 门槛金额
	ExpireDays      uint32   `form:"expire_days" json:"expire_days" binding:"required,omitempty,max=100000"`                                // 领取后有效天数
	RangeType       uint32   `form:"range_type" json:"range_type" binding:"oneof=0 1"`                                                      // 使用范围 0全场无限制 1指定活动id
	EntityIDS       []uint64 `form:"entity_ids" json:"entity_ids" binding:"requiredIF=RangeType 1" msg:"至少选定一个活动"`                          // 活动id数组
	Extra           *CExtra  `form:"extra" json:"extra" binding:"required,omitempty"`                                                       // 文字展示配置
}

type AdminOperateCouponReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close,omitempty"`
}

type AdminSetCouponSourceReq struct {
	ID   uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Name string `form:"name" json:"name" binding:"required,omitempty,max=50"`
}

type AdminCouponSourceItem struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

type HomepageCouponItem struct {
	CouponID        uint64  `json:"coupon_id"`        // 优惠券id
	IssueID         uint64  `json:"issue_id"`         // 发放id
	CouponName      string  `json:"coupon_name"`      // 优惠券名称
	DiscountType    uint32  `json:"discount_type"`    // 折扣类型 1整单折扣
	DiscountMethod  uint32  `json:"discount_method"`  // 优惠方式 1现金立减
	DiscountAmount  uint64  `json:"discount_amount"`  // 优惠金额(分)
	ThresholdType   uint32  `json:"threshold_type"`   // 门槛类型 0无门槛 1实付满减
	ThresholdAmount uint64  `json:"threshold_amount"` // 门槛金额
	ExpireDays      uint32  `json:"expire_days"`      // 领取有效天数
	RangeType       uint32  `json:"range_type"`       // 使用范围 0全场无限制 1指定活动id
	Extra           *CExtra `json:"extra"`            // 文字展示配置
}
