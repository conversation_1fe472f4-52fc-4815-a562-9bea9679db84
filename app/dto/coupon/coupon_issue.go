package coupon

import "blind_box/app/dto/common"

type AdminCouponIssueListReq struct {
	CouponCode string `form:"coupon_code" json:"coupon_code"`
	IssueType  uint32 `form:"issue_type" json:"issue_type"`
	common.DataListReq
}

type AdminCouponIssueListItem struct {
	ID              uint64  `json:"id"`
	Name            string  `json:"name"`
	StartTime       string  `json:"start_time"`
	EndTime         string  `json:"end_time"`
	Status          uint32  `json:"status"`         // 状态 1 启用 2停用
	IssueType       uint32  `json:"issue_type"`     // 发放类型 1全部用户 2指定用户
	Stock           uint32  `json:"stock"`          // 发放数量
	UsedStock       uint32  `json:"used_stock"`     // 指定用户-已使用库存
	ReceivedStock   uint32  `json:"received_stock"` // 指定用户-已领取数
	SourceID        uint64  `json:"source_id"`
	SourceName      string  `json:"source_name"`
	CouponID        uint64  `json:"coupon_Id"`
	CouponCode      string  `json:"coupon_code"`
	CouponName      string  `json:"coupon_name"`
	DiscountType    uint32  `json:"discount_type"`
	DiscountMethod  uint32  `json:"discount_method"`
	DiscountAmount  float64 `json:"discount_amount"`
	ThresholdType   uint32  `json:"threshold_type"`
	ThresholdAmount float64 `json:"threshold_amount"`
	CreatedTime     string  `json:"created_time"`
}

type AdminCouponIssueListResp struct {
	List  []*AdminCouponIssueListItem `json:"list"`
	Total int64                       `json:"total"`
}

type AdminCountIssueContainCouponreq struct {
	ID       uint64 `form:"id" json:"id"`
	CouponID uint64 `form:"coupon_id" json:"coupon_id" binding:"required,omitempty"`
}

type AdminCouponIssueDetailReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminCouponIssueDetailResp struct {
	ID              uint64  `json:"id"`
	Name            string  `json:"name"`
	StartTime       string  `json:"start_time"`
	EndTime         string  `json:"end_time"`
	Status          uint32  `json:"status"`     // 状态 1 启用 2停用
	IssueType       uint32  `json:"issue_type"` // 发放类型 1全部用户 2指定用户
	SourceID        uint64  `json:"source_id"`
	CouponID        uint64  `json:"coupon_Id"`
	CouponCode      string  `json:"coupon_code"`
	CouponName      string  `json:"coupon_name"`
	DiscountType    uint32  `json:"discount_type"`    // 折扣类型 1整单折扣
	DiscountMethod  uint32  `json:"discount_method"`  // 优惠方式 1现金立减
	DiscountAmount  float64 `json:"discount_amount"`  // 优惠金额
	ThresholdType   uint32  `json:"threshold_type"`   // 门槛类型 0无门槛 1实付满减
	ThresholdAmount float64 `json:"threshold_amount"` // 门槛金额
	ExpireDays      uint32  `json:"expire_days"`      // 领取有效天数
	Extra           *CExtra `json:"extra"`            // 文字展示配置
	CreatedTime     string  `json:"created_time"`
}

type AdminSetCouponIssueReq struct {
	ID        uint64 `form:"id" json:"id"`
	Name      string `form:"name" json:"name" binding:"required,omitempty,max=128"`
	IssueType uint32 `form:"issue_type" json:"issue_type" binding:"oneof=1 2"`
	StartTime int64  `form:"start_time" json:"start_time" binding:"required,omitempty"`
	EndTime   int64  `form:"end_time" json:"end_time" binding:"required,omitempty"`
	CouponID  uint64 `form:"coupon_id" json:"coupon_id" binding:"required,omitempty"`
	SourceID  uint64 `form:"source_id" json:"source_id" binding:"oneof=1 2"`
	UploadKey string `form:"upload_key" json:"upload_key" binding:"requiredIF=IssueType 2" msg:"未上传名单"`
}

type AdminOperateCouponIssueReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close,omitempty"`
}

type AdminCouponIssueTargetSetReq struct {
	ID        uint64 `form:"id" json:"id" binding:"required,omitempty"`
	UploadKey string `form:"upload_key" json:"upload_key" binding:"required,omitempty"`
	Action    string `form:"action" json:"action" binding:"oneof=add subtract,omitempty"`
}

type AdminCouponIssueTargetUploadResp struct {
	UploadKey  string `json:"upload_key"`
	Total      int    `json:"total"`
	SuccessNum int    `json:"success_num"`
}

type AdminCouponIssueTargetExportReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}
