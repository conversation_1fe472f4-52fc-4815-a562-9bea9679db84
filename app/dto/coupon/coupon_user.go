package coupon

import "blind_box/app/dto/common"

type AdminCouponUserListReq struct {
	CouponID   uint64 `form:"coupon_id" json:"coupon_id"`
	CouponName string `form:"coupon_name" json:"coupon_name"`
	CouponCode string `form:"coupon_code" json:"coupon_code"`
	UserID     uint64 `form:"user_id" json:"user_id"`
	SourceID   uint64 `form:"source_id" json:"source_id"`
	Status     uint32 `form:"status" json:"status"`
	TradeNo    string `form:"trade_no" json:"trade_no"`
	StartTime  int64  `form:"start_time" json:"start_time"`
	EndTime    int64  `form:"end_time" json:"end_time"`
	common.DataListReq
}

type AdminCouponUserListItem struct {
	ID             uint64  `json:"id"`
	CouponID       uint64  `json:"coupon_id"`
	CouponCode     string  `json:"coupon_code"`     // 优惠券code
	CouponName     string  `json:"coupon_name"`     // 优惠券名称
	UserID         uint64  `json:"user_id"`         // 用id
	UserName       string  `json:"user_name"`       // 用户名称
	TradeNo        string  `json:"trade_no"`        // 支付流水号
	DiscountAmount float64 `json:"discount_amount"` // 订单优惠金额
	SourceID       uint64  `json:"source_id"`       // 来源id
	SourceName     string  `json:"source_name"`     // 来源名称
	ExpireTime     string  `json:"expire_time"`     // 过期时间
	IsExpired      uint32  `json:"is_expired"`      // 是否过期
	Status         uint32  `json:"status"`          // 状态
	Remark         string  `json:"remark"`          // 备注
	CreatedTime    string  `json:"created_time"`    // 创建时间
}
type AdminCouponUserListResp struct {
	List  []*AdminCouponUserListItem `json:"list"`
	Total int64                      `json:"total"`
}

type AdminOperateCouponUserReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=abandon recover,omitempty"`
	Remark string `form:"remark" json:"remark" binding:"omitempty,max=128"`
}

type UserCouponListReq struct {
	Status string `form:"status" json:"status" binding:"oneof=waiting used expired"`
	common.DataListReq
}

type UserCouponListItem struct {
	CUID           uint64   `json:"cu_id"`
	CouponID       uint64   `json:"coupon_id"`
	CouponCode     string   `json:"coupon_code"`     // 优惠券code
	CouponName     string   `json:"coupon_name"`     // 优惠券名称
	UserID         uint64   `json:"user_id"`         // 用id
	TradeNo        string   `json:"trade_no"`        // 支付流水号
	DiscountAmount uint64   `json:"discount_amount"` // 订单优惠金额
	SourceID       uint64   `json:"source_id"`       // 来源id
	ExpireTime     string   `json:"expire_time"`     // 过期时间
	RangeType      uint32   `json:"range_type"`      // 使用范围
	EntityIDS      []uint64 `json:"entity_ids"`      // 实体ID列表
	Extra          *CExtra  `json:"extra"`           // 文字展示配置
	Status         uint32   `json:"status"`          // 状态
	CreatedTime    string   `json:"created_time"`    // 创建时间
}
type UserCouponListResp struct {
	List  []*UserCouponListItem `json:"list"`
	Total int64                 `json:"total"`
}

type UseCouponReq struct {
	UID     uint64 `form:"uid" json:"uid"`
	UCID    uint64 `form:"uc_id" json:"uc_id" binding:"required,omitempty"`
	ActID   uint64 `form:"act_id" json:"act_id" binding:"required,omitempty"`
	DrawNum uint32 `form:"draw_num" json:"draw_num" binding:"required,omitempty"`
	Status  uint32 `form:"status" json:"status"`
	TradeNo string `form:"trade_no" json:"trade_no"`
}
