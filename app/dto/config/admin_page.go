package config

import (
	"blind_box/app/dto/common"
)

type AdminUpdatePageConfigReq struct {
	Key    string `json:"key" binding:"required"`  // 配置项的键
	Data   string `json:"data" binding:"required"` // 配置项的数据
	Status uint32 `json:"status"`                  // 配置项的状态，1表示启用 2表示禁用
}

type AdminUpdatePageConfigResp struct {
	AdminPageConfigItem
}

type AdminPageConfigReq struct {
	common.DataListReq
	Key    string `form:"key"`    // 配置项的键
	Status uint32 `form:"status"` // 配置项的状态，1表示启用 2表示禁用
}

type AdminPageConfigResp struct {
	List  []AdminPageConfigItem `json:"list"`  // 配置项列表
	Count int64                 `json:"count"` // 配置项数量
	IsEnd bool                  `json:"isEnd"` // 是否结束分页
}

type AdminPageConfigItem struct {
	ID     uint32 `json:"id"`     // 配置项ID
	Key    string `json:"key"`    // 配置项的键
	Data   string `json:"data"`   // 配置项的数据
	Status uint32 `json:"status"` // 配置项的状态，1表示启用 2表示禁用
}

// ================================ 微信小程序端的DTO ================================

type PageConfigReq struct {
	Key string `form:"key"` // 配置项的键，可选，支持逗号分隔多个key，为空则返回所有启用的页面配置
}

type PageConfigResp struct {
	Config map[string]string `json:"config"` // 页面配置键值对
}
