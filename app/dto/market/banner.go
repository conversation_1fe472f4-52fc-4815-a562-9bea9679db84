package market

type BannerListReq struct {
	Position uint32 `form:"position" json:"position" binding:"required"` // 1 首页
}

type BannerListResp struct {
	List []BannerListItem `json:"list"`
}

type BannerListItem struct {
	ID        uint64 `json:"id"`        // id
	Title     string `json:"title"`     // 标题
	Position  uint32 `json:"position"`  // 位置
	Image     string `json:"image"`     // 图片
	JumpType  uint32 `json:"jumpType"`  // 跳转类型 0-不跳转 1-活动 2-h5 3-小程序原生 4- app原生 5-单品
	JumpParam string `json:"jumpParam"` // 跳转参数
	JumpUrl   string `json:"jumpUrl"`   // 跳转链接
	Channel   uint32 `json:"channel"`   // 渠道 1-微信小程序 3-ios 4-安卓
	ActiveID  uint64 `json:"activeId"`  // 活动id
}
