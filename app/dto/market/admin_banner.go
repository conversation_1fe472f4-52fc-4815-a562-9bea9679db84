package market

import (
	"blind_box/app/dto/common"
)

type AdminBannerCreateReq struct {
	Title     string `json:"title" binding:"required"`    // 标题
	Position  uint32 `json:"position" binding:"required"` // 位置
	Image     string `json:"image" binding:"required"`    // 图片
	JumpType  uint32 `json:"jumpType" binding:"required"` // 跳转类型 0-不跳转 1-活动 2-h5 3-小程序原生 4- app原生 5-单品
	JumpParam string `json:"jumpParam"`                   // 跳转参数
	JumpUrl   string `json:"jumpUrl"`                     // 跳转链接
	Sort      uint32 `json:"sort"`                        // 排序
	ActiveID  uint64 `json:"activeId"`                    // 活动id
	Channel   uint32 `json:"channel"`                     // 渠道 1-微信小程序 3-ios 4-安卓
}

type AdminBannerCreateResp struct {
	ID uint64 `json:"id"` // id
}

type AdminBannerUpdateReq struct {
	ID uint64 `json:"id" binding:"required"` // id
	AdminBannerCreateReq

	Status uint32 `json:"status"` // 状态
}

type AdminBannerUpdateResp struct {
	ID uint64 `json:"id"` // id
}

type AdminBannerListReq struct {
	Position uint32 `form:"position" json:"position"` // 位置
	Status   uint32 `form:"status" json:"status"`     // 状态
	JumpType uint32 `form:"jumpType" json:"jumpType"` // 跳转类型
	Channel  uint32 `form:"channel" json:"channel"`   // 渠道

	common.DataListReq
}

type AdminBannerListResp struct {
	List  []AdminBannerListItem `json:"list"`  // 列表
	Count int64                 `json:"count"` // 总数
}

type AdminBannerListItem struct {
	ID          uint64 `json:"id"`          // id
	Title       string `json:"title"`       // 标题
	Position    uint32 `json:"position"`    // 位置
	Image       string `json:"image"`       // 图片
	JumpType    uint32 `json:"jumpType"`    // 跳转类型 0-不跳转 1-活动 2-h5 3-小程序原生 4- app原生 5-单品
	JumpParam   string `json:"jumpParam"`   // 跳转参数
	JumpUrl     string `json:"jumpUrl"`     // 跳转链接
	Status      uint32 `json:"status"`      // 状态
	Sort        uint32 `json:"sort"`        // 排序
	ActiveID    uint64 `json:"activeId"`    // 活动id
	ActiveTitle string `json:"activeTitle"` // 活动标题
	Channel     uint32 `json:"channel"`     // 渠道 1-微信小程序 3-ios 4-安卓
}

type AdminBannerDetailReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"` // id
}

type AdminBannerDetailResp struct {
	AdminBannerListItem
}

type AdminBannerDeleteReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"` // id
}

type AdminBannerDeleteResp struct {
}
