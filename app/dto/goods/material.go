package goods

import (
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	shopDao "blind_box/app/dao/resource/shop"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

func ToSpuItemResp(ctx *gin.Context, spu *spuDao.Model, batch *batchDao.Model) AdminSpuListItem {
	ret := AdminSpuListItem{
		BatchId:       batch.ID,
		BatchTitle:    batch.Title,
		BatchSellType: batch.SellType,

		SpuId:     spu.ID,
		Title:     spu.Title,
		SubTitle:  spu.SubTitle,
		Code:      spu.Code,
		Cover:     helper.GetImageCdnUrl(ctx, spu.Cover),
		ImageList: []string{},
		// BrandID:      m.BrandID,
		// VendorID:     m.VendorID,
		// SupplierID:   m.SupplierID,
		Status: spu.Status,
		// SellType:     m.SellType,
		PreStartTime: spu.GetPreStartTime(),
		PreEndTime:   spu.GetPreEndTime(),
		LowestPrice:  spu.LowestPrice,
		HighestPrice: spu.HighestPrice,
		IsRedeem:     spu.IsRedeem,
		Sort:         spu.Sort,
		Detail:       spu.Detail,
		CreatedAt:    spu.GetCreatedTime(),
	}
	return ret
}

func ToSkuItemResp(ctx *gin.Context, sku *skuDao.Model, spu *spuDao.Model, batch *batchDao.Model,
	shopMap map[uint64]*shopDao.Model, skuUnitListMap map[uint64]skuUnitDao.ModelList) CrSkuItem {

	ret := CrSkuItem{
		SpuID:    spu.ID,
		SpuTitle: spu.Title,
		SpuCode:  spu.Code,
		SpuCover: helper.GetImageCdnUrl(ctx, spu.Cover),

		BatchID:       batch.ID,
		BatchTitle:    batch.Title,
		BatchSellType: batch.SellType,

		SkuID:        sku.ID,
		SkuTitle:     sku.Title,
		SkuCode:      sku.Code,
		SkuCover:     helper.GetImageCdnUrl(ctx, sku.Cover),
		DeliveryList: []*CrSkuDeliveryItem{},
		SkuUnitList:  []SkuUnitItem{},

		Total:         sku.Total,
		LockNum:       sku.LockNum,
		UsedNum:       sku.UsedNum,
		UsableNum:     sku.GetUsableNum(),
		RefundNum:     sku.RefundNum,
		SoldOut:       sku.IsSoldOut(),
		OriginalPrice: sku.OriginalPrice,
		SellPrice:     sku.SellPrice,
		RefPrice:      sku.RefPrice,

		BarCode:   sku.BarCode,
		Status:    sku.Status,
		CreatedAt: sku.GetCreatedTime(),
	}

	if sku.DeliveryList != "" && sku.DeliveryList != "{}" {
		skuDeliveryList := SkuDeliveryList{}
		json.Unmarshal([]byte(sku.DeliveryList), &skuDeliveryList)

		for _, delivery := range skuDeliveryList {
			item := &CrSkuDeliveryItem{
				DeliveryID: delivery.DeliveryId,
				ShopList:   []CrSkuDeliveryShopItem{},
				JumpUrl:    delivery.JumpUrl,
			}
			shopList := []CrSkuDeliveryShopItem{}
			for _, shopId := range delivery.ShopIds {
				if shop, ok := shopMap[shopId]; ok {
					shopList = append(shopList, CrSkuDeliveryShopItem{
						ID:      shop.ID,
						Name:    shop.Name,
						Address: shop.Address,
						Mobile:  shop.Mobile,
					})
				}
			}
			item.ShopList = shopList

			ret.DeliveryList = append(ret.DeliveryList, item)
		}
	}

	if skuUnitList, ok := skuUnitListMap[sku.ID]; ok {
		for _, val := range skuUnitList {
			item := SkuUnitItem{
				ID:        val.ID,
				Title:     val.Title,
				UnitRatio: val.UnitRatio,
				IsPrimary: val.IsPrimary,
				Status:    val.Status,
			}
			ret.SkuUnitList = append(ret.SkuUnitList, item)
		}
	}

	return ret
}
