package goods

import "blind_box/app/dto/common"

type AdminBatchListReq struct {
	Title    string `json:"title" form:"title"`
	SellType uint32 `json:"sell_type" form:"sell_type"`
	Status   uint32 `json:"status" form:"status"`
	common.DataListReq
}

type AdminBatchListResp struct {
	List  []AdminBatchListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminBatchListItem struct {
	ID        uint64 `json:"id"`
	Title     string `json:"title"`
	SellType  uint32 `json:"sell_type"`
	Desc      string `json:"desc"`
	Status    uint32 `json:"status"`
	CreatedAt string `json:"created_at"`
}

type AdminBatchCreateReq struct {
	Title    string `json:"title" binding:"required,checkStringLength=50" msg:"批次名长度超出限制"`
	SellType uint32 `form:"sell_type" json:"sell_type" binding:"required"`
	Desc     string `json:"desc" binding:"required,checkStringLength=200" msg:"批次描述长度超出限制"`
	Status   uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminBatchUpdateReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
	AdminBatchCreateReq
}

type AdminBatchDetailReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminBatchDetailResp struct {
	AdminBatchListItem
	SpuList []AdminSpuListItem `json:"spu_list"`
}

type AdminBatchOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
}
