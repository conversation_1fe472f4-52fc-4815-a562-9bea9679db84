package goods

import "blind_box/app/dto/common"

type AdminTagListReq struct {
	GroupID uint32 `form:"group_id" json:"group_id"`
	Name    string `form:"name" json:"name"`
	Status  uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminTagListResp struct {
	List  []*AdminTagListItem `json:"list"`
	Total int64               `json:"total"`
}

type AdminTagListItem struct {
	ID      uint64 `json:"id"`       // 标签ID
	Name    string `json:"name"`     // 标签名
	GroupID uint32 `json:"group_id"` // 标签组ID
	Status  uint32 `json:"status"`   // 标签状态

}

type AdminTagCreateReq struct {
	Name    string `json:"name" binding:"required,checkStringLength=120" msg:"标签名长度超出限制"`
	GroupID uint32 `form:"group_id" json:"group_id" binding:"required"`
}

type AdminTagUpdateReq struct {
	ID   uint64 `form:"id" json:"id" binding:"required"`
	Name string `json:"name" binding:"required,checkStringLength=120" msg:"标签名长度超出限制"`
}

type AdminTagDeleteReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminTagOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
}

type AdminTagCommonListReq struct {
	GroupID uint32 `form:"group_id" json:"group_id" binding:"required"`
	Enable  uint32 `form:"enable" json:"enable"`
}
