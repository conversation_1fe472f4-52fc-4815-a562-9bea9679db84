package goods

import (
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type CrSkuItem struct {
	SpuID    uint64 `json:"spu_id"`
	SpuTitle string `json:"spu_title"`
	SpuCode  string `json:"spu_code"`
	SpuCover string `json:"spu_cover"`

	BatchID       uint64 `json:"batch_id"`
	BatchTitle    string `json:"batch_title"`
	BatchSellType uint32 `json:"batch_sell_type"`

	SkuID        uint64               `json:"sku_id"`
	SkuTitle     string               `json:"sku_title"`
	SkuCode      string               `json:"sku_code"`
	SkuCover     string               `json:"sku_cover"`
	DeliveryList []*CrSkuDeliveryItem `json:"delivery_list"`
	SkuUnitList  []SkuUnitItem        `json:"sku_unit_list"`

	Total     uint32 `json:"total"`
	LockNum   uint32 `json:"lock_num"`
	UsedNum   uint32 `json:"used_num"`
	UsableNum uint32 `json:"usable_num"`
	RefundNum uint32 `json:"refund_num"`
	SoldOut   uint32 `json:"sold_out"`

	OriginalPrice uint64 `json:"original_price"`
	SellPrice     uint64 `json:"sell_price"`
	RefPrice      uint64 `json:"ref_price"`

	BarCode   string `json:"bar_code"`
	Status    uint32 `json:"status"` // 状态 1上架 2下架
	IsSub     uint32 `json:"is_sub"` // 是否订阅 1是 0否
	CreatedAt string `json:"created_at"`
}

type CrSkuDeliveryItem struct {
	DeliveryID uint32                  `json:"delivery_id"`
	ShopList   []CrSkuDeliveryShopItem `json:"shop_list"`
	JumpUrl    string                  `json:"jump_url"`
	CartNum    uint32                  `json:"cart_num"`
}

type CrSkuDeliveryShopItem struct {
	ID      uint64 `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
	Mobile  string `json:"mobile"`
}
