package goods

import (
	"blind_box/app/common/dbs"
	skuDao "blind_box/app/dao/goods/sku"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	spuDao "blind_box/app/dao/goods/spu"
	spuTagDao "blind_box/app/dao/goods/spu_tag"
	tagDao "blind_box/app/dao/goods/tag"
	imageDao "blind_box/app/dao/resource/image"
	"blind_box/app/dto/common"
	"blind_box/pkg/helper"
	"errors"
)

type AdminSpuListReq struct {
	common.CommonSpuSkuFilterReq
	BrandId    uint64 `form:"brand_id" json:"brand_id" `
	SupplierId uint64 `form:"supplier_id" json:"supplier_id"`
	VendorId   uint64 `form:"vendor_id" json:"vendor_id"`
	Status     uint32 `form:"status" json:"status"`
	SellType   uint32 `form:"sell_type" json:"sell_type"`
	common.DataListReq
}

type AdminSpuListResp struct {
	List  []AdminSpuListItem `json:"list"`
	Total int64              `json:"total"`
}

type AdminSpuListItem struct {
	BatchId       uint64 `json:"batch_id"`
	BatchTitle    string `json:"batch_title"`
	BatchSellType uint32 `json:"batch_sell_type"`

	SpuId        uint64              `json:"spu_id"`          // goods_spu id
	Title        string              `json:"title"`           // 商品名称
	Code         string              `json:"code"`            // 商品编码
	SubTitle     string              `json:"sub_title"`       // 商品副标题
	Cover        string              `json:"cover"`           // 封面图片
	ImageList    []string            `json:"image_list"`      // 图片列表
	Category     common.GoodsTagResp `json:"category"`        // 商品分类
	Supplier     common.GoodsTagResp `json:"supplier"`        // 商品供应商
	Vendor       common.GoodsTagResp `json:"vendor"`          // 商品厂商
	Brand        common.GoodsTagResp `json:"brand"`           // 商品品牌
	Status       uint32              `json:"status"`          // 状态 1上架 2下架
	PreStartTime string              `json:"pre_start_time" ` // 支付开始时间
	PreEndTime   string              `json:"pre_end_time"`    // 支付结束时间
	LowestPrice  uint64              `json:"lowest_price"`    // 最低价格
	HighestPrice uint64              `json:"highest_price"`   // 最高价格
	IsRedeem     uint32              `json:"is_redeem"`       // 是否可兑换 1是 0否
	Detail       string              `json:"detail"`          // 商品详情
	Sort         uint32              `json:"sort"`            // 排序
	CreatedAt    string              `json:"created_at"`      // 创建时间
}

type AdminSpuDetailReq struct {
	SpuId uint64 `form:"spu_id" json:"spu_id" binding:"required"`
}

type AdminSpuDetailResp struct {
	AdminSpuListItem
	SkuList []CrSkuItem `json:"sku_list"` // 商品sku列表
}

type AdminSpuCreateReq struct {
	BatchId      uint64                `json:"batch_id" binding:"required"`
	Title        string                `json:"title" binding:"required,checkStringLength=150" msg:"商品名称长度不正确"`       // 商品名称
	SubTitle     string                `json:"sub_title" binding:"omitempty,checkStringLength=150" msg:"商品副标题长度不正确"` // 商品副标题
	Code         string                `json:"code" binding:"required,checkStringLength=100" msg:"商品编号无效"`           // 商品编码
	Cover        string                `json:"cover" binding:"required"`                                             // 封面图片
	CategoryId   uint64                `json:"category_id" binding:"required" msg:"请选择分类"`                           // 商品分类ID
	BrandId      uint64                `json:"brand_id" binding:"required" msg:"请选择品牌"`                              // 商品品牌ID
	SupplierId   uint64                `json:"supplier_id" binding:"required" msg:"请选择供应商"`                          // 商品供应商ID
	VendorId     uint64                `json:"vendor_id" binding:"required" msg:"请选择厂商"`                             // 商品厂商ID
	Status       uint32                `json:"status" binding:"oneof=1 2"`                                           // 上架状态 1上架 2下架
	PreStartTime int64                 `json:"pre_start_time"`
	PreEndTime   int64                 `json:"pre_end_time"`
	ImageList    []string              `json:"image_list" binding:"required,checkArrayRequired" msg:"商品图片必填"` // 商品图片列表
	IsRedeem     uint32                `json:"is_redeem"`
	Sort         uint32                `json:"sort"`
	Detail       string                `json:"detail"`
	SkuList      AdminSpuCreateSkuList `json:"sku_list" binding:"required,checkArrayStructRequired,dive" msg:"sku必填"`
}

type AdminSpuCreateSkuList []*AdminSpuCreateSkuItem
type AdminSpuCreateSkuItem struct {
	SkuId         uint64          `json:"sku_id"`
	DeliveryList  SkuDeliveryList `json:"delivery_list" binding:"required,checkArrayStructRequired,dive" msg:"配送方式必填"`
	SkuUnitList   SkuUnitList     `json:"sku_unit_list" binding:"required,checkArrayStructRequired,dive" msg:"sku单位必填"`
	Title         string          `json:"title" binding:"required,checkStringLength=100" msg:"sku标题长度不正确"` // SKU 名称
	Code          string          `json:"code" binding:"required,checkStringLength=100" msg:"sku 编号无效"`    // SKU 编号
	Cover         string          `json:"cover" binding:"required"`                                        // 封面图片
	OriginalPrice uint64          `json:"original_price"`
	SellPrice     uint64          `json:"sell_price" binding:"required"`
	RefPrice      uint64          `json:"ref_price"`
	BarCode       string          `json:"bar_code" binding:"required"`
	Status        uint32          `json:"status" binding:"oneof=1 2"`
}

type SkuDeliveryList []*SkuDeliveryItem
type SkuDeliveryItem struct {
	DeliveryId uint32   `json:"delivery_id" binding:"oneof=1 2 3" msg:"配送方式必填"`
	ShopIds    []uint64 `json:"shop_ids" binding:"requiredIF=DeliveryId 2" msg:"请选择店铺"`
	JumpUrl    string   `json:"jump_url" binding:"requiredIF=DeliveryId 3" msg:"跳转链接必填"`
}

type SkuUnitList []*SkuUnitItem
type SkuUnitItem struct {
	ID        uint64 `json:"id"`
	Title     string `json:"title" binding:"required,checkStringLength=100" msg:"单位名称长度不正确"`
	UnitRatio uint32 `json:"unit_ratio" binding:"required,min=1,max=1000000" msg:"单位比例必填"`
	IsPrimary uint32 `json:"is_primary" binding:"required,oneof=1 2" msg:"是否为基础单位必填"`
	Status    uint32 `json:"status" binding:"oneof=1 2"`
}

func (ml SkuUnitList) CheckSkuUnitList() error {
	var (
		hasPrimary bool
	)
	for _, item := range ml {
		if item.IsPrimary == 1 {
			if hasPrimary {
				return errors.New("只可设置一个基础单位")
			}
			hasPrimary = true
			if item.UnitRatio != 1 {
				return errors.New("sku基础单位比例不正确")
			}
			if item.Status != 1 {
				return errors.New("sku基础单位状态必须为上架")
			}
		}
	}
	if !hasPrimary {
		return errors.New("请填写sku基础单位")
	}
	return nil
}

func (ml AdminSpuCreateSkuList) GetSkuInfos() ([]string, []uint64) {
	skuIds := []uint64{}
	skuIdMap := map[uint64]struct{}{}

	codeList := []string{}
	codeMap := map[string]struct{}{}
	for _, item := range ml {
		if item.Code == "" {
			continue
		}
		if _, ok := codeMap[item.Code]; ok {
			continue
		}
		codeMap[item.Code] = struct{}{}
		codeList = append(codeList, item.Code)

		if item.SkuId > 0 {
			if _, ok := skuIdMap[item.SkuId]; ok {
				continue
			}
			skuIdMap[item.SkuId] = struct{}{}
			skuIds = append(skuIds, item.SkuId)
		}
	}
	return codeList, skuIds
}

func (ml AdminSpuCreateSkuList) GetLowHighPrice() (uint64, uint64) {
	var lowPrice, highPrice uint64

	for idx, item := range ml {
		if idx == 0 {
			lowPrice, highPrice = item.SellPrice, item.SellPrice
		} else {
			if lowPrice > item.SellPrice {
				lowPrice = item.SellPrice
			}
			if highPrice < item.SellPrice {
				highPrice = item.OriginalPrice
			}
		}
	}
	return lowPrice, highPrice
}

func (ml AdminSpuCreateSkuList) CheckSkuList() error {
	for _, item := range ml {
		if err := item.SkuUnitList.CheckSkuUnitList(); err != nil {
			return err
		}
	}
	return nil
}
func (ml AdminSpuCreateSkuList) GetSkuList(spuId, batchId uint64) (skuDao.ModelList, map[string]skuShopDao.ModelList, map[string]SkuUnitList) {
	skuList := skuDao.ModelList{}
	skuShopMap := map[string]skuShopDao.ModelList{}
	skuUnitMap := map[string]SkuUnitList{}
	for _, item := range ml {
		skuItem, skuShopItem := item.ReqToSku(spuId, batchId)
		skuList = append(skuList, skuItem)
		skuShopMap[skuItem.Code] = skuShopItem
		skuUnitMap[skuItem.Code] = item.SkuUnitList
	}
	return skuList, skuShopMap, skuUnitMap
}

func (req AdminSpuCreateReq) ReqToSpuModel(spuId uint64) *spuDao.Model {
	ret := &spuDao.Model{
		ID:           spuId,
		BatchId:      req.BatchId,
		Title:        req.Title,
		SubTitle:     req.SubTitle,
		Code:         req.Code,
		Cover:        dbs.CdnImg(req.Cover),
		BrandId:      req.BrandId,
		VendorId:     req.VendorId,
		SupplierId:   req.SupplierId,
		Status:       req.Status,
		PreStartTime: req.PreStartTime,
		PreEndTime:   req.PreEndTime,
		Sort:         req.Sort,
		IsRedeem:     req.IsRedeem,
		Detail:       req.Detail,
	}
	ret.LowestPrice, ret.HighestPrice = req.SkuList.GetLowHighPrice()
	return ret
}

func (req AdminSpuCreateReq) ReqToSpuAtrr(spuId uint64) (imageDao.ModelList, spuTagDao.ModelList) {
	tagList, imageList := spuTagDao.ModelList{}, imageDao.ModelList{}

	for _, url := range req.ImageList {
		item := &imageDao.Model{
			MappingType: imageDao.MappingSpuImages,
			MappingID:   spuId,
			URL:         dbs.CdnImg(url),
		}
		imageList = append(imageList, item)
	}

	if req.CategoryId > 0 {
		tagList = append(tagList, &spuTagDao.Model{
			SpuId:   spuId,
			TagId:   req.CategoryId,
			GroupId: uint32(tagDao.GroupCateID),
		})
	}

	return imageList, tagList
}

func (req AdminSpuCreateSkuItem) ReqToSku(spuId, batchId uint64) (*skuDao.Model, skuShopDao.ModelList) {
	skuItem := &skuDao.Model{
		ID:            req.SkuId,
		SpuId:         spuId,
		BatchId:       batchId,
		Title:         req.Title,
		Code:          req.Code,
		Cover:         dbs.CdnImg(req.Cover),
		OriginalPrice: req.OriginalPrice,
		SellPrice:     req.SellPrice,
		RefPrice:      req.RefPrice,
		BarCode:       req.BarCode,
		Status:        req.Status,
	}
	skuShopList := skuShopDao.ModelList{}
	methodMap := map[uint32]struct{}{}

	for _, delivery := range req.DeliveryList {
		if _, ok := methodMap[delivery.DeliveryId]; ok {
			continue
		}
		if delivery.DeliveryId == 2 {
			for _, shopId := range delivery.ShopIds {
				skuShopList = append(skuShopList, &skuShopDao.Model{
					SpuId:  spuId,
					SkuId:  req.SkuId,
					ShopId: shopId,
				})
			}
		} else {
			delivery.ShopIds = []uint64{}
		}
		methodMap[delivery.DeliveryId] = struct{}{}

	}
	deliveryList, _ := json.Marshal(req.DeliveryList)
	skuItem.DeliveryList = string(deliveryList)
	return skuItem, skuShopList
}

type AdminSpuUpdateReq struct {
	SpuId uint64 `form:"spu_id" json:"spu_id" binding:"required"`
	AdminSpuCreateReq
}

type AdminSpuOperateReq struct {
	SpuIds []uint64 `form:"spu_ids" json:"spu_ids" binding:"required"`
	Action string   `form:"action" json:"action" binding:"oneof=open close"`
}

type GoodsCodeGenReq struct {
	CodeType helper.AppNoType `form:"code_type" json:"code_type" binding:"required"`
}

type GoodsCodeGenResp struct {
	Code string `json:"code"`
}
