package goods

import (
	"blind_box/app/dto/common"
)

type AdminSkuListReq struct {
	common.CommonSpuSkuFilterReq
	Status uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminSkuListResp struct {
	List  []CrSkuItem `json:"list"`
	Total int64       `json:"total"`
}

type AdminSkuDetailReq struct {
	SkuId uint64 `form:"sku_id" json:"sku_id" binding:"required"`
}

type AdminSkuOperateReq struct {
	SkuIds []uint64 `form:"sku_ids" json:"sku_ids" binding:"required"`
	Action string   `form:"action" json:"action" binding:"oneof=open close"`
}

type AdminSkuStockSetReq struct {
	SkuIds []uint64 `form:"sku_ids" json:"sku_ids" binding:"required"`
	Val    uint32   `form:"val" json:"val"`
}

type AdminSkuStockLogListReq struct {
	SkuId   uint64 `form:"sku_id" json:"sku_id" binding:"required"`
	LogType uint32 `form:"log_type" json:"log_type"`
	common.DataListReq
}

type AdminSkuStockLogListResp struct {
	List  []AdminSkuStockLogListItem `json:"list"`
	Total int64                      `json:"total"`
}

type AdminSkuStockLogListItem struct {
	ID        uint64    `json:"id"`
	LogType   uint32    `json:"log_type"`
	UserId    uint64    `json:"user_id"`
	EntityId  uint64    `json:"entity_id"` // 关联实体ID，比如盲盒ID、订单ID等
	Value     uint32    `json:"value"`
	BeforeVal uint32    `json:"before_val"`
	AfterVal  uint32    `json:"after_val"`
	CreatedAt string    `json:"created_at"`
	SkuInfo   CrSkuItem `json:"sku_info"`
}
