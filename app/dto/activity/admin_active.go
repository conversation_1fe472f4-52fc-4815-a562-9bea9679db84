package activity

import (
	"blind_box/app/dto/common"
)

type AdminActiveCreateReq struct {
	ActiveTitle string `json:"activeTitle" binding:"required"` // 活动标题

	ActiveStatus uint32 `json:"activeStatus" binding:"required,oneof=1 2"` // 活动状态 1:进行中 2:已结束

	DeliveryExpect string `json:"deliveryExpect"` // 预计发货时间

	Store uint32 `json:"store"` // 库存

	ActiveImage string `json:"activeImage" binding:"required"` // 活动图片

	DetailImages []string `json:"detailImages"` // 活动详情图片

	ActivePrice uint64 `json:"activePrice" binding:"required"` // 活动价格

	OpenTime int64 `json:"openTime" binding:"required"` // 开启时间

	EndTime int64 `json:"endTime"` // 结束时间

	//ActiveTagId string `json:"activeTagId"` // 活动标签ID
}

type AdminActiveCreateResp struct {
	ID uint64 `json:"id"` // 活动ID
}

type AdminActiveListReq struct {
	common.DataListReq

	StartTime      int64  `json:"startTime" form:"startTime"`           // 开始时间
	EndTime        int64  `json:"endTime" form:"endTime"`               // 结束时间
	ActivityID     uint64 `json:"activityId" form:"activityId"`         // 活动ID
	ActivityTitle  string `json:"activityTitle" form:"activityTitle"`   // 活动标题
	ActivityStatus uint32 `json:"activityStatus" form:"activityStatus"` // 活动状态 1:进行中 2:已结束
}

type AdminActiveListResp struct {
	Count int64                  `json:"count"` // 总数
	List  []*AdminActiveListItem `json:"list"`
}

type AdminActiveListItem struct {
	ID             uint64   `json:"id"`             // 活动ID
	ActivityTitle  string   `json:"activityTitle"`  // 活动标题
	Store          uint32   `json:"store"`          // 库存
	ActiveImage    string   `json:"activeImage"`    // 活动图片
	ActivePrice    uint64   `json:"activePrice"`    // 活动价格
	ActiveStatus   uint32   `json:"activeStatus"`   // 活动状态 1:进行中 2:已结束
	OpenTime       int64    `json:"openTime"`       // 开启时间
	EndTime        int64    `json:"endTime"`        // 结束时间
	CurStock       uint32   `json:"curStock"`       // 当前库存 // TODO: 商品库存?
	Stock          uint32   `json:"stock"`          // 库存
	OverSell       uint32   `json:"overSell"`       // 超卖
	DeliveryExpect string   `json:"deliveryExpect"` // 预计发货时间
	DetailImages   []string `json:"detailImages"`   // 活动详情图片
}

type AdminActiveUpdateReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"` // 活动ID
	*AdminActiveCreateReq
}

type AdminActiveUpdateResp struct {
	ID uint64 `json:"id"` // 活动ID
}

type BoxActiveRecommendCheckReq struct {
	ActiveId uint64 `form:"activeId" json:"activeId" binding:"required"` // 活动ID
}

type AdminActiveInfoReq struct {
	ActiveId uint64 `form:"activeId" json:"activeId" binding:"required"` // 活动ID
}

type AdminActiveInfoResp struct {
	*AdminActiveListItem
}
