package activity

type SubscribeActivityReq struct {
	ActID uint64 `form:"act_id" json:"act_id" binding:"required,omitempty"`
}

type ActivitySubscribeListItem struct {
	ID          uint64 `json:"id"`
	ActID       uint64 `json:"act_id"`       // 活动id
	ActTitle    string `json:"act_title"`    // 活动标题
	StartTime   string `json:"start_time"`   // 活动开始时间
	Cover       string `json:"cover"`        // 活动缩略图
	ActPrice    uint64 `json:"act_price"`    // 活动价格
	CreatedTime string `json:"created_time"` // 创建日期
}

type ActivitySubscribeListResp struct {
	List     []*ActivitySubscribeListItem `json:"list"`
	Total    int64                        `json:"total"`
	ToRemind int64                        `json:"to_remind"`
}
