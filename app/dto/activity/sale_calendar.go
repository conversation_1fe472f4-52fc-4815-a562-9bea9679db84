package activity

import "blind_box/app/dto/common"

type AdminSaleCalendarListReq struct {
	ActID    uint64 `form:"act_id" json:"act_id"`
	ActTitle string `form:"act_title" json:"act_title"`
	common.DataListReq
}

type AdminSaleCalendarListItem struct {
	ID           uint64 `json:"id"`
	ActID        uint64 `json:"act_id"`        // 活动id
	ActTitle     string `json:"act_title"`     // 活动标题
	StartTime    string `json:"start_time"`    // 活动开始时间
	Cover        string `json:"cover"`         // 活动缩略图
	Sort         uint32 `json:"sort"`          // 后台排序
	TimeDesc     string `json:"time_desc"`     // 时间标志说明
	SubscribeNum uint32 `json:"subscribe_num"` // 预约人数
	CreatedTime  string `json:"created_time"`  // 创建日期
}

type AdminSaleCalendarListResp struct {
	List  []*AdminSaleCalendarListItem `json:"list"`
	Total int64                        `json:"total"`
}

type AdminSetSaleCalendarReq struct {
	ID       uint64 `form:"id" json:"id"`
	ActID    uint64 `form:"act_id" json:"act_id" binding:"required,omitempty"`
	Sort     uint32 `form:"sort" json:"sort" binding:"required,omitempty,min=0,max=100000000"`
	TimeDesc string `form:"time_desc" json:"time_desc" binding:"omitempty,max=200"`
}

type AdminDelSaleCalendarReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type SaleCalendarListReq struct {
	UID uint64 `form:"uid" json:"uid"`
	common.DataListReq
}

type SaleCalendarListItem struct {
	ID          uint64 `json:"id"`
	ActID       uint64 `json:"act_id"`       // 活动id
	ActTitle    string `json:"act_title"`    // 活动标题
	StartTime   string `json:"start_time"`   // 活动开始时间
	Cover       string `json:"cover"`        // 活动缩略图
	Sort        uint32 `json:"sort"`         // 后台排序
	TimeDesc    string `json:"time_desc"`    // 时间标志说明
	ActPrice    uint64 `json:"act_price"`    // 活动价格
	IsSubscribe uint32 `json:"is_subscribe"` // 是否订阅
	CreatedTime string `json:"created_time"` // 创建日期
}

type SaleCalendarListResp struct {
	List  []*SaleCalendarListItem `json:"list"`
	Total int64                   `json:"total"`
}
