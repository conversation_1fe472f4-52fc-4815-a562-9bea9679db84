package admin

import "blind_box/app/dto/common"

type AdminAccountListReq struct {
	ID      uint64 `form:"id" json:"id"`
	RoleID  uint64 `form:"role_id" json:"role_id"`
	Account string `form:"account" json:"account"`
	Mobile  string `form:"mobile" json:"mobile"`
	Name    string `form:"name" json:"name"`
	Status  uint32 `form:"status" json:"status"`
	NotRids []uint64
	NotAids []uint64
	common.DataListReq
}

type AdminAccountListItem struct {
	ID        uint64 `json:"id"`
	Account   string `json:"account"`
	Name      string `json:"name"`
	Mobile    string `json:"mobile"`
	RoleID    uint64 `json:"role_id"`
	RoleName  string `json:"role_name"`
	Status    uint32 `json:"status"`
	IsAdmin   bool   `json:"is_admin"`
	CreatedAt string `json:"created_at"`
}

type AdminAccountListResp struct {
	List  []*AdminAccountListItem `json:"list"`
	Total int64                   `json:"total"`
}

type AdminSetAccountReq struct {
	ID      uint64 `form:"id" json:"id" binding:"omitempty"`
	Account string `form:"account" json:"account" binding:"required,omitempty,max=32"`
	Name    string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Mobile  string `form:"mobile" json:"mobile" binding:"required,omitempty"`
	RoleID  uint64 `form:"role_id" json:"role_id" binding:"required,omitempty"`
	Status  uint32 `form:"status" json:"status" binding:"oneof=1 2"`
	Pwd     string `form:"pwd" json:"pwd" binding:"omitempty,min=6,max=32"`
}

type AdminDelAccountReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminSetAccountPwdReq struct {
	ID  uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Pwd string `form:"pwd" json:"pwd" binding:"required,omitempty,min=6,max=32"`
}

type AdminGetAccountInfoResp struct {
	ID        uint64   `json:"id"`
	Account   string   `json:"account"`
	Name      string   `json:"name"`
	Mobile    string   `json:"mobile"`
	RoleID    uint64   `json:"role_id"`
	RoleName  string   `json:"role_name"`
	FeAuth    []string `json:"fe_auth"`
	CreatedAt string   `json:"created_at"`
}

type AdminAccountAllReq struct {
	FilterAdmin uint32 `form:"filter_admin" json:"filter_admin"`
}
type AdminAccountAllItem struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}
