package admin

import "blind_box/app/dto/common"

// 用户积分编辑请求
type AdminEditUserPointsReq struct {
	UserID     uint64 `json:"userId" binding:"required" msg:"用户ID不能为空"`
	ActionType uint32 `json:"actionType" binding:"required,oneof=1 2" msg:"操作类型错误：1-增加，2-减少"`
	Points     uint64 `json:"points" binding:"required,min=1" msg:"积分数量必须大于0"`
	Remark     string `json:"remark" binding:"required,max=200" msg:"备注不能为空且长度不超过200字符"`
	OperatorID uint64 `json:"-"` // 操作员ID，从上下文获取
}

// 用户积分编辑响应
type AdminEditUserPointsRes struct {
	UserID        uint64 `json:"userId"`        // 用户ID
	UserName      string `json:"userName"`      // 用户昵称
	ActionType    uint32 `json:"actionType"`    // 操作类型：1-增加，2-减少
	Points        uint64 `json:"points"`        // 变更的积分数量
	BalanceBefore uint64 `json:"balanceBefore"` // 操作前积分余额
	BalanceAfter  uint64 `json:"balanceAfter"`  // 操作后积分余额
	Remark        string `json:"remark"`        // 备注
	CreatedTime   string `json:"createdTime"`   // 创建时间
}

// 用户积分记录查询请求
type AdminUserPointsLogReq struct {
	common.DataListReq
	UserID     uint64 `json:"userId" form:"userId"`         // 用户ID
	ActionType uint32 `json:"actionType" form:"actionType"` // 操作类型：0-全部，1-增加，2-减少，3-抵扣明细，4-过期扣减
	SourceType uint32 `json:"sourceType" form:"sourceType"` // 来源类型：0-全部，1-注册奖励...10-管理员操作
	StartTime  string `json:"startTime" form:"startTime"`   // 开始时间 YYYY-MM-DD
	EndTime    string `json:"endTime" form:"endTime"`       // 结束时间 YYYY-MM-DD
	Name       string `json:"name" form:"name"`             // 用户昵称模糊搜索
}

// 用户积分记录查询响应
type AdminUserPointsLogRes struct {
	List  []*AdminUserPointsLogItem `json:"list"`
	Total int64                     `json:"total"`
}

// 用户积分记录项
type AdminUserPointsLogItem struct {
	ID              uint64 `json:"id"`              // 记录ID
	UserID          uint64 `json:"userId"`          // 用户ID
	UserName        string `json:"userName"`        // 用户昵称
	ActionType      uint32 `json:"actionType"`      // 操作类型
	ActionTypeName  string `json:"actionTypeName"`  // 操作类型名称
	Points          uint64 `json:"points"`          // 变更积分数
	BalanceBefore   uint64 `json:"balanceBefore"`   // 操作前余额
	BalanceAfter    uint64 `json:"balanceAfter"`    // 操作后余额
	SourceType      uint32 `json:"sourceType"`      // 来源类型
	SourceTypeName  string `json:"sourceTypeName"`  // 来源类型名称
	SourceID        uint64 `json:"sourceId"`        // 来源ID
	Remark          string `json:"remark"`          // 备注
	RemainingPoints uint64 `json:"remainingPoints"` // 剩余积分（仅对增加类型有效）
	ExpireAt        string `json:"expireAt"`        // 过期时间
	IsExpired       bool   `json:"isExpired"`       // 是否已过期
	CreatedTime     string `json:"createdTime"`     // 创建时间
}

// 用户积分统计请求
type AdminUserPointsStatsReq struct {
	UserID uint64 `json:"userId" form:"userId" binding:"required" msg:"用户ID不能为空"`
}

// 用户积分统计响应
type AdminUserPointsStatsRes struct {
	UserID        uint64 `json:"userId"`        // 用户ID
	UserName      string `json:"userName"`      // 用户昵称
	TotalEarned   uint64 `json:"totalEarned"`   // 总获得积分
	TotalSpent    uint64 `json:"totalSpent"`    // 总消费积分
	CurrentPoints uint64 `json:"currentPoints"` // 当前积分余额
	ExpiredPoints uint64 `json:"expiredPoints"` // 已过期积分
	ExpiringCount uint64 `json:"expiringCount"` // 即将过期积分（30天内）
}
