package user

import couponDto "blind_box/app/dto/coupon"

type RewardNewUserTideItem struct {
	Name string `json:"name"`
	Img  string `json:"img"`
	Num  uint32 `json:"num"`
}

type RewardNewUserCardItem struct {
	Name       string `json:"name"`
	Type       uint32 `json:"type"`
	Img        string `json:"img"`
	Num        uint32 `json:"num"`
	ExpireDays uint32 `json:"expire_days"`
}

type RewardNewUserInfoResp struct {
	Title      string                          `json:"title"`
	Desc       string                          `json:"desc"`
	TideList   []*RewardNewUserTideItem        `json:"tide_list"`
	CouponList []*couponDto.HomepageCouponItem `json:"coupon_list"`
	CardList   []*RewardNewUserCardItem        `json:"card_list"`
}

type RewardNewUserStatusResp struct {
	IsGet uint32 `json:"is_get"`
}
