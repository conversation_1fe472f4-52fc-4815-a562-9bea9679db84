package user

type UserLoginThirdReq struct {
	AuthType  uint32 `form:"auth_type" json:"auth_type" binding:"oneof=1 2 3"` // 1苹果ID 2google邮箱 3faceBook
	AuthToken string `form:"auth_token" json:"auth_token" binding:"required,omitempty"`
}

type UserLoginCommonReq struct {
	AuthToken string `form:"auth_token" json:"auth_token" binding:"required,omitempty"`
	Code      string `form:"code" json:"code" binding:"required,omitempty"`
}

type UserLoginResp struct {
	IsFirst  uint32             `json:"is_first"`
	Token    string             `json:"token"`
	UserInfo *AdminUserListItem `json:"user_info"`
}

type UserEditReq struct {
	Nickname string `form:"nickname" json:"nickname" binding:"omitempty,max=64"`
	Avatar   string `form:"avatar" json:"avatar" binding:"omitempty,max=128"`
	Email    string `form:"email" json:"email" binding:"omitempty,max=64"`
}

type UserEditPwdReq struct {
	OldPwd     string `form:"old_pwd" json:"old_pwd" binding:"required"`
	NewPwd     string `form:"new_pwd" json:"new_pwd" binding:"required,omitempty,min=6,max=32"`
	ConfirmPwd string `form:"confirm_pwd" json:"confirm_pwd" binding:"required,omitempty,min=6,max=32"`
}

type UserIndexCountResp struct {
	PendingCollectionNum int64  `json:"pending_collection_num"` // 待付款
	PendingSendNum       int64  `json:"pending_send_num"`       // 待发放
	SendNum              int64  `json:"send"`                   // 已发放
	PendingOutNum        uint64 `json:"pending_out_num"`        // 待发货
	OutNum               uint64 `json:"out_num"`                // 已发货
}
