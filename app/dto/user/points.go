package user

// UserPointsBillItem 用户积分明细项
type UserPointsBillItem struct {
	ID          uint64 `json:"id"`           // 日志ID
	UserID      uint64 `json:"user_id"`      // 用户ID
	UserName    string `json:"user_name"`    // 用户昵称
	ActionType  uint32 `json:"action_type"`  // 操作类型: 1增加 2减少 3抵扣明细 4过期扣减
	Points      uint64 `json:"points"`       // 变更积分数
	SourceType  uint32 `json:"source_type"`  // 来源类型
	SourceName  string `json:"source_name"`  // 来源名称
	Remark      string `json:"remark"`       // 备注
	CreatedTime string `json:"created_time"` // 创建时间
}

// UserPointsBillResp 用户积分明细响应
type UserPointsBillResp struct {
	List  []*UserPointsBillItem `json:"list"`
	Total int64                 `json:"total"`
}
