package user

import "blind_box/app/dto/common"

type AdminUserFilterReq struct {
	UserID     uint64 `form:"user_id" json:"user_id"`
	UserName   string `form:"user_name" json:"user_name"`
	Account    string `form:"account" json:"account"`
	Mobile     string `form:"mobile" json:"mobile"`
	UserTypeID uint64 `form:"user_type_id" json:"user_type_id"`
	ManagerID  uint64 `form:"manager_id" json:"manager_id"`
}

type AdminUserListReq struct {
	ID       uint64 `form:"id" json:"id"`
	Nickname string `form:"nickname" json:"nickname"`
	Mobile   string `form:"mobile" json:"mobile"`
	Email    string `form:"email" json:"email"`
	Status   uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminUserListItem struct {
	ID            uint64              `json:"id"`
	Nickname      string              `json:"nickname"`
	Avatar        string              `json:"avatar"`
	Mobile        string              `json:"mobile"`
	Email         string              `json:"email"`
	Status        uint32              `json:"status"`
	TideVal       uint32              `json:"tide_val"` // 潮玩值
	Points        uint64              `json:"points"`   // 积分余额
	LastLoginTime string              `json:"last_login_time"`
	LevelInfo     *AdminLevelListItem `json:"level_info"`

	// CreatedAt     string `json:"created_at"`
}

type AdminUserListResp struct {
	List  []*AdminUserListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminUserInfoReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminOperateUserReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}
