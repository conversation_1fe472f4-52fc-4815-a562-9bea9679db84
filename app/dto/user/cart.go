package user

import (
	"blind_box/app/dto/common"
	goodsDto "blind_box/app/dto/goods"
)

type UserCartListReq struct {
	SellType uint32 `form:"sell_type" json:"sell_type"`
	SpuTitle string `form:"spu_title" json:"spu_title"`
	common.DataListReq
}

type UserCartListSkuItem struct {
	CartId     uint64             `json:"cart_id"`
	UserId     uint64             `json:"user_id"`
	SpuId      uint64             `json:"spu_id"`
	SkuId      uint64             `json:"sku_id"`
	UnitNum    uint32             `json:"unit_num"`
	DeliveryId uint32             `json:"delivery_id"`
	ShopId     uint64             `json:"shop_id"`
	ShopName   string             `json:"shop_name"`
	CreatedAt  string             `json:"created_at"`
	SkuInfo    goodsDto.CrSkuItem `json:"sku_info"`
}

type UserCartListResp struct {
	List  []*UserCartListSkuItem `json:"list"`
	Total int64                  `json:"total"`
}

type UserCartAddReq struct {
	SkuId      uint64 `form:"sku_id" json:"sku_id" binding:"required,omitempty"`
	UnitNum    uint32 `form:"unit_num" json:"unit_num" binding:"required,omitempty,min=1,max=10000000"`
	DeliveryId uint32 `form:"delivery_id" json:"delivery_id" binding:"required,omitempty"`
	ShopId     uint64 `form:"shop_id" json:"shop_id" binding:"requiredIF=DeliveryId 2" msg:"请选择店铺"`
}

type UserCartEditReq struct {
	CartId     uint64 `form:"cart_id" json:"cart_id" binding:"required,omitempty"`
	UnitNum    uint32 `form:"unit_num" json:"unit_num" binding:"required,omitempty,min=1,max=10000000"`
	DeliveryId uint32 `form:"delivery_id" json:"delivery_id"`
	ShopId     uint64 `form:"shop_id" json:"shop_id"`
}

type UserCartDelReq struct {
	CartIds []uint64 `form:"cart_ids" json:"cart_ids" binding:"checkArrayRequired,dive,gt=0" msg:"请选择商品"`
}

type UserCartCountResp struct {
	CartNum      int64 `json:"cart_num"`
	CartGoodsNum int64 `json:"cart_goods_num"`
}
