package user

type SignConfig struct {
	Days       uint32 `json:"days"`        // 连续签到天数(1-7)
	RewardType uint32 `json:"reward_type"` // 奖励类型: 1 潮气值
	RewardVal  uint32 `json:"reward_val"`  // 奖励值: 潮气值
	IsSign     uint32 `json:"is_sign"`     // 是否签到: 1已签到 2未签到
}

type SignConfigResp struct {
	ConfigList       []*SignConfig `json:"config_list"`
	ContinueSignDays uint32        `json:"continue_sign_days"` // 连续签到天数
	TodayIsSign      uint32        `json:"today_is_sign"`      // 今日是否已签到
}
