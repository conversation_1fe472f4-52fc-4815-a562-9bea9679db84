package user

import "blind_box/app/dto/common"

type AdminLevelListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminLevelListItem struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	Icon      string `json:"icon"`
	Min       uint32 `json:"min"`
	Max       uint32 `json:"max"`
	Status    uint32 `json:"status,omitempty"`
	CreatedAt string `json:"created_at,omitempty"`
}

type AdminLevelListResp struct {
	List  []*AdminLevelListItem `json:"list"`
	Total int64                 `json:"total"`
}

type AdminLevelSetReq struct {
	ID   uint64 `form:"id" json:"id" binding:"omitempty"`
	Name string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Icon string `form:"icon" json:"icon" binding:"omitempty,max=128"`
	Min  uint32 `form:"min" json:"min" binding:"omitempty"`
	Max  uint32 `form:"max" json:"max" binding:"omitempty"`
}

type AdminLevelOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}
