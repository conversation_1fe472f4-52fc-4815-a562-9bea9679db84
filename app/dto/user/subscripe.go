package user

import "blind_box/app/dto/common"

type SubscribeListReq struct {
	EntityType uint32 `json:"entity_type" form:"entity_type" binding:"oneof=1 2 3 4 5"`
	common.DataListReq
}

type SubscribeListResp struct {
	List  []*SubscribeListItem `json:"list"`
	Total int64                `json:"total"`
}

type SubscribeListItem struct {
	SubId      uint64                     `json:"sub_id"`
	UserId     uint64                     `json:"user_id"`
	EntityId   uint64                     `json:"entity_id"`
	EntityType uint32                     `json:"entity_type"`
	Status     uint32                     `json:"status"`
	BoxInfo    *SubscribeBoxListItem      `json:"box_info"`
	SkuInfo    *SubscribeSkuListItem      `json:"sku_info"`
	ActInfo    *SubscribeActivityListItem `json:"act_info"`
	CreatedAt  string                     `json:"created_at"`
}

type SubscribeBoxListItem struct {
	ID          uint64 `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Image       string `json:"image"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

type SubscribeSkuListItem struct {
	ID          uint64 `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Image       string `json:"image"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

type SubscribeActivityListItem struct {
	ID          uint64 `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Image       string `json:"image"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

type SubscribeAddReq struct {
	EntityId   uint64 `json:"entity_id" form:"entity_id" binding:"required"`
	EntityType uint32 `json:"entity_type" form:"entity_type" binding:"oneof=1 2 3 4 5"`
}
