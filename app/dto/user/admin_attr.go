package user

import (
	"blind_box/app/dto/common"
)

type AdminUserAddrListReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	common.DataListReq
}

type AdminUserAddrListResp struct {
	List  []*common.CommonUserAddrInfo `json:"list"`
	Total int64                        `json:"total"`
}

type AdminSetUserAddrReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	SetUserAddrReq
}
type SetUserAddrReq struct {
	ID        uint64 `form:"id" json:"id" binding:"omitempty"`
	Consignee string `form:"consignee" json:"consignee" binding:"required,omitempty,max=32"`
	Mobile    string `form:"mobile" json:"mobile" binding:"required,omitempty"`
	Area      string `form:"area" json:"area" binding:"required,omitempty,max=128"`
	Address   string `form:"address" json:"address" binding:"required,omitempty,max=128"`
	IsDefault uint32 `form:"is_default" json:"is_default" binding:"oneof=0 1"`
}

type AdminUserAddrInfoReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminUserLoginLogListReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	common.DataListReq
}

type AdminUserLoginLogListItem struct {
	ID            uint64 `json:"id"`
	UserID        uint64 `json:"user_id"`
	ClientIp      string `json:"client_ip"`
	Source        uint32 `json:"source"`
	Device        string `json:"device"`
	AgreeProtocol uint32 `json:"agree_protocol"`
	CreatedAt     string `json:"created_at"`
}
type AdminUserLoginLogListResp struct {
	List  []*AdminUserLoginLogListItem `json:"list"`
	Total int64                        `json:"total"`
}
