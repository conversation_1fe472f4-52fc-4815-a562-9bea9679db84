package user

type TideTaskConfig struct {
	Id       uint64 `json:"id"`        // 任务id
	Title    string `json:"title"`     // 任务标题
	Cover    string `json:"cover"`     // 任务封面
	TideVal  uint32 `json:"tide_val"`  // 潮气值
	TaskType uint32 `json:"task_type"` // 任务类型
	IsFinish uint32 `json:"is_finish"` // 是否完成
	IsGet    uint32 `json:"is_get"`    // 是否领取
}
type TideTaskConfigResp struct {
	ConfigList []*TideTaskConfig `json:"config_list"`
}

type UserTideBillItem struct {
	Id          uint64 `json:"id"`            // 日志id
	UserId      uint64 `json:"user_id"`       // 用户id
	UserName    string `json:"user_name"`     // 用户昵称
	Source      uint32 `json:"source"`        // 来源
	SourceName  string `json:"source_name"`   // 来源名称
	TideVal     uint32 `json:"tide_val"`      // 潮气值
	TideValType uint32 `json:"tide_val_type"` // 潮气值类型:1 增加 2减少
	Remark      string `json:"remark"`        // 备注
	CreatedTime string `json:"created_time"`  // 创建日期
}

type UserTideBillResp struct {
	List  []*UserTideBillItem `json:"list"`
	Total int64               `json:"total"`
}

type UserGetTaskTideReq struct {
	TaskId uint64 `form:"task_id" json:"task_id" binding:"required,omitempty"`
}
