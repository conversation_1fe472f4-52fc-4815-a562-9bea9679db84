package user

type UserCreateReceiptReq struct {
	ReceiptType      uint32   `form:"receipt_type" json:"receipt_type" binding:"oneof=1 2 3 4"`                  // 收款单类别 1:销售收入 2:预充值收入 3:保证金收入 4:其他收入
	Amount           uint64   `form:"amount" json:"amount" binding:"required,max=***************"`               // 金额(分)
	CollectionTime   int64    `form:"collection_time" json:"collection_time" binding:"required,omitempty"`       // 收款时间
	FinanceAccountID uint64   `form:"finance_account_id" json:"finance_account_id" binding:"required,omitempty"` // 收款账户id
	Attachment       []string `form:"attachment" json:"attachment"`                                              // 附件
	Remark           string   `form:"remark" json:"remark" binding:"omitempty,max=100"`                          // 备注说明
}

type UserUpdateReceiptReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
	UserCreateReceiptReq
}
