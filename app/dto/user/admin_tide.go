package user

import "blind_box/app/dto/common"

type AdminTideBillReq struct {
	UserId    uint64 `form:"user_id" json:"user_id"`
	Source    uint32 `form:"source" json:"source"`
	StartTime int64  `form:"start_time" json:"start_time"`
	EndTime   int64  `form:"end_time" json:"end_time"`
	common.DataListReq
}

type AdminSendTideReq struct {
	UserId  uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	TideVal int32  `form:"tide_val" json:"tide_val" binding:"required,min=-100,max=100,omitempty"` // 发放 or 扣除 tide 值
	Remark  string `form:"remark" json:"remark" binding:"omitempty,max=200"`
}
