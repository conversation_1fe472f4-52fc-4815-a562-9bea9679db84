package user

type TideCardConfig struct {
	Id              uint64 `json:"id"`                // 道具id
	CardType        uint32 `json:"card_type"`         // 卡片类型
	CardNum         uint32 `json:"card_num"`          // 卡片数量
	OriginalTideVal uint32 `json:"original_tide_val"` // 原来所需潮气值
	TideVal         uint32 `json:"tide_val"`          // 所需潮气值
	ExpireHour      uint32 `json:"expire_hour"`       // 过期时间(h)
	DayLimit        uint32 `json:"day_limit"`         // 每天限制兑换次数
	IsChange        uint32 `json:"is_change"`         // 是否兑换
}
type TideCardConfigResp struct {
	ConfigList []*TideCardConfig `json:"config_list"`
}

type UserTideExchangeCardReq struct {
	CardId uint64 `form:"card_id" json:"card_id" binding:"required,omitempty"`
}
