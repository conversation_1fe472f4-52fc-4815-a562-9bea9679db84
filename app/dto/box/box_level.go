package box

type AdminBoxLevelAddReq struct {
	LevelName string `json:"levelName" binding:"required"`
}

type AdminBoxLevelAddResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxLevelUpdateReq struct {
	ID        uint64 `form:"id" json:"id" binding:"required"`
	LevelName string `json:"levelName" binding:"required"`
}

type AdminBoxLevelUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxLevelListReq struct {
}

type AdminBoxLevelListResp struct {
	List []AdminBoxLevelListItem `json:"list"`
}

type AdminBoxLevelListItem struct {
	ID        uint64 `json:"id"`
	LevelName string `json:"levelName"`
}
