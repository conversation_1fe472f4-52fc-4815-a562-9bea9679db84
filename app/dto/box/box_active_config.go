package box

type AdminActiveRecommendCheckReq struct {
	ActiveID uint64 `form:"activeId" json:"activeId" binding:"required"`
}

type AdminActiveRecommendCheckResp struct {
}

type AdminActiveConfigCreateReq struct {
	ActiveId      uint64 `json:"activeId" binding:"required"`                                    // 活动id
	LotteryNum    uint32 `json:"lotteryNum" binding:"required,gte=6,lte=16" msg:"抽奖数量范围限制在6-16"` // 抽奖次数范围6-16次
	HeadImage     string `json:"headImage" binding:"required"`                                   // 头像
	LocationImage string `json:"locationImage" binding:"required"`                               // 位置图
	BoxImage      string `json:"boxImage" binding:"required"`                                    // 盲盒图
	LevelText     string `json:"levelText"`                                                      // 盲盒说明
	ShakeNum      uint32 `json:"shakeNum"`
}

type AdminActiveConfigCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminActiveConfigUpdateReq struct {
	ID uint64 `json:"id" binding:"required"`
	*AdminActiveConfigCreateReq
}

type AdminActiveConfigUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminActiveConfigInfoReq struct {
	ID       uint64 `form:"id" json:"id"`
	ActiveID uint64 `form:"activeId" json:"activeId" binding:"required"`
}

type AdminActiveConfigInfoResp struct {
	ID            uint64 `json:"id"`
	ActiveID      uint64 `json:"activeId"`
	LotteryNum    uint32 `json:"lotteryNum"`
	HeadImage     string `json:"headImage"`
	LocationImage string `json:"locationImage"`
	BoxImage      string `json:"boxImage"`
	LevelText     string `json:"levelText"`
	ShakeNum      uint32 `json:"shakeNum"`
}
