package box

import "blind_box/app/dto/common"

// 商品列表请求
type GoodsListReq struct {
	common.DataListReq
	ActiveID uint64 `json:"activeId" form:"activeId"` // 活动ID，可选
	LevelID  uint64 `json:"levelId" form:"levelId"`   // 等级ID，可选
	Keyword  string `json:"keyword" form:"keyword"`   // 搜索关键词，可选
}

// 商品列表响应
type GoodsListRes struct {
	List  []*GoodsListItem `json:"list"`
	Count int64            `json:"count"`
	IsEnd bool             `json:"isEnd"`
}

// 商品列表项
type GoodsListItem struct {
	ID          uint64  `json:"id"`
	GoodsName   string  `json:"goodsName"`
	GoodsCover  string  `json:"goodsCover"`
	LevelName   string  `json:"levelName"`
	Stock       uint32  `json:"stock"`
	CurStock    uint32  `json:"curStock"`
	Price       uint64  `json:"price"`       // 商品价格
	OriginPrice uint64  `json:"originPrice"` // 原价
	SaleRate    float64 `json:"saleRate"`    // 销售率
}

// 商品详情请求
type GoodsDetailReq struct {
	GoodsID uint64 `json:"goodsId" form:"goodsId" binding:"required"`
}

// 商品详情响应
type GoodsDetailRes struct {
	ID          uint64           `json:"id"`
	GoodsName   string           `json:"goodsName"`
	GoodsCover  string           `json:"goodsCover"`
	LevelName   string           `json:"levelName"`
	Stock       uint32           `json:"stock"`
	CurStock    uint32           `json:"curStock"`
	Price       uint64           `json:"price"`
	OriginPrice uint64           `json:"originPrice"`
	Description string           `json:"description"`
	Images      []string         `json:"images"`
	SkuInfo     *GoodsSkuInfo    `json:"skuInfo"`
	ActiveInfo  *GoodsActiveInfo `json:"activeInfo"`
	Statistics  *GoodsStatistics `json:"statistics"`
}

// 商品SKU信息
type GoodsSkuInfo struct {
	SpuID    uint64            `json:"spuId"`
	SkuID    uint64            `json:"skuId"`
	SkuName  string            `json:"skuName"`
	SkuImage string            `json:"skuImage"`
	Attrs    map[string]string `json:"attrs"` // SKU属性
}

// 商品关联活动信息
type GoodsActiveInfo struct {
	ActiveID    uint64 `json:"activeId"`
	ActiveTitle string `json:"activeTitle"`
	ActiveType  uint32 `json:"activeType"`
	StartTime   string `json:"startTime"`
	EndTime     string `json:"endTime"`
}

// 商品统计信息
type GoodsStatistics struct {
	TotalSales uint32  `json:"totalSales"` // 总销量
	SaleRate   float64 `json:"saleRate"`   // 销售率
	UserCount  uint32  `json:"userCount"`  // 购买用户数
}

// 购物车商品条目
type CartItem struct {
	SkuID    uint64 `json:"skuId" binding:"required"`          // SKU商品ID
	Quantity uint32 `json:"quantity" binding:"required,min=1"` // 购买数量
}

// 直接购买请求（统一购物车模式）
type DirectPurchaseReq struct {
	UserID    uint64      `json:"userId" form:"userId"`
	CartItems []*CartItem `json:"cartItems"` // 购物车商品列表（单个商品也使用此结构）
	PayMethod uint32      `json:"payMethod" form:"payMethod" binding:"required"`
	CouponID  uint64      `json:"couponId" form:"couponId"`

	DeliveryID uint32 `json:"delivery_id" form:"delivery_id"` // 配送方式ID，可选
	ShopID     uint64 `json:"shop_id" form:"shop_id"`         // 店铺ID，可选
	AddressID  uint64 `json:"address_id" form:"address_id"`   // 收货地址ID，可选

	UsePoints uint32 `json:"usePoints" form:"usePoints"` // 是否使用积分抵扣，1-使用

	OrderID uint64 `json:"orderId" form:"orderId"` // 订单ID，更新订单时使用
}

// 直接购买响应
type DirectPurchaseRes struct {
	OrderID    uint64      `json:"orderId"`
	TradeNo    string      `json:"tradeNo"`
	TotalFee   uint64      `json:"totalFee"`
	TotalItems uint32      `json:"totalItems"` // 总商品数量
	SkuCount   uint32      `json:"skuCount"`   // SKU种类数量
	PayInfo    interface{} `json:"payInfo"`    // 支付信息，具体结构依赖支付方式
}

// 商品库存检查请求（支持CartItems）
type GoodsStockCheckReq struct {
	CartItems []*CartItem `json:"cartItems" binding:"required,min=1,dive"` // 购物车商品列表
}

// 商品库存检查响应（返回详细SKU信息）
type GoodsStockCheckRes struct {
	Items        []*CartItemStockInfo `json:"items"`        // 各商品库存详情
	TotalItems   uint32               `json:"totalItems"`   // 总商品数量
	SkuCount     uint32               `json:"skuCount"`     // SKU种类数量
	AllAvailable bool                 `json:"allAvailable"` // 所有商品是否都有足够库存
}

// 购物车商品库存详情
type CartItemStockInfo struct {
	SkuID          uint64 `json:"skuId"`          // SKU ID
	SkuName        string `json:"skuName"`        // SKU名称
	SkuCover       string `json:"skuCover"`       // SKU封面图
	Quantity       uint32 `json:"quantity"`       // 需求数量
	AvailableStock uint32 `json:"availableStock"` // 可用库存
	TotalStock     uint32 `json:"totalStock"`     // 总库存
	Available      bool   `json:"available"`      // 是否有足够库存
	IsInStock      bool   `json:"isInStock"`      // 商品是否有库存
	CanOrder       bool   `json:"canOrder"`       // 是否可订购
	ErrorMessage   string `json:"errorMessage"`   // 错误信息（如库存不足的具体描述）
}

// 商品价格计算请求（统一CartItems模式）
type GoodsPriceCalcReq struct {
	UserID    uint64      `json:"userId" form:"userId"`                    // 用户ID，验证优惠券时需要
	CartItems []*CartItem `json:"cartItems" binding:"required,min=1,dive"` // 购物车商品列表（单个商品也使用此结构）
	CouponID  uint64      `json:"couponId" form:"couponId"`                // 优惠券ID

	UsePoints uint32 `json:"usePoints" form:"usePoints"` // 是否使用积分抵扣，1-使用
}

// 商品价格计算响应
type GoodsPriceCalcRes struct {
	Items      []*CartItemPriceInfo `json:"items"`      // 各商品明细
	TotalFee   uint64               `json:"totalFee"`   // 总金额（对应订单的TotalFee）
	FreightFee uint64               `json:"freightFee"` // 运费
	CouponFee  uint64               `json:"couponFee"`  // 优惠券抵扣金额（对应订单的CouponFee）
	PointsFee  uint64               `json:"pointsFee"`  // 积分抵扣金额（对应订单的PointsFee）
	PointsUse  uint64               `json:"pointsUse"`  // 消耗的积分数额（对应订单的PointsUse）
	UsedFee    uint64               `json:"usedFee"`    // 实付金额（对应订单的UsedFee）
	TotalSaved uint64               `json:"totalSaved"` // 总节省金额（优惠券+积分）
	TotalItems uint32               `json:"totalItems"` // 总商品数量
	SkuCount   uint32               `json:"skuCount"`   // SKU种类数量
}

// 购物车商品价格明细
type CartItemPriceInfo struct {
	SkuID      uint64 `json:"skuId"`      // SKU ID
	SkuName    string `json:"skuName"`    // SKU名称
	SkuCover   string `json:"skuCover"`   // SKU封面图
	UnitPrice  uint64 `json:"unitPrice"`  // 单价
	Quantity   uint32 `json:"quantity"`   // 数量
	TotalPrice uint64 `json:"totalPrice"` // 小计
	Available  bool   `json:"available"`  // 是否有足够库存
	CanOrder   bool   `json:"canOrder"`   // 是否可订购
}
