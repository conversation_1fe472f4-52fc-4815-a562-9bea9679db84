package box

type BoxQueueReq struct {
	UserID uint64 `form:"userId" uri:"userId" json:"userId"`

	BoxID uint64 `form:"boxId" uri:"boxId" json:"boxId" binding:"required"`
}

type BoxQueueRes struct {
	BoxID       uint64 `json:"boxId"`
	Lock        bool   `json:"lock"`
	LockType    uint32 `json:"lockType"`
	InvalidTime int64  `json:"invalidTime"`
	ServerTime  int64  `json:"serverTime"`
	Name        string `json:"name"`
	Pic         string `json:"pic"`
	UserID      uint64 `json:"userId"`
}

type BoxQueuePushReq struct {
	UserID uint64 `form:"userId" uri:"userId" json:"userId"`

	BoxID uint64 `form:"boxId" uri:"boxId" json:"boxId" binding:"required"`
}

type BoxQueuePushRes struct {
}
