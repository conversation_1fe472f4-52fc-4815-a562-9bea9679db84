package box

import (
	"blind_box/app/dto/goods"
)

type AdminBoxGoodsAddReq struct {
	ActiveID uint64 `json:"activeId" binding:"required"` // 活动id
	//GoodsID  uint64 `json:"goodsId" binding:"required"`  // 商品id

	SpuID uint64 `json:"spuId" binding:"required"` // 商品id
	SkuID uint64 `json:"skuId" binding:"required"` // 商品id

	LevelID uint64 `json:"levelId" binding:"required"` // 级别id
	Stock   uint32 `json:"stock"`                      // 库存

	GoodsName  string `json:"goodsName" binding:"required"` // 商品名称
	GoodsCover string `json:"goodsCover"`                   // 商品封面
}

type AdminBoxGoodsAddResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxGoodsUpdateReq struct {
	ID uint64 `json:"id" binding:"required"`
	*AdminBoxGoodsAddReq
}

type AdminBoxGoodsUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxGoodsSortReq struct {
	ID   uint64 `json:"id" binding:"required"`
	Sort uint32 `json:"sort"`
}

type AdminBoxGoodsSortResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxGoodsDelReq struct {
	ID uint64 `json:"id" binding:"required"`
}

type AdminBoxGoodsDelResp struct {
}

type AdminBoxGoodsUpdateStockReq struct {
	ID    uint64 `json:"id" binding:"required"`
	Stock uint32 `json:"stock"`
}

type AdminBoxGoodsUpdateStockResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxGoodsListReq struct {
	ActiveID uint64 `form:"activeId" json:"activeId" binding:"required"` // 活动id
}

type AdminBoxGoodsListResp struct {
	List  []*AdminBoxGoodsListItem `json:"list"`
	Count int64                    `json:"count"`
}

type AdminBoxGoodsListItem struct {
	ID         uint64 `json:"id"`
	Sort       uint32 `json:"sort"`
	ActiveID   uint64 `json:"activeId"`
	GoodsName  string `json:"goodsName"`
	GoodsCover string `json:"goodsCover"`
	LevelID    uint64 `json:"levelId"`
	LevelName  string `json:"levelName"`
	Stock      uint32 `json:"stock"`
	CurStock   uint32 `json:"curStock"`
	UsedStock  uint32 `json:"usedStock"`

	GoodsDetail *goods.CrSkuItem `json:"goodsDetail"`
}

type AdminBoxGoodsInfoReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminBoxGoodsInfoResp struct {
	*AdminBoxGoodsListItem
}
