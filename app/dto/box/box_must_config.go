package box

type AdminBoxMustConfigBagCreateReq struct {
	ActiveID   uint64 `json:"activeId" binding:"required"`   // 活动id
	LotteryNum uint32 `json:"lotteryNum" binding:"required"` //
	OutLevel   string `json:"outLevel" binding:"required"`   // 盲盒说明
}

type AdminBoxMustConfigBagCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxMustConfigUserCreateReq struct {
	ActiveID    uint64   `json:"activeId" binding:"required"`    // 活动id
	LotteryNum  uint32   `json:"lotteryNum" binding:"required"`  //
	NotOutGoods []uint64 `json:"notOutGoods" binding:"required"` // 没获取的赏品
	OutGoods    []uint64 `json:"outGoods" binding:"required"`    // 必出赏品
}

type AdminBoxMustConfigUserCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxMustConfigActiveCreateReq struct {
	ActiveID   uint64   `json:"activeId" binding:"required"`   // 活动id
	LotteryNum uint32   `json:"lotteryNum" binding:"required"` //
	OutGoods   []uint64 `json:"outGoods" binding:"required"`   // 必出赏品
	GoodsNum   uint32   `json:"goodsNum" binding:"required"`   // 允许产出商品数量
}

type AdminBoxMustConfigActiveCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminBoxMustConfigInfoReq struct {
	ActiveID uint64 `form:"activeId" json:"activeId" binding:"required"` // 活动id
}

type AdminBoxMustConfigInfoResp struct {
	Bag    []*AdminBoxMustConfigBag    `json:"bag"`    // 盲盒说明
	User   []*AdminBoxMustConfigUser   `json:"user"`   // 用户说明
	Active []*AdminBoxMustConfigActive `json:"active"` // 活动说明
}

type AdminBoxMustConfigBag struct {
	ID         uint64 `json:"id"`
	ActiveID   uint64 `json:"activeId"`
	LotteryNum uint32 `json:"lotteryNum"`
	OutLevel   string `json:"outLevel"` // 盲盒说明
}

type AdminBoxMustConfigUser struct {
	ID             uint64   `json:"id"`
	ActiveID       uint64   `json:"activeId"`
	LotteryNum     uint32   `json:"lotteryNum"`
	NotOutGoods    []string `json:"notOutGoods"`
	NotOutGoodsIDs []uint64 `json:"-"`
	OutGoods       []string `json:"outGoods"`
	OutGoodsIDs    []uint64 `json:"-"`
}

type AdminBoxMustConfigActive struct {
	ID          uint64   `json:"id"`
	ActiveID    uint64   `json:"activeId"`
	LotteryNum  uint32   `json:"lotteryNum"`
	OutGoods    []string `json:"outGoods"`
	OutGoodsIDs []uint64 `json:"-"`
	GoodsNum    uint32   `json:"goodsNum"`
}

type AdminBoxMustConfigActiveDelReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"` // 活动id
}

type AdminBoxMustConfigActiveDelResp struct {
}
