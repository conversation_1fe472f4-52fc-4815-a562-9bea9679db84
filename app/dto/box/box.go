package box

import (
	"blind_box/app/dto/common"
	"blind_box/app/dto/goods"
)

type BoxCreateReq struct {
	*BoxBase
}

type BoxBase struct {
	// 活动标题
	ActiveTitle string `json:"activeTitle"`
	// 活动状态 1 预售 2 现货
	ActiveStatus int `json:"activeStatus"`
	// 预计到货时间说明
	DeliveryExpect string `json:"deliveryExpect"`
	// 0 没有仓库 1 青浦 2全新菜
	Store int `json:"store"`
	// 微缩图
	ActiveImage string `json:"activeImage"`
	// 详情图
	DetailImages []string `json:"detailImages"`
	// 活动价格
	ActivePrice int `json:"activePrice"`
	// 开始时间
	OpenTime string `json:"openTime"`
	// 结束时间
	EndTime *string `json:"endTime"`
	// 分类标签
	ActiveTagId string `json:"activeTagId"`
}

type BoxUpdateReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"`

	*BoxBase
}

type BoxListReq struct {
	UserID uint64 `json:"userId" form:"userId"`
	common.DataListReq
	ActiveType uint32 `json:"activeType" form:"activeType"`

	Zone     uint64 `json:"zone" form:"zone"`
	V        uint64 `json:"v" form:"v"`
	ZoneType uint64 `json:"zoneType" form:"zoneType"`
}

type BoxListRes struct {
	ActiveList   []*BoxActiveInfo `json:"activeList"`
	LogoImageUrl string           `json:"logoImageUrl"`
	Title        string           `json:"title"`
	IsNextPage   bool             `json:"isNextPage"`
	Count        int64            `json:"count"`
}

type BoxActiveInfo struct {
	ID                uint64 `json:"id"`
	ActiveTitle       string `json:"activeTitle"`
	ActiveStatus      uint32 `json:"activeStatus"`
	ActiveGoodsStatus uint32 `json:"activeGoodsStatus"`
	OpenTimeStamp     int64  `json:"openTimeStamp"`
	ActivePrice       uint64 `json:"activePrice"`
	ActiveImage       string `json:"activeImage"`
	ActiveType        uint32 `json:"activeType"`

	OriginalPrice uint64 `json:"originalPrice"`
}

type BoxSearchReq struct {
	Search string `json:"search" form:"search"`

	common.DataListReq

	ActiveType uint32 `json:"activeType" form:"activeType"`
	Zone       uint64 `json:"zone" form:"zone"`
	V          uint64 `json:"v" form:"v"`
	ZoneType   uint64 `json:"zoneType" form:"zoneType"`
}

type BoxSearchRes struct {
	List  []*BoxSearchItem `json:"list"`
	Count int64            `json:"count"`
}

type BoxSearchItem struct {
	ID            uint64 `json:"id"`
	ActiveType    uint32 `json:"activeType"`
	ActiveTitle   string `json:"activeTitle"`
	ActivePrice   uint64 `json:"activePrice"`
	ActiveStatus  uint32 `json:"activeStatus"`
	OpenTimeStamp int64  `json:"openTimeStamp"`
	ActiveImage   string `json:"activeImage"`
}

type BoxActiveReq struct {
	ActiveID uint64 `json:"activeId" form:"activeId" binding:"required"`
}

type BoxActiveRes struct {
	ActiveID          uint64 `json:"activeId"`
	ActiveTitle       string `json:"activeTitle"`
	ActiveStatus      uint32 `json:"activeStatus"`
	ActiveGoodsStatus uint32 `json:"activeGoodsStatus"`
	ActivePrice       uint64 `json:"activePrice"`
	ActiveImage       string `json:"activeImage"`
	ActiveType        uint32 `json:"activeType"`
	Remark            string `json:"remark"`
	DeliveryExpect    string `json:"deliveryExpect"`

	OpenTimeStamp int64  `json:"openTimeStamp"`
	EndTimeStamp  int64  `json:"endTimeStamp"`
	OpenTime      string `json:"openTime"`
	EndTime       string `json:"endTime"`

	LotteryNum uint32 `json:"lotteryNum"`

	Images       []string              `json:"images"`
	GoodsDetails []*BoxGoodsDetailItem `json:"goodsDetails"`
	IsSubscribe  uint32                `json:"isSubscribe"`
}

type BoxGoodsDetailItem struct {
	ID uint64 `json:"id"`

	GoodsDetail *goods.CrSkuItem `json:"goodsDetail"`

	GoodsName  string `json:"goodsName"`
	GoodsCover string `json:"goodsCover"`

	Sort uint32 `json:"sort"`

	IsSold   bool   `json:"isSold"`
	IsHint   bool   `json:"isHint"`
	HintType uint32 `json:"hintType"`
}

type BoxGetReq struct {
	UserID   uint64 `json:"userId" form:"userId"`
	ActiveID uint64 `json:"activeId" form:"activeId" binding:"required"`
}

type BoxGetRes struct {
	*BoxDetailRes
	Active *BoxActiveRes `json:"active"`
}

type BoxDetailReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	ActiveID uint64 `json:"activeId" form:"activeId" binding:"required"`
	BoxID    uint64 `json:"boxId" form:"boxId" binding:"required"`
}

type BoxDetailRes struct {
	ActiveID     uint64 `json:"activeId"`
	BoxID        uint64 `json:"boxId"`
	BoxNo        string `json:"boxNo"`
	ActiveTitle  string `json:"activeTitle"`
	ActiveStatus uint32 `json:"activeStatus"`

	OpenTime      string `json:"openTime"`
	OpenTimeStamp int64  `json:"openTimeStamp"`
	EndTime       string `json:"endTime"`
	EndTimeStamp  int64  `json:"endTimeStamp"`

	ActivePrice   uint64                `json:"activePrice"`
	ActiveImage   string                `json:"activeImage"`
	LotteryNum    uint32                `json:"lotteryNum"`
	AvaSlots      []*BoxAvaSlotItem     `json:"avaSlots"`
	HeadImage     string                `json:"headImage"`
	LocationImage string                `json:"locationImage"`
	BoxImage      string                `json:"boxImage"`
	LevelText     string                `json:"levelText"`
	ShakeNum      uint32                `json:"shakeNum"`
	GoodsDetails  []*BoxGoodsDetailItem `json:"goodsDetails"`

	TipCount  uint32 `json:"tipCount"`
	CardCount uint32 `json:"cardCount"`
}

type BoxAvaSlotItem struct {
	SlotID       uint32 `json:"slotId"`
	SlotShakeNum uint32 `json:"slotShakeNum"`
}

type BoxSlotReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	ActiveID uint64 `json:"activeId" form:"activeId" binding:"required"`

	BoxID uint64 `json:"boxId" form:"boxId" binding:"required"`

	Slot uint32 `json:"slot" form:"slot" binding:"required"`
}

type BoxSlotRes struct {
	BoxID        uint64                `json:"boxId"`
	SlotShakeNum uint32                `json:"slotShakeNum"`
	ShakeNum     uint32                `json:"shakeNum"`
	GoodsDetails []*BoxGoodsDetailItem `json:"goodsDetails"`
	TipCount     uint32                `json:"tipCount"`
	CardCount    uint32                `json:"cardCount"`
}

type BoxActionsReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	common.DataListReq

	ActiveID uint64 `json:"activeId" form:"activeId"`
}

type BoxActionsRes struct {
	List           []*BoxActionItem `json:"list"`
	Count          int64            `json:"count"`
	StartTime      string           `json:"startTime"`
	EndTime        string           `json:"endTime"`
	StartTimeStamp int64            `json:"startTimeStamp"`
	EndTimeStamp   int64            `json:"endTimeStamp"`
}

type BoxActionItem struct {
	ActiveID    uint64   `json:"activeId"`
	BoxID       uint64   `json:"boxId"`
	BoxNo       string   `json:"boxNo"`
	LotteryNum  uint32   `json:"lotteryNum"`
	AvaSlot     []uint32 `json:"avaSlot"`
	ActiveTitle string   `json:"activeTitle"`
	OperateAt   int64    `json:"operateAt"`
}

type BoxBagReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	common.DataListReq
}

type BoxBagRes struct {
	List  []*BoxBagItem `json:"list"`
	Count int64         `json:"count"`
	IsEnd bool          `json:"isEnd"`
}

type BoxBagItem struct {
	OrderID        uint64            `json:"orderId"`
	UsedFee        uint64            `json:"usedFee"`
	ActiveID       uint64            `json:"activeId"`
	ActiveTitle    string            `json:"activeTitle"`
	ActivePrice    uint64            `json:"activePrice"`
	ActiveStatus   uint32            `json:"activeStatus"`
	DeliveryExpect string            `json:"deliveryExpect"`
	Trades         []BoxBagTradeItem `json:"trades"`
}

type BoxBagTradeItem struct {
	ID          uint64           `json:"id"`
	GoodsID     uint64           `json:"goodsId"`
	GoodsName   string           `json:"goodsName"`
	GoodsImg    string           `json:"goodsImg"`
	GoodsStatus uint32           `json:"goodsStatus"`
	GoodsDetail *goods.CrSkuItem `json:"goodsDetail"`
	Remark      string           `json:"remark"`
}

type BoxTradeListReq struct {
	UserID       uint64 `json:"userId" form:"userId"`
	Type         uint32 `json:"type" form:"type" binding:"oneof=1 2 3"`
	OnlyDelivery uint32 `json:"onlyDelivery" form:"onlyDelivery"`
	common.DataListReq
}

type BoxTradeListRes struct {
	List  []BoxTradeListResItem `json:"list"`
	Count int64                 `json:"count"`
	IsEnd bool                  `json:"isEnd"`
}

type BoxTradeListResItem struct {
	TradeID     uint64 `json:"tradeId"`
	ActiveID    uint64 `json:"activeId"`
	ActiveTitle string `json:"activeTitle"`
	ActivePrice uint64 `json:"activePrice"`
	GoodsID     uint64 `json:"goodsId"`
	GoodsName   string `json:"goodsName"`
	//GoodsImg       string                  `json:"goodsImg"`
	GoodsImage     string           `json:"goodsImage"`
	GoodsDetail    *goods.CrSkuItem `json:"goodsDetail"`
	Type           uint32           `json:"type"`
	PackType       uint32           `json:"packType"`
	TypeAmount     uint32           `json:"typeAmount"`
	DeliveryStatus uint32           `json:"deliveryStatus"`
	GoodsStatus    uint32           `json:"goodsStatus"`
	Remark         string           `json:"remark"`
	ActiveType     uint32           `json:"activeType"`

	ExpressTradeNo string `json:"expressTradeNo"`
	ExpressNo      string `json:"expressNo"`
	ExpressCompany string `json:"expressCompany"`
}

type BoxTradeDetailReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	TradeID uint64 `json:"tradeId" form:"tradeId" binding:"required"`
}

type BoxTradeDetailRes struct {
	TradeID      uint64 `json:"tradeId"`
	ActiveID     uint64 `json:"activeId"`
	ActiveType   uint32 `json:"activeType"`
	ActiveTitle  string `json:"activeTitle"`
	ActivePrice  uint64 `json:"activePrice"`
	ActiveStatus uint32 `json:"activeStatus"`

	OutTradeNo string `json:"outTradeNo"`
	CreateTime string `json:"createTime"`
	PayTime    int64  `json:"payTime"`
	PayTimeStr string `json:"payTimeStr"`
	PayType    uint32 `json:"payType"`

	GoodsStatus uint32           `json:"goodsStatus"`
	GoodsName   string           `json:"goodsName"`
	GoodsImage  string           `json:"goodsImage"`
	GoodsDetail *goods.CrSkuItem `json:"goodsDetail"`
	CouponFee   uint64           `json:"couponFee"`
	RealFee     uint64           `json:"realFee"`
	Remark      string           `json:"remark"`

	DeliveryStatus      uint32 `json:"deliveryStatus"`
	DeliveryType        uint32 `json:"deliveryType"`
	DeliveryTime        string `json:"deliveryTime"`
	DeliveryUpdateTime  string `json:"deliveryUpdateTime"`
	DeliveryUserName    string `json:"deliveryUserName"`
	DeliveryUserPhone   string `json:"deliveryUserPhone"`
	DeliveryUserAddress string `json:"deliveryUserAddress"`

	ExpressNo      string `json:"expressNo"`
	ExpressCompany string `json:"expressCompany"`
}

type BoxCardCountReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	CardType uint32 `json:"cardType" form:"cardType"`
}

type BoxCardCountRes struct {
	Num uint32 `json:"num"`
}

type BoxCardUserHistoryListReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	common.DataListReq
}

type BoxCardUserHistoryListRes struct {
	List  []*BoxCardUserHistoryItem `json:"list"`
	Count int64                     `json:"count"`
	IsEnd bool                      `json:"isEnd"`
}

type BoxCardUserHistoryItem struct {
	ID uint64 `json:"id"`

	CardName   string `json:"cardName"`
	Status     uint32 `json:"status"`
	ExpireTime string `json:"expireTime"`
	CardImg    string `json:"cardImg"`
}

type BoxCardUserListReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	common.DataListReq
}

type BoxCardUserListRes struct {
	List  []*BoxCardUserItem `json:"list"`
	Count int64              `json:"count"`
	IsEnd bool               `json:"isEnd"`
}

type BoxCardUserItem struct {
	ID         uint64 `json:"id"`
	CardName   string `json:"cardName"`
	Status     uint32 `json:"status"`
	ExpireTime string `json:"expireTime"`
	CardImg    string `json:"cardImg"`
	CardType   uint32 `json:"cardType"`
}

type BoxCardLoginSendReq struct {
	UserID uint64 `json:"userId" form:"userId"`
}

type BoxCardLoginSendRes struct {
}

type BoxItemUseReq struct {
	UserID uint64 `json:"userId" form:"userId"`

	BoxID uint64 `json:"boxId" form:"boxId" binding:"required"`

	Slot []uint32 `json:"slot" form:"slot" binding:"required"`

	ICType uint32 `json:"icType" form:"icType" binding:"required,oneof=1 2 3"`
}

type BoxItemUseRes struct {
	BoxNo     string           `json:"boxNo"`
	BoxID     uint64           `json:"boxId"`
	ICID      uint64           `json:"icId"`
	ICType    uint32           `json:"icType"`
	GoodsInfo []*SlotGoodsInfo `json:"goodsInfo"`
}

type SlotGoodsInfo struct {
	GoodsID   uint64 `json:"goodsId"`
	GoodsName string `json:"goodsName"`
}

type BoxPayTestReq struct {
	//OrderID uint64 `json:"orderId" form:"orderId" binding:"required"`
	UserID      uint64 `json:"userId" form:"userId" binding:"required"`
	OutTradeNo  string `json:"outTradeNo" form:"outTradeNo" binding:"required"`
	TotalAmount uint64 `json:"totalAmount" form:"totalAmount" binding:"required"`
	TradeNo     string `json:"tradeNo" form:"tradeNo" binding:"required"`
}

type BoxPayReq struct {
	UserID   uint64 `json:"userId" form:"userId"`
	BoxID    uint64 `json:"boxId" form:"boxId" binding:"required"`
	ActiveID uint64 `json:"activeId" form:"activeId" binding:"required"`

	PayMethod uint32 `json:"payMethod" form:"payMethod" binding:"required"`
	CouponID  uint64 `json:"couponId" form:"couponId"`
	UsePoints uint32 `json:"usePoints" form:"usePoints"` // 是否使用积分抵扣，1-使用

	// 盲盒购买模式字段
	Total *uint32   `json:"total" form:"total"` // 盲盒数量（盲盒模式必填）
	Slot  *[]uint32 `json:"slot" form:"slot"`   // 槽位数组（盲盒模式必填）

	// 直接购买模式字段
	PurchaseType uint32      `json:"purchaseType" form:"purchaseType" binding:"oneof=1 2 3"` // 购买类型：1-盲盒购买，3-直接购买（可选，默认1）
	CartItems    []*CartItem `json:"cartItems" form:"cartItems" binding:"dive"`              // 购物车商品列表（直接购买模式必填）
	OrderID      uint64      `json:"orderId" form:"orderId"`                                 // 订单ID，重新支付时使用

	DeliveryType uint32 `json:"delivery_type" form:"delivery_type"` // 配送方式类型,1-快递,2-到店取
	DeliveryID   uint32 `json:"delivery_id" form:"delivery_id"`     // 配送方式ID，可选
	ShopID       uint64 `json:"shop_id" form:"shop_id"`             // 店铺ID，可选
	AddressID    uint64 `json:"address_id" form:"address_id"`       // 收货地址ID，可选
}

type BoxWxPayRes struct {
	OrderID   uint64 `json:"order_id"` // 单号id
	TradeType string `json:"trade_type"`
	TimeStamp int64  `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	TradeNo   string `json:"trade_no"`
	SignType  string `json:"signType"`
	PaySign   string `json:"paySign"`
}

type BoxWechatNotifyReq struct {
	OutTradeNo  string `json:"outTradeNo"`  //
	TotalAmount uint64 `json:"totalAmount"` //
	TradeNo     string `json:"tradeNo"`     //
	LogData     string `json:"logData"`
}
