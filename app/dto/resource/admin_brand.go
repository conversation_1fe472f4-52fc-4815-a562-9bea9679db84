package resource

import "blind_box/app/dto/common"

type AdminBrandListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminBrandListItem struct {
	ID     uint64 `json:"id"`
	Name   string `json:"name"`
	Cover  string `json:"cover"`
	Status uint32 `json:"status,omitempty"`
}

type AdminBrandListResp struct {
	List  []*AdminBrandListItem `json:"list"`
	Total int64                 `json:"total"`
}

type AdminBrandSetReq struct {
	ID    uint64 `form:"id" json:"id" binding:"omitempty"`
	Name  string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Cover string `form:"cover" json:"cover" binding:"omitempty,max=128"`
	// Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminBrandOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminBrandAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
