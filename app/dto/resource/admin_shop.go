package resource

import "blind_box/app/dto/common"

type AdminShopListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	common.DataListReq
}

type AdminShopListItem struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Cover       string `json:"cover"`
	Address     string `json:"address"`
	Mobile      string `json:"mobile"`
	Description string `json:"description"`
	Status      uint32 `json:"status,omitempty"`
	CreatedAt   string `json:"created_at"`
}

type AdminShopListResp struct {
	List  []*AdminShopListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminShopSetReq struct {
	ID          uint64 `form:"id" json:"id" binding:"omitempty"`
	Name        string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Cover       string `form:"cover" json:"cover" binding:"omitempty,max=128"`
	Address     string `form:"address" json:"address" binding:"omitempty,max=128"`
	Mobile      string `form:"mobile" json:"mobile" binding:"omitempty,max=64"`
	Description string `form:"description" json:"description" binding:"omitempty,max=128"`
}

type AdminShopOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminShopAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
