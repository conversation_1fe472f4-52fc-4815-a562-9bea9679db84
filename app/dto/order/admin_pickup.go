package order

import "errors"

// ===== 管理后台取货码验证 + 发货单号功能 =====

// AdminPickupCodeVerifyReq 管理后台验证取货码请求
type AdminPickupCodeVerifyReq struct {
	// 取货码验证参数
	PickupCode string `form:"pickupCode" json:"pickupCode" binding:"required" msg:"取货码不能为空"`

	// 可选的订单信息用于双重验证
	OrderID uint64 `form:"orderId" json:"orderId"` // 订单ID，可选
	OrderNo string `form:"orderNo" json:"orderNo"` // 订单号，可选

	// 管理员信息（内部使用）
	AccountID uint64 `json:"-"` // 管理员账户ID，内部使用
	AdminName string `json:"-"` // 管理员姓名，内部使用
}

// AdminPickupCodeVerifyRes 管理后台验证取货码响应
type AdminPickupCodeVerifyRes struct {
	// 基础信息
	OrderID    uint64 `json:"orderId"`    // 订单ID
	OrderNo    string `json:"orderNo"`    // 订单号
	PickupCode string `json:"pickupCode"` // 取货码
	IsValid    bool   `json:"isValid"`    // 取货码是否有效

	// 订单信息
	OrderInfo *AdminOrderInfo `json:"orderInfo,omitempty"` // 订单信息，仅在有效时返回

	// 发货信息（到店取货相关）
	DeliveryInfo *AdminDeliveryInfo `json:"deliveryInfo,omitempty"` // 发货信息

	// 验证结果
	OrderVerified bool   `json:"orderVerified"` // 是否进行了订单双重验证
	VerifyMessage string `json:"verifyMessage"` // 验证结果消息
}

// AdminOrderInfo 管理后台订单信息
type AdminOrderInfo struct {
	OrderID      uint64 `json:"orderId"`      // 订单ID
	OrderNo      string `json:"orderNo"`      // 订单号
	UserID       uint64 `json:"userId"`       // 用户ID
	UserName     string `json:"userName"`     // 用户昵称（如有）
	TotalFee     uint64 `json:"totalFee"`     // 总金额
	CashFee      uint64 `json:"cashFee"`      // 实付金额
	OrderStatus  uint32 `json:"orderStatus"`  // 订单状态
	OrderType    uint32 `json:"orderType"`    // 订单类型
	DeliveryType uint32 `json:"deliveryType"` // 配送方式
	PayMethod    uint32 `json:"payMethod"`    // 支付方式
	CreatedAt    string `json:"createdAt"`    // 创建时间
	PayTime      string `json:"payTime"`      // 支付时间
}

// ===== 管理后台订单完成 + 发货状态更新功能 =====

// AdminOrderCompleteReq 管理后台完成订单请求
type AdminOrderCompleteReq struct {
	// 订单标识（三选一）
	OrderID    uint64 `form:"orderId" json:"orderId"`       // 订单ID，可选
	OrderNo    string `form:"orderNo" json:"orderNo"`       // 订单号，可选
	PickupCode string `form:"pickupCode" json:"pickupCode"` // 取货码，可选

	// 发货信息更新（可选）
	TrackingNumber string `form:"trackingNumber" json:"trackingNumber"` // 快递单号
	DeliveryStatus uint32 `form:"deliveryStatus" json:"deliveryStatus"` // 发货状态更新：3-已送达
	UpdateDelivery bool   `form:"updateDelivery" json:"updateDelivery"` // 是否同时更新发货状态
	DeliveryRemark string `form:"deliveryRemark" json:"deliveryRemark"` // 发货备注

	// 订单完成信息
	CompleteRemark string `form:"completeRemark" json:"completeRemark"` // 完成备注

	// 管理员信息（内部使用）
	AccountID uint64 `json:"-"` // 管理员账户ID，内部使用
	AdminName string `json:"-"` // 管理员姓名，内部使用
}

func (req *AdminOrderCompleteReq) Validate() error {
	if req.OrderID == 0 && req.OrderNo == "" {
		// 未提供订单标识，只能依赖取货码，但系统已不支持取货码反向查订单
		return errors.New("订单ID或订单号不能为空")
	}

	// 如果要更新发货状态，必须提供快递单号
	if req.UpdateDelivery && req.TrackingNumber == "" {
		return errors.New("更新发货状态时快递单号不能为空")
	}

	return nil
}

// AdminOrderCompleteRes 管理后台完成订单响应
type AdminOrderCompleteRes struct {
	// 基础结果
	OrderID uint64 `json:"orderId"` // 订单ID
	OrderNo string `json:"orderNo"` // 订单号
	Message string `json:"message"` // 操作结果消息

	// 发货更新结果
	DeliveryUpdated bool   `json:"deliveryUpdated"` // 发货信息是否已更新
	DeliveryMessage string `json:"deliveryMessage"` // 发货更新消息

	// 完成时间
	CompletedAt string `json:"completedAt"` // 订单完成时间
}

// ===== 管理后台快递单号验证功能 =====

// AdminTrackingNumberVerifyReq 管理后台验证快递单号请求
type AdminTrackingNumberVerifyReq struct {
	// 快递单号验证参数
	TrackingNumber string `form:"trackingNumber" json:"trackingNumber" binding:"required" msg:"快递单号不能为空"`

	// 可选的订单信息用于验证
	OrderID uint64 `form:"orderId" json:"orderId"` // 订单ID，可选
	OrderNo string `form:"orderNo" json:"orderNo"` // 订单号，可选

	// 管理员信息（内部使用）
	AccountID uint64 `json:"-"` // 管理员账户ID，内部使用
	AdminName string `json:"-"` // 管理员姓名，内部使用
}

func (req *AdminTrackingNumberVerifyReq) Validate() error {
	if req.TrackingNumber == "" {
		return errors.New("快递单号不能为空")
	}
	return nil
}

// AdminTrackingNumberVerifyRes 管理后台验证快递单号响应
type AdminTrackingNumberVerifyRes struct {
	// 基础信息
	OrderID        uint64 `json:"orderId"`        // 订单ID
	OrderNo        string `json:"orderNo"`        // 订单号
	TrackingNumber string `json:"trackingNumber"` // 快递单号
	IsValid        bool   `json:"isValid"`        // 快递单号是否有效

	// 订单信息
	OrderInfo *AdminOrderInfo `json:"orderInfo,omitempty"` // 订单信息，仅在有效时返回

	// 发货信息
	DeliveryInfo *AdminDeliveryInfo `json:"deliveryInfo,omitempty"` // 发货信息

	// 验证结果
	VerifyMessage string `json:"verifyMessage"` // 验证结果消息
}
