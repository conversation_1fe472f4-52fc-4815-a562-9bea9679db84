package order

import (
	"errors"

	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dto/common"
	"blind_box/app/dto/goods"
)

type OrderStatusReq struct {
	ID uint64 `form:"id" json:"id" binding:"required" msg:"订单ID不能为空"`
}

type OrderStatusRes struct {
	IsSuccess   bool   `json:"is_success"`
	OrderStatus uint32 `json:"order_status"`
}

type OrderDetailReq struct {
	UserID     uint64
	OrderID    uint64 `form:"orderId" json:"orderId"`
	OutTradeNo string `form:"outTradeNo" json:"outTradeNo"`
}

func (req *OrderDetailReq) Validate() error {
	if req.OrderID == 0 && req.OutTradeNo == "" {
		return errors.New("订单号不能为空")
	}
	return nil
}

type OrderDetailRes struct {
	OrderInfo    *OrderInfo   `json:"orderInfo"`
	GoodsInfo    []*TradeGood `json:"goodsInfo"`
	OrderPayInfo *OrderPay    `json:"orderPayInfo"`

	ActiveTitle   string                   `json:"activeTitle"`
	BoxActive     *box_active_config.Model `json:"boxActive"`
	OrderDelivery *OrderDelivery           `json:"orderDelivery"` // 订单发货信息
}

type OrderDelivery struct {
	Delivery       *goods.CrSkuDeliveryItem   `json:"delivery"`       // 发货信息
	Address        *common.CommonUserAddrInfo `json:"address"`        // 收货地址
	PickupCode     string                     `json:"pickupCode"`     // 取货码
	ExpressCode    string                     `json:"expressCode"`    // 快递单号
	ExpressCompany string                     `json:"expressCompany"` // 快递公司
}

type OrderInfo struct {
	ActiveId       uint64 `json:"activeId"`
	ActiveName     string `json:"activeName"`
	ActiveImage    string `json:"activeImage"`
	ActiveType     uint32 `json:"activeType"`
	LotteryUsedNum uint32 `json:"lotteryUsedNum"` // 抽赏次数
	UsedFee        uint64 `json:"usedFee"`        // 实付抽赏金额
	TotalFee       uint64 `json:"totalFee"`       // 总抽赏金额
	FreightFee     uint64 `json:"freightFee"`     // 运费
	BagId          uint64 `json:"bagId"`

	OrderId      uint64 `json:"orderId"`      // 订单ID
	OrderNo      string `json:"orderNo"`      // 订单号
	OrderType    uint32 `json:"orderType"`    // 订单类型
	OrderStatus  uint32 `json:"orderStatus"`  // 订单状态
	OrderSubType uint32 `json:"orderSubType"` // 订单子类型
	CreatedAt    int64  `json:"createdAt"`    // 创建时间
	PayAt        int64  `json:"payAt"`        // 支付时间
}

type TradeGood struct {
	Id              uint64           `json:"id"`
	GoodId          uint64           `json:"goodId"`
	GoodName        string           `json:"goodName"`
	GoodLevel       string           `json:"goodLevel"`
	GoodStatus      uint32           `json:"goodStatus"`
	GoodImage       string           `json:"goodsImage"`
	GoodDetailImage string           `json:"goodDetailImage"`
	GoodDetail      *goods.CrSkuItem `json:"goodDetail,omitempty"`
	ExpireTime      int64            `json:"expireTime"`
	Remark          string           `json:"remark"`

	BoxId     uint64 `json:"boxId,omitempty"`
	BoxNo     string `json:"boxNo,omitempty"`
	BoxSlot   uint32 `json:"boxSlot,omitempty"`
	BatchInfo *goods.AdminBatchListItem

	Quantity uint32 `json:"quantity"`
}

type OrderPay struct {
	RefundId      uint64 `json:"refundId"`
	UsedFee       uint64 `json:"usedFee"`
	CouponFee     uint64 `json:"couponFee"`
	PointsFee     uint64 `json:"pointsFee"` // 积分抵扣金额
	PointsUse     uint64 `json:"pointsUse"` // 消耗的积分数额
	PayMethod     uint32 `json:"payMethod"`
	OrderNo       string `json:"orderNo"`
	OrderStatus   uint32 `json:"orderStatus"`
	TransactionId string `json:"transactionId"`
	CreatedTime   string `json:"createdTime"`

	PayExpireTime    int64  `json:"payExpireTime"`    // 最后支付时间，秒级时间戳
	PayExpireTimeStr string `json:"payExpireTimeStr"` // 最后支付时间，格式化字符串
}

type OrderListReq struct {
	UserID uint64 `form:"userId" json:"userId"`

	common.DataListReq

	OrderType       uint32   `form:"orderType" json:"orderType"`     // 订单类型：0-全部，1-盲盒，2-直购
	OrderStatusList []uint32 `form:"orderStatus" json:"orderStatus"` // 订单状态列表：支持单个或多个状态，前端传参格式：orderStatus=1 或 orderStatus=1&orderStatus=2&orderStatus=3

	OutTradeNo string `form:"outTradeNo" json:"outTradeNo"` // 订单号

	CreatedAtStart int64 `form:"createdAtStart" json:"createdAtStart"` // 创建时间开始（秒级时间戳）
	CreatedAtEnd   int64 `form:"createdAtEnd" json:"createdAtEnd"`     // 创建时间结束（秒级时间戳）
}

type OrderListRes struct {
	List  []*OrderListItem `json:"list"`
	Count int64            `json:"count"` // 总数
}

type OrderListItem struct {
	*OrderDetailRes
}

// OrderCancelReq 订单取消请求
type OrderCancelReq struct {
	OrderID  uint64 `form:"orderId" json:"orderId" binding:"required" msg:"订单ID不能为空"`
	OrderNo  string `form:"orderNo" json:"orderNo"`   // 订单号，可选
	IsRefund bool   `form:"isRefund" json:"isRefund"` // 是否退款
	IsReturn bool   `form:"isReturn" json:"isReturn"` // 是否返还库存
	Reason   string `form:"reason" json:"reason"`     // 取消原因
	UserID   uint64 `form:"-" json:"-"`               // 用户ID，内部使用
}

// OrderCancelRes 订单取消响应
type OrderCancelRes struct {
	OrderID uint64 `json:"orderId"` // 订单ID
	Message string `json:"message"` // 操作结果消息
}

// AdminOrderCancelReq 管理后台订单取消请求
type AdminOrderCancelReq struct {
	OrderID   uint64 `form:"orderId" json:"orderId" binding:"required" msg:"订单ID不能为空"`
	OrderNo   string `form:"orderNo" json:"orderNo"`   // 订单号，可选
	IsRefund  bool   `form:"isRefund" json:"isRefund"` // 是否退款
	IsReturn  bool   `form:"isReturn" json:"isReturn"` // 是否返还库存
	Reason    string `form:"reason" json:"reason"`     // 取消原因
	AccountID uint64 `form:"-" json:"-"`               // 管理员账户ID，内部使用
}

// AdminOrderCancelRes 管理后台订单取消响应
type AdminOrderCancelRes struct {
	OrderID uint64 `json:"orderId"` // 订单ID
	Message string `json:"message"` // 操作结果消息
}

// PickupCodeGenerateReq 生成取货码请求
type PickupCodeGenerateReq struct {
	OrderID      uint64 `form:"orderId" json:"orderId"`           // 订单ID，可选
	OrderNo      string `form:"orderNo" json:"orderNo"`           // 订单号，可选
	UserID       uint64 `form:"-" json:"-"`                       // 用户ID，内部使用
	ForceRefresh bool   `form:"forceRefresh" json:"forceRefresh"` // 是否强制刷新取货码
}

// PickupCodeGenerateRes 生成取货码响应
type PickupCodeGenerateRes struct {
	OrderID    uint64 `json:"orderId"`    // 订单ID
	OrderNo    string `json:"orderNo"`    // 订单号
	PickupCode string `json:"pickupCode"` // 取货码
	IsNew      bool   `json:"isNew"`      // 是否为新生成的取货码
	//QRCodeURL  string `json:"qrCodeUrl"`  // 二维码URL（可选）
}

// PickupCodeQueryReq 查询取货码请求
type PickupCodeQueryReq struct {
	OrderID uint64 `form:"orderId" json:"orderId"` // 订单ID，可选
	OrderNo string `form:"orderNo" json:"orderNo"` // 订单号，可选
	UserID  uint64 `form:"-" json:"-"`             // 用户ID，内部使用
}

func (req *PickupCodeQueryReq) Validate() error {
	if req.OrderID == 0 && req.OrderNo == "" {
		return errors.New("订单ID或订单号不能为空")
	}
	return nil
}

// PickupCodeQueryRes 查询取货码响应
type PickupCodeQueryRes struct {
	OrderID    uint64 `json:"orderId"`    // 订单ID
	OrderNo    string `json:"orderNo"`    // 订单号
	PickupCode string `json:"pickupCode"` // 取货码
	IsValid    bool   `json:"isValid"`    // 是否有效
}

// PickupCodeVerifyReq 验证取货码请求（门店端使用）
type PickupCodeVerifyReq struct {
	PickupCode string `form:"pickupCode" json:"pickupCode" binding:"required" msg:"取货码不能为空"`
	// 额外携带的订单标识，用于与取货码进行匹配校验
	OrderID uint64 `form:"orderId" json:"orderId"` // 订单ID，可选
	OrderNo string `form:"orderNo" json:"orderNo"` // 订单号，可选
}

// Validate 校验请求参数
func (req *PickupCodeVerifyReq) Validate() error {
	// 取货码必填，订单ID 与 订单号至少提供一个
	if req.PickupCode == "" {
		return errors.New("取货码不能为空")
	}
	if req.OrderID == 0 && req.OrderNo == "" {
		return errors.New("订单ID或订单号不能为空")
	}
	return nil
}

// PickupCodeVerifyRes 验证取货码响应（门店端使用）
type PickupCodeVerifyRes struct {
	OrderID    uint64     `json:"orderId"`             // 订单ID
	OrderNo    string     `json:"orderNo"`             // 订单号
	PickupCode string     `json:"pickupCode"`          // 取货码
	IsValid    bool       `json:"isValid"`             // 是否有效
	OrderInfo  *OrderInfo `json:"orderInfo,omitempty"` // 订单信息，仅在有效时返回
}

// OrderCompleteReq 订单完成请求（门店端使用）
type OrderCompleteReq struct {
	OrderID    uint64 `form:"orderId" json:"orderId"`       // 订单ID，可选
	OrderNo    string `form:"orderNo" json:"orderNo"`       // 订单号，可选
	PickupCode string `form:"pickupCode" json:"pickupCode"` // 取货码，可选
}

func (req *OrderCompleteReq) Validate() error {
	if req.OrderID == 0 && req.OrderNo == "" {
		// 未提供订单标识，只能依赖取货码，但系统已不支持取货码反向查订单
		return errors.New("订单ID或订单号不能为空")
	}
	return nil
}

// OrderCompleteRes 订单完成响应（门店端使用）
type OrderCompleteRes struct {
	OrderID uint64 `json:"orderId"` // 订单ID
	OrderNo string `json:"orderNo"` // 订单号
	Message string `json:"message"` // 操作结果消息
}

// ===== 管理后台发货相关 DTO =====

// AdminOrderListReq 管理后台订单列表请求
type AdminOrderListReq struct {
	common.DataListReq

	OrderID         uint64   `form:"orderId" json:"orderId"`               // 订单ID
	OutTradeNo      string   `form:"outTradeNo" json:"outTradeNo"`         // 订单号
	UserID          uint64   `form:"userId" json:"userId"`                 // 用户ID
	OrderType       uint32   `form:"orderType" json:"orderType"`           // 订单类型：0-全部，1-盲盒，2-直购
	OrderStatusList []uint32 `form:"orderStatus" json:"orderStatus"`       // 订单状态列表
	DeliveryType    uint32   `form:"deliveryType" json:"deliveryType"`     // 配送方式：1-快递，2-到店取货
	DeliveryStatus  uint32   `form:"deliveryStatus" json:"deliveryStatus"` // 发货状态：1-待发货，2-已发货，3-已送达，4-已退回
	CreatedAtStart  int64    `form:"createdAtStart" json:"createdAtStart"` // 创建时间开始（秒级时间戳）
	CreatedAtEnd    int64    `form:"createdAtEnd" json:"createdAtEnd"`     // 创建时间结束（秒级时间戳）
	ShippedAtStart  int64    `form:"shippedAtStart" json:"shippedAtStart"` // 发货时间开始（秒级时间戳）
	ShippedAtEnd    int64    `form:"shippedAtEnd" json:"shippedAtEnd"`     // 发货时间结束（秒级时间戳）
}

// AdminOrderListRes 管理后台订单列表响应
type AdminOrderListRes struct {
	List  []*AdminOrderListItem `json:"list"`
	Count int64                 `json:"count"` // 总数
}

// AdminOrderListItem 管理后台订单列表项 - 基于OrderDetailRes进行扩展
type AdminOrderListItem struct {
	*OrderDetailRes

	// 管理后台特有的字段
	UserName string `json:"userName"` // 用户昵称
}

// AdminOrderDetailReq 管理后台订单详情请求
type AdminOrderDetailReq struct {
	OrderID    uint64 `form:"orderId" json:"orderId"`
	OutTradeNo string `form:"outTradeNo" json:"outTradeNo"`
}

func (req *AdminOrderDetailReq) Validate() error {
	if req.OrderID == 0 && req.OutTradeNo == "" {
		return errors.New("订单ID或订单号不能为空")
	}
	return nil
}

// AdminOrderDetailRes 管理后台订单详情响应
type AdminOrderDetailRes struct {
	*OrderDetailRes

	// 管理后台特有的字段
	UserName string `json:"userName"` // 用户昵称
}

// AdminDeliveryInfo 管理后台发货信息
type AdminDeliveryInfo struct {
	ID             uint64 `json:"id"`             // 发货记录ID
	OrderID        uint64 `json:"orderId"`        // 订单ID
	ExpressCompany string `json:"expressCompany"` // 快递公司
	ExpressCode    string `json:"expressCode"`    // 快递公司编码
	TrackingNumber string `json:"trackingNumber"` // 快递单号
	DeliveryStatus uint32 `json:"deliveryStatus"` // 发货状态
	ShippedAt      string `json:"shippedAt"`      // 发货时间
	DeliveredAt    string `json:"deliveredAt"`    // 送达时间
	Remark         string `json:"remark"`         // 备注
	AdminName      string `json:"adminName"`      // 操作管理员
	CreatedAt      string `json:"createdAt"`      // 创建时间
	UpdatedAt      string `json:"updatedAt"`      // 更新时间
}

// AdminOrderDeliveryCreateReq 管理后台创建发货记录请求
type AdminOrderDeliveryCreateReq struct {
	OrderID        uint64 `json:"orderId" binding:"required" msg:"订单ID不能为空"`
	ExpressCompany string `json:"expressCompany" binding:"required" msg:"快递公司不能为空"`
	ExpressCode    string `json:"expressCode"`                                      // 快递公司编码，可选
	TrackingNumber string `json:"trackingNumber" binding:"required" msg:"快递单号不能为空"` // 快递单号
	Remark         string `json:"remark"`                                           // 备注
	AccountID      uint64 `json:"-"`                                                // 管理员账户ID，内部使用
}

// AdminOrderDeliveryCreateRes 管理后台创建发货记录响应
type AdminOrderDeliveryCreateRes struct {
	ID      uint64 `json:"id"`      // 发货记录ID
	OrderID uint64 `json:"orderId"` // 订单ID
	Message string `json:"message"` // 操作结果消息
}

// AdminOrderDeliveryUpdateReq 管理后台更新发货记录请求
type AdminOrderDeliveryUpdateReq struct {
	ID             uint64 `json:"id" binding:"required" msg:"发货记录ID不能为空"`
	OrderID        uint64 `json:"orderId"`        // 订单ID，可选
	ExpressCompany string `json:"expressCompany"` // 快递公司
	ExpressCode    string `json:"expressCode"`    // 快递公司编码
	TrackingNumber string `json:"trackingNumber"` // 快递单号
	DeliveryStatus uint32 `json:"deliveryStatus"` // 发货状态
	Remark         string `json:"remark"`         // 备注
	AccountID      uint64 `json:"-"`              // 管理员账户ID，内部使用
}

// AdminOrderDeliveryUpdateRes 管理后台更新发货记录响应
type AdminOrderDeliveryUpdateRes struct {
	ID      uint64 `json:"id"`      // 发货记录ID
	OrderID uint64 `json:"orderId"` // 订单ID
	Message string `json:"message"` // 操作结果消息
}

// AdminOrderDeliveryDetailReq 管理后台获取发货详情请求
type AdminOrderDeliveryDetailReq struct {
	ID      uint64 `form:"id" json:"id"`           // 发货记录ID，可选
	OrderID uint64 `form:"orderId" json:"orderId"` // 订单ID，可选
}

func (req *AdminOrderDeliveryDetailReq) Validate() error {
	if req.ID == 0 && req.OrderID == 0 {
		return errors.New("发货记录ID或订单ID不能为空")
	}
	return nil
}

// AdminOrderDeliveryDetailRes 管理后台获取发货详情响应
type AdminOrderDeliveryDetailRes struct {
	*AdminDeliveryInfo
}

// AdminOrderDeliveryListReq 管理后台发货记录列表请求
type AdminOrderDeliveryListReq struct {
	common.DataListReq

	OrderID        uint64 `form:"orderId" json:"orderId"`               // 订单ID
	DeliveryStatus uint32 `form:"deliveryStatus" json:"deliveryStatus"` // 发货状态
	TrackingNumber string `form:"trackingNumber" json:"trackingNumber"` // 快递单号
	ExpressCode    string `form:"expressCode" json:"expressCode"`       // 快递公司编码
	ShippedAtStart int64  `form:"shippedAtStart" json:"shippedAtStart"` // 发货时间开始（秒级时间戳）
	ShippedAtEnd   int64  `form:"shippedAtEnd" json:"shippedAtEnd"`     // 发货时间结束（秒级时间戳）
}

// AdminOrderDeliveryListRes 管理后台发货记录列表响应
type AdminOrderDeliveryListRes struct {
	List  []*AdminDeliveryInfo `json:"list"`
	Count int64                `json:"count"` // 总数
}
