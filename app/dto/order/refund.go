package order

import (
	"errors"

	"blind_box/app/dto/common"
)

// ===== 用户端退款相关 DTO =====

// RefundCreateReq 用户申请退款请求
type RefundCreateReq struct {
	OrderID    uint64 `form:"orderId" json:"orderId"`       // 订单ID，可选
	OrderNo    string `form:"orderNo" json:"orderNo"`       // 订单号，可选
	RefundDesc string `form:"refundDesc" json:"refundDesc"` // 退款说明
	UserID     uint64 `form:"-" json:"-"`                   // 用户ID，内部使用
}

func (req *RefundCreateReq) Validate() error {
	if req.OrderID == 0 && req.OrderNo == "" {
		return errors.New("订单ID或订单号不能为空")
	}
	return nil
}

// RefundCreateRes 用户申请退款响应
type RefundCreateRes struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	OutRefundNo  string `json:"outRefundNo"`  // 商户退款单号
	RefundAmount uint64 `json:"refundAmount"` // 退款金额（分）
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	EstimateTime string `json:"estimateTime"` // 预计到账时间
	Message      string `json:"message"`      // 操作结果消息
}

// RefundDetailReq 退款详情查询请求
type RefundDetailReq struct {
	RefundID    uint64 `form:"refundId" json:"refundId"`       // 退款记录ID，可选
	OutRefundNo string `form:"outRefundNo" json:"outRefundNo"` // 商户退款单号，可选
	OrderID     uint64 `form:"orderId" json:"orderId"`         // 订单ID，可选
	UserID      uint64 `form:"-" json:"-"`                     // 用户ID，内部使用
}

func (req *RefundDetailReq) Validate() error {
	if req.RefundID == 0 && req.OutRefundNo == "" && req.OrderID == 0 {
		return errors.New("退款ID、退款单号或订单ID不能全部为空")
	}
	return nil
}

// RefundDetailRes 退款详情响应
type RefundDetailRes struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	OrderID      uint64 `json:"orderId"`      // 订单ID
	OrderNo      string `json:"orderNo"`      // 原订单号
	OutRefundNo  string `json:"outRefundNo"`  // 商户退款单号
	WxRefundID   string `json:"wxRefundId"`   // 微信退款单号
	TotalAmount  uint64 `json:"totalAmount"`  // 订单总金额（分）
	RefundAmount uint64 `json:"refundAmount"` // 退款金额（分）
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	RefundReason uint32 `json:"refundReason"` // 退款原因
	RefundDesc   string `json:"refundDesc"`   // 退款说明
	SuccessTime  string `json:"successTime"`  // 退款成功时间
	FailReason   string `json:"failReason"`   // 失败原因
	CreatedAt    string `json:"createdAt"`    // 申请时间
	EstimateTime string `json:"estimateTime"` // 预计到账时间
}

// RefundListReq 用户退款记录列表请求
type RefundListReq struct {
	UserID         uint64 `form:"userId" json:"userId"`
	RefundStatus   uint32 `form:"refundStatus" json:"refundStatus"`     // 退款状态过滤，0-全部
	CreatedAtStart int64  `form:"createdAtStart" json:"createdAtStart"` // 创建时间开始（秒级时间戳）
	CreatedAtEnd   int64  `form:"createdAtEnd" json:"createdAtEnd"`     // 创建时间结束（秒级时间戳）
	common.DataListReq
}

// RefundListRes 用户退款记录列表响应
type RefundListRes struct {
	List  []*RefundListItem `json:"list"`
	Count int64             `json:"count"`
}

// RefundListItem 退款记录列表项
type RefundListItem struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	OrderID      uint64 `json:"orderId"`      // 订单ID
	OrderNo      string `json:"orderNo"`      // 原订单号
	OutRefundNo  string `json:"outRefundNo"`  // 商户退款单号
	RefundAmount uint64 `json:"refundAmount"` // 退款金额（分）
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	RefundDesc   string `json:"refundDesc"`   // 退款说明
	SuccessTime  string `json:"successTime"`  // 退款成功时间
	CreatedAt    string `json:"createdAt"`    // 申请时间
}

// RefundCancelReq 用户取消退款请求
type RefundCancelReq struct {
	RefundID    uint64 `form:"refundId" json:"refundId"`       // 退款记录ID，可选
	OutRefundNo string `form:"outRefundNo" json:"outRefundNo"` // 商户退款单号，可选
	UserID      uint64 `form:"-" json:"-"`                     // 用户ID，内部使用
}

func (req *RefundCancelReq) Validate() error {
	if req.RefundID == 0 && req.OutRefundNo == "" {
		return errors.New("退款ID或退款单号不能为空")
	}
	return nil
}

// RefundCancelRes 用户取消退款响应
type RefundCancelRes struct {
	RefundID uint64 `json:"refundId"` // 退款记录ID
	Message  string `json:"message"`  // 操作结果消息
}

// ===== 管理后台退款相关 DTO =====

// AdminRefundCreateReq 管理后台发起退款请求
type AdminRefundCreateReq struct {
	OrderID      uint64 `form:"orderId" json:"orderId"`           // 订单ID，可选
	OrderNo      string `form:"orderNo" json:"orderNo"`           // 订单号，可选
	RefundAmount uint64 `form:"refundAmount" json:"refundAmount"` // 退款金额（分），0表示全额退款
	RefundReason uint32 `form:"refundReason" json:"refundReason"` // 退款原因
	RefundDesc   string `form:"refundDesc" json:"refundDesc"`     // 退款说明
	Remark       string `form:"remark" json:"remark"`             // 管理员备注
	AccountID    uint64 `form:"-" json:"-"`                       // 管理员账户ID，内部使用
}

func (req *AdminRefundCreateReq) Validate() error {
	if req.OrderID == 0 && req.OrderNo == "" {
		return errors.New("订单ID或订单号不能为空")
	}
	if req.RefundReason == 0 {
		return errors.New("退款原因不能为空")
	}
	return nil
}

// AdminRefundCreateRes 管理后台发起退款响应
type AdminRefundCreateRes struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	OutRefundNo  string `json:"outRefundNo"`  // 商户退款单号
	RefundAmount uint64 `json:"refundAmount"` // 退款金额（分）
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	Message      string `json:"message"`      // 操作结果消息
}

// AdminRefundListReq 管理后台退款记录列表请求
type AdminRefundListReq struct {
	RefundID       uint64 `form:"refundId" json:"refundId"`             // 退款记录ID
	OrderID        uint64 `form:"orderId" json:"orderId"`               // 订单ID
	OrderNo        string `form:"orderNo" json:"orderNo"`               // 订单号
	OutRefundNo    string `form:"outRefundNo" json:"outRefundNo"`       // 商户退款单号
	WxRefundID     string `form:"wxRefundId" json:"wxRefundId"`         // 微信退款单号
	UserID         uint64 `form:"userId" json:"userId"`                 // 用户ID
	RefundStatus   uint32 `form:"refundStatus" json:"refundStatus"`     // 退款状态过滤，0-全部
	RefundReason   uint32 `form:"refundReason" json:"refundReason"`     // 退款原因过滤，0-全部
	AccountID      uint64 `form:"accountID" json:"accountID"`           // 操作管理员ID过滤
	CreatedAtStart int64  `form:"createdAtStart" json:"createdAtStart"` // 创建时间开始（秒级时间戳）
	CreatedAtEnd   int64  `form:"createdAtEnd" json:"createdAtEnd"`     // 创建时间结束（秒级时间戳）
	common.DataListReq
}

// AdminRefundListRes 管理后台退款记录列表响应
type AdminRefundListRes struct {
	List  []*AdminRefundListItem `json:"list"`
	Count int64                  `json:"count"`
}

// AdminRefundListItem 管理后台退款记录列表项
type AdminRefundListItem struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	OrderID      uint64 `json:"orderId"`      // 订单ID
	OrderNo      string `json:"orderNo"`      // 原订单号
	OutRefundNo  string `json:"outRefundNo"`  // 商户退款单号
	WxRefundID   string `json:"wxRefundId"`   // 微信退款单号
	UserID       uint64 `json:"userId"`       // 用户ID
	UserName     string `json:"userName"`     // 用户昵称
	TotalAmount  uint64 `json:"totalAmount"`  // 订单总金额（分）
	RefundAmount uint64 `json:"refundAmount"` // 退款金额（分）
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	RefundReason uint32 `json:"refundReason"` // 退款原因
	RefundDesc   string `json:"refundDesc"`   // 退款说明
	SuccessTime  string `json:"successTime"`  // 退款成功时间
	FailReason   string `json:"failReason"`   // 失败原因
	AccountID    uint64 `json:"adminId"`      // 操作管理员ID
	AdminName    string `json:"adminName"`    // 操作管理员姓名
	Remark       string `json:"remark"`       // 管理员备注
	CreatedAt    string `json:"createdAt"`    // 创建时间
	UpdatedAt    string `json:"updatedAt"`    // 更新时间
}

// AdminRefundDetailReq 管理后台退款详情请求
type AdminRefundDetailReq struct {
	RefundID    uint64 `form:"refundId" json:"refundId"`       // 退款记录ID，可选
	OutRefundNo string `form:"outRefundNo" json:"outRefundNo"` // 商户退款单号，可选
}

func (req *AdminRefundDetailReq) Validate() error {
	if req.RefundID == 0 && req.OutRefundNo == "" {
		return errors.New("退款ID或退款单号不能为空")
	}
	return nil
}

// AdminRefundDetailRes 管理后台退款详情响应
type AdminRefundDetailRes struct {
	*AdminRefundListItem

	// 额外的详细信息
	OrderInfo    *OrderInfo `json:"orderInfo"`    // 订单详情
	RequestData  string     `json:"requestData"`  // 请求数据
	ResponseData string     `json:"responseData"` // 响应数据
}

// AdminRefundRetryReq 管理后台重试退款请求
type AdminRefundRetryReq struct {
	RefundID    uint64 `form:"refundId" json:"refundId"`       // 退款记录ID，可选
	OutRefundNo string `form:"outRefundNo" json:"outRefundNo"` // 商户退款单号，可选
	AccountID   uint64 `form:"-" json:"-"`                     // 管理员账户ID，内部使用
}

func (req *AdminRefundRetryReq) Validate() error {
	if req.RefundID == 0 && req.OutRefundNo == "" {
		return errors.New("退款ID或退款单号不能为空")
	}
	return nil
}

// AdminRefundRetryRes 管理后台重试退款响应
type AdminRefundRetryRes struct {
	RefundID     uint64 `json:"refundId"`     // 退款记录ID
	RefundStatus uint32 `json:"refundStatus"` // 退款状态
	Message      string `json:"message"`      // 操作结果消息
}

// ===== 退款状态枚举和常量 =====

const (
	RefundStatusPending   = 1 // 退款中
	RefundStatusSuccess   = 2 // 退款成功
	RefundStatusFailed    = 3 // 退款失败
	RefundStatusCancelled = 4 // 退款已取消
)

const (
	RefundReasonUserRequest = 1 // 用户申请
	RefundReasonAdminRefund = 2 // 管理员退款
	RefundReasonSystemError = 3 // 系统错误
	RefundReasonOrderCancel = 4 // 订单取消
	RefundReasonGoodsDefect = 5 // 商品问题
)

// GetRefundStatusText 获取退款状态文本描述
func GetRefundStatusText(status uint32) string {
	switch status {
	case RefundStatusPending:
		return "退款中"
	case RefundStatusSuccess:
		return "退款成功"
	case RefundStatusFailed:
		return "退款失败"
	case RefundStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetRefundReasonText 获取退款原因文本描述
func GetRefundReasonText(reason uint32) string {
	switch reason {
	case RefundReasonUserRequest:
		return "用户申请"
	case RefundReasonAdminRefund:
		return "管理员退款"
	case RefundReasonSystemError:
		return "系统错误"
	case RefundReasonOrderCancel:
		return "订单取消"
	case RefundReasonGoodsDefect:
		return "商品问题"
	default:
		return "其他原因"
	}
}
