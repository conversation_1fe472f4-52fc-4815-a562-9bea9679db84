# app/dto 目录代码规范

本文档记录 `/app/dto` 目录下的数据传输对象（Data Transfer Object）定义规范和最佳实践。

## 目录结构规范

### 模块组织方式
```
app/dto/
├── [模块名]/          # 业务模块目录
│   ├── [功能].go     # 具体功能的 DTO 定义
│   └── admin_[功能].go  # 管理后台相关 DTO
├── common/           # 通用 DTO 定义
│   ├── common.go     # 通用结构体
│   ├── goods.go      # 商品相关通用
│   └── user.go       # 用户相关通用
├── api/              # 第三方 API 相关
└── openapi/          # 开放 API 相关
```

### 典型模块示例
- `user/`: 用户相关 DTO
- `order/`: 订单相关 DTO
- `goods/`: 商品相关 DTO
- `box/`: 盲盒相关 DTO
- `admin/`: 管理后台通用 DTO
- `common/`: 公共复用 DTO

## 命名规范

### 文件命名
```go
// 普通功能
user.go           // 用户相关
login.go          // 登录相关
cart.go           // 购物车相关

// 管理后台功能
admin_user.go     // 管理后台用户管理
admin_order.go    // 管理后台订单管理
admin_sku.go      // 管理后台 SKU 管理
```

### 结构体命名

#### 请求结构体 - Req 后缀
```go
// 基础请求
type UserLoginReq struct {}
type OrderDetailReq struct {}
type BoxCreateReq struct {}

// 管理后台请求
type AdminUserListReq struct {}
type AdminOrderCancelReq struct {}
type AdminSkuOperateReq struct {}
```

#### 响应结构体 - Res/Resp 后缀
```go
// 响应结构体（两种后缀都可，保持模块内一致）
type UserLoginResp struct {}    // Resp 后缀
type OrderDetailRes struct {}    // Res 后缀
type BoxListRes struct {}        // Res 后缀

// 列表响应
type AdminUserListResp struct {}
type OrderListRes struct {}
```

#### 列表项结构体 - Item 后缀
```go
// 列表中的单个项目
type AdminUserListItem struct {}
type OrderListItem struct {}
type BoxSearchItem struct {}
type AdminSkuStockLogListItem struct {}
```

#### 基础/通用结构体
```go
// Base 后缀表示基础结构
type BoxBase struct {}

// Info 后缀表示信息结构
type OrderInfo struct {}
type BoxActiveInfo struct {}
type BoxGoodsDetailItem struct {}
```

## 请求结构体规范

### 基本定义
```go
type UserEditReq struct {
    // 字段定义包含多个标签
    Nickname string `form:"nickname" json:"nickname" binding:"omitempty,max=64"`
    Avatar   string `form:"avatar" json:"avatar" binding:"omitempty,max=128"`
    Email    string `form:"email" json:"email" binding:"omitempty,email,max=64"`
}
```

### 标签说明

#### form 标签 - 表单/查询参数绑定
```go
// 用于 GET 请求的查询参数或表单提交
`form:"field_name"`
```

#### json 标签 - JSON 绑定
```go
// 用于 POST/PUT 请求的 JSON body
`json:"field_name"`

// 空值忽略
`json:"field_name,omitempty"`
```

#### binding 标签 - 参数验证
```go
// 必填字段
`binding:"required"`

// 必填且可省略空值
`binding:"required,omitempty"`

// 字符串长度限制
`binding:"min=6,max=32"`

// 数值范围
`binding:"min=1,max=100"`

// 枚举值
`binding:"oneof=1 2 3"`

// 邮箱格式
`binding:"email"`

// 手机号格式（需自定义验证器）
`binding:"mobile"`

// 组合验证
`binding:"required,omitempty,min=6,max=32"`

// 字段间比较
`binding:"gtefield=StartTime"`  // 大于等于 StartTime 字段

// 自定义验证（如时间跨度）
`binding:"withinOneYear"`
```

#### msg 标签 - 自定义错误消息
```go
// 提供友好的错误提示
`binding:"required" msg:"订单ID不能为空"`
`binding:"min=6,max=32" msg:"密码长度必须在6-32位之间"`
```

### 参数验证示例

#### 基础验证
```go
type AdminSetAccountReq struct {
    ID      uint64 `form:"id" json:"id" binding:"omitempty"`
    Account string `form:"account" json:"account" binding:"required,omitempty,max=32"`
    Name    string `form:"name" json:"name" binding:"required,omitempty,max=32"`
    Mobile  string `form:"mobile" json:"mobile" binding:"required,omitempty"`
    RoleID  uint64 `form:"role_id" json:"role_id" binding:"required,omitempty"`
    Status  uint32 `form:"status" json:"status" binding:"oneof=1 2"`
    Pwd     string `form:"pwd" json:"pwd" binding:"omitempty,min=6,max=32"`
}
```

#### 自定义验证方法
```go
type OrderDetailReq struct {
    UserID     uint64 `form:"userId" json:"userId"`
    OrderID    uint64 `form:"orderId" json:"orderId"`
    OutTradeNo string `form:"outTradeNo" json:"outTradeNo"`
}

// 自定义验证逻辑
func (req *OrderDetailReq) Validate() error {
    if req.OrderID == 0 && req.OutTradeNo == "" {
        return errors.New("订单号不能为空")
    }
    return nil
}
```

#### 时间范围验证
```go
type BaseTimeRangeReq struct {
    StartTime int64 `form:"start_time" json:"start_time" binding:"required" msg:"开始时间不能为空"`
    EndTime   int64 `form:"end_time" json:"end_time" binding:"required,gtefield=StartTime,withinOneYear" msg:"结束时间必须大于开始时间且时间跨度不超过1年"`
}
```

## 响应结构体规范

### 基本响应
```go
type UserLoginResp struct {
    IsFirst  uint32             `json:"is_first"`
    Token    string             `json:"token"`
    UserInfo *AdminUserListItem `json:"user_info"`
}
```

### 列表响应
```go
// 标准列表响应结构
type AdminUserListResp struct {
    List  []*AdminUserListItem `json:"list"`
    Total int64                `json:"total"`  // 总数
}

// 或使用 Count
type OrderListRes struct {
    List  []*OrderListItem `json:"list"`
    Count int64            `json:"count"`  // 总数
}

// 带分页标识
type BoxListRes struct {
    ActiveList   []*BoxActiveInfo `json:"activeList"`
    Count        int64            `json:"count"`
    IsNextPage   bool             `json:"isNextPage"`  // 是否有下一页
}
```

### 详情响应
```go
type OrderDetailRes struct {
    OrderInfo    *OrderInfo    `json:"orderInfo"`
    GoodsInfo    []*TradeGood  `json:"goodsInfo"`
    OrderPayInfo *OrderPay     `json:"orderPayInfo"`
    ActiveTitle  string        `json:"activeTitle"`
}
```

### 操作响应
```go
// 简单操作响应
type OrderCancelRes struct {
    OrderID uint64 `json:"orderId"`
    Message string `json:"message"`
}

// 生成类响应
type PickupCodeGenerateRes struct {
    OrderID    uint64 `json:"orderId"`
    OrderNo    string `json:"orderNo"`
    PickupCode string `json:"pickupCode"`
    IsNew      bool   `json:"isNew"`  // 标识是否新生成
}
```

## 通用 DTO 和复用模式

### 分页请求基类
```go
// app/dto/common/common.go
type DataListReq struct {
    Page  int `form:"page,default=1" json:"page,default=1" binding:"default=1"`
    Limit int `form:"limit,default=20" json:"limit,default=20" binding:"default=20"`
}

// 使用继承
type AdminUserListReq struct {
    // 特定字段
    RoleID  uint64 `form:"role_id" json:"role_id"`
    Account string `form:"account" json:"account"`
    
    // 继承分页
    common.DataListReq
}
```

### 通用 ID 请求
```go
type IdReq struct {
    ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}
```

### 通用时间范围
```go
type BaseTimeRangeReq struct {
    StartTime int64 `form:"start_time" json:"start_time" binding:"required"`
    EndTime   int64 `form:"end_time" json:"end_time" binding:"required,gtefield=StartTime"`
}
```

### 通用过滤器
```go
// 商品 SPU/SKU 通用过滤
type CommonSpuSkuFilterReq struct {
    SpuID      uint64   `form:"spu_id" json:"spu_id"`
    SpuIds     []uint64 `form:"spu_ids" json:"spu_ids"`
    SpuCode    string   `form:"spu_code" json:"spu_code"`
    SpuTitle   string   `form:"spu_title" json:"spu_title"`
    SkuID      uint64   `form:"sku_id" json:"sku_id"`
    SkuIds     []uint64 `form:"sku_ids" json:"sku_ids"`
    SkuCode    string   `form:"sku_code" json:"sku_code"`
    SkuTitle   string   `form:"sku_title" json:"sku_title"`
    CategoryID uint64   `form:"category_id" json:"category_id"`
}

// 继承使用
type AdminSkuListReq struct {
    common.CommonSpuSkuFilterReq
    Status uint32 `form:"status" json:"status"`
    common.DataListReq
}
```

## 复杂结构体组织

### 嵌套结构体
```go
type OrderDetailRes struct {
    OrderInfo     *OrderInfo     `json:"orderInfo"`
    GoodsInfo     []*TradeGood   `json:"goodsInfo"`
    OrderPayInfo  *OrderPay      `json:"orderPayInfo"`
    OrderDelivery *OrderDelivery `json:"orderDelivery"`
}

// 子结构定义
type OrderDelivery struct {
    Delivery       *goods.CrSkuDeliveryItem   `json:"delivery"`
    Address        *common.CommonUserAddrInfo `json:"address"`
    PickupCode     string                     `json:"pickupCode"`
    ExpressCode    string                     `json:"expressCode"`
    ExpressCompany string                     `json:"expressCompany"`
}
```

### 继承与扩展
```go
// 基础结构
type OrderDetailRes struct {
    OrderInfo    *OrderInfo   `json:"orderInfo"`
    GoodsInfo    []*TradeGood `json:"goodsInfo"`
}

// 管理后台扩展
type AdminOrderListItem struct {
    *OrderDetailRes  // 继承基础结构
    
    // 管理后台特有字段
    UserName string `json:"userName"`
}
```

### 结构体复用
```go
// 定义基础结构
type BoxBase struct {
    ActiveTitle    string   `json:"activeTitle"`
    ActiveStatus   int      `json:"activeStatus"`
    ActiveImage    string   `json:"activeImage"`
    DetailImages   []string `json:"detailImages"`
    ActivePrice    int      `json:"activePrice"`
}

// 创建请求复用
type BoxCreateReq struct {
    *BoxBase
}

// 更新请求复用并扩展
type BoxUpdateReq struct {
    ID uint64 `json:"id" form:"id" binding:"required"`
    *BoxBase
}
```

## 特殊字段处理

### 内部字段标记
```go
type OrderCancelReq struct {
    OrderID  uint64 `form:"orderId" json:"orderId" binding:"required"`
    IsRefund bool   `form:"isRefund" json:"isRefund"`
    Reason   string `form:"reason" json:"reason"`
    
    // 内部使用字段，不接收外部输入
    UserID uint64 `form:"-" json:"-"`  // 使用 - 忽略绑定
}
```

### 可选字段处理
```go
type BoxActiveRes struct {
    // 必填字段
    ActiveID    uint64 `json:"activeId"`
    ActiveTitle string `json:"activeTitle"`
    
    // 可选字段，仅在特定条件返回
    OrderInfo *OrderInfo `json:"orderInfo,omitempty"`
}
```

### 默认值设置
```go
type DataListReq struct {
    // 使用 default 标签设置默认值
    Page  int `form:"page,default=1" json:"page,default=1" binding:"default=1"`
    Limit int `form:"limit,default=20" json:"limit,default=20" binding:"default=20"`
}
```

## 常量定义
```go
// 操作结果常量
const (
    OperateNothing = "nothing"
    OperateSuccess = "success"
    OperateError   = "error"
)
```

## 最佳实践

### 1. 保持命名一致性
- 请求用 `Req` 后缀
- 响应用 `Res` 或 `Resp` 后缀（模块内保持一致）
- 列表项用 `Item` 后缀
- 基础结构用 `Base` 后缀
- 信息结构用 `Info` 后缀

### 2. 合理使用继承
- 通过组合复用通用结构（如 `DataListReq`）
- 避免过深的嵌套层级
- 优先组合而非继承

### 3. 参数验证原则
- 在 DTO 层进行基础验证（格式、长度、必填等）
- 复杂业务验证在 Service 层
- 提供友好的错误消息（msg 标签）

### 4. JSON 标签使用
- 统一使用小驼峰命名
- 可选字段加 `omitempty`
- 内部字段使用 `json:"-"` 忽略

### 5. 分页规范
- 继承 `common.DataListReq`
- 默认 page=1, limit=20
- 列表响应包含 Total/Count 字段

### 6. 时间字段处理
```go
// 使用时间戳（秒级）
CreatedAtStart int64 `form:"createdAtStart" json:"createdAtStart"`

// 时间字符串
OpenTime string `json:"openTime"`

// 可选时间（指针）
EndTime *string `json:"endTime"`
```

### 7. 数组和切片
```go
// 支持多值查询
OrderStatusList []uint32 `form:"orderStatus" json:"orderStatus"`
SkuIds         []uint64 `form:"sku_ids" json:"sku_ids"`

// 前端传参：orderStatus=1&orderStatus=2&orderStatus=3
```

### 8. 验证方法
```go
// 复杂验证逻辑使用自定义方法
func (req *OrderDetailReq) Validate() error {
    // 验证逻辑
    return nil
}
```

## 注意事项

1. **不要在 DTO 层加入业务逻辑**：DTO 只负责数据传输
2. **避免循环引用**：注意包之间的依赖关系
3. **保持字段类型一致**：与数据库模型字段类型对应
4. **文档注释**：为复杂结构体和字段添加注释
5. **版本兼容**：修改 DTO 时考虑前端兼容性