package card

import (
	"blind_box/app/dto/common"
)

type AdminCardConfigListReq struct {
}

type AdminCardConfigListResp struct {
	List []*CardConfigListNameItem `json:"list"`
}

type CardConfigListNameItem struct {
	ID       uint64 `json:"id"`
	CardName string `json:"cardName"`
}

type AdminCardConfigAddReq struct {
	CardConfig
}

type CardConfig struct {
	Code      string `json:"code" binding:"required"`
	Desc      string `json:"desc" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Type      uint32 `json:"type" binding:"required,oneof=2 3"` // 道具功能 1 提示卡 2透视卡
	ExpireDay uint32 `json:"expireDay" binding:"required"`      // 过期时间
	SourceID  uint64 `json:"sourceId" binding:"required"`       // 来源ID
	Image     string `json:"image" binding:"omitempty"`         // 图片
	DetailMsg string `json:"detailMsg" binding:"omitempty"`     // 详情说明
}

type AdminCardConfigAddResp struct {
	ID uint64 `json:"id"`
}

type AdminCardConfigUpdateReq struct {
	ID uint64 `json:"id"`
	CardConfig
}

type AdminCardConfigUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminCardConfigListInfoReq struct {
	common.DataListReq

	Name       string `form:"name" json:"name"`
	SourceName string `form:"sourceName" json:"sourceName"`
}

type AdminCardConfigListInfoResp struct {
	Total int64                     `json:"total"`
	List  []*CardConfigListInfoItem `json:"list"`
}

type CardConfigListInfoItem struct {
	ID uint64 `json:"id"`
	CardConfig
	SourceName string `json:"sourceName"`
	CreateTime string `json:"createTime"`
}

type AdminCardConfigInfoReq struct {
	ID uint64 `form:"id" json:"id" uri:"id" binding:"required"`
}

type AdminCardConfigInfoResp struct {
	CardConfigListInfoItem
}
