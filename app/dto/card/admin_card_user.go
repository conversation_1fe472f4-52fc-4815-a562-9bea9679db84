package card

import (
	"blind_box/app/dto/common"
)

type AdminCardSendReq struct {
	AccountID uint64 `json:"accountId" form:"accountId"`
	Uid       uint64 `json:"uid" form:"uid"`
	UserID    uint64 `json:"userId" form:"userId" binding:"required"`
	CardCode  string `json:"cardCode" form:"cardCode" binding:"required"`
	Num       uint32 `json:"num" form:"num" binding:"required"`
	Remark    string `json:"remark" form:"remark"`
}

type AdminCardSendResp struct {
	ID uint64 `json:"id"`
}

type AdminUserCardReq struct {
	common.DataListReq

	StartTime int64 `json:"startTime" form:"startTime"`
	EndTime   int64 `json:"endTime" form:"endTime"`

	CardCode string `json:"cardCode" form:"cardCode"`

	Status uint32 `json:"status" form:"status"`

	SourceID uint64 `json:"sourceId" form:"sourceId"`

	UserID uint64 `json:"userId" form:"userId"`
}

type AdminUserCardResp struct {
	Count int64               `json:"count"`
	List  []*UserCardListItem `json:"list"`
}

type UserCardListItem struct {
	ID       uint64 `json:"id"`
	CardCode string `json:"cardCode"`
	CardName string `json:"cardName"`

	CreateTime uint64 `json:"createTime"`

	Uid      uint64 `json:"uid"`
	UserName string `json:"userName"`
	Status   uint32 `json:"status"`

	ExpireTime    uint64 `json:"expireTime"`
	UseActiveInfo string `json:"useActiveInfo"`
	SourceType    uint32 `json:"sourceType"`
	Remark        string `json:"remark"`
	AdminUserName string `json:"adminUserName"`
	IsDelete      uint32 `json:"isDelete"`
}

type AdminUserCardDelReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"`
}

type AdminUserCardDelResp struct {
}
