package card

type AdminCardSourceListReq struct {
}

type AdminCardSourceListResp struct {
	List []*AdminCardSource `json:"list"`
}

type AdminCardSource struct {
	ID         uint64 `json:"id"`
	SourceName string `json:"sourceName"`
}

type AdminCardSourceAddReq struct {
	SourceName string `json:"sourceName" binding:"required"`
}

type AdminCardSourceAddResp struct {
	ID uint64 `json:"id"`
}

type AdminCardSourceUpdateReq struct {
	ID         uint64 `json:"id" binding:"required"`
	SourceName string `json:"sourceName" binding:"required"`
}

type AdminCardSourceUpdateResp struct {
	ID uint64 `json:"id"`
}
