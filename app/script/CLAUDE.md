# app/script 目录规范文档

本文档记录 `app/script` 目录下脚本工具的代码规范和最佳实践。

## 目录结构

```
app/script/
├── order/         # 订单相关脚本
│   ├── interface.go  # 接口定义（预留）
│   └── order.go      # 订单脚本实现（预留）
└── user/          # 用户相关脚本
    └── user.go       # 用户脚本实现（预留）
```

## 模块概述

### 当前状态
`script` 模块目前处于预留状态，主要文件都是空实现。当前仅实现了一个脚本登录功能，用于管理员以特定用户身份登录系统进行调试和测试。

### 设计目的
1. **脚本化操作**: 提供批量处理、数据迁移、系统维护等脚本化操作能力
2. **调试支持**: 允许管理员以用户身份登录，方便调试用户相关问题
3. **自动化任务**: 支持定时任务、批处理等自动化脚本执行
4. **数据修复**: 提供数据修复、批量更新等运维脚本功能

## 脚本认证机制

### 认证流程
```
管理员登录 → CheckAccountLogin() → CheckScriptAuth() → 执行脚本功能
```

### 权限要求
```go
// CheckScriptAuth 中间件
// 只允许 Root 和 Admin 角色访问脚本功能
if ctxAccount.RoleID != dbs.RoleRootID && ctxAccount.RoleID != dbs.RoleAdminID {
    // 拒绝访问
}
```

### 路由配置
```go
// router/script.go
func loadScriptApi(e *gin.RouterGroup) {
    e.POST("/script/user/login", 
        mw.CheckAccountLogin(),    // 管理员登录验证
        mw.CheckScriptAuth(),      // 脚本权限验证
        user.ScriptUserLogin)      // 脚本登录处理
}
```

## 当前实现功能

### 脚本用户登录
允许管理员以指定用户身份登录系统，用于调试和测试。

#### 接口定义
- **路径**: `/script/user/login`
- **方法**: POST
- **权限**: 需要管理员权限（Root 或 Admin）
- **参数**: 
  ```json
  {
    "id": 用户ID
  }
  ```

#### 实现流程
1. 管理员通过后台登录
2. 调用脚本登录接口，传入目标用户 ID
3. 系统验证管理员权限
4. 生成目标用户的登录 Token
5. 返回用户信息和 Token

#### 使用场景
- 调试用户报告的问题
- 测试用户特定功能
- 验证用户权限和数据
- 处理用户投诉和问题

## 接口定义规范

### interface.go 结构
```go
package [module]

import (
    "sync"
)

// Script 脚本接口定义
type Script interface {
    // 脚本方法定义
    BatchProcess(ctx *gin.Context, params interface{}) error
    DataMigration(ctx *gin.Context, params interface{}) error
}

// Entry 实现结构体
type Entry struct {
    // 依赖注入
}

// 单例模式
var (
    defaultEntry         Script
    defaultEntryInitOnce sync.Once
)

// GetScript 获取脚本实例
func GetScript() Script {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}

// newEntry 初始化
func newEntry() *Entry {
    res := &Entry{
        // 初始化依赖
    }
    return res
}
```

## 脚本开发规范

### 命名规范
- **文件名**: 小写下划线 `data_migration.go`
- **函数名**: 大驼峰 `BatchUpdateUserStatus`
- **脚本类型**: 
  - 数据处理: `Process*`
  - 数据迁移: `Migrate*`
  - 数据修复: `Fix*`
  - 批量操作: `Batch*`

### 安全规范

#### 权限控制
```go
// 所有脚本必须验证权限
func ScriptHandler(ctx *gin.Context) {
    // 自动通过中间件验证
    // CheckAccountLogin() + CheckScriptAuth()
}
```

#### 操作日志
```go
// 记录脚本执行日志
log.Ctx(ctx).WithFields(log.Fields{
    "script":     "BatchUpdateUserStatus",
    "operator":   ctxAccount.AccountID,
    "params":     params,
    "affected":   affectedCount,
}).Info("Script executed")
```

#### 数据备份
```go
// 执行危险操作前备份数据
func BackupBeforeScript(ctx *gin.Context, tableName string) error {
    // 1. 创建备份表
    backupTable := fmt.Sprintf("%s_backup_%s", tableName, time.Now().Format("20060102_150405"))
    
    // 2. 复制数据
    sql := fmt.Sprintf("CREATE TABLE %s AS SELECT * FROM %s", backupTable, tableName)
    
    // 3. 记录备份信息
    log.Ctx(ctx).Info("Data backed up to %s", backupTable)
    
    return nil
}
```

### 错误处理
```go
// 脚本错误处理
func ProcessScript(ctx *gin.Context) error {
    // 1. 参数验证
    if err := validateParams(params); err != nil {
        return ecode.ParamErr.WithMessage(err.Error())
    }
    
    // 2. 执行前检查
    if !canExecute() {
        return ecode.ScriptNotAllowedErr
    }
    
    // 3. 事务处理
    err := db.Transaction(func(tx *gorm.DB) error {
        // 脚本逻辑
        return nil
    })
    
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("Script execution failed")
        return err
    }
    
    return nil
}
```

## 脚本类型模板

### 数据迁移脚本
```go
func MigrateUserData(ctx *gin.Context, params *MigrationParams) error {
    // 1. 验证参数
    if err := params.Validate(); err != nil {
        return err
    }
    
    // 2. 备份数据
    if err := BackupBeforeScript(ctx, "user"); err != nil {
        return err
    }
    
    // 3. 批量处理
    batchSize := 1000
    offset := 0
    
    for {
        // 分批查询
        users, err := userRepo.FindByPage(ctx, offset, batchSize)
        if err != nil {
            return err
        }
        
        if len(users) == 0 {
            break
        }
        
        // 处理数据
        for _, user := range users {
            if err := processUser(ctx, user); err != nil {
                log.Ctx(ctx).WithError(err).Error("Process user failed: %d", user.ID)
                // 决定是否继续
            }
        }
        
        offset += batchSize
        
        // 进度日志
        log.Ctx(ctx).Info("Processed %d users", offset)
    }
    
    return nil
}
```

### 数据修复脚本
```go
func FixOrderStatus(ctx *gin.Context, params *FixParams) error {
    // 1. 查找异常数据
    abnormalOrders, err := findAbnormalOrders(ctx)
    if err != nil {
        return err
    }
    
    log.Ctx(ctx).Info("Found %d abnormal orders", len(abnormalOrders))
    
    // 2. 逐个修复
    fixed := 0
    failed := 0
    
    for _, order := range abnormalOrders {
        if err := fixSingleOrder(ctx, order); err != nil {
            log.Ctx(ctx).WithError(err).Error("Fix order failed: %d", order.ID)
            failed++
            continue
        }
        fixed++
    }
    
    // 3. 报告结果
    log.Ctx(ctx).Info("Fix completed: fixed=%d, failed=%d", fixed, failed)
    
    return nil
}
```

### 批量操作脚本
```go
func BatchUpdateUserPoints(ctx *gin.Context, params *BatchUpdateParams) error {
    // 1. 构建更新条件
    filter := buildFilter(params)
    
    // 2. 执行批量更新
    result := db.Model(&userDao.Model{}).
        Where(filter).
        Updates(map[string]interface{}{
            "points":      gorm.Expr("points + ?", params.AddPoints),
            "updated_at":  time.Now(),
        })
    
    if result.Error != nil {
        return result.Error
    }
    
    // 3. 记录操作
    log.Ctx(ctx).Info("Batch update completed: affected=%d", result.RowsAffected)
    
    return nil
}
```

## 执行方式

### 通过 API 执行
```go
// 1. 定义 Handler
func ExecuteScript(ctx *gin.Context) {
    req := &ScriptExecuteReq{}
    if err := ctx.ShouldBind(req); err != nil {
        helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
        return
    }
    
    // 2. 调用脚本
    result, err := scriptService.Execute(ctx, req)
    if err != nil {
        helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
        return
    }
    
    helper.AppWithDataResp(ctx, ecode.OK.Code(), "Script executed", result)
}

// 3. 注册路由
e.POST("/script/execute", 
    mw.CheckAccountLogin(),
    mw.CheckScriptAuth(),
    handler.ExecuteScript)
```

### 通过命令行执行（未来扩展）
```go
// cmd/script/main.go
func main() {
    // 1. 解析参数
    scriptName := flag.String("script", "", "Script name to execute")
    params := flag.String("params", "", "Script parameters (JSON)")
    flag.Parse()
    
    // 2. 初始化环境
    config.InitConfig()
    dbs.InitDB()
    
    // 3. 执行脚本
    if err := script.Execute(*scriptName, *params); err != nil {
        log.Fatal("Script failed: %v", err)
    }
    
    log.Info("Script completed successfully")
}
```

## 最佳实践

### 1. 幂等性设计
脚本应该支持重复执行而不产生副作用：
```go
// 检查是否已处理
if isProcessed(record) {
    log.Debug("Record already processed: %d", record.ID)
    continue
}
```

### 2. 进度追踪
长时间运行的脚本应提供进度信息：
```go
// 定期输出进度
if processed % 100 == 0 {
    progress := float64(processed) / float64(total) * 100
    log.Info("Progress: %.2f%% (%d/%d)", progress, processed, total)
}
```

### 3. 可恢复性
支持从中断点恢复执行：
```go
// 记录处理位置
checkpoint := getCheckpoint(scriptName)
if checkpoint > 0 {
    log.Info("Resuming from checkpoint: %d", checkpoint)
    offset = checkpoint
}

// 定期保存检查点
if processed % 1000 == 0 {
    saveCheckpoint(scriptName, processed)
}
```

### 4. 资源控制
避免占用过多系统资源：
```go
// 控制并发
sem := make(chan struct{}, 10) // 最多10个并发

for _, item := range items {
    sem <- struct{}{}
    go func(item Item) {
        defer func() { <-sem }()
        processItem(item)
    }(item)
}
```

### 5. 监控告警
关键脚本应有监控和告警：
```go
// 发送执行通知
notifyScriptStart(scriptName, params)
defer notifyScriptComplete(scriptName, result)

// 异常告警
if err != nil {
    alertScriptError(scriptName, err)
}
```

## 安全注意事项

1. **权限验证**: 所有脚本必须通过 `CheckScriptAuth()` 验证
2. **参数校验**: 严格验证输入参数，防止注入攻击
3. **操作审计**: 记录详细的操作日志，包括操作者、参数、结果
4. **数据备份**: 危险操作前自动备份相关数据
5. **执行限制**: 设置执行时间限制，避免长时间占用资源
6. **回滚机制**: 提供数据回滚能力，支持撤销操作
7. **测试环境**: 先在测试环境验证脚本正确性

## 扩展指南

### 添加新脚本模块

1. **创建模块目录**
```bash
mkdir app/script/newmodule
```

2. **创建接口文件**
```go
// app/script/newmodule/interface.go
package newmodule

type Script interface {
    // 定义脚本方法
}
```

3. **实现脚本功能**
```go
// app/script/newmodule/process.go
func (e *Entry) ProcessData(ctx *gin.Context, params interface{}) error {
    // 实现逻辑
}
```

4. **注册路由**
```go
// router/script.go
e.POST("/script/newmodule/process",
    mw.CheckAccountLogin(),
    mw.CheckScriptAuth(),
    handler.ProcessScript)
```

### 脚本参数设计
```go
// 定义参数结构
type ScriptParams struct {
    // 通用参数
    DryRun     bool      `json:"dry_run"`     // 试运行模式
    Limit      int       `json:"limit"`       // 处理数量限制
    StartTime  time.Time `json:"start_time"`  // 时间范围
    EndTime    time.Time `json:"end_time"`
    
    // 业务参数
    UserIDs    []uint64  `json:"user_ids"`
    OrderNos   []string  `json:"order_nos"`
    Status     []int     `json:"status"`
}

// 参数验证
func (p *ScriptParams) Validate() error {
    if p.Limit <= 0 {
        p.Limit = 1000 // 默认值
    }
    if p.Limit > 10000 {
        return errors.New("limit too large")
    }
    return nil
}
```

## 常见问题

### Q: 脚本执行失败如何处理？
A: 
1. 查看日志定位问题
2. 修复数据或代码
3. 从检查点恢复执行
4. 必要时回滚数据

### Q: 如何测试脚本？
A: 
1. 在测试环境先执行
2. 使用 DryRun 模式验证逻辑
3. 小批量数据测试
4. 验证结果正确性

### Q: 脚本执行时间过长怎么办？
A: 
1. 优化查询和处理逻辑
2. 增加批处理大小
3. 使用并发处理
4. 考虑分时段执行

## 未来规划

1. **命令行工具**: 支持通过命令行执行脚本
2. **定时任务**: 集成定时任务框架，支持周期性执行
3. **Web 界面**: 提供脚本管理和执行的 Web 界面
4. **脚本市场**: 共享常用脚本模板
5. **执行历史**: 记录和查询脚本执行历史
6. **依赖管理**: 脚本间依赖关系管理
7. **分布式执行**: 支持脚本分布式执行提高效率