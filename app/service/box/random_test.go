package box

import (
	"blind_box/pkg/util/safe_random"
	"math"
	"testing"
)

// TestRandomDistribution 测试随机数分布的正确性
func TestRandomDistribution(t *testing.T) {
	const iterations = 100000
	const tolerance = 0.05 // 5% 误差容忍度

	// 测试场景：三个商品，权重分别为 100, 200, 300
	weights := []uint32{100, 200, 300}
	totalWeight := uint32(600)

	// 期望的概率分布
	expectedRatios := []float64{
		float64(weights[0]) / float64(totalWeight), // 1/6 ≈ 0.1667
		float64(weights[1]) / float64(totalWeight), // 2/6 ≈ 0.3333
		float64(weights[2]) / float64(totalWeight), // 3/6 ≈ 0.5000
	}

	// 统计实际选中次数
	results := make([]int, len(weights))

	for i := 0; i < iterations; i++ {
		// 模拟修复后的随机选择算法
		randomNum := safe_random.Intn(int(totalWeight)) // [0, totalWeight-1]

		var cumulativeWeight uint32
		for j, weight := range weights {
			cumulativeWeight += weight
			if randomNum < int(cumulativeWeight) { // 使用 < 而不是 <=
				results[j]++
				break
			}
		}
	}

	// 验证分布是否符合预期
	for i, expectedRatio := range expectedRatios {
		actualRatio := float64(results[i]) / float64(iterations)
		diff := math.Abs(actualRatio - expectedRatio)

		t.Logf("商品 %d: 期望概率=%.4f, 实际概率=%.4f, 差异=%.4f, 选中次数=%d",
			i+1, expectedRatio, actualRatio, diff, results[i])

		if diff > tolerance {
			t.Errorf("商品 %d 的概率分布不符合预期: 期望=%.4f, 实际=%.4f, 差异=%.4f > 容忍度=%.4f",
				i+1, expectedRatio, actualRatio, diff, tolerance)
		}
	}

	// 验证总选中次数等于迭代次数
	totalSelected := 0
	for _, count := range results {
		totalSelected += count
	}

	if totalSelected != iterations {
		t.Errorf("总选中次数不匹配: 期望=%d, 实际=%d", iterations, totalSelected)
	}
}

// TestRandomBoundary 测试边界条件
func TestRandomBoundary(t *testing.T) {
	const iterations = 10000

	// 测试单个商品权重为1的情况
	totalWeight := uint32(1)
	selectedCount := 0

	for i := 0; i < iterations; i++ {
		randomNum := safe_random.Intn(int(totalWeight)) // 只能是 0

		var cumulativeWeight uint32 = 1
		if randomNum < int(cumulativeWeight) { // 0 < 1 总是为真
			selectedCount++
		}
	}

	if selectedCount != iterations {
		t.Errorf("边界测试失败: 权重为1时应该100%%被选中, 实际选中率=%.2f%%",
			float64(selectedCount)/float64(iterations)*100)
	}
}

// TestRandomEmptyWeight 测试权重为0的情况
func TestRandomEmptyWeight(t *testing.T) {
	// 权重为0应该不会被选中
	totalWeight := uint32(0)

	// 权重为0时不应该调用随机数生成器
	// 这种情况应该在调用随机选择前就被处理

	if totalWeight == 0 {
		t.Log("权重为0的情况被正确识别，不会进行随机选择")
	}
}

// TestMultipleRounds 测试多轮抽取的一致性
func TestMultipleRounds(t *testing.T) {
	const rounds = 10
	const iterationsPerRound = 10000
	const tolerance = 0.02 // 2% 容忍度

	weights := []uint32{150, 250, 350, 250}
	totalWeight := uint32(1000)

	expectedRatios := make([]float64, len(weights))
	for i, weight := range weights {
		expectedRatios[i] = float64(weight) / float64(totalWeight)
	}

	// 多轮测试验证一致性
	for round := 0; round < rounds; round++ {
		results := make([]int, len(weights))

		for i := 0; i < iterationsPerRound; i++ {
			randomNum := safe_random.Intn(int(totalWeight))

			var cumulativeWeight uint32
			for j, weight := range weights {
				cumulativeWeight += weight
				if randomNum < int(cumulativeWeight) {
					results[j]++
					break
				}
			}
		}

		// 验证这一轮的结果
		for i, expectedRatio := range expectedRatios {
			actualRatio := float64(results[i]) / float64(iterationsPerRound)
			diff := math.Abs(actualRatio - expectedRatio)

			if diff > tolerance {
				t.Errorf("轮次 %d, 商品 %d: 期望=%.4f, 实际=%.4f, 差异=%.4f > 容忍度=%.4f",
					round+1, i+1, expectedRatio, actualRatio, diff, tolerance)
			}
		}
	}

	t.Logf("完成 %d 轮一致性测试，每轮 %d 次迭代", rounds, iterationsPerRound)
}

// BenchmarkRandomSelection 性能基准测试
func BenchmarkRandomSelection(b *testing.B) {
	weights := []uint32{100, 200, 300, 400, 500}
	totalWeight := uint32(1500)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		randomNum := safe_random.Intn(int(totalWeight))

		var cumulativeWeight uint32
		for _, weight := range weights {
			cumulativeWeight += weight
			if randomNum < int(cumulativeWeight) {
				break
			}
		}
	}
}
