package box

import (
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	goodsDao "blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_level"
	boxDto "blind_box/app/dto/box"
	"blind_box/app/dto/goods"
	"blind_box/app/service/common"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm/clause"
)

// AdminBoxGoodsAdd adds a new box goods item.
func (e *Entry) AdminBoxGoodsAdd(ctx *gin.Context, req *boxDto.AdminBoxGoodsAddReq) (res *boxDto.AdminBoxGoodsAddResp, err error) {
	res = &boxDto.AdminBoxGoodsAddResp{}

	var (
		activeModel = &actDao.Model{}
		levelModel  = &box_level.Model{}

		goodsModel = &goodsDao.Model{}
	)

	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsAdd FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}

	levelModel, err = e.BoxLevelRepo.FetchByID(ctx, req.LevelID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsAdd FetchByID")
		return res, ecode.SystemErr
	}
	if levelModel == nil {
		err = ecode.BoxLevelNotExistErr
		return
	}

	goodsModel = &goodsDao.Model{
		ActiveID:   req.ActiveID,
		GoodsName:  req.GoodsName,
		GoodsCover: req.GoodsCover,
		SpuID:      req.SpuID,
		SkuID:      req.SkuID,
		LevelID:    req.LevelID,
		LevelName:  levelModel.LevelName,
	}

	err = e.BoxGoodsRepo.CreateOrUpdate(ctx, goodsModel)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsAdd CreateOrUpdate")
		return nil, ecode.SystemErr
	}
	res.ID = goodsModel.ID

	return res, nil
}

func (e *Entry) AdminBoxGoodsUpdate(ctx *gin.Context, req *boxDto.AdminBoxGoodsUpdateReq) (res *boxDto.AdminBoxGoodsUpdateResp, err error) {
	res = &boxDto.AdminBoxGoodsUpdateResp{
		ID: req.ID,
	}

	var (
		goodsModel = &goodsDao.Model{}
		updateMap  = make(map[string]interface{})

		activeModel = &actDao.Model{}
		levelModel  = &box_level.Model{}

		eg errgroup.Group
	)
	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdate FetchByID")
		return res, ecode.SystemErr
	}
	if goodsModel == nil {
		err = ecode.BoxGoodsNotExistErr
		return
	}

	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdate FetchByID")
			return ecode.SystemErr
		}
		if activeModel == nil {
			err = ecode.ActNotExistErr
			return err
		}
		return nil
	})

	eg.Go(func() error {
		levelModel, err = e.BoxLevelRepo.FetchByID(ctx, req.LevelID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdate FetchByID")
			return ecode.SystemErr
		}
		if levelModel == nil {
			err = ecode.BoxLevelNotExistErr
			return err
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdate FetchByID")
		return res, ecode.SystemErr
	}

	if goodsModel.ActiveID != req.ActiveID {
		updateMap["ActiveID"] = req.ActiveID
	}
	if goodsModel.LevelID != req.LevelID {
		updateMap["LevelID"] = req.LevelID
		updateMap["LevelName"] = levelModel.LevelName
	}
	if goodsModel.GoodsName != req.GoodsName {
		updateMap["GoodsName"] = req.GoodsName
	}
	if goodsModel.GoodsCover != req.GoodsCover {
		updateMap["GoodsCover"] = req.GoodsCover
	}
	if goodsModel.SpuID != req.SpuID {
		updateMap["SpuID"] = req.SpuID
	}
	if goodsModel.SkuID != req.SkuID {
		updateMap["SkuID"] = req.SkuID
	}
	if len(updateMap) == 0 {
		return res, nil
	}

	err = e.BoxGoodsRepo.UpdateMapByID(ctx, goodsModel.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdate UpdateMapByID")
		return nil, ecode.SystemErr
	}

	return res, nil
}

func (e *Entry) AdminBoxGoodsSort(ctx *gin.Context, req *boxDto.AdminBoxGoodsSortReq) (res *boxDto.AdminBoxGoodsSortResp, err error) {
	res = &boxDto.AdminBoxGoodsSortResp{
		ID: req.ID,
	}

	var (
		goodsModel = &goodsDao.Model{}
		updateMap  = make(map[string]interface{})
	)

	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsSort FetchByID")
		return res, ecode.SystemErr
	}
	if goodsModel == nil {
		err = ecode.BoxGoodsNotExistErr
		return res, err
	}

	updateMap["Sort"] = req.Sort
	if req.Sort == 0 {
		updateMap["sort"] = nil
	}

	err = e.BoxGoodsRepo.UpdateMapByID(ctx, goodsModel.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsSort UpdateMapByID")
		return nil, ecode.SystemErr
	}

	return res, nil
}

func (e *Entry) AdminBoxGoodsDel(ctx *gin.Context, req *boxDto.AdminBoxGoodsDelReq) (res *boxDto.AdminBoxGoodsDelResp, err error) {
	res = &boxDto.AdminBoxGoodsDelResp{}

	var (
		goodsModel = &goodsDao.Model{}
	)

	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsDel FetchByID")
		return res, ecode.SystemErr
	}
	if goodsModel == nil {
		err = ecode.BoxGoodsNotExistErr
		return res, err
	}

	err = e.BoxGoodsRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsDel UpdateMapByID")
		return nil, ecode.SystemErr
	}

	return res, nil
}

func (e *Entry) AdminBoxGoodsUpdateStock(ctx *gin.Context, req *boxDto.AdminBoxGoodsUpdateStockReq) (res *boxDto.AdminBoxGoodsUpdateStockResp, err error) {
	res = &boxDto.AdminBoxGoodsUpdateStockResp{
		ID: req.ID,
	}

	var (
		goodsModel = &goodsDao.Model{}

		updateMap = make(map[string]interface{})
	)

	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdateStock FetchByID")
		return res, ecode.SystemErr
	}
	if goodsModel == nil {
		err = ecode.BoxGoodsNotExistErr
		return res, err
	}

	updateMap["stock"] = req.Stock
	if req.Stock == 0 {
		updateMap["stock"] = nil
	}

	err = e.BoxGoodsRepo.UpdateMapByID(ctx, goodsModel.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsUpdateStock UpdateMapByID")
		return nil, ecode.SystemErr
	}

	return res, nil
}

func (e *Entry) AdminBoxGoodsList(ctx *gin.Context, req *boxDto.AdminBoxGoodsListReq) (res *boxDto.AdminBoxGoodsListResp, err error) {
	res = &boxDto.AdminBoxGoodsListResp{}
	res.List = make([]*boxDto.AdminBoxGoodsListItem, 0)

	var (
		goodsList = make(goodsDao.ModelList, 0)
		count     int64
		filter    = &goodsDao.Filter{
			ActiveID: req.ActiveID,
			Sort: []clause.OrderByColumn{
				{
					Column: clause.Column{
						Name: "sort",
					},
					Desc: true,
				},
				{
					Column: clause.Column{
						Name: "id",
					},
					Desc: true,
				},
			},
		}

		skuIDs = make([]uint64, 0)
		spuIDs = make([]uint64, 0)

		skuMap = make(map[uint64]goods.CrSkuItem)
	)

	count, goodsList, err = e.BoxGoodsRepo.DataPageList(ctx, filter, 1, 999)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsList DataPageList")
		return res, ecode.SystemErr
	}
	if len(goodsList) == 0 {
		return res, nil
	}
	res.Count = count

	skuIDs = goodsList.GetSkuIDs()
	spuIDs = goodsList.GetSpuIDs()

	if len(skuIDs) > 0 && len(spuIDs) > 0 {
		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsList GetCommonSkuResp")
			return nil, err
		}
	}

	for _, goodsModel := range goodsList {
		item := &boxDto.AdminBoxGoodsListItem{
			ID:         goodsModel.ID,
			Sort:       goodsModel.Sort,
			ActiveID:   goodsModel.ActiveID,
			GoodsName:  goodsModel.GoodsName,
			GoodsCover: goodsModel.GoodsCover,
			LevelID:    goodsModel.LevelID,
			LevelName:  goodsModel.LevelName,
			Stock:      goodsModel.Stock,
			CurStock:   goodsModel.GetCurStock(),
			UsedStock:  goodsModel.GetUsedStock(),
		}
		if sku, ok := skuMap[goodsModel.SkuID]; ok {
			item.GoodsDetail = &sku
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

func (e *Entry) AdminBoxGoodsInfo(ctx *gin.Context, req *boxDto.AdminBoxGoodsInfoReq) (res *boxDto.AdminBoxGoodsInfoResp, err error) {
	res = &boxDto.AdminBoxGoodsInfoResp{}
	res.AdminBoxGoodsListItem = &boxDto.AdminBoxGoodsListItem{}

	var (
		goodsModel = &goodsDao.Model{}

		skuMap = make(map[uint64]goods.CrSkuItem)
	)

	goodsModel, err = e.BoxGoodsRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsInfo FetchByID")
		return res, ecode.SystemErr
	}
	if goodsModel == nil {
		err = ecode.BoxGoodsNotExistErr
		return res, err
	}

	skuMap, err = common.GetService().GetCommonSkuResp(ctx, []uint64{goodsModel.SpuID}, []uint64{goodsModel.SkuID})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxGoodsInfo GetCommonSkuResp")
		return nil, err
	}

	res.ID = req.ID
	res.Sort = goodsModel.Sort
	res.ActiveID = goodsModel.ActiveID
	res.GoodsName = goodsModel.GoodsName
	res.GoodsCover = goodsModel.GoodsCover
	res.LevelID = goodsModel.LevelID
	res.LevelName = goodsModel.LevelName
	res.Stock = goodsModel.Stock
	// TODO: curStock avaStock

	if sku, ok := skuMap[goodsModel.SkuID]; ok {
		res.GoodsDetail = &sku
	}

	return res, nil
}
