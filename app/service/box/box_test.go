package box

import (
	"testing"

	"blind_box/app/common/dbs"
	boxDto "blind_box/app/dto/box"
	"blind_box/config"
	"blind_box/pkg/helper"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
)

func TestMain(m *testing.M) {
	// Initialize the test environment
	// For example, you can set up a test database connection here

	config.SetCfgPath("../../../config/test.ini")
	config.Setup()
	dbs.Setup()
	redis.Setup()
	helper.Setup()

	// Run the tests
	m.Run()

	// Clean up the test environment
	// For example, you can close the database connection here
}

func TestEntry_BoxPayTest(t *testing.T) {
	type args struct {
		ctx *gin.Context
		req *boxDto.BoxPayTestReq
	}
	tests := []struct {
		name    string
		args    args
		wantRes interface{}
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: helper.GenGinCtx(),
				req: &boxDto.BoxPayTestReq{
					UserID:      7,
					OutTradeNo:  "BT-20250625-1868122522",
					TotalAmount: 1,
					TradeNo:     "VWXYZ-4",
				},
			},
		},
		{
			name: "test2",
			args: args{
				ctx: helper.GenGinCtx(),
				req: &boxDto.BoxPayTestReq{
					UserID:      6,
					OutTradeNo:  "BT-20250516-9813150146",
					TotalAmount: 1,
					TradeNo:     "VWXYZ",
				},
			},
		},
	}

	srv := GetService()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, err := srv.BoxPayTest(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BoxPayTest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotRes == nil {
				t.Errorf("BoxPayTest() gotRes = %v", gotRes)
			}
		})
	}
}

func TestEntry_DirectPayTest(t *testing.T) {
	type args struct {
		ctx *gin.Context
		req *boxDto.BoxPayReq
	}
	tests := []struct {
		name    string
		args    args
		wantRes interface{}
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{
				ctx: helper.GenGinCtx(),
				req: &boxDto.BoxPayReq{
					UserID:       7,
					PayMethod:    1,
					CouponID:     0,
					PurchaseType: 3, // STRATEGY_DIRECT
					CartItems: []*boxDto.CartItem{
						{
							SkuID:    2,
							Quantity: 3,
						},
					},
				},
			},
		},
		{
			name: "test2",
			args: args{
				ctx: helper.GenGinCtx(),
				req: &boxDto.BoxPayReq{
					UserID:       6,
					PayMethod:    1,
					CouponID:     0,
					PurchaseType: 3, // STRATEGY_DIRECT
					CartItems:    []*boxDto.CartItem{},
				},
			},
		},
	}

	srv := GetService()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, err := srv.DirectPay(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BoxPayTest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotRes == nil {
				t.Errorf("BoxPayTest() gotRes = %v", gotRes)
			}
		})
	}
}
