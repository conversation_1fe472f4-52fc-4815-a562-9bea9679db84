package box

import (
	"sort"
	"time"

	"blind_box/app/common/dbs"
	"blind_box/app/dao/box/box_queue_lock"
	"blind_box/app/dao/user"
	boxDto "blind_box/app/dto/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 排队
func (e *Entry) boxQueueLock(ctx *gin.Context, tx *gorm.DB, boxId, userId uint64) (isLockFree bool, err error) {
	queueLock, err := e.BoxQueueLockRepo.GetQueueLockByFilter(ctx, &box_queue_lock.Filter{
		BoxID: boxId,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetQueueLock")
		return
	}
	if queueLock != nil && queueLock.ID > 0 {
		isLockFree = true
		return
	}

	// 加锁
	boxInfo, err := e.BoxRecordRepo.FetchByID(ctx, boxId)
	if err != nil {
		return false, err
	}
	if boxInfo == nil {
		log.Ctx(ctx).Error("boxInfo not found")
		return false, nil
	}

	now := time.Now()
	tmp := &box_queue_lock.Model{
		ActiveID: boxInfo.ActiveID,
		BoxID:    boxId,
		//BoxNo:           boxNo,
		UserID:          userId,
		InvalidTime:     now.Add(240 * time.Second),
		InvalidShowTime: now.Add(250 * time.Second),
	}

	err = e.BoxQueueLockRepo.CreateOrUpdateWithTx(ctx, tx, tmp)
	if err != nil {
		return isLockFree, err
	}

	return isLockFree, nil
}

func (e *Entry) QueuePush(ctx *gin.Context, req *boxDto.BoxQueuePushReq) (res *boxDto.BoxQueuePushRes, err error) {
	var (
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)
	isLockFree, err := e.boxQueueLock(ctx, tx, req.BoxID, req.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("boxQueueLock")
		tx.Rollback()
		return nil, err
	}
	tx.Commit()
	if !isLockFree {
		log.Ctx(ctx).Error("boxQueueLock")
	}
	return res, nil
}

func (e *Entry) Queue(ctx *gin.Context, req *boxDto.BoxQueueReq) (res *boxDto.BoxQueueRes, err error) {
	res = &boxDto.BoxQueueRes{
		BoxID: req.BoxID,
		Lock:  false,
	}

	var (
		timeNow     = time.Now()
		invalidTime = timeNow.Add(-10 * time.Second).Format(dbs.TimeDateFormatFull)
		userInfo    = &user.Model{}
		lock        = &box_queue_lock.Model{}

		queueList = make(box_queue_lock.ModelList, 0)
	)

	queueList, err = e.BoxQueueLockRepo.FindByFilter(ctx, &box_queue_lock.Filter{
		UserID:           req.UserID,
		BoxID:            req.BoxID,
		InvalidTimeBegin: invalidTime,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxQueueLockRepo.FindByFilter")
		err = ecode.SystemErr
		return nil, err
	}
	if len(queueList) <= 0 {
		return res, nil
	}
	sort.Slice(queueList, func(i, j int) bool {
		return queueList[i].InvalidTime.Unix() > queueList[j].InvalidTime.Unix()
	})
	lock = queueList[0]

	userInfo, err = e.UserRepo.FetchByID(ctx, lock.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UserRepo.FetchByID")
		err = ecode.SystemErr
		return nil, err
	}
	if userInfo == nil {
		err = ecode.UserNotExistErr
		return nil, err
	}

	if lock.UserID == req.UserID && lock.InvalidTime.Unix() <= timeNow.Unix() {
		return res, nil
	}

	res.UserID = userInfo.ID
	res.Name = userInfo.Nickname
	res.Pic = helper.GetImageCdnUrl(ctx, userInfo.Avatar)
	res.Lock = true
	res.InvalidTime = lock.InvalidTime.UnixNano() / 1e6
	res.ServerTime = timeNow.UnixNano() / 1e6
	res.LockType = 3

	if lock.UserID != req.UserID {
		res.InvalidTime += 10000
	}

	return res, nil
}
