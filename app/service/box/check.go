package box

import (
	"strconv"
	"strings"
)

// 校验slot中是否都是可用的
func checkAvaSlot(avaSlot string, slot []uint32) (check bool) {
	if len(avaSlot) == 0 || avaSlot == "" {
		return false
	}

	avaSlotSli := strings.Split(avaSlot, ",")
	//lockSlotSli := strings.Split(lockSlot, ",")
	//avaSlotSli = append(avaSlotSli, lockSlotSli...)
	setB := make(map[uint32]struct{})

	// 将数组B中的元素添加到集合setB中
	for _, s := range avaSlotSli {
		num, err := strconv.ParseUint(s, 10, 64)
		if err != nil {
			continue
		}
		setB[uint32(num)] = struct{}{}
	}

	// 遍历数组A，如果有元素不在setB中，则返回false
	for _, num := range slot {
		if _, exists := setB[num]; !exists {
			return false
		}
	}

	return true
}
