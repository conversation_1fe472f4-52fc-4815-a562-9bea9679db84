package box

import (
	"container/list"
	"sync"
)

type Window struct {
	limit         uint32
	DrawQueue     *list.List
	ItemDrawCount map[uint64]uint32
	mu            sync.Mutex
}

func (w *Window) SetLimit(limit uint32) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.limit = limit
}

func NewWindow() *Window {
	return &Window{
		//limit:         limit,
		DrawQueue:     list.New(),
		ItemDrawCount: make(map[uint64]uint32),
	}
}

type DrawEvent struct {
	UserID  uint64
	GoodsID uint64
}

func (w *Window) Len() int {
	w.mu.Lock()
	defer w.mu.Unlock()
	return w.DrawQueue.Len()
}

func (w *Window) handleDrawEvent(event DrawEvent) {
	w.mu.Lock()
	defer w.mu.Unlock()
	// 添加新的抽取事件到队列末尾
	w.DrawQueue.PushBack(event)
	w.ItemDrawCount[event.GoodsID]++

	// 移除不在窗口内的事件
	for w.DrawQueue.Len() > int(w.limit) {
		oldEvent := w.DrawQueue.Remove(w.DrawQueue.Front()).(DrawEvent)
		w.ItemDrawCount[oldEvent.GoodsID]--
		if w.ItemDrawCount[oldEvent.GoodsID] == 0 {
			delete(w.ItemDrawCount, oldEvent.GoodsID)
		}
	}
}

func (w *Window) drawItemNum(GoodsId uint64) uint32 {
	w.mu.Lock()
	defer w.mu.Unlock()
	n, ok := w.ItemDrawCount[GoodsId]
	if !ok { // 没出过
		return 0
	}
	return n // 出过n次
}

func (w *Window) addHistoricalDrawEvents(events []DrawEvent) {
	w.mu.Lock()
	defer w.mu.Unlock()
	if len(events) == 0 {
		return
	}
	for _, event := range events {
		// 添加事件到队列
		w.DrawQueue.PushBack(event)
		// 更新商品抽取计数
		w.ItemDrawCount[event.GoodsID]++
	}

	// 如果历史事件数量超过窗口大小，移除旧的事件
	for w.DrawQueue.Len() > int(w.limit) {
		oldEvent := w.DrawQueue.Remove(w.DrawQueue.Front()).(DrawEvent)
		w.ItemDrawCount[oldEvent.GoodsID]--
		if w.ItemDrawCount[oldEvent.GoodsID] == 0 {
			delete(w.ItemDrawCount, oldEvent.GoodsID)
		}
	}
}
