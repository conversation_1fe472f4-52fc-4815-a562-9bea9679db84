package box

import (
	"errors"
	"fmt"
	"strconv"

	activeDao "blind_box/app/dao/activity"
	"blind_box/app/dao/box/box_action_detail"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_level"
	"blind_box/app/dao/box/box_record"
	itemCardDao "blind_box/app/dao/card/card_config"
	userCardDao "blind_box/app/dao/card/card_user"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	"blind_box/pkg/log"
	"blind_box/pkg/util/safe_random"
	"blind_box/pkg/util/sliceUtil"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	STRATEGY_PURCHASE = 1 // 盲盒策略
	STRATEGY_ITEMCARD = 2 // 道具卡策略
	STRATEGY_DIRECT   = 3 // 直接购买策略
)

// StrategySelect gen 策略选择 factory
func (e *Entry) StrategySelect(strategyType int) Operator {
	switch strategyType {
	case 1:
		return &PurchaseStrategy{
			entry: e,
		}
	case 2:
		return &ItemCardStrategy{
			entry: e,
		}
	case 3:
		return &DirectPurchaseStrategy{
			entry: e,
		}
	default:
		return nil
	}
}

// 盲盒箱子
type BoxModel struct {
	Active        *activeDao.Model
	Config        *box_active_config.Model
	OrderInfo     *orderDao.Model
	BoxInfo       *box_record.Model
	Slot          []uint32
	GoodsStockMap goodsStockMap
	BoxRecord     *boxRecordInfo

	// 直接购买相关字段 - SKU为最小购买粒度
	SkuID    uint64 // SKU商品ID（最小购买粒度）
	Quantity uint32 // 购买数量

	// 公共字段
	UserID      uint64 // 用户ID
	ActiveID    uint64 // 活动ID
	BoxID       uint64 // 盒子ID
	BoxNo       string // 盒子编号
	OrderID     uint64 // 订单ID
	ActiveTitle string // 活动标题
	ActiveType  uint32 // 活动类型
}

// 道具卡
type ItemCardModel struct {
	ItemCardConfig *itemCardDao.Model
	UserItemCard   *userCardDao.Model
	ICType         uint32
}

// 抽象操作
type Operator interface {
	Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error)
}

// 购买盲盒
type PurchaseStrategy struct {
	entry *Entry
}

func (p *PurchaseStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error) {
	res = &ActionResult{}
	res.List = make([]*ActionResultItem, 0)
	res.OrderInfo = b.OrderInfo
	actionType := 0

	if i != nil {
		return nil, errors.New("not support item card")
	}

	sgReq := &selectGoodsReq{
		UserID:    b.OrderInfo.UserID,
		BoxId:     b.BoxInfo.ID,
		Slot:      b.Slot,
		OrderInfo: b.OrderInfo,
		BoxInfo:   b.BoxInfo,
	}

	drawWindow, err := p.entry.getBoxRulesAndDrawWindow(ctx, b, sgReq)
	if err != nil {
		// handle error
	}

	res, actionType, err = p.entry.selectRandomItemProduce(ctx, sgReq, b.GoodsStockMap, b.BoxRecord, b.Active, drawWindow)
	actionType = box_action_detail.ACTION_TYPE_1 // 支付情况下为明确产出
	res.ActionType = uint32(actionType)
	res.OrderInfo = b.OrderInfo

	return
}

// 随机选择商品并产出
func (e *Entry) selectRandomItemProduce(
	ctx *gin.Context,
	req *selectGoodsReq,
	goods goodsStockMap,
	box *boxRecordInfo,
	active *activeDao.Model,
	drawWindow *Window,
) (result *ActionResult, actionType int, err error) {
	result = &ActionResult{}
	result.List = make([]*ActionResultItem, 0)
	var (
		preLockRecords = make([]*box_action_detail.Model, 0)
	)

	// 校验预产出锁定商品
	preLockRecords, err = e.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
		BoxID:      req.BoxId,
		Slots:      req.Slot,
		UserID:     req.UserID,
		ActionType: box_action_detail.ACTION_TYPE_3,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxActionDetailRepo.FindByFilter")
		return nil, 0, err
	}
	if len(preLockRecords) > 0 { // 预产出锁定商品
		err = e.handlePreLockedGoods(ctx, preLockRecords, req, goods, active, result)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("handlePreLockedGoods")
			return nil, 0, err
		}
	}
	if len(req.Slot) <= 0 {
		return
	}

	for _, stock := range goods {
		if stock.LevelName != box_level.BoxLevelNormal {
			if stock.GetCurStock() <= 0 {
				delete(goods, stock.ID) // 当隐藏款式库存为0时，不参与抽取
			}
		}
	}

	// 删除已经产出的商品种类
	delGoods, err := e.BoxActionDetailRepo.FindXidsByFilter(ctx, &box_action_detail.Filter{
		BoxID: req.BoxId,
		ActionTypeList: []uint32{
			box_action_detail.ACTION_TYPE_1,
			box_action_detail.ACTION_TYPE_3,
		},
	}, "goods_id")
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxActionDetailRepo.FindByFilter")
		return nil, 0, err
	}
	if len(delGoods) > 0 {
		for _, d := range delGoods {
			if d > 0 {
				delete(goods, d)
			}
		}
	}
	if len(goods) == 1 {
		err = e.handleOnlyOneGoods(ctx, req, goods, active, result)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("handleOnlyOneGoods")
			return nil, 0, err
		}
		return
	}
	if len(goods) <= 0 {
		log.Ctx(ctx).Error("没有可抽取的商品")
		return nil, 0, errors.New("没有可抽取的商品")
	}

	err = e.selectRandomGoodsUsingRules(ctx, req, goods, active, drawWindow, result)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("selectRandomGoodsUsingRules")
		return nil, 0, err
	}

	return
}

// 处理预锁定商品
func (e *Entry) handlePreLockedGoods(
	ctx *gin.Context,
	preLockRecord []*box_action_detail.Model,
	req *selectGoodsReq,
	goods goodsStockMap,
	active *activeDao.Model,
	result *ActionResult,
) (err error) {
	var (
		preLockGoodsMap = make(map[slot]goodsId)
		preLockGoods    = make([]uint64, 0)
		goodsInfoList   = make(box_goods.ModelList, 0)
		goodsInfoMap    = make(map[uint64]*box_goods.Model)
	)

	// 先处理锁定

	for _, p := range preLockRecord {
		preLockGoods = append(preLockGoods, p.GoodsID)
	}

	goodsInfoList, err = e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
		ActiveID: req.BoxInfo.ActiveID,
		IDs:      preLockGoods,
	})
	if err != nil {
		log.Ctx(ctx).Error("selectRandomItemProduce GetGoodsInfoByGoodsIds err: %v", err)
		return err
	}
	goodsInfoMap = goodsInfoList.GetIDMap()

	for _, i := range preLockRecord {
		boxSlot, atoiErr := strconv.Atoi(i.BoxSlot)
		if atoiErr != nil {
			log.Ctx(ctx).Error("selectRandomItemProduce strconv.Atoi err: %v", atoiErr)
			continue
		}
		req.Slot = sliceUtil.RemoveValue(req.Slot, uint32(boxSlot)) // 从当前选择的所有槽位中去除预产出锁定的槽位

		goodsInfo, ok := goodsInfoMap[i.GoodsID]
		if !ok {
			log.Ctx(ctx).Warn("selectRandomItemProduce goodsInfoMap not found goodsId: %v", i.GoodsID)
			continue
		}

		preLockGoodsMap[slot(boxSlot)] = goodsId(i.GoodsID)

		detail := orderDetailDao.NewBoxTradeDetailInfo(
			req.OrderInfo,
			req.BoxInfo,
			goodsInfo,
			active,
			fmt.Sprintf("%d 位置预产出锁定", boxSlot),
			uint32(boxSlot),
		)
		result.OrderInfo = req.OrderInfo
		resItem := &ActionResultItem{
			BoxId:           req.BoxId,
			GoodsInfo:       goodsInfo,
			TradeDetailInfo: detail,
		}
		slot, _ := strconv.Atoi(i.BoxSlot)
		resItem.Slot = uint32(slot)
		result.List = append(result.List, resItem)

		delete(goods, i.GoodsID) // 删除已经出过的商品
	}

	return
}

// 处理只有一个商品的情况
func (e *Entry) handleOnlyOneGoods(
	ctx *gin.Context,
	req *selectGoodsReq,
	goods goodsStockMap,
	active *activeDao.Model,
	result *ActionResult,
) (err error) {
	var (
		gInfo *box_goods.Model
	)
	for _, stock := range goods {
		gInfo = stock.Model
	}
	detail := orderDetailDao.NewBoxTradeDetailInfo(
		req.OrderInfo,
		req.BoxInfo,
		gInfo,
		active,
		fmt.Sprintf("%d 位置", req.Slot[0]),
		req.Slot[0],
	)
	result.OrderInfo = req.OrderInfo
	result.List = append(result.List, &ActionResultItem{
		BoxId:           req.BoxId,
		Slot:            req.Slot[0],
		GoodsInfo:       gInfo,
		TradeDetailInfo: detail,
	})
	return
}

// 道具卡使用
type ItemCardStrategy struct {
	entry *Entry
}

func (p *ItemCardStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (result *ActionResult, err error) {
	result = &ActionResult{}
	result.List = make([]*ActionResultItem, 0)
	var (
		actionType = 0
	)

	// 道具卡使用
	// 道具卡不同种类 根据goods库存操作并更新box中各个slot
	details, err := p.entry.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
		BoxID: b.BoxInfo.ID,
		Slot:  b.Slot[0],
	})
	if err != nil {
		return nil, err
	}
	for _, d := range details {
		if d.ActionType == box_action_detail.ACTION_TYPE_1 {

			return nil, errors.New("该位置已售出")
		}
		if d.ActionType == box_action_detail.ACTION_TYPE_3 {

			return nil, errors.New("该位置已使用透视卡")
		}
	}

	switch i.ICType {
	case itemCardDao.CardType_yao:
	// 摇一摇
	case itemCardDao.CardType_tip:
	// 提示卡
	case itemCardDao.CardType_tou:
	// 透视卡
	default:
		return nil, errors.New("icType error")
	}

	sgReq := &selectGoodsReq{
		BoxId: b.BoxInfo.ID,
		Slot:  b.Slot,
		//OrderInfo: b.OrderInfo,
		BoxInfo: b.BoxInfo,
	}

	if i.ICType == itemCardDao.CardType_tou {
		sgReq.ActionType = box_action_detail.ACTION_TYPE_3
	} else {
		sgReq.ActionType = box_action_detail.ACTION_TYPE_2
	}

	drawWindow, err := p.entry.getBoxRulesAndDrawWindow(ctx, b, sgReq)
	if err != nil {
		// handle error
	}

	result, actionType, err = p.entry.selectRandomItemProduce(ctx, sgReq, b.GoodsStockMap, b.BoxRecord, b.Active, drawWindow)

	_ = actionType
	result.ActionType = sgReq.ActionType // 道具卡情况下为预产出或预产出锁定
	return
}

type ActionResult struct {
	ActionType uint32
	OrderInfo  *orderDao.Model
	List       []*ActionResultItem
}

// 直接购买策略
type DirectPurchaseStrategy struct {
	entry *Entry
}

func (p *DirectPurchaseStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error) {
	res = &ActionResult{}
	res.List = make([]*ActionResultItem, 0)
	res.OrderInfo = b.OrderInfo

	if i != nil {
		return nil, errors.New("direct purchase not support item card")
	}

	// 验证SKU模式的参数（SKU为最小购买粒度）
	if b.SkuID == 0 {
		return nil, errors.New("sku_id is required for direct purchase")
	}

	if b.Quantity == 0 {
		return nil, errors.New("quantity must be greater than 0")
	}

	// 直接查询SKU信息（不使用box_goods表）
	skuModel, err := p.entry.SkuRepo.FetchByID(ctx, b.SkuID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPurchaseStrategy fetch SKU error")
		return nil, fmt.Errorf("failed to find SKU: %w", err)
	}

	if skuModel.ID == 0 {
		return nil, errors.New("SKU not found")
	}

	// 检查SKU状态是否可订购
	if !skuModel.JudgeCanOrdered() {
		return nil, errors.New("SKU is not available for purchase")
	}

	// 查询对应的SPU信息
	spuModel, err := p.entry.SpuRepo.FetchByID(ctx, skuModel.SpuId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPurchaseStrategy fetch SPU error")
		return nil, fmt.Errorf("failed to find SPU: %w", err)
	}

	if spuModel.ID == 0 {
		return nil, errors.New("SPU not found")
	}

	// 检查SPU状态是否可订购
	if !spuModel.JudgeCanOrdered() {
		return nil, errors.New("SPU is not available for purchase")
	}

	// 检查SKU库存是否足够（使用SKU表的可用库存）
	availableStock := skuModel.GetUsableNum()
	if availableStock < b.Quantity {
		return nil, fmt.Errorf("insufficient stock: required %d, available %d", b.Quantity, availableStock)
	}

	// 扣减SKU库存 - 先锁库存，支付成功后再实际扣减
	// 这里我们先增加锁定库存数量
	lockUpdates := map[string]interface{}{
		"lock_num": skuModel.LockNum + b.Quantity,
	}

	if err := p.entry.SkuRepo.UpdateMapByIDWithTx(ctx, tx, b.SkuID, lockUpdates); err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPurchaseStrategy lock stock error")
		return nil, fmt.Errorf("failed to lock stock: %w", err)
	}

	// 创建交易详情 - 直接购买模式：一个SKU+数量组合创建一条trade记录
	detail := &orderDetailDao.Model{
		OrderID:        b.OrderID,
		ActiveID:       b.ActiveID,
		BoxID:          b.BoxID,
		BoxNo:          b.BoxNo,
		BoxSlot:        "", // 直接购买无槽位概念，设为空字符串
		GoodsID:        0,  // 直购模式不使用box_goods，设为0
		GoodsLevel:     "", // 直购模式不使用级别概念
		UserID:         b.UserID,
		SpuID:          spuModel.ID,
		SkuID:          skuModel.ID,
		GoodsName:      skuModel.Title, // 使用SKU的标题
		ActiveTitle:    b.ActiveTitle,
		TradeStatus:    1, // 已支付
		DeliveryStatus: 1, // 待发货
		Remark:         fmt.Sprintf("直接购买 - SKU: %s, 数量: %d", skuModel.Code, b.Quantity),
		ActiveType:     b.ActiveType,
		Quantity:       b.Quantity, // 使用新的quantity字段存储数量
	}

	// 构建结果项
	resItem := &ActionResultItem{
		BoxId:           b.BoxID,
		Slot:            0,   // 直接购买无槽位概念，设为0
		GoodsInfo:       nil, // 直购模式不使用box_goods，设为nil
		TradeDetailInfo: detail,
		ActionDetail: &box_action_detail.Model{
			UserID:     b.UserID,
			ActiveID:   b.ActiveID,
			BoxID:      b.BoxID,
			BoxSlot:    "",                              // 直接购买无槽位概念，设为空字符串
			ActionType: box_action_detail.ACTION_TYPE_1, // 明确产出
			GoodsID:    0,                               // 直购模式不使用box_goods ID，设为0
		},
	}

	res.List = append(res.List, resItem)
	res.ActionType = uint32(box_action_detail.ACTION_TYPE_1) // 直接购买为明确产出
	res.OrderInfo = b.OrderInfo

	log.Ctx(ctx).Info("DirectPurchaseStrategy executed successfully: SkuID=%d, SpuID=%d, Quantity=%d",
		b.SkuID, spuModel.ID, b.Quantity)

	return res, nil
}

func (e *Entry) getBoxRulesAndDrawWindow(ctx *gin.Context, b *BoxModel, sgReq *selectGoodsReq) (*Window, error) {
	var (
		err error

		drawWindow    = NewWindow()
		drawEventList = make([]DrawEvent, 0)
	)

	detailInfo, err := e.TradeRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		UserID:   sgReq.UserID,
		ActiveID: b.Active.ID,
		Limit:    int(100),
		Sort: []clause.OrderByColumn{
			{
				Column: clause.Column{
					Name: "id",
				},
				Desc: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	drawWindow.SetLimit(100) // 设置窗口大小

	for _, d := range detailInfo {
		tmp := DrawEvent{
			UserID:  d.UserID,
			GoodsID: d.GoodsID,
		}
		drawEventList = append(drawEventList, tmp)
	}
	// 反转drawEventList TODO: youhua
	for i, j := 0, len(drawEventList)-1; i < j; i, j = i+1, j-1 {
		drawEventList[i], drawEventList[j] = drawEventList[j], drawEventList[i]
	}

	drawWindow.addHistoricalDrawEvents(drawEventList) // 设置历史抽盒记录

	return drawWindow, nil
}

// 处理常规抽盒，适用规则
func (e *Entry) selectRandomGoodsUsingRules(
	ctx *gin.Context,
	req *selectGoodsReq,
	goods goodsStockMap,
	active *activeDao.Model,
	drawWindow *Window,
	result *ActionResult,
) (err error) {
	// --------------------------------- 随机选择商品 ---------------------------------
	var (
		goodsMap = make(map[uint64]*box_goods.Model)

		goodRes *goodsStock
	)

	gSelect := &goodsSelect{
		BoxId: req.BoxId,
		//GoodsStockMap: goods,
		MustRandomIn: make([]uint64, 0),
		DrawWindow:   drawWindow,
	}

	// 获取库存
	goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
		ActiveID: req.BoxInfo.ActiveID,
	})
	if err != nil {
		log.Ctx(ctx).Error("selectRandomItemProduce GetGoodsInfoByActiveIdAtRead err: %v", err)
		return err
	}

	goodsMap = goodsList.GetIDMap()

	gsm := make(goodsStockMap)
	for goodsId, info := range goodsMap {
		gsm[goodsId] = &goodsStock{
			Model: info,
		}
	}

	boxAction, err := e.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
		BoxID: req.BoxId,
	})
	if err != nil {
		log.Ctx(ctx).Error("BoxDetail BoxActionDetailByBox err: %v", err)
		return
	}
	tipCount := make(map[uint32]uint32)
	slotNotG := make(map[uint32][]goodsId)
	for _, boxActionDetail := range boxAction {
		if boxActionDetail.ActionType == box_action_detail.ACTION_TYPE_2 {
			slot, _ := strconv.Atoi(boxActionDetail.BoxSlot)
			tipCount[uint32(slot)]++
			if _, ok := slotNotG[uint32(slot)]; !ok {
				slotNotG[uint32(slot)] = make([]goodsId, 0)
			}
			slotNotG[uint32(slot)] = append(slotNotG[uint32(slot)], goodsId(boxActionDetail.GoodsID))
		}
	}

	for _, i := range req.Slot {
		tmpGoodsStock := make(goodsStockMap)
		for _, stock := range goods {
			tmpGoodsStock[stock.ID] = stock
		}
		gSelect.GoodsStockMap = tmpGoodsStock

		if notGs, ok := slotNotG[i]; ok {
			for _, g := range notGs {
				if _, ok := gSelect.GoodsStockMap[uint64(g)]; ok {
					delete(gSelect.GoodsStockMap, uint64(g))
				}
			}
		}

		goodRes, err = e.getRandomItemUseRules(ctx, gSelect)
		if err != nil {
			log.Ctx(ctx).Error("selectRandomItemProduce getRandomItemUseRules err: %v", err)
			return err
		}
		if goodRes == nil {
			log.Ctx(ctx).Error("selectRandomItemProduce getRandomItemUseRules goodRes nil")
			return errors.New("goodRes nil")
		}

		if req.OrderInfo == nil { // 道具卡预产出/锁定
			r := &ActionResultItem{
				BoxId:     req.BoxId,
				Slot:      i,
				GoodsInfo: goodRes.Model,
			}
			if req.ActionType == box_action_detail.ACTION_TYPE_2 { // 道具卡预产出, 无需产出
				// 去除假产出的商品，从剩下的中随机一个
				tmpGoods := gSelect.GoodsStockMap
				delete(tmpGoods, goodRes.ID)
				notG, err := e.randomNotGoods(ctx, req.BoxId, gsm, goodRes.ID, slotNotG[i], tipCount[i], req.BoxInfo.Cap)
				if err != nil {
					log.Ctx(ctx).Error("selectRandomItemProduce randomNotGoods err: %v", err)
					for _, ng := range tmpGoods {
						r.NotGoodsInfo = ng.Model
						break
					}
				} else {
					r.NotGoodsInfo = notG.Model
				}
			}
			result.List = append(result.List, r)

			break
		}

		detail := orderDetailDao.NewBoxTradeDetailInfo(
			req.OrderInfo,
			req.BoxInfo,
			goodRes.Model,
			active,
			fmt.Sprintf("%d 位置明确产出", i),
			i,
		)

		// ----   抽取后的更新window   ----
		delete(goods, goodRes.ID)
		tmp := DrawEvent{
			UserID:  req.OrderInfo.UserID,
			GoodsID: goodRes.ID,
		}
		gSelect.DrawWindow.addHistoricalDrawEvents([]DrawEvent{tmp})
		gSelect.MustRandomIn = make([]uint64, 0)

		if goodRes.LevelName != box_level.BoxLevelNormal {

		}

		// -------------------------------

		result.List = append(result.List, &ActionResultItem{
			BoxId:           req.BoxId,
			Slot:            i,
			GoodsInfo:       goodRes.Model,
			TradeDetailInfo: detail,
		})
	}

	log.Ctx(ctx).Info("REQ=%s DEBUG=%d ----------------------END------------------")

	return
}

func (e *Entry) randomNotGoods(
	ctx *gin.Context,
	boxId uint64,
	gsm goodsStockMap, resId uint64, notG []goodsId, tipCount uint32, boxCap uint32) (goodsRes *goodsStock, err error) {
	var totalWeight = uint32(0)
	tmpGoodsStockMap := make(goodsStockMap)

	for k, stock := range gsm {
		tmpGoodsStockMap[k] = stock
	}

	delete(tmpGoodsStockMap, resId)

	// notG 需要排除的goods_id
	// gsm 所有的库存
	// tmpGoodsStockMap 排除notG后的库存

	for _, i := range notG {
		i := uint64(i)
		if _, ok := tmpGoodsStockMap[i]; ok {
			delete(tmpGoodsStockMap, i)
		}
	}

	ignoreHiddenWeight := true
	if tipCount*2 > boxCap { // 提示次数大于商品的1/2，则计算隐藏商品的权重
		ignoreHiddenWeight = false
	}
	log.Ctx(ctx).Info("REQ=%s DEBUG=%d tipCard randomExcludeGoods tipCount %d boxCap %d ignoreHiddenWeight: %v")

	for _, s := range tmpGoodsStockMap {
		// 权重逻辑调整：与主抽取流程保持一致，使用"剩余库存"作为权重；
		// 若剩余库存为 0，为避免全部权重为 0，将权重设为 1
		if ignoreHiddenWeight && s.LevelName != box_level.BoxLevelNormal {
			// 隐藏款权重为 0
			s.SetWeight(0)
			continue
		}

		cur := s.GetCurStock()
		if cur == 0 {
			// 无库存，权重为0，跳过
			s.SetWeight(0)
			continue
		}

		totalWeight += cur
		s.SetWeight(cur)
	}

	log.Ctx(ctx).Info("REQ=%s DEBUG=%d tipCard randomExcludeGoods: ")
	for i, stock := range tmpGoodsStockMap {
		log.Ctx(ctx).WithFields(logrus.Fields{
			"goodsId": i,
			"stock":   &stock,
		}).Info("REQ=%s DEBUG=%d tipCard randomExcludeGoods stock: goodsId %d, weight: %+v")
	}

	if totalWeight <= 0 {
		// 所有候选商品均无库存或被排除
		err = errors.New("all items are out of stock after exclusion")
		log.Ctx(ctx).WithError(err).Warn("randomNotGoods: no available goods")
		return nil, err
	}

	// 使用安全的随机数生成器 - 修复边界问题
	randomNum := safe_random.Intn(int(totalWeight)) // 范围 [0, totalWeight-1]
	log.Ctx(ctx).Info("REQ=%s DEBUG=%d tipCard randomExcludeGoods randomNum: %d totalWeight: %d", randomNum, totalWeight)

	var cumulativeWeight uint32
	for _, g := range tmpGoodsStockMap {
		log.Ctx(ctx).Info("REQ=%s DEBUG=%d cumulativeWeight %d goods %s == weight %d")
		cumulativeWeight += g.Weight
		log.Ctx(ctx).Info("REQ=%s DEBUG=%d cumulativeWeight %d")
		if randomNum < int(cumulativeWeight) { // 使用 < 而不是 <=
			log.Ctx(ctx).Info("REQ=%s DEBUG=%d getRandomItemUseRules goods %s == weight %d")
			return g, nil
		}
	}

	return
}

func (e *Entry) getRandomItemUseRules(ctx *gin.Context, gSelect *goodsSelect) (goodsRes *goodsStock, err error) {
	var (
		totalWeight = uint32(0)
	)

	for _, stock := range gSelect.GoodsStockMap {
		totalWeight += stock.Weight
	}

	tmpGoodsStockMap := make(goodsStockMap)
	if len(gSelect.MustRandomIn) > 0 { // 需要走必中逻辑
		tmpTotalWeight := uint32(0)
		for _, i := range gSelect.MustRandomIn {
			if v, ok := gSelect.GoodsStockMap[i]; ok {
				// 只选用必中的商品
				tmpGoodsStockMap[i] = v
				tmpTotalWeight += v.GetWeight()
			}
		}
		if tmpTotalWeight > 0 {
			totalWeight = tmpTotalWeight
		}
	}
	if len(tmpGoodsStockMap) <= 0 {
		tmpGoodsStockMap = gSelect.GoodsStockMap
	}

	log.Ctx(ctx).Info("REQ=%s DEBUG=%d Operate common tmpGoodsStockMap: ")
	for i, stock := range tmpGoodsStockMap {
		log.Ctx(ctx).WithFields(logrus.Fields{
			"goodsId": i,
			"stock":   &stock,
		}).
			Info("REQ=%s Operate common stock: goodsId %d, weight: %+v")
	}

	if totalWeight <= 0 {
		log.Ctx(ctx).Error("getRandomItemUseRules totalWeight <= 0")
		// 取出其中最大数
		for _, stock := range tmpGoodsStockMap {
			stock.Weight = stock.GetOverStockWeight()
			totalWeight += stock.GetOverStockWeight()
			log.Ctx(ctx).Info("REQ=%s The Final Operate common stock: goodsId %d, weight: %+v")
		}
	}

	// 使用安全的随机数生成器 - 修复边界问题
	randomNum := safe_random.Intn(int(totalWeight)) // 范围 [0, totalWeight-1]
	log.Ctx(ctx).Info("REQ=%s DEBUG=%d Operate common randomNum: %d totalWeight: %d", randomNum, totalWeight)

	var cumulativeWeight uint32
	for _, g := range tmpGoodsStockMap {
		log.Ctx(ctx).Info("REQ=%s DEBUG=%d cumulativeWeight %d goods %s == weight %d")
		cumulativeWeight += g.Weight
		log.Ctx(ctx).Info("REQ=%s DEBUG=%d cumulativeWeight %d")
		if randomNum < int(cumulativeWeight) { // 使用 < 而不是 <=
			log.Ctx(ctx).Info("REQ=%s DEBUG=%d getRandomItemUseRules goods %s == weight %d")
			return g, nil
		}
	}

	return nil, errors.New("getRandomItemUseRules error")
}
