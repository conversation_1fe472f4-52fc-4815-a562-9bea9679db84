package box

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	activeDao "blind_box/app/dao/activity"
	actionDetailDao "blind_box/app/dao/box/box_action_detail"
	"blind_box/app/dao/box/box_goods"
	boxGoodsDao "blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_record"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	tradeDetailDao "blind_box/app/dao/order/order_detail"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	strUtil "blind_box/pkg/util/str_util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ActionResultItem 操作结果 在该箱子上的某个位置产出了商品，生成了交易明细记录
// BoxId 箱子ID
// Slot 位置
// GoodsInfo 商品信息
// TradeDetailInfo 交易明细
type ActionResultItem struct {
	BoxId           uint64                 // 箱子ID
	Slot            uint32                 // 箱子槽位
	GoodsInfo       *boxGoodsDao.Model     // 商品信息
	TradeDetailInfo *tradeDetailDao.Model  // 交易明细
	NotGoodsInfo    *boxGoodsDao.Model     // 非商品信息
	ActionDetail    *actionDetailDao.Model // 操作明细
}

/*

- 活动信息 activeInfo
- 订单信息 orderInfo
- 盲盒信息 boxRecordInfo
- 该箱槽位信息 slots
- 商品信息 goodsInfo


主要调用方式有两种
- 使用道具卡
- 直接购买
*/

type BoxSelectReq struct {
	ActiveInfo *activeDao.Model // 活动信息
	OrderInfo  *orderDao.Model  // 订单信息
	Slot       []uint32
	// 直接购买相关字段 - SKU为最小购买粒度
	PurchaseType uint32 // 购买类型：1-盲盒随机，3-直接购买
	SkuID        uint64 // SKU商品ID（直接购买模式使用，最小购买粒度）
	Quantity     uint32 // 购买数量（直接购买模式使用）
}

type goodsStockMap map[uint64]*goodsStock

func (s *goodsStock) GetWeight() uint32 {
	return s.Weight
}

func (s *goodsStock) SetDefaultWeight(w uint32) {
	s.Weight = w
}

func (s *goodsStock) SetWeight(w uint32) {
	//if s.CanChange() {
	//	return
	//}
	s.Weight = w
	//s.setChange()
}

type goodsStock struct {
	*box_goods.Model
	Weight uint32
	//Changed bool
}

type BoxSelectRes struct {
	NoUsedTotal uint32                           // 未使用的总数
	IsLast      bool                             // 是否最后一个
	TradeInfos  orderDetailDao.ModelList         // 交易信息列表
	GoodsMap    map[uint64]*orderDetailDao.Model // 商品信息map
}

type slot uint32    // 槽位ID
type goodsId uint64 // 商品ID

type selectGoodsReq struct {
	UserID     uint64
	ActionType uint32   `json:"actionType" form:"actionType"`
	BoxId      uint64   `json:"boxId" form:"boxId"`
	Slot       []uint32 `json:"slot" form:"slot"` // 选择的槽位

	OrderInfo *orderDao.Model
	BoxInfo   *box_record.Model
}

func (e *Entry) payBox(ctx *gin.Context, tx *gorm.DB, req *BoxSelectReq) (res *BoxSelectRes, err error) {
	res = &BoxSelectRes{}
	res.TradeInfos = make(orderDetailDao.ModelList, 0)
	res.GoodsMap = make(map[uint64]*orderDetailDao.Model)

	var (
		boxRecord    = &boxRecordInfo{}
		gsm          = make(goodsStockMap)
		tradeDetails = make(orderDetailDao.ModelList, 0)
	)

	boxInfo, err := e.BoxRecordRepo.FetchByID(ctx, req.OrderInfo.BoxID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("error box record")
		return nil, err
	}

	if !checkAvaSlot(boxInfo.AvaSlot, req.Slot) {
		log.Ctx(ctx).Warn("error box slot selected")
		return nil, errors.New("位置选择错误")
	}

	goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
		ActiveID: req.ActiveInfo.ID,
	})
	if err != nil {
		return nil, err
	}

	for _, goods := range goodsList {
		if goods.ID == 0 {
			continue
		}
		goodsInfo := &goodsStock{
			Model: goods,
		}
		gsm[goods.ID] = goodsInfo
	}

	boxRecord = newBoxRecordInfo(boxInfo)
	log.Ctx(ctx).WithFields(logrus.Fields{
		"DEBUG": req.OrderInfo.BoxID,
		"BOX":   &boxRecord,
	}).Info("box record")

	// 根据购买类型选择策略
	var strategyType int
	if req.PurchaseType == 3 {
		strategyType = STRATEGY_DIRECT
	} else {
		strategyType = STRATEGY_PURCHASE
	}

	purchaseInfo := e.StrategySelect(strategyType)
	if purchaseInfo == nil {
		log.Ctx(ctx).Warn("error purchase strategy")
		return nil, ecode.SystemErr
	}

	result, err := purchaseInfo.Operate(ctx, tx, &BoxModel{
		Active:        req.ActiveInfo,
		OrderInfo:     req.OrderInfo,
		BoxInfo:       boxInfo,
		Slot:          req.Slot,
		GoodsStockMap: gsm,
		BoxRecord:     boxRecord,
		SkuID:         req.SkuID,    // SKU商品ID（最小购买粒度）
		Quantity:      req.Quantity, // 购买数量
		UserID:        req.OrderInfo.UserID,
		ActiveID:      req.ActiveInfo.ID,
		BoxID:         req.OrderInfo.BoxID,
		OrderID:       req.OrderInfo.ID,
		ActiveTitle:   req.ActiveInfo.Title,
		ActiveType:    req.ActiveInfo.ActType,
	}, nil)
	if err != nil {
		log.Ctx(ctx).WithFields(logrus.Fields{
			"DEBUG": req.OrderInfo.BoxID,
			"BOX":   &boxRecord,
		}).WithError(err).Error("error purchase")
		return nil, ecode.SystemErr
	}
	if len(result.List) <= 0 {
		return nil, errors.New("pay results error")
	}

	for _, item := range result.List {
		if _, ok := boxRecord.SoldSlot[item.Slot]; !ok {
			boxRecord.SoldSlot[item.Slot] = struct{}{}
		}
		if _, ok := boxRecord.AvaSlot[item.Slot]; ok {
			delete(boxRecord.AvaSlot, item.Slot)
		}
		tradeDetails = append(tradeDetails, item.TradeDetailInfo)
	}

	res.TradeInfos = tradeDetails

	// 箱子内槽位情况
	boxUpdates := map[string]interface{}{
		"ava_slot":      strUtil.UintMapJoin(boxRecord.AvaSlot, ","),
		"sold_slot":     strUtil.UintMapJoin(boxRecord.SoldSlot, ","),
		"len":           len(boxRecord.AvaSlot),
		"cap":           boxRecord.Cap,
		"pre_lock_slot": strUtil.UintMapJoin(boxRecord.PreLockSlot, ","),
		"lock_slot":     strUtil.UintMapJoin(boxRecord.LockSlot, ","),
	}

	err = e.BoxRecordRepo.UpdateMapByIDWithTx(ctx, tx, boxInfo.ID, boxUpdates)
	if err != nil {
		log.Ctx(ctx).
			WithError(err).
			Error("error update box record")
		return nil, err
	}

	return res, nil
}

// payDirect 专门处理直购模式的支付逻辑
func (e *Entry) payDirect(ctx *gin.Context, tx *gorm.DB, req *BoxSelectReq) (res *BoxSelectRes, err error) {
	res = &BoxSelectRes{}
	res.TradeInfos = make(orderDetailDao.ModelList, 0)
	res.GoodsMap = make(map[uint64]*orderDetailDao.Model)

	// 验证直购参数
	if req.SkuID == 0 {
		return nil, errors.New("sku_id is required for direct purchase")
	}
	if req.Quantity == 0 {
		return nil, errors.New("quantity must be greater than 0")
	}

	// 直接查询SKU信息
	skuModel, err := e.SkuRepo.FetchByID(ctx, req.SkuID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("payDirect fetch SKU error")
		return nil, fmt.Errorf("failed to find SKU: %w", err)
	}
	if skuModel.ID == 0 {
		return nil, errors.New("SKU not found")
	}
	if !skuModel.JudgeCanOrdered() {
		return nil, errors.New("SKU is not available for purchase")
	}

	// 查询对应的SPU信息
	spuModel, err := e.SpuRepo.FetchByID(ctx, skuModel.SpuId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("payDirect fetch SPU error")
		return nil, fmt.Errorf("failed to find SPU: %w", err)
	}
	if spuModel.ID == 0 {
		return nil, errors.New("SPU not found")
	}
	if !spuModel.JudgeCanOrdered() {
		return nil, errors.New("SPU is not available for purchase")
	}

	// 检查库存
	availableStock := skuModel.GetUsableNum()
	if availableStock < req.Quantity {
		return nil, fmt.Errorf("insufficient stock: required %d, available %d", req.Quantity, availableStock)
	}

	// 锁定SKU库存
	lockUpdates := map[string]interface{}{
		"lock_num": skuModel.LockNum + req.Quantity,
	}
	if err := e.SkuRepo.UpdateMapByID(ctx, req.SkuID, lockUpdates); err != nil {
		log.Ctx(ctx).WithError(err).Error("payDirect lock stock error")
		return nil, fmt.Errorf("failed to lock stock: %w", err)
	}

	// 创建交易详情
	tradeDetail := &orderDetailDao.Model{
		OrderID:        req.OrderInfo.ID,
		ActiveID:       req.ActiveInfo.ID,
		BoxID:          req.OrderInfo.BoxID,
		BoxNo:          "",
		BoxSlot:        fmt.Sprintf("DIRECT_%d", req.Quantity),
		GoodsID:        0, // 直购模式不使用box_goods
		GoodsLevel:     "",
		UserID:         req.OrderInfo.UserID,
		SpuID:          spuModel.ID,
		SkuID:          skuModel.ID,
		GoodsName:      skuModel.Title,
		ActiveTitle:    req.ActiveInfo.Title,
		TradeStatus:    1, // 已支付
		DeliveryStatus: 1, // 待发货
		Remark:         fmt.Sprintf("直接购买 - SKU: %s, 数量: %d", skuModel.Code, req.Quantity),
		ActiveType:     req.ActiveInfo.ActType,
	}

	res.TradeInfos = append(res.TradeInfos, tradeDetail)
	res.GoodsMap[req.SkuID] = tradeDetail

	log.Ctx(ctx).Info("payDirect executed successfully: SkuID=%d, SpuID=%d, Quantity=%d",
		req.SkuID, spuModel.ID, req.Quantity)

	return res, nil
}

type boxRecordInfo struct {
	BoxNo       string
	BoxId       uint64
	Cap         uint32
	Len         uint32
	AvaSlot     map[uint32]struct{}
	SoldSlot    map[uint32]struct{}
	PreLockSlot map[uint32]struct{}
	LockSlot    map[uint32]struct{}

	Weight int
}

// 初始化箱子信息
func newBoxRecordInfo(box *box_record.Model) *boxRecordInfo {
	var (
		avaSlot     = make(map[uint32]struct{})
		soldSlot    = make(map[uint32]struct{})
		preLockSlot = make(map[uint32]struct{})
		lockSlot    = make(map[uint32]struct{})
	)

	fn := func(s string, m map[uint32]struct{}) {
		if s == "" {
			return
		}
		sli := strings.Split(s, ",")
		for _, s := range sli {
			num, err := strconv.Atoi(s)
			if err != nil {
				continue
			}
			m[uint32(num)] = struct{}{}
		}
	}

	fn(box.AvaSlot, avaSlot)
	fn(box.SoldSlot, soldSlot)
	fn(box.PreLockSlot, preLockSlot)
	fn(box.LockSlot, lockSlot)

	return &boxRecordInfo{
		BoxNo:       box.BoxNo,
		BoxId:       box.ID,
		Cap:         box.Cap,
		Len:         box.Len,
		AvaSlot:     avaSlot,
		SoldSlot:    soldSlot,
		PreLockSlot: preLockSlot,
		LockSlot:    lockSlot,
	}
}
