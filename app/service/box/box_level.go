package box

import (
	levelDao "blind_box/app/dao/box/box_level"
	boxDto "blind_box/app/dto/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
)

func (e *Entry) AdminBoxLevelAdd(ctx *gin.Context, req *boxDto.AdminBoxLevelAddReq) (res *boxDto.AdminBoxLevelAddResp, err error) {
	res = &boxDto.AdminBoxLevelAddResp{}
	datas, err := e.BoxLevelRepo.FindByFilter(ctx, &levelDao.Filter{
		LevelName: req.LevelName,
	})
	if err != nil {
		return nil, err
	}
	if len(datas) > 0 {
		return nil, ecode.BoxLevelNameErr
	}

	model := &levelDao.Model{
		LevelName: req.LevelName,
	}

	err = e.BoxLevelRepo.CreateOrUpdate(ctx, model)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateOrUpdate error")
		return nil, err
	}
	res.ID = model.ID
	return res, nil
}

func (e *Entry) AdminBoxLevelUpdate(ctx *gin.Context, req *boxDto.AdminBoxLevelUpdateReq) (res *boxDto.AdminBoxLevelUpdateResp, err error) {
	res = &boxDto.AdminBoxLevelUpdateResp{}
	res.ID = req.ID

	data, err := e.BoxLevelRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByID error")
		return nil, err
	}
	if data == nil {
		return nil, ecode.BoxLevelNotExistErr
	}

	updateMap := map[string]interface{}{
		"level_name": req.LevelName,
	}

	err = e.BoxLevelRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByID error")
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminBoxLevelList(ctx *gin.Context, req *boxDto.AdminBoxLevelListReq) (res *boxDto.AdminBoxLevelListResp, err error) {
	res = &boxDto.AdminBoxLevelListResp{}
	res.List = make([]boxDto.AdminBoxLevelListItem, 0)

	list, err := e.BoxLevelRepo.FindByFilter(ctx, &levelDao.Filter{})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return nil, err
	}

	for _, val := range list {
		res.List = append(res.List, boxDto.AdminBoxLevelListItem{
			ID:        val.ID,
			LevelName: val.LevelName,
		})
	}

	return res, nil
}
