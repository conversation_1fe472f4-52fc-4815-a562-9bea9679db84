package box

import (
	"strconv"
	"strings"

	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_must_config"
	boxDto "blind_box/app/dto/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

func (e *Entry) AdminBoxMustConfigBagCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigBagCreateReq) (res *boxDto.AdminBoxMustConfigBagCreateResp, err error) {
	res = &boxDto.AdminBoxMustConfigBagCreateResp{}

	var (
		activeModel = &actDao.Model{}
		mustConfigs = make(box_must_config.ModelList, 0)
		mustConfig  = &box_must_config.Model{}
	)
	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}
	mustConfigs, err = e.BoxMustConfigRepo.FindByFilter(ctx, &box_must_config.Filter{
		ActiveID: req.ActiveID,
		Type:     box_must_config.CONFIG_TYPE_BAG,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FindByFilter")
		return res, ecode.SystemErr
	}
	for _, config := range mustConfigs {
		if config.OutLevel == req.OutLevel {
			err = ecode.BoxActiveMustConfigLevelExistErr
			return
		}
	}
	mustConfig.Type = box_must_config.CONFIG_TYPE_BAG
	mustConfig.ActiveID = req.ActiveID
	mustConfig.OutLevel = req.OutLevel
	mustConfig.LotteryNum = req.LotteryNum

	err = e.BoxMustConfigRepo.CreateOrUpdate(ctx, mustConfig)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate CreateOrUpdate")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminBoxMustConfigUserCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigUserCreateReq) (res *boxDto.AdminBoxMustConfigUserCreateResp, err error) {
	res = &boxDto.AdminBoxMustConfigUserCreateResp{}

	var (
		activeModel = &actDao.Model{}

		notOutGoods = make([]string, 0)
		outGoods    = make([]string, 0)
		mustConfig  = &box_must_config.Model{}
	)
	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}

	for _, goods := range req.NotOutGoods {
		notOutGoods = append(notOutGoods, strconv.FormatUint(goods, 10))
	}
	for _, goods := range req.OutGoods {
		outGoods = append(outGoods, strconv.FormatUint(goods, 10))
	}
	mustConfig.Type = box_must_config.CONFIG_TYPE_USER
	mustConfig.ActiveID = req.ActiveID
	mustConfig.LotteryNum = req.LotteryNum
	mustConfig.NotOutGoods = strings.Join(notOutGoods, ",")
	mustConfig.OutGoods = strings.Join(outGoods, ",")

	err = e.BoxMustConfigRepo.CreateOrUpdate(ctx, mustConfig)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigUserCreate CreateOrUpdate")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminBoxMustConfigActiveCreate(ctx *gin.Context, req *boxDto.AdminBoxMustConfigActiveCreateReq) (res *boxDto.AdminBoxMustConfigActiveCreateResp, err error) {
	res = &boxDto.AdminBoxMustConfigActiveCreateResp{}

	var (
		activeModel = &actDao.Model{}

		outGoods   = make([]string, 0)
		mustConfig = &box_must_config.Model{}
	)
	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}

	for _, goods := range req.OutGoods {
		outGoods = append(outGoods, strconv.FormatUint(goods, 10))
	}
	mustConfig.Type = box_must_config.CONFIG_TYPE_ACTIVE
	mustConfig.ActiveID = req.ActiveID
	mustConfig.LotteryNum = req.LotteryNum
	mustConfig.OutGoods = strings.Join(outGoods, ",")
	mustConfig.GoodsNum = req.GoodsNum

	err = e.BoxMustConfigRepo.CreateOrUpdate(ctx, mustConfig)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigUserCreate CreateOrUpdate")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminBoxMustConfigInfo(ctx *gin.Context, req *boxDto.AdminBoxMustConfigInfoReq) (res *boxDto.AdminBoxMustConfigInfoResp, err error) {
	res = &boxDto.AdminBoxMustConfigInfoResp{}
	res.Bag = make([]*boxDto.AdminBoxMustConfigBag, 0)
	res.User = make([]*boxDto.AdminBoxMustConfigUser, 0)
	res.Active = make([]*boxDto.AdminBoxMustConfigActive, 0)

	var (
		activeModel = &actDao.Model{}
		mustConfigs = make(box_must_config.ModelList, 0)
		goodsIDs    = make([]uint64, 0)
		goodsMap    = make(map[uint64]*box_goods.Model)
	)
	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}

	mustConfigs, err = e.BoxMustConfigRepo.FindByFilter(ctx, &box_must_config.Filter{
		ActiveID: req.ActiveID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigBagCreate FindByFilter")
		return res, ecode.SystemErr
	}

	for _, config := range mustConfigs {
		switch config.Type {
		case box_must_config.CONFIG_TYPE_BAG:
			res.Bag = append(res.Bag, &boxDto.AdminBoxMustConfigBag{
				ID:         config.ID,
				ActiveID:   config.ActiveID,
				LotteryNum: config.LotteryNum,
				OutLevel:   config.OutLevel,
			})

		case box_must_config.CONFIG_TYPE_USER:
			var (
				notOutGoods    = strings.Split(config.NotOutGoods, ",")
				notOutGoodsIDs = make([]uint64, 0)
				outGoods       = strings.Split(config.OutGoods, ",")
				outGoodsIDs    = make([]uint64, 0)
			)

			for _, a := range notOutGoods {
				goodsID, err := strconv.ParseUint(a, 10, 64)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigInfo ParseUint")
					return res, ecode.SystemErr
				}
				goodsIDs = append(goodsIDs, goodsID)
				notOutGoodsIDs = append(notOutGoodsIDs, goodsID)
			}

			for _, a := range outGoods {
				goodsID, err := strconv.ParseUint(a, 10, 64)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigInfo ParseUint")
					return res, ecode.SystemErr
				}
				goodsIDs = append(goodsIDs, goodsID)
				outGoodsIDs = append(outGoodsIDs, goodsID)
			}

			res.User = append(res.User, &boxDto.AdminBoxMustConfigUser{
				ID:         config.ID,
				ActiveID:   config.ActiveID,
				LotteryNum: config.LotteryNum,
				//NotOutGoods:    notOutGoods,
				NotOutGoodsIDs: notOutGoodsIDs,
				//OutGoods:       outGoods,
				OutGoodsIDs: outGoodsIDs,
			})
		case box_must_config.CONFIG_TYPE_ACTIVE:
			var (
				outGoods    = strings.Split(config.OutGoods, ",")
				outGoodsIDs = make([]uint64, 0)
			)
			for _, a := range outGoods {
				goodsID, err := strconv.ParseUint(a, 10, 64)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigInfo ParseUint")
					return res, ecode.SystemErr
				}
				goodsIDs = append(goodsIDs, goodsID)
				outGoodsIDs = append(outGoodsIDs, goodsID)
			}
			res.Active = append(res.Active, &boxDto.AdminBoxMustConfigActive{
				ID:          config.ID,
				ActiveID:    config.ActiveID,
				LotteryNum:  config.LotteryNum,
				GoodsNum:    config.GoodsNum,
				OutGoodsIDs: outGoodsIDs,
				//OutGoods:   outGoods,
			})

		}
	}

	if len(goodsIDs) > 0 {
		goodsIDs = lo.Uniq(goodsIDs)
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			IDs:      goodsIDs,
			ActiveID: req.ActiveID,
		})
		if err != nil {
			return nil, err
		}
		goodsMap = goodsList.GetIDMap()
	}

	for _, conf := range res.User {
		for _, goodsID := range conf.NotOutGoodsIDs {
			if goodsModel, ok := goodsMap[goodsID]; ok {
				conf.NotOutGoods = append(conf.NotOutGoods, goodsModel.GoodsName)
			}
		}
		for _, goodsID := range conf.OutGoodsIDs {
			if goodsModel, ok := goodsMap[goodsID]; ok {
				conf.OutGoods = append(conf.OutGoods, goodsModel.GoodsName)
			}
		}
	}

	for _, conf := range res.Active {
		for _, goodsID := range conf.OutGoodsIDs {
			if goodsModel, ok := goodsMap[goodsID]; ok {
				conf.OutGoods = append(conf.OutGoods, goodsModel.GoodsName)
			}
		}
	}

	return res, nil
}

func (e *Entry) AdminBoxMustConfigActiveDel(ctx *gin.Context, req *boxDto.AdminBoxMustConfigActiveDelReq) (res *boxDto.AdminBoxMustConfigActiveDelResp, err error) {
	res = &boxDto.AdminBoxMustConfigActiveDelResp{}

	var (
		mustConfig = &box_must_config.Model{}
	)

	mustConfig, err = e.BoxMustConfigRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigActiveDel FetchByID")
		return res, ecode.SystemErr
	}
	if mustConfig == nil {
		err = ecode.EmptyDataErr
		return
	}

	err = e.BoxMustConfigRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBoxMustConfigActiveDel UpdateMapByID")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}
