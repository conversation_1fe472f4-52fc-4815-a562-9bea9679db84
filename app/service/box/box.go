package box

import (
	"errors"
	"fmt"
	"math/rand/v2"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"

	"blind_box/app/api/wechat/order"
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	configDao "blind_box/app/dao/admin/config"
	"blind_box/app/dao/box/box_action_detail"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_goods_stock_log"
	"blind_box/app/dao/box/box_level"
	"blind_box/app/dao/box/box_queue_lock"
	"blind_box/app/dao/box/box_record"
	cardConfDao "blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_day_send_log"
	"blind_box/app/dao/card/card_user"
	common2 "blind_box/app/dao/common"
	couponDao "blind_box/app/dao/coupon"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	skuDao "blind_box/app/dao/goods/sku"
	spuDao "blind_box/app/dao/goods/spu"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	imageDao "blind_box/app/dao/resource/image"
	userDao "blind_box/app/dao/user"
	tideDao "blind_box/app/dao/user/tide"
	boxDto "blind_box/app/dto/box"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/app/service/common"
	cUserSrv "blind_box/app/service/coupon"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"blind_box/pkg/util/decimalUtil"
	"blind_box/pkg/util/sliceUtil"
	strUtil "blind_box/pkg/util/str_util"
	"blind_box/pkg/util/timeUtil"

	redisPkg "github.com/go-redis/redis/v8"

	wechatOrderApi "blind_box/app/api/wechat/order"
	"blind_box/config"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type goodsSelect struct {
	BoxId         uint64
	GoodsStockMap goodsStockMap
	DrawWindow    *Window

	ActiveWindow *Window

	MustRandomIn []uint64 // 一些必中逻辑
}

func (s goodsSelect) PrintMustRandomIn() string {

	var b strings.Builder

	b.WriteString("must random in: ")
	for _, i := range s.MustRandomIn {
		fmt.Fprintf(&b, "%d, ", i)
	}
	//b.WriteString("\n")
	return b.String()
}

func (e *Entry) BoxSearch(ctx *gin.Context, req *boxDto.BoxSearchReq) (res *boxDto.BoxSearchRes, err error) {
	res = &boxDto.BoxSearchRes{}
	res.List = make([]*boxDto.BoxSearchItem, 0)

	var (
		activeList = make(actDao.ModelList, 0)
		total      int64
	)

	total, activeList, err = e.ActiveRepo.DataPageList(ctx, &actDao.Filter{
		Sort: []clause.OrderByColumn{
			{
				Column: clause.Column{
					Name: "sort",
				},
				Desc: false,
			},
			{
				Column: clause.Column{
					Name: "act_status",
				},
				Desc: false,
			},
			{
				Column: clause.Column{
					Name: "id",
				},
				Desc: true,
			},
		},
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxList DataPageList")
		err = ecode.SystemErr
		return nil, err
	}

	res.Count = total

	for _, m := range activeList {

		tmp := &boxDto.BoxSearchItem{
			ID:            m.ID,
			ActiveTitle:   m.Title,
			ActiveStatus:  m.ActStatus,
			OpenTimeStamp: m.StartTime,
			ActivePrice:   m.ActPrice,
			ActiveImage:   m.Image,
			ActiveType:    m.ActType,
		}
		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) BoxList(ctx *gin.Context, req *boxDto.BoxListReq) (res *boxDto.BoxListRes, err error) {
	res = &boxDto.BoxListRes{}
	res.ActiveList = make([]*boxDto.BoxActiveInfo, 0)

	var (
		activeList = make(actDao.ModelList, 0)
		total      int64
	)

	total, activeList, err = e.ActiveRepo.DataPageList(ctx, &actDao.Filter{

		Sort: []clause.OrderByColumn{
			{
				Column: clause.Column{
					Name: "sort",
				},
				Desc: false,
			},
			{
				Column: clause.Column{
					Name: "act_status",
				},
				Desc: false,
			},
			{
				Column: clause.Column{
					Name: "id",
				},
				Desc: true,
			},
		},
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxList DataPageList")
		err = ecode.SystemErr
		return nil, err
	}

	if total > int64(req.Page*req.Limit) {
		res.IsNextPage = true
	}

	res.Count = total

	for _, m := range activeList {

		tmp := &boxDto.BoxActiveInfo{
			ID:            m.ID,
			ActiveTitle:   m.Title,
			ActiveStatus:  m.ActStatus,
			OpenTimeStamp: m.StartTime,
			ActivePrice:   m.ActPrice,
			ActiveImage:   m.Image,
			ActiveType:    m.ActType,
		}
		res.ActiveList = append(res.ActiveList, tmp)
	}

	return res, nil
}

func (e *Entry) BoxActive(ctx *gin.Context, req *boxDto.BoxActiveReq) (res *boxDto.BoxActiveRes, err error) {
	res = &boxDto.BoxActiveRes{}

	var (
		activeModel  = &actDao.Model{}
		activeConf   = &box_active_config.Model{}
		activeImages = make([]*imageDao.Model, 0)

		goodsMap       = make(map[uint64]*box_goods.Model)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)
		eg             errgroup.Group
	)
	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxActive FetchByID")
			err = ecode.SystemErr
			return err
		}
		if activeModel == nil {
			err = ecode.ActNotExistErr
			return err
		}
		return nil
	})

	eg.Go(func() error {
		activeConf, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxActive FetchByActiveID")
			err = ecode.SystemErr
			return err
		}
		if activeConf == nil {
			err = ecode.ActNotExistErr
			return err
		}
		return nil
	})

	eg.Go(func() error {
		activeImages, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			MappingID:   activeModel.ID,
			MappingType: imageDao.MappingActiveDetailImages,
		})
		if err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() error {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ActiveID: req.ActiveID,
		})
		if err != nil {
			return err
		}

		goodsMap = goodsList.GetIDMap()
		spuIDs = goodsList.GetSpuIDs()
		skuIDs = goodsList.GetSkuIDs()

		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			return err
		}

		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	res.ActiveID = activeModel.ID
	res.ActiveTitle = activeModel.Title
	res.ActiveStatus = activeModel.ActStatus
	res.ActivePrice = activeModel.ActPrice
	res.ActiveImage = activeModel.Image
	res.ActiveType = activeModel.ActType
	res.Remark = activeModel.Remark
	res.DeliveryExpect = activeModel.Tips
	res.OpenTimeStamp = activeModel.StartTime
	res.OpenTime = activeModel.GetStartTime()
	res.EndTimeStamp = activeModel.EndTime
	res.EndTime = activeModel.GetEndTime()

	res.LotteryNum = activeConf.LotteryNum

	if len(activeImages) > 0 {
		res.Images = make([]string, 0)
		for _, image := range activeImages {
			res.Images = append(res.Images, helper.GetImageCdnUrl(ctx, image.URL))
		}
	}

	for _, goodsModel := range goodsMap {
		if skuItem, ok := skuMap[goodsModel.SkuID]; ok {
			res.GoodsDetails = append(res.GoodsDetails, &boxDto.BoxGoodsDetailItem{
				ID:          goodsModel.ID,
				GoodsDetail: &skuItem,
				GoodsName:   goodsModel.GoodsName,
				GoodsCover:  goodsModel.GoodsCover,
				Sort:        goodsModel.Sort,
			})
		}
	}

	sortGoodsDetail(res.GoodsDetails)

	return res, nil
}

func (e *Entry) BoxGet(ctx *gin.Context, req *boxDto.BoxGetReq) (res *boxDto.BoxGetRes, err error) {
	res = &boxDto.BoxGetRes{}
	res.Active = &boxDto.BoxActiveRes{}
	res.BoxDetailRes = &boxDto.BoxDetailRes{}
	res.BoxDetailRes.GoodsDetails = make([]*boxDto.BoxGoodsDetailItem, 0)

	var (
		activeRes  = &boxDto.BoxActiveRes{}
		activeConf = &box_active_config.Model{}

		goodsMap       = make(map[uint64]*box_goods.Model)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		eg errgroup.Group
	)

	eg.Go(func() error {
		if activeRes, err = e.BoxActive(ctx, &boxDto.BoxActiveReq{
			ActiveID: req.ActiveID,
		}); err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxGet BoxActive")
			return err
		}
		if activeRes == nil {
			err = ecode.ActNotExistErr
			return err
		}
		res.Active = activeRes
		return nil
	})

	eg.Go(func() error {
		if req.UserID <= 0 {
			return nil
		}
		if e.CommonSrv.JudgeUserSubscribe(ctx, req.UserID, req.ActiveID, uint32(actDao.ActTypeBox)) {
			res.Active.IsSubscribe = dbs.True
		}
		return nil
	})

	eg.Go(func() error {
		activeConf, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxGet FetchByActiveID")
			return err
		}
		if activeConf == nil {
			err = ecode.ActNotExistErr
			return err
		}

		return nil
	})

	eg.Go(func() error {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ActiveID: req.ActiveID,
		})
		if err != nil {
			return err
		}
		goodsMap = goodsList.GetIDMap()
		spuIDs = goodsList.GetSpuIDs()
		skuIDs = goodsList.GetSkuIDs()

		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			return err
		}

		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	res.HeadImage = activeConf.HeadImage
	res.LocationImage = activeConf.LocationImage
	res.BoxImage = activeConf.BoxImage
	res.LevelText = activeConf.LevelText

	res.BoxDetailRes.ActiveID = activeRes.ActiveID
	res.BoxDetailRes.ActiveTitle = activeRes.ActiveTitle
	res.BoxDetailRes.ActiveStatus = activeRes.ActiveStatus

	res.OpenTimeStamp = activeRes.OpenTimeStamp
	res.OpenTime = activeRes.OpenTime
	res.EndTimeStamp = activeRes.EndTimeStamp
	res.EndTime = activeRes.EndTime
	res.BoxDetailRes.ActiveImage = activeRes.ActiveImage
	res.BoxDetailRes.ActivePrice = activeRes.ActivePrice
	res.LotteryNum = activeConf.LotteryNum

	for _, goodsModel := range goodsMap {
		if skuItem, ok := skuMap[goodsModel.SkuID]; ok {
			res.GoodsDetails = append(res.GoodsDetails, &boxDto.BoxGoodsDetailItem{
				ID:          goodsModel.ID,
				GoodsDetail: &skuItem,
				GoodsName:   goodsModel.GoodsName,
				GoodsCover:  goodsModel.GoodsCover,
				Sort:        goodsModel.Sort,
			})
		}
	}

	sortGoodsDetail(res.GoodsDetails)

	if activeRes.ActiveStatus == uint32(actDao.ActStatusSaleout) {
		return
	}

	var (
		soldCnt = genCnt(ctx, goodsMap, activeConf.LotteryNum)
		avaSlot = getAvailablePositions(ctx, activeConf.LotteryNum, soldCnt)
		boxNo   string
		newBox  = e.newBoxRecord(activeRes, &BoxRecordInfo{
			BoxNo:       boxNo,
			Cap:         activeConf.LotteryNum,
			Len:         uint32(len(avaSlot)),
			AvaSlot:     avaSlot,
			SoldSlot:    make([]uint32, 0),
			PreLockSlot: make([]uint32, 0),
			LockSlot:    make([]uint32, 0),
		}, req.UserID)
		totalSharkNum = int64(0)
	)
	boxNo = helper.GetAppNo(helper.AppNoBox)
	newBox.BoxNo = boxNo
	res.BoxNo = boxNo

	err = e.BoxRecordRepo.CreateOrUpdate(ctx, newBox)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxGet CreateOrUpdate")
		return nil, err
	}
	res.BoxID = newBox.ID

	totalSharkNum, err = e.BoxActionDetailRepo.BoxActionDetailSelectYaoLimitDay(ctx, &box_action_detail.Filter{
		ActionType:      box_action_detail.ACTION_TYPE_2,
		UserID:          req.UserID,
		CreateTimeStart: timeUtil.TruncateToDay(time.Now()).Format(dbs.TimeDateFormatFull),
		CreateTimeEnd:   timeUtil.TruncateToDayEnd(time.Now()).Format(dbs.TimeDateFormatFull),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxGet BoxActionDetailSelectYaoLimitDay")
		err = ecode.SystemErr
		return nil, err
	}

	// 使用安全减法避免溢出
	maxShakeNum := int64(12)
	if maxShakeNum > totalSharkNum {
		res.BoxDetailRes.ShakeNum = uint32(maxShakeNum - totalSharkNum)
	} else {
		res.BoxDetailRes.ShakeNum = 0
	}

	res.AvaSlots = make([]*boxDto.BoxAvaSlotItem, 0)
	for _, slot := range avaSlot {
		res.AvaSlots = append(res.AvaSlots, &boxDto.BoxAvaSlotItem{
			SlotID:       slot,
			SlotShakeNum: activeConf.ShakeNum,
		})
	}

	return res, nil
}

type BoxRecordInfo struct {
	BoxNo       string
	Cap         uint32
	Len         uint32
	AvaSlot     []uint32
	SoldSlot    []uint32
	PreLockSlot []uint32
	LockSlot    []uint32
}

func (e *Entry) newBoxRecord(active *boxDto.BoxActiveRes, box *BoxRecordInfo, userID uint64) *box_record.Model {
	return &box_record.Model{
		ActiveID:    active.ActiveID,
		BoxNo:       box.BoxNo,
		Cap:         box.Cap,
		Len:         box.Len,
		AvaSlot:     sliceUtil.UintSliJoin(box.AvaSlot, ","),
		SoldSlot:    sliceUtil.UintSliJoin(box.SoldSlot, ","),
		PreLockSlot: sliceUtil.UintSliJoin(box.PreLockSlot, ","),
		LockSlot:    sliceUtil.UintSliJoin(box.LockSlot, ","),
		UserId:      userID,
	}

}

func genCnt(ctx *gin.Context, products map[uint64]*box_goods.Model, N uint32) uint32 {

	// 计算总库存百分比
	totalStockPercentage := calculateTotalStockPercentage(ctx, products)
	log.Ctx(ctx).Info("BoxCreate Total Stock Percentage: %f", totalStockPercentage)

	// 生成预售个数
	soldCnt := getSoldOutCount(ctx, N, totalStockPercentage)
	log.Ctx(ctx).Info("BoxCreate Generated getSoldOutCount: %d", soldCnt)

	// 统计仍有库存的商品数量
	var validGoods uint32
	for _, g := range products {
		if g.GetCurStock() > 0 {
			validGoods++
		}
	}

	// 若可售商品已覆盖全部槽位，则无需生成预售位
	if validGoods >= N {
		return 0
	}

	// 预售位最少需要覆盖缺口 (N - validGoods)
	minSoldCnt := N - validGoods
	if soldCnt < minSoldCnt {
		soldCnt = minSoldCnt
	}

	// 修复边界条件：确保 soldCnt 不超过 N-1，但至少满足 minSoldCnt 的需求
	maxAllowedSoldCnt := N - 1
	if maxAllowedSoldCnt < minSoldCnt {
		// 特殊情况：N=1 且 validGoods=0 时，minSoldCnt=1, maxAllowedSoldCnt=0
		// 这种情况下，所有槽位都必须是预售位，返回 N-1 (实际上是 0)
		soldCnt = maxAllowedSoldCnt
	} else if soldCnt >= N {
		soldCnt = maxAllowedSoldCnt
	}

	return soldCnt
}

func getAvailablePositions(ctx *gin.Context, N uint32, soldOutCount uint32) []uint32 {
	// 初始化位置数组
	positions := make([]uint32, N)
	for i := uint32(0); i < N; i++ {
		positions[i] = i + 1
	}

	// 随机打乱数组
	rand.Shuffle(len(positions), func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	log.Ctx(ctx).Info("BoxCreate positions: %v", positions)
	log.Ctx(ctx).Info("BoxCreate slice: %d", N-soldOutCount)

	// 选择前 N - soldOutCount 个元素作为可售位置
	availablePositions := positions[:N-soldOutCount]

	// 对 availablePositions 进行升序排序
	sort.Slice(availablePositions, func(i, j int) bool {
		return availablePositions[i] < availablePositions[j]
	})
	//slices.Sort(availablePositions)

	return availablePositions
}

// 用于计算总库存百分比
func calculateTotalStockPercentage(ctx *gin.Context, products map[uint64]*box_goods.Model) float64 {
	totalStock := uint32(0)
	maxTotalStock := uint32(0)
	for _, product := range products {
		totalStock += product.GetCurStock()
		maxTotalStock += product.GetAllStock()
	}
	return float64(totalStock) / float64(maxTotalStock) * 100
}
func getSoldOutCount(ctx *gin.Context, N uint32, totalStockPercentage float64) uint32 {
	weights := make([]uint32, N)

	switch {
	case totalStockPercentage == 100:
		return 0
	case totalStockPercentage > 95:
		weights[0] = N / 2
		for i := 1; i < 3; i++ {
			weights[i] = 1
		}
	case totalStockPercentage > 85:
		weights[0] = N / 2
		for i := uint32(1); i < N; i++ {
			weights[i] = 1
		}

	case totalStockPercentage > 30:
		for i := uint32(0); i < N; i++ {
			weights[i] = 1
		}
	case totalStockPercentage > 5:
		for i := uint32(3); i < N; i++ {
			weights[i] = 1
		}
	case totalStockPercentage > 0:
		if N >= 2 {
			weights[N-2] = 1
			weights[N-1] = 1
		}
	default:
		return N
	}

	totalWeight := uint32(0)
	for i, weight := range weights {
		log.Ctx(ctx).Info("BoxCreate %d weight: %d", i, weight)
		totalWeight += weight
	}

	var r uint32
	if totalWeight > 0 {
		// 初始化
		r = rand.Uint32N(totalWeight)
		log.Ctx(ctx).Info("BoxCreate randNum: %d", r)
	} else {
		return N
	}

	accumulatedWeight := uint32(0)
	for i, weight := range weights {
		accumulatedWeight += weight
		if r < accumulatedWeight {
			return uint32(i)
		}
	}

	return N - 1
}

func (e *Entry) BoxDetail(ctx *gin.Context, req *boxDto.BoxDetailReq) (res *boxDto.BoxDetailRes, err error) {
	res = &boxDto.BoxDetailRes{}
	res.GoodsDetails = make([]*boxDto.BoxGoodsDetailItem, 0)

	var (
		activeModel    = &actDao.Model{}
		boxModel       = &box_record.Model{}
		activeConf     = &box_active_config.Model{}
		goodsMap       = make(map[uint64]*box_goods.Model)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		boxActionDetails = make([]*box_action_detail.Model, 0)

		totalSharkNum = int64(0)

		slotSharkNum = make(map[uint32]uint32)
		goodsHintMap = make(map[uint64]uint32)
		goodsSoldMap = make(map[uint64]struct{})

		avaSlots = make([]uint32, 0)

		tipCount  = int64(0)
		cardCount = int64(0)

		eg errgroup.Group
	)

	eg.Go(func() error {
		tipCount, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   req.UserID,
			Status:   card_user.UserItemCardStatusUnused,
			CardType: cardConfDao.CardType_tip,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail CountByFilter")
			return err
		}
		if tipCount > 0 {
			res.TipCount = uint32(tipCount)
		}

		cardCount, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   req.UserID,
			Status:   card_user.UserItemCardStatusUnused,
			CardType: cardConfDao.CardType_tou,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail CountByFilter")
			return err
		}
		if cardCount > 0 {
			res.CardCount = uint32(cardCount)
		}

		return nil
	})

	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail FetchByID")
			return ecode.SystemErr
		}
		if activeModel == nil {
			return ecode.ActNotExistErr
		}
		return nil
	})

	eg.Go(func() error {
		boxModel, err = e.BoxRecordRepo.FetchByID(ctx, req.BoxID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail FetchByID")
			return ecode.SystemErr
		}
		if boxModel == nil {
			return ecode.ActNotExistErr
		}
		return nil
	})

	eg.Go(func() error {
		activeConf, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail FetchByActiveID")
			return ecode.SystemErr
		}
		if activeConf == nil {
			return ecode.ActNotExistErr
		}
		return nil
	})

	eg.Go(func() error {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ActiveID: activeModel.ID,
		})
		if err != nil {
			return err
		}
		goodsMap = goodsList.GetIDMap()
		spuIDs = goodsList.GetSpuIDs()
		skuIDs = goodsList.GetSkuIDs()

		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			return err
		}

		return nil
	})

	eg.Go(func() error {
		totalSharkNum, err = e.BoxActionDetailRepo.BoxActionDetailSelectYaoLimitDay(ctx, &box_action_detail.Filter{
			ActionType:      box_action_detail.ACTION_TYPE_2,
			UserID:          req.UserID,
			CreateTimeStart: timeUtil.TruncateToDay(time.Now()).Format(dbs.TimeDateFormatFull),
			CreateTimeEnd:   timeUtil.TruncateToDayEnd(time.Now()).Format(dbs.TimeDateFormatFull),
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail BoxActionDetailSelectYaoLimitDay")
			return ecode.SystemErr
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	boxActionDetails, err = e.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
		BoxID: boxModel.ID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxDetail FindByFilter")
		return res, ecode.SystemErr
	}

	if boxModel.AvaSlot != "" {
		avaSlots = sliceUtil.SplitStrToUintSli(boxModel.AvaSlot, ",")
		sort.Slice(avaSlots, func(i, j int) bool {
			return avaSlots[i] < avaSlots[j]
		})
	}

	for _, detail := range boxActionDetails {
		if detail.ActionType == box_action_detail.ACTION_TYPE_1 {
			goodsSoldMap[detail.GoodsID] = struct{}{}
			continue
		}

		if detail.CardID == 0 {
			slotID, err := strconv.Atoi(detail.BoxSlot)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxDetail strconv.Atoi")
				continue
			}
			slotSharkNum[uint32(slotID)]++
		}
		goodsHintMap[detail.GoodsID] = detail.ActionType
	}

	res.AvaSlots = make([]*boxDto.BoxAvaSlotItem, 0)
	for _, slot := range avaSlots {
		tmp := &boxDto.BoxAvaSlotItem{
			SlotID: slot,
		}
		if num, ok := slotSharkNum[slot]; ok {
			// 修复uint32溢出问题：在减法前检查大小关系
			if activeConf.ShakeNum > num {
				tmp.SlotShakeNum = activeConf.ShakeNum - num
			} else {
				tmp.SlotShakeNum = 0
			}
		} else {
			tmp.SlotShakeNum = activeConf.ShakeNum
		}
		res.AvaSlots = append(res.AvaSlots, tmp)
	}

	res.ActiveID = activeModel.ID
	res.ActiveTitle = activeModel.Title
	res.ActiveStatus = activeModel.ActStatus

	res.BoxID = boxModel.ID
	res.BoxNo = boxModel.BoxNo

	res.OpenTime = activeModel.GetStartTime()
	res.OpenTimeStamp = activeModel.StartTime
	res.EndTime = activeModel.GetEndTime()
	res.EndTimeStamp = activeModel.EndTime
	res.ActiveImage = activeModel.Image
	res.ActivePrice = activeModel.ActPrice

	res.LotteryNum = activeConf.LotteryNum
	res.HeadImage = activeConf.HeadImage
	res.LocationImage = activeConf.LocationImage
	res.BoxImage = activeConf.BoxImage
	res.LevelText = activeConf.LevelText
	// 使用安全减法避免溢出
	maxShakeNum := int64(12)
	if maxShakeNum > totalSharkNum {
		res.ShakeNum = uint32(maxShakeNum - totalSharkNum)
	} else {
		res.ShakeNum = 0
	}

	for _, goodsInfo := range goodsMap {
		tmp := &boxDto.BoxGoodsDetailItem{
			ID:         goodsInfo.ID,
			GoodsName:  goodsInfo.GoodsName,
			GoodsCover: goodsInfo.GoodsCover,
			Sort:       goodsInfo.Sort,
		}

		if skuItem, ok := skuMap[goodsInfo.SkuID]; ok {
			tmp.GoodsDetail = &skuItem
		}

		if _, ok := goodsSoldMap[goodsInfo.ID]; ok {
			tmp.IsSold = true
		}
		if icType, ok := goodsHintMap[goodsInfo.ID]; ok {
			tmp.IsHint = true
			if icType == cardConfDao.CardType_tip || icType == 0 {
				tmp.HintType = cardConfDao.CardType_tip
			} else if icType == cardConfDao.CardType_tou {
				tmp.HintType = cardConfDao.CardType_tou
			}
		}
		res.GoodsDetails = append(res.GoodsDetails, tmp)
	}

	sortGoodsDetail(res.GoodsDetails)

	return res, nil
}

func sortGoodsDetail(goodsDetail []*boxDto.BoxGoodsDetailItem) {
	// 优先根据isSold排序, isSold为true的排在前面
	// 其次根据（isHint*hintType) 排序，结果较大的排在前
	// 根据sort从大到小排序
	// 最后根据ID从大到小排序
	sort.Slice(goodsDetail, func(i, j int) bool {
		if goodsDetail[i].IsSold != goodsDetail[j].IsSold {
			return goodsDetail[i].IsSold
		}
		if goodsDetail[i].IsHint != goodsDetail[j].IsHint {
			return goodsDetail[i].IsHint
		}
		if goodsDetail[i].HintType != goodsDetail[j].HintType {
			return goodsDetail[i].HintType > goodsDetail[j].HintType
		}
		if goodsDetail[i].Sort != goodsDetail[j].Sort {
			return goodsDetail[i].Sort > goodsDetail[j].Sort
		}

		return goodsDetail[i].ID > goodsDetail[j].ID
	})
}

func (e *Entry) BoxSlot(ctx *gin.Context, req *boxDto.BoxSlotReq) (res *boxDto.BoxSlotRes, err error) {
	res = &boxDto.BoxSlotRes{
		BoxID: req.BoxID,
	}

	var (
		tipCount  = int64(0)
		cardCount = int64(0)

		activeConf = &box_active_config.Model{}

		goodsMap       = make(map[uint64]*box_goods.Model)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		totalSharkNum = int64(0)

		slotSharkNum = make(map[uint32]uint32)
		goodsHintMap = make(map[uint64]uint32)
		goodsSoldMap = make(map[uint64]struct{})

		boxActionDetails = make([]*box_action_detail.Model, 0)

		eg errgroup.Group
	)

	eg.Go(func() error {
		tipCount, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   req.UserID,
			Status:   card_user.UserItemCardStatusUnused,
			CardType: cardConfDao.CardType_tip,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail CountByFilter")
			return err
		}
		if tipCount > 0 {
			res.TipCount = uint32(tipCount)
		}

		cardCount, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   req.UserID,
			Status:   card_user.UserItemCardStatusUnused,
			CardType: cardConfDao.CardType_tou,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail CountByFilter")
			return err
		}
		if cardCount > 0 {
			res.CardCount = uint32(cardCount)
		}

		return nil
	})
	eg.Go(func() error {
		activeConf, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail FetchByActiveID")
			return ecode.SystemErr
		}
		if activeConf == nil {
			return ecode.ActNotExistErr
		}
		return nil
	})
	eg.Go(func() error {
		boxActionDetails, err = e.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
			BoxID: req.BoxID,
			Slot:  req.Slot,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail FindByFilter")
			return ecode.SystemErr
		}
		return nil
	})
	eg.Go(func() error {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ActiveID: req.ActiveID,
		})
		if err != nil {
			return err
		}
		goodsMap = goodsList.GetIDMap()
		spuIDs = goodsList.GetSpuIDs()
		skuIDs = goodsList.GetSkuIDs()

		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			return err
		}

		return nil
	})
	eg.Go(func() error {
		totalSharkNum, err = e.BoxActionDetailRepo.BoxActionDetailSelectYaoLimitDay(ctx, &box_action_detail.Filter{
			ActionType:      box_action_detail.ACTION_TYPE_2,
			UserID:          req.UserID,
			CreateTimeStart: timeUtil.TruncateToDay(time.Now()).Format(dbs.TimeDateFormatFull),
			CreateTimeEnd:   timeUtil.TruncateToDayEnd(time.Now()).Format(dbs.TimeDateFormatFull),
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxDetail BoxActionDetailSelectYaoLimitDay")
			return ecode.SystemErr
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	// 使用安全减法避免溢出
	maxShakeNum := int64(12)
	if maxShakeNum > totalSharkNum {
		res.ShakeNum = uint32(maxShakeNum - totalSharkNum)
	} else {
		res.ShakeNum = 0
	}

	for _, detail := range boxActionDetails {
		if detail.ActionType == box_action_detail.ACTION_TYPE_1 {
			goodsSoldMap[detail.GoodsID] = struct{}{}
			continue
		}

		if detail.CardID == 0 {
			slotID, err := strconv.Atoi(detail.BoxSlot)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxDetail strconv.Atoi")
				continue
			}
			slotSharkNum[uint32(slotID)]++
		}
		goodsHintMap[detail.GoodsID] = detail.ActionType
	}

	if num, ok := slotSharkNum[req.Slot]; ok {
		// 修复uint32溢出问题：在减法前检查大小关系
		if activeConf.ShakeNum > num {
			res.SlotShakeNum = activeConf.ShakeNum - num
		} else {
			res.SlotShakeNum = 0
		}
	} else {
		res.SlotShakeNum = activeConf.ShakeNum
	}

	for _, goodsInfo := range goodsMap {
		tmp := &boxDto.BoxGoodsDetailItem{
			ID:         goodsInfo.ID,
			GoodsName:  goodsInfo.GoodsName,
			GoodsCover: goodsInfo.GoodsCover,
			Sort:       goodsInfo.Sort,
		}

		if skuModel, ok := skuMap[goodsInfo.SkuID]; ok {
			tmp.GoodsDetail = &skuModel
		}

		if _, ok := goodsSoldMap[goodsInfo.ID]; ok {
			tmp.IsSold = true
		}
		if icType, ok := goodsHintMap[goodsInfo.ID]; ok {
			tmp.IsHint = true
			if icType == cardConfDao.CardType_tip || icType == 0 {
				tmp.HintType = cardConfDao.CardType_tip
			} else if icType == cardConfDao.CardType_tou {
				tmp.HintType = cardConfDao.CardType_tou
			}
		}
		res.GoodsDetails = append(res.GoodsDetails, tmp)
	}

	sortGoodsDetail(res.GoodsDetails)

	return res, nil
}

func (e *Entry) BoxActions(ctx *gin.Context, req *boxDto.BoxActionsReq) (res *boxDto.BoxActionsRes, err error) {
	res = &boxDto.BoxActionsRes{}
	res.List = make([]*boxDto.BoxActionItem, 0)

	var (
		uc     = GetUnderConstruct()
		limitS time.Time
		limitE time.Time

		boxQueueList = make(box_queue_lock.ModelList, 0)
		count        = int64(0)

		activeIDs = make([]uint64, 0)
		activeMap = make(map[uint64]*actDao.Model)
		boxIDs    = make([]uint64, 0)
		boxMap    = make(map[uint64]*box_record.Model)

		eg errgroup.Group
	)
	if time.Now().After(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[0])*time.Hour)) && time.Now().Before(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1])*time.Hour)) {
		errStr := fmt.Sprintf("%d-%d点为每日抽盒系统维护时间，无法操作", uc[0], uc[1])
		return nil, errors.New(errStr)
	}
	if time.Now().Before(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1]-24) * time.Hour)) {
		limitS = timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1]-24) * time.Hour)
	} else {
		limitS = timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1]) * time.Hour)
	}
	limitE = time.Now()

	res.StartTimeStamp = limitS.Unix()
	res.StartTime = limitS.Format(dbs.TimeDateFormatFull)
	res.EndTimeStamp = limitE.Unix()
	res.EndTime = limitE.Format(dbs.TimeDateFormatFull)

	count, boxQueueList, err = e.BoxQueueLockRepo.DataPageList(ctx, &box_queue_lock.Filter{
		ActiveID:         req.ActiveID,
		UserID:           req.UserID,
		CreatedTimeBegin: limitS.Format(dbs.TimeDateFormatFull),
		CreatedTimeEnd:   limitE.Format(dbs.TimeDateFormatFull),
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxActions DataPageList")
		return nil, err
	}
	if count <= 0 {
		return res, nil
	}
	res.Count = count

	activeIDs = boxQueueList.GetActiveIDs()
	boxIDs = boxQueueList.GetBoxIDs()

	if len(activeIDs) > 0 {
		eg.Go(func() error {
			activeList, err := e.ActiveRepo.FindByFilter(ctx, &actDao.Filter{
				Ids: activeIDs,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxActions FindByFilter")
				return err
			}
			activeMap = activeList.GetIDMap()
			return nil
		})
	}

	if len(boxIDs) > 0 {
		eg.Go(func() error {
			boxList, err := e.BoxRecordRepo.FindByFilter(ctx, &box_record.Filter{
				IDs: boxIDs,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxActions FindByFilter")
				return err
			}
			boxMap = boxList.GetIDMap()
			return nil
		})
	}
	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxActions errgroup.Wait")
		return nil, err
	}

	for _, q := range boxQueueList {
		tmp := &boxDto.BoxActionItem{
			BoxID: q.BoxID,
		}
		if boxModel, ok := boxMap[q.BoxID]; ok {
			tmp.BoxNo = boxModel.BoxNo
			tmp.AvaSlot = sliceUtil.SplitStrToUintSli(boxModel.AvaSlot, ",")

			sort.Slice(tmp.AvaSlot, func(i, j int) bool {
				return tmp.AvaSlot[i] < tmp.AvaSlot[j]
			})

			tmp.LotteryNum = boxModel.Cap
		}
		if activeModel, ok := activeMap[q.ActiveID]; ok {
			tmp.ActiveID = activeModel.ID
			tmp.ActiveTitle = activeModel.Title
		}

		tmp.OperateAt = q.CreatedAt.Unix()

		res.List = append(res.List, tmp)
	}

	return res, nil
}

func GetUnderConstruct() []int {
	defaultNumStart := 5
	defaultNumEnd := 6
	return []int{defaultNumStart, defaultNumEnd}

	//t, err := global.Dao.GetCommonSettingByKey(model.CommonSettingUnderConstruct)
	//if err != nil {
	//	return []int{defaultNumStart, defaultNumEnd}
	//}
	//s := strings.Split(t.SETTING_VALUE, ",")
	//if len(s) != 2 {
	//	return []int{defaultNumStart, defaultNumEnd}
	//}
	//start, err := strconv.ParseInt(s[0], 10, 64)
	//if err != nil || (start < 0 || start > 23) {
	//	return []int{defaultNumStart, defaultNumEnd}
	//}
	//end, err := strconv.ParseInt(s[1], 10, 64)
	//if err != nil || (end < 0 || end > 23) {
	//	return []int{defaultNumStart, defaultNumEnd}
	//}
	//if start > end {
	//	return []int{defaultNumStart, defaultNumEnd}
	//}
	//return []int{int(start), int(end)}
}

func (e *Entry) BoxBag(ctx *gin.Context, req *boxDto.BoxBagReq) (res *boxDto.BoxBagRes, err error) {
	res = &boxDto.BoxBagRes{}
	res.List = make([]*boxDto.BoxBagItem, 0)

	var (
		orderList  = make(orderDao.ModelList, 0)
		orderCount = int64(0)

		tradeList     = make(orderDetailDao.ModelList, 0)
		tradeOrderMap = make(map[uint64]orderDetailDao.ModelList)

		activeIDs = make([]uint64, 0)
		activeMap = make(map[uint64]*actDao.Model)

		orderIDs       = make([]uint64, 0)
		goodsIDs       = make([]uint64, 0)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		goodsMap       = make(map[uint64]*box_goods.Model)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		eg errgroup.Group
	)

	orderCount, orderList, err = e.OrderRepo.DataPageList(ctx, &orderDao.Filter{
		UserID: req.UserID,
		Status: uint32(orderDao.OrderStatusPayOk),
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxBag DataPageList")
		return nil, err
	}
	res.Count = orderCount
	if orderCount <= 0 {
		res.IsEnd = true
		return res, nil
	}
	if len(orderList) < req.Limit {
		res.IsEnd = true
	}

	orderIDs = orderList.GetIDs()
	activeIDs = orderList.GetActiveIDs()

	tradeList, err = e.TradeRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderIDs: orderIDs,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxBag FindByFilter")
		return nil, err
	}

	tradeOrderMap = tradeList.GetOrderMap()
	goodsIDs = tradeList.GetGoodsIDs()

	if len(activeIDs) > 0 {
		eg.Go(func() error {
			activeList, err := e.ActiveRepo.FindByFilter(ctx, &actDao.Filter{
				Ids: activeIDs,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxBag FindByFilter")
				return err
			}
			activeMap = activeList.GetIDMap()
			return nil
		})
	}
	if len(goodsIDs) > 0 {
		eg.Go(func() error {
			goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
				IDs: goodsIDs,
			})
			if err != nil {
				return err
			}
			goodsMap = goodsList.GetIDMap()
			spuIDs = goodsList.GetSpuIDs()
			skuIDs = goodsList.GetSkuIDs()

			skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
			if err != nil {
				return err
			}

			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxBag errgroup.Wait")
		return nil, err
	}

	for _, order := range orderList {
		tmp := &boxDto.BoxBagItem{
			OrderID:  order.ID,
			ActiveID: order.ActiveID,
			UsedFee:  order.UsedFee,
			Trades:   make([]boxDto.BoxBagTradeItem, 0),
		}
		if activeModel, ok := activeMap[order.ActiveID]; ok {
			tmp.ActiveTitle = activeModel.Title
			tmp.ActiveStatus = activeModel.ActStatus
			tmp.ActivePrice = activeModel.ActPrice
			tmp.DeliveryExpect = activeModel.Tips
		}
		if trades, ok := tradeOrderMap[order.ID]; ok {
			for _, trade := range trades {
				goodsTmp := boxDto.BoxBagTradeItem{
					ID:        trade.ID,
					GoodsID:   trade.GoodsID,
					GoodsName: trade.GoodsName,
				}
				if goodsModel, ok := goodsMap[trade.GoodsID]; ok {
					goodsTmp.GoodsImg = goodsModel.GoodsCover
					if skuItem, ok := skuMap[goodsModel.SkuID]; ok {
						goodsTmp.GoodsDetail = &skuItem
					}
					//goodsTmp.GoodsStatus = goodsModel.GoodStatus
				}
				tmp.Trades = append(tmp.Trades, goodsTmp)
			}
		}
		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) BoxTradeList(ctx *gin.Context, req *boxDto.BoxTradeListReq) (res *boxDto.BoxTradeListRes, err error) {
	res = &boxDto.BoxTradeListRes{}
	res.List = make([]boxDto.BoxTradeListResItem, 0)

	var (
		tradeList = make(orderDetailDao.ModelList, 0)
		count     = int64(0)

		activeIDs      = make([]uint64, 0)
		activeMap      = make(map[uint64]*actDao.Model)
		goodsIDs       = make([]uint64, 0)
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		goodsMap       = make(map[uint64]*box_goods.Model)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		eg     errgroup.Group
		filter = &orderDetailDao.Filter{
			UserID:       req.UserID,
			DeliveryType: req.Type,
		}
	)

	if req.Type == 1 && req.OnlyDelivery == 1 {
		filter.OnlyDelivery = 1
	}
	count, tradeList, err = e.TradeRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxTradeList DataPageList")
		return nil, err
	}
	if count <= 0 {
		res.IsEnd = true
		return res, nil
	}

	activeIDs = tradeList.GetActiveIDs()
	goodsIDs = tradeList.GetGoodsIDs()

	if len(activeIDs) > 0 {
		eg.Go(func() error {
			activeList, err := e.ActiveRepo.FindByFilter(ctx, &actDao.Filter{
				Ids: activeIDs,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxTradeList FindByFilter")
				return err
			}
			activeMap = activeList.GetIDMap()
			return nil
		})
	}
	if len(goodsIDs) > 0 {
		eg.Go(func() error {
			goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
				IDs: goodsIDs,
			})
			if err != nil {
				return err
			}
			goodsMap = goodsList.GetIDMap()
			spuIDs = goodsList.GetSpuIDs()
			skuIDs = goodsList.GetSkuIDs()

			skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
			if err != nil {
				return err
			}

			return nil
		})
	}
	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxTradeList errgroup.Wait")
		return nil, err
	}

	for _, trade := range tradeList {
		tmp := boxDto.BoxTradeListResItem{
			TradeID:     trade.ID,
			ActiveID:    trade.ActiveID,
			ActiveTitle: trade.ActiveTitle,
			ActiveType:  trade.ActiveType,
			GoodsID:     trade.GoodsID,
			GoodsName:   trade.GoodsName,
		}
		if activeModel, ok := activeMap[trade.ActiveID]; ok {
			tmp.ActivePrice = activeModel.ActPrice
		}
		if goodsModel, ok := goodsMap[trade.GoodsID]; ok {
			//tmp.GoodsName = goodsModel.GoodsName
			tmp.GoodsImage = goodsModel.GoodsCover
			if skuItem, ok := skuMap[goodsModel.SkuID]; ok {
				tmp.GoodsDetail = &skuItem
				tmp.GoodsStatus = skuItem.BatchSellType
			}
		}
		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) BoxTradeDetail(ctx *gin.Context, req *boxDto.BoxTradeDetailReq) (res *boxDto.BoxTradeDetailRes, err error) {
	res = &boxDto.BoxTradeDetailRes{}

	var (
		tradeModel     = &orderDetailDao.Model{}
		orderID        uint64
		orderModel     = &orderDao.Model{}
		activeID       uint64
		activeModel    = &actDao.Model{}
		goodsID        uint64
		spuIDs, skuIDs = make([]uint64, 0), make([]uint64, 0)
		goodsModel     = &box_goods.Model{}
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)

		eg errgroup.Group
	)

	tradeModel, err = e.TradeRepo.FetchByID(ctx, req.TradeID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxTradeDetail FetchByID")
		return nil, err
	}
	if tradeModel == nil {
		return res, nil
	}
	if tradeModel.UserID != req.UserID {
		return res, nil
	}

	orderID = tradeModel.OrderID
	activeID = tradeModel.ActiveID
	goodsID = tradeModel.GoodsID

	eg.Go(func() error {
		orderModel, err = e.OrderRepo.FetchByID(ctx, orderID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxTradeDetail FetchByID")
			return err
		}
		if orderModel == nil {
			return ecode.SystemBusyErr
		}
		return nil
	})
	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, activeID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxTradeDetail FetchByID")
			return err
		}
		if activeModel == nil {
			return ecode.ActNotExistErr
		}
		return nil
	})
	eg.Go(func() error {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ID: goodsID,
		})
		if err != nil {
			return err
		}
		if len(goodsList) == 0 {
			return ecode.BoxGoodsNotExistErr
		}
		goodsModel = goodsList[0]
		spuIDs = goodsList.GetSpuIDs()
		skuIDs = goodsList.GetSkuIDs()

		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			return err
		}

		return nil
	})
	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxTradeDetail errgroup.Wait")
		return nil, err
	}

	res.ActiveID = activeID
	res.ActiveTitle = activeModel.Title
	res.ActivePrice = activeModel.ActPrice
	res.OutTradeNo = orderModel.OutTradeNo
	res.CreateTime = orderModel.CreatedAt.Format(dbs.TimeFormatFull)
	res.PayTime = int64(orderModel.PayTime)
	res.PayTimeStr = time.Unix(int64(orderModel.PayTime), 0).Format(dbs.TimeDateFormatFull)
	res.PayType = orderModel.PayMethod
	res.ActiveStatus = activeModel.ActStatus
	res.RealFee = activeModel.ActPrice
	res.DeliveryStatus = tradeModel.DeliveryStatus
	res.GoodsName = tradeModel.GoodsName

	//res.GoodsStatus = goodsModel.GoodStatus
	//res.Remark = goodsModel.Remark
	//res.GoodsName = goodsModel.GoodsName
	//res.GoodsImage = goodsModel.GoodsImage

	if orderModel.CouponFee > 0 {
		orderTotal := orderModel.LotteryTotal - orderModel.LotteryLeft
		goodsCoupon := decimal.NewFromInt(int64(orderModel.CouponFee)).
			Div(decimal.NewFromInt(int64(orderTotal))).IntPart()
		res.CouponFee = uint64(goodsCoupon)

		// 使用安全减法避免下溢
		realFeeAfterCoupon, err := decimalUtil.SafeSubtract(res.RealFee, res.CouponFee)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxTradeDetail RealFee subtraction error")
			res.RealFee = 0
		} else {
			res.RealFee = realFeeAfterCoupon
		}
	}

	if goodsModel.ID > 0 {
		//res.GoodsName = goodsModel.GoodsName
		res.GoodsImage = goodsModel.GoodsCover
		if skuItem, ok := skuMap[goodsModel.SkuID]; ok {
			res.GoodsStatus = skuItem.BatchSellType
			res.GoodsDetail = &skuItem
		}
	}

	return res, nil
}

func (e *Entry) BoxCardCount(ctx *gin.Context, req *boxDto.BoxCardCountReq) (res *boxDto.BoxCardCountRes, err error) {
	res = &boxDto.BoxCardCountRes{}

	count, err := e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
		UserID:   req.UserID,
		CardType: req.CardType,
		Status:   card_user.UserItemCardStatusUnused,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardCount CountByFilter")
		return nil, err
	}

	res.Num = uint32(count)

	return res, nil
}

func (e *Entry) BoxCardUserHistoryList(ctx *gin.Context, req *boxDto.BoxCardUserHistoryListReq) (res *boxDto.BoxCardUserHistoryListRes, err error) {
	res = &boxDto.BoxCardUserHistoryListRes{}
	res.List = make([]*boxDto.BoxCardUserHistoryItem, 0)

	count, cardList, err := e.UserCardRepo.DataPageList(ctx, &card_user.Filter{
		UserID: req.UserID,
		Status: card_user.UserItemCardStatusExpire,
		Sort:   []clause.OrderByColumn{{Column: clause.Column{Name: "expire_time"}, Desc: true}},
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardUserHistoryList DataPageList")
		return nil, err
	}
	if count <= 0 {
		res.IsEnd = true
		return res, nil
	}

	if count > int64(req.Page*req.Limit) {
		res.IsEnd = false
	}

	for _, card := range cardList {
		tmp := &boxDto.BoxCardUserHistoryItem{
			ID:         card.ID,
			CardName:   card.CardName,
			Status:     card.Status,
			ExpireTime: time.Unix(card.ExpireTime, 0).Format(dbs.TimeDateFormatFull),
		}
		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) BoxCardUserList(ctx *gin.Context, req *boxDto.BoxCardUserListReq) (res *boxDto.BoxCardUserListRes, err error) {
	res = &boxDto.BoxCardUserListRes{}
	res.List = make([]*boxDto.BoxCardUserItem, 0)

	count, cardList, err := e.UserCardRepo.DataPageList(ctx, &card_user.Filter{
		UserID: req.UserID,
		Status: card_user.UserItemCardStatusUnused,
		Sort:   []clause.OrderByColumn{{Column: clause.Column{Name: "expire_time"}, Desc: false}},
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardUserList DataPageList")
		return nil, err
	}
	if count <= 0 {
		res.IsEnd = true
		return res, nil
	}

	if count > int64(req.Page*req.Limit) {
		res.IsEnd = false
	}

	for _, card := range cardList {
		tmp := &boxDto.BoxCardUserItem{
			ID:         card.ID,
			CardName:   card.CardName,
			Status:     card.Status,
			ExpireTime: time.Unix(card.ExpireTime, 0).Format(dbs.TimeDateFormatFull),
			CardImg:    card.CardImg,
			CardType:   card.CardType,
		}
		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) BoxCardLoginSend(ctx *gin.Context, req *boxDto.BoxCardLoginSendReq) (res *boxDto.BoxCardLoginSendRes, err error) {
	res = &boxDto.BoxCardLoginSendRes{}

	if err = e.RedisCli.Lock(ctx, redis.GetLoginSendCardLockKey(req.UserID), redis.DefaultLockTime); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardLoginSend Lock")
		return nil, err
	}
	defer func() {
		e.RedisCli.Unlock(ctx, redis.GetLoginSendCardLockKey(req.UserID))
	}()

	var (
		sendLogs   = make(card_day_send_log.ModelList, 0)
		cardConf   = &cardConfDao.Model{}
		expireTime = time.Now().AddDate(0, 0, 1).Unix()

		userCardList = make(card_user.ModelList, 0)
	)

	sendLogs, err = e.CardDayRepo.FindByFilter(ctx, &card_day_send_log.Filter{
		UserID: req.UserID,
		Day:    time.Now().Format(dbs.TimeDateFormat),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardLoginSend FindByFilter")
		return res, err
	}
	if len(sendLogs) > 0 {
		return
	}
	cardConfList, err := e.CardConfRepo.FindByFilter(ctx, &cardConfDao.Filter{
		CardType: cardConfDao.CardType_yao,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardLoginSend FindByFilter")
		return res, err
	}
	if len(cardConfList) <= 0 {
		return
	}
	cardConf = cardConfList[0]

	for i := 0; i < 12; i++ {
		userCard := &card_user.Model{
			UserID:     req.UserID,
			CardCode:   cardConf.CardCode,
			CardType:   cardConf.CardType,
			CardName:   cardConf.CardName,
			CardImg:    cardConf.CardImg,
			DetailMsg:  cardConf.DetailMsg,
			ExpireTime: expireTime,
			Remark:     "每日发放",
		}
		userCardList = append(userCardList, userCard)
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()

	err = e.UserCardRepo.BatchCreateWithTx(ctx, tx, userCardList)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardLoginSend BatchCreateWithTx")
		tx.Rollback()
		return nil, err
	}

	sendLog := &card_day_send_log.Model{
		UserID: req.UserID,
		Day:    time.Now().Format(dbs.TimeDateFormat),
	}

	_, err = e.CardDayRepo.CreateWithTx(ctx, tx, sendLog)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCardLoginSend CreateWithTx")
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return res, nil
}

func (e *Entry) BoxItemUse(ctx *gin.Context, req *boxDto.BoxItemUseReq) (res *boxDto.BoxItemUseRes, err error) {
	res = &boxDto.BoxItemUseRes{}
	res.GoodsInfo = make([]*boxDto.SlotGoodsInfo, 0)
	var (
		uc = GetUnderConstruct()

		// 移除事务内并发，所有数据准备在事务外串行完成
		boxModel           = &box_record.Model{}
		actModel           = &actDao.Model{}
		userCardModel      = &card_user.Model{}
		boxActiveConfModel = &box_active_config.Model{}
		gsm                = make(map[uint64]*goodsStock)
	)
	if time.Now().After(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[0])*time.Hour)) && time.Now().Before(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1])*time.Hour)) {
		errStr := fmt.Sprintf("%d-%d点为每日抽盒系统维护时间，无法操作", uc[0], uc[1])
		log.Ctx(ctx).Error(errStr)
		return nil, errors.New(errStr)
	}

	// TODO: redis user lock + redis box lock
	if err = e.RedisCli.Lock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID), redis.DefaultLockTime); err != nil {
		return nil, ecode.SystemBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID))

	// 串行准备所有数据，事务外
	if boxModel, err = e.checkBox(ctx, req.BoxID); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkBox")
		return nil, err
	}
	if boxModel.Len <= 0 {
		return nil, ecode.BoxActiveErr
	}
	if actModel, err = e.checkActive(ctx, boxModel.ActiveID); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkActive")
		return nil, err
	}
	if err = e.checkActiveTime(ctx, actModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkActiveTime")
		return nil, err
	}
	if _, err = e.AdminActiveRecommendCheck(ctx, &boxDto.AdminActiveRecommendCheckReq{
		ActiveID: boxModel.ActiveID,
	}); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse AdminActiveRecommendCheck")
		return nil, err
	}
	if len(req.Slot) <= 0 {
		return nil, ecode.BoxSlotNotSelectErr
	}
	if !e.checkAvaSlot(ctx, boxModel.AvaSlot, req.Slot) {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkAvaSlot")
		return nil, err
	}
	if userCardModel, err = e.checkItemCard(ctx, 0, req.ICType, req.UserID); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkItemCard")
		return nil, err
	}
	if userCardModel == nil {
		log.Ctx(ctx).Error("BoxItemUse checkItemCard userCardModel is nil")
		return nil, ecode.SystemBusyErr
	}
	goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
		ActiveID: boxModel.ActiveID,
	})
	if err != nil {
		return nil, err
	}
	for _, goods := range goodsList {
		if goods.ID == 0 {
			continue
		}
		goodsInfo := &goodsStock{
			Model: goods,
		}
		gsm[goods.ID] = goodsInfo
	}
	if boxActiveConfModel, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, boxModel.ActiveID); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse FetchByActiveID")
		return nil, err
	}
	if boxActiveConfModel == nil {
		log.Ctx(ctx).Error("BoxItemUse FetchByActiveID boxActiveConfModel is nil")
		return nil, ecode.SystemBusyErr
	}
	if err = e.checkCount(ctx, req, req.BoxID, boxActiveConfModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse checkCount")
		return nil, err
	}

	var (
		boxRecord        = newBoxRecordInfo(boxModel)
		itemCardStrategy = e.StrategySelect(STRATEGY_ITEMCARD)
		secretLotteryNum = uint32(0)
	)
	if itemCardStrategy == nil {
		log.Ctx(ctx).Error("BoxItemUse itemCardStrategy is nil")
		return nil, ecode.SystemBusyErr
	}

	// 临时解决方案：为了避免编译错误，传入nil作为tx参数
	// TODO: 需要重构整个事务边界，将策略调用移到事务内
	results, err := itemCardStrategy.Operate(
		ctx,
		nil, // 临时传入nil，因为ItemCardStrategy目前只做读操作
		&BoxModel{
			Active:        actModel,
			BoxInfo:       boxModel,
			Slot:          req.Slot,
			GoodsStockMap: gsm,
			BoxRecord:     boxRecord,
		}, &ItemCardModel{
			ICType: req.ICType,
		},
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse Operate")
		return nil, err
	}

	if len(results.List) <= 0 || len(results.List) > 1 {
		log.Ctx(ctx).Error("BoxItemUse Operate results is not correct")
		return nil, ecode.SystemBusyErr
	}
	
	actionDetail, err := e.BoxActionDetailRepo.FindByFilterAtLast(ctx, &box_action_detail.Filter{
		BoxID: req.BoxID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxItemUse FindByFilterAtLast")
		return nil, err
	}
	if actionDetail != nil {
		secretLotteryNum = actionDetail.SecretLotteryNum
	}

	res = &boxDto.BoxItemUseRes{
		BoxID:  req.BoxID,
		ICType: req.ICType,
	}

	lockStock := make(map[uint64]*box_goods.Model)
	for _, goods := range results.List {
		tmp := &boxDto.SlotGoodsInfo{
			GoodsID:   goods.GoodsInfo.ID,
			GoodsName: goods.GoodsInfo.GoodsName,
		}

		if results.ActionType == box_action_detail.ACTION_TYPE_3 {
			if _, ok := boxRecord.LockSlot[goods.Slot]; !ok {
				boxRecord.LockSlot[goods.Slot] = struct{}{}
			}
			if goods.GoodsInfo.LevelName != box_level.BoxLevelNormal {
				secretLotteryNum++
				lockStock[goods.GoodsInfo.ID] = goods.GoodsInfo
			}
		}

		if results.ActionType == box_action_detail.ACTION_TYPE_2 {
			res.GoodsInfo = append(res.GoodsInfo, &boxDto.SlotGoodsInfo{
				GoodsID:   goods.NotGoodsInfo.ID,
				GoodsName: goods.NotGoodsInfo.GoodsName,
			})
		} else {
			res.GoodsInfo = append(res.GoodsInfo, tmp)
		}
		break
	}

	// 事务内所有 SQL 串行执行
	err = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		// Redis 操作异步执行，避免影响主要业务流程
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Ctx(ctx).Error("goroutine panic in BoxItemUse: %v", r)
				}
			}()
			err := tideDao.GetRepo().RedisSetUserTideTaskStatus(ctx, req.UserID, tideDao.TideTaskTypeShake, uint32(len(req.Slot)))
			if err != nil {
				log.Ctx(ctx).Error("BoxItemUse RedisSetUserTideTaskStatus err: %v", err)
			}
		}()

		for _, info := range lockStock {
			err = e.ChangeBoxLockGoodsStock(ctx, tx, info.ID, &changeBoxGoodsStock{
				ChangeNumber: 1,
				UserId:       req.UserID,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxItemUse ChangeBoxLockGoodsStock")
				return err
			}
		}

		// 箱子内槽位情况
		boxUpdates := map[string]interface{}{
			"ava_slot":      strUtil.UintMapJoin(boxRecord.AvaSlot, ","),
			"sold_slot":     strUtil.UintMapJoin(boxRecord.SoldSlot, ","),
			"len":           boxRecord.Len,
			"cap":           boxRecord.Cap,
			"pre_lock_slot": strUtil.UintMapJoin(boxRecord.PreLockSlot, ","),
			"lock_slot":     strUtil.UintMapJoin(boxRecord.LockSlot, ","),
		}
		err = e.BoxRecordRepo.UpdateMapByIDWithTx(ctx, tx, boxModel.ID, boxUpdates)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxItemUse UpdateMapByIDWithTx")
			return err
		}

		if len(results.List) == 1 {
			m := box_action_detail.NewBoxActionDetail(
				req.UserID,
				boxModel,
				sliceUtil.UintSliJoin(req.Slot, ","),
				results.ActionType,
				&box_action_detail.BoxLotteryNum{},
				nil,
				&orderDetailDao.Model{
					OrderID:    0,
					ActiveID:   boxModel.ActiveID,
					GoodsID:    results.List[0].GoodsInfo.ID,
					GoodsLevel: results.List[0].GoodsInfo.LevelName,
				},
			)
			if m.ActionType == box_action_detail.ACTION_TYPE_2 {
				m.GoodsID = results.List[0].NotGoodsInfo.ID
			}
			m.CardID = userCardModel.ID
			if actionDetail != nil && secretLotteryNum > actionDetail.SecretLotteryNum {
				m.SecretLotteryNum = secretLotteryNum
			}
			err = e.BoxActionDetailRepo.CreateOrUpdateWithTx(ctx, tx, m)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxItemUse CreateOrUpdateWithTx")
				return err
			}
		}
		if userCardModel.ID > 0 {
			// 道具卡核销记录
			itemCardUpdateMap := map[string]interface{}{
				"status":    card_user.UserItemCardStatusUsed,
				"used_time": time.Now().Format(dbs.TimeDateFormatFull),
				"active_id": actModel.ID,
				"box_id":    boxModel.ID,
				"box_slot":  req.Slot[0],
			}
			err = e.UserCardRepo.UpdateMapByIDWithTx(ctx, tx, userCardModel.ID, itemCardUpdateMap)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxItemUse UpdateMapByIDWithTx")
				return err
			}

		} else {
			icYao := card_user.NewYaoItemCardModel(
				req.UserID,
				&cardConfDao.Model{
					CardType: cardConfDao.CardType_yao,
					CardName: "摇一摇",
				},
				card_user.UserItemCardStatusUsed,
			)
			err = e.UserCardRepo.CreateOrUpdateWithTx(ctx, tx, icYao)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxItemUse CreateOrUpdateWithTx")
				return err
			}
		}

		return nil
	})

	return res, nil
}

func (e *Entry) DirectPay(ctx *gin.Context, req *boxDto.BoxPayReq) (res interface{}, err error) {

	if req.OrderID > 0 {
		// 二次支付：根据订单ID直接走支付
		return e.handleDirectPayWithOrderID(ctx, req)
	}
	if req.PurchaseType != STRATEGY_DIRECT {
		return nil, ecode.ParamErr
	}
	// 首次支付：创建订单并走支付
	return e.handleDirectPayCreateOrder(ctx, req)
}

// handleDirectPayWithOrderID 处理二次支付逻辑
func (e *Entry) handleDirectPayWithOrderID(ctx *gin.Context, req *boxDto.BoxPayReq) (res interface{}, err error) {
	orderInfo, err := e.OrderRepo.FetchByID(ctx, req.OrderID)
	if err != nil || orderInfo == nil {
		return nil, ecode.BoxOrderNotExistErr
	}
	if orderInfo.OrderStatus != uint32(orderDao.OrderStatusCreated) && orderInfo.OrderStatus != uint32(orderDao.OrderStatusPayIng) {
		return nil, ecode.BoxOrderNotAllowPayErr
	}
	if orderInfo.PayExpireTime > 0 && time.Now().Unix() > orderInfo.PayExpireTime {
		return nil, ecode.BoxOrderPayTimeoutErr
	}
	userInfo, err := e.UserRepo.FetchAuthByUid(ctx, orderInfo.UserID)
	if err != nil || userInfo == nil {
		return nil, ecode.UserNotExistErr
	}

	// 从Redis获取prepay_id
	redisKey := redis.GetBoxOrderPrepayIdKey(orderInfo.OutTradeNo)
	prepayId, err := e.RedisCli.Get(ctx, redisKey).Result()
	if errors.Is(err, redisPkg.Nil) {
		return nil, ecode.BoxOrderPayTimeoutErr
	} else if err != nil {
		return nil, err
	}

	// 调用PaySignOfApplet生成签名
	appletSign, err := e.callPaySignOfApplet(ctx, prepayId)
	if err != nil {
		return nil, err
	}

	return appletSign, nil
}

// handleDirectPayCreateOrder 处理首次支付逻辑（原有下单+支付流程）
func (e *Entry) handleDirectPayCreateOrder(ctx *gin.Context, req *boxDto.BoxPayReq) (res interface{}, err error) {
	if req.CartItems == nil || len(req.CartItems) == 0 {
		log.Ctx(ctx).Error("direct purchase requires cartItems")
		return nil, ecode.ParamErr
	}
	for _, item := range req.CartItems {
		if item.SkuID == 0 || item.Quantity == 0 {
			log.Ctx(ctx).Error("direct purchase requires valid skuId and quantity > 0")
			return nil, ecode.ParamErr
		}
	}

	if err = e.RedisCli.Lock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID), redis.DefaultLockTime); err != nil {
		return nil, ecode.SystemBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID))

	createOrderParams, err := e.checkCreateOrder(ctx, req)
	if err != nil {
		log.Ctx(ctx).Error("%+v CreateOrder CheckCreateOrder err is %s", req, err.Error())
		return
	}
	// 获取实际total值
	var total uint32
	if req.CartItems != nil {
		for _, item := range req.CartItems {
			total += item.Quantity
		}
	}

	couponFee := uint64(0)
	if req.CouponID > 0 {
		couponInfo, err := cUserSrv.GetService().CheckWhenUseCoupon(
			ctx,
			req.UserID,
			req.CouponID,
			req.ActiveID,
			total)
		if err != nil {
			return nil, err
		}

		if couponInfo != nil {
			couponFee = couponInfo.DiscountAmount
		}
	}

	// 计算总费用（支持多SKU）
	var totalPrice uint64
	for _, item := range req.CartItems {
		// 直接购买应该使用SKU的销售价格，而不是盲盒活动价格
		skuModel, err := e.SkuRepo.FetchByID(ctx, item.SkuID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay fetch SKU error for price calculation")
			return nil, fmt.Errorf("failed to find SKU %d for pricing: %w", item.SkuID, err)
		}
		if skuModel.ID == 0 {
			return nil, fmt.Errorf("SKU %d not found for pricing", item.SkuID)
		}

		// 使用SKU的销售价格计算
		itemPrice, err := decimalUtil.SafeMultiply(skuModel.SellPrice, item.Quantity)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay price calculation overflow")
			return nil, fmt.Errorf("price calculation error for SKU %d: %w", item.SkuID, err)
		}

		// 使用安全的加法避免溢出
		newTotalPrice, err := decimalUtil.SafeAdd(totalPrice, itemPrice)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay total price calculation overflow")
			return nil, fmt.Errorf("total price calculation error: %w", err)
		}
		totalPrice = newTotalPrice
	}

	// 计算运费
	freightFeeRule, err := e.getFreightFeeRule(ctx)
	var freightFee uint64
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("DirectPay get freight fee rule failed")
		// 获取配置失败时，不影响订单流程，运费设为0
		freightFee = 0
	} else {
		// 根据商品总价（未计算优惠）计算运费
		freightFee = configDao.CalculateFreightFee(totalPrice, freightFeeRule)
	}

	tradeNo := helper.GetAppNo(helper.AppNoBoxTrade)

	// 总金额需要加上运费
	totalFeeWithFreight, err := decimalUtil.SafeAdd(totalPrice, freightFee)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPay total fee with freight calculation overflow")
		return nil, fmt.Errorf("total fee with freight calculation error: %w", err)
	}

	payMethod := req.PayMethod
	usedFee := totalFeeWithFreight
	//sourcePlatform := checkSourcePlatform(req.ClientType)

	//组装订单数据
	orderInfo := &orderDao.Model{
		PurchaseType: STRATEGY_DIRECT,
		OutTradeNo:   tradeNo,
		ActiveID:     req.ActiveID,
		UserID:       req.UserID,
		BoxID:        req.BoxID,
		TotalFee:     totalFeeWithFreight, // 总费用包含运费
		LotteryTotal: total,
		LotteryLeft:  0,
		OrderStatus:  uint32(orderDao.OrderStatusCreated),
		FeeType:      "CNY",
		Fee:          totalPrice, // 商品单价不含运费
		FreightFee:   freightFee, // 保存运费
		ConsumeType:  uint32(orderDao.ConsumeTypeDirect),
		ConsumeMsg:   "直接购买商品",
		OrderType:    uint32(orderDao.OrderTypeDirect),
		UsedFee:      usedFee,
		PayMethod:    req.PayMethod,
		DeliveryID:   req.DeliveryID,
		ShopID:       req.ShopID,
		AddressID:    req.AddressID,
	}

	if couponFee > 0 {
		orderInfo.CouponFee = couponFee
		// 使用安全减法避免下溢
		usedFeeAfterCoupon, err := decimalUtil.SafeSubtract(orderInfo.UsedFee, couponFee)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay UsedFee subtraction error")
			return nil, fmt.Errorf("coupon fee calculation error: %w", err)
		}
		orderInfo.UsedFee = usedFeeAfterCoupon
		orderInfo.CouponID = req.CouponID
	}

	// 处理积分抵扣
	var pointsDiscount uint64
	var actualPointsUsed uint64
	if req.UsePoints == 1 {
		pointsResult, err := e.calculateAndApplyPointsDiscount(ctx, &PointsDiscountRequest{
			UserID:      req.UserID,
			TotalAmount: orderInfo.UsedFee, // 使用优惠券后的金额作为基准
			MaxPercent:  95,                // 最大抵扣95%，确保至少支付5%
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay points discount calculation failed")
			return nil, err
		}

		if pointsResult != nil && pointsResult.DiscountAmount > 0 {
			pointsDiscount = pointsResult.DiscountAmount
			actualPointsUsed = pointsResult.ActualPointsUsed

			// 使用安全减法计算积分抵扣后的金额
			usedFeeAfterPoints, err := decimalUtil.SafeSubtract(orderInfo.UsedFee, pointsDiscount)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("DirectPay points discount calculation error")
				return nil, fmt.Errorf("points discount calculation error: %w", err)
			}

			orderInfo.PointsFee = pointsDiscount
			orderInfo.PointsUse = actualPointsUsed // 保存消耗的积分数量
			orderInfo.UsedFee = usedFeeAfterPoints

			log.Ctx(ctx).Info("DirectPay applied points discount: UserID=%d, PointsDiscount=%d, PointsUsed=%d, FinalAmount=%d",
				req.UserID, pointsDiscount, actualPointsUsed, orderInfo.UsedFee)
		}
	}

	// 创建订单并生成trade记录
	err = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		// 先创建订单
		if err := e.OrderRepo.CreateOrUpdateWithTx(ctx, tx, orderInfo); err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay create order")
			return err
		}

		// 处理优惠券状态更新
		if req.CouponID > 0 {
			if err := cUserSrv.GetService().UpdateUserCouponStatus(ctx, tx, req.CouponID, cUserDao.CUserUsing, tradeNo, couponFee); err != nil {
				log.Ctx(ctx).WithError(err).Error("DirectPay update coupon status")
				return fmt.Errorf("failed to update coupon status: %w", err)
			}
		}

		// 处理积分抵扣（在事务中扣减用户积分）
		if actualPointsUsed > 0 {
			userSrv := user.GetService()
			if err := userSrv.UsePointsDiscountWithTx(ctx, tx, req.UserID, actualPointsUsed, tradeNo); err != nil {
				log.Ctx(ctx).WithError(err).Error("DirectPay use points discount")
				return fmt.Errorf("failed to use points discount: %w", err)
			}
			log.Ctx(ctx).Info("DirectPay successfully deducted points: UserID=%d, Points=%d", req.UserID, actualPointsUsed)
		}

		// 批量查询SKU和SPU信息 - 优化N+1查询问题
		skuIDs := make([]uint64, 0, len(req.CartItems))
		for _, item := range req.CartItems {
			skuIDs = append(skuIDs, item.SkuID)
		}

		// 批量查询SKU信息
		skuList, err := e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{
			Ids: skuIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay batch fetch SKU error")
			return fmt.Errorf("failed to batch find SKUs: %w", err)
		}

		// 将SKU结果转换为map
		skuModels := make(map[uint64]*skuDao.Model)
		spuIDs := make([]uint64, 0, len(skuList))
		for _, skuModel := range skuList {
			skuModels[skuModel.ID] = skuModel
			spuIDs = append(spuIDs, skuModel.SpuId)
		}

		// 检查是否有SKU未找到
		for _, skuID := range skuIDs {
			if _, exists := skuModels[skuID]; !exists {
				return fmt.Errorf("SKU %d not found", skuID)
			}
		}

		// 批量查询SPU信息
		spuList, err := e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{
			Ids: spuIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectPay batch fetch SPU error")
			return fmt.Errorf("failed to batch find SPUs: %w", err)
		}

		// 将SPU结果转换为map
		spuModels := make(map[uint64]*spuDao.Model)
		for _, spuModel := range spuList {
			spuModels[spuModel.ID] = spuModel
		}

		// 为每个SKU+数量组合创建trade记录
		for _, item := range req.CartItems {
			// 获取SKU信息
			skuModel, exists := skuModels[item.SkuID]
			if !exists {
				return fmt.Errorf("SKU %d not found", item.SkuID)
			}

			// 获取SPU信息
			spuModel, exists := spuModels[skuModel.SpuId]
			if !exists {
				return fmt.Errorf("SPU %d not found for SKU %d", skuModel.SpuId, item.SkuID)
			}

			// 检查库存并锁定
			availableStock := skuModel.GetUsableNum()
			if availableStock < item.Quantity {
				return fmt.Errorf("insufficient stock for SKU %d: required %d, available %d",
					item.SkuID, item.Quantity, availableStock)
			}

			// 锁定库存
			lockUpdates := map[string]interface{}{
				"lock_num": skuModel.LockNum + item.Quantity,
			}
			if err := e.SkuRepo.UpdateMapByIDWithTx(ctx, tx, item.SkuID, lockUpdates); err != nil {
				log.Ctx(ctx).WithError(err).Error("DirectPay lock stock error")
				return fmt.Errorf("failed to lock stock for SKU %d: %w", item.SkuID, err)
			}

			// 创建trade记录
			tradeDetail := &orderDetailDao.Model{
				OrderID:        orderInfo.ID,
				ActiveID:       req.ActiveID,
				BoxID:          req.BoxID,
				BoxNo:          "",
				BoxSlot:        "", // 直接购买无槽位概念，设为空字符串
				GoodsID:        0,  // 直购模式不使用box_goods，设为0
				GoodsLevel:     "",
				UserID:         req.UserID,
				SpuID:          spuModel.ID,
				SkuID:          skuModel.ID,
				BatchID:        spuModel.BatchId,
				GoodsName:      skuModel.Title,
				ActiveTitle:    createOrderParams.ActiveInfo.Title,
				TradeStatus:    1, // 已支付（待支付回调确认）
				DeliveryStatus: 1, // 待发货
				Remark:         fmt.Sprintf("直接购买 - SKU: %s, 数量: %d", skuModel.Code, item.Quantity),
				ActiveType:     createOrderParams.ActiveInfo.ActType,
				Quantity:       item.Quantity, // 使用新的quantity字段存储数量
			}

			if err := e.TradeRepo.CreateOrUpdateWithTx(ctx, tx, tradeDetail); err != nil {
				log.Ctx(ctx).WithError(err).Error("DirectPay create trade detail")
				return fmt.Errorf("failed to create trade detail for SKU %d: %w", item.SkuID, err)
			}

			log.Ctx(ctx).Info("DirectPay created trade record: SkuID=%d, Quantity=%d", item.SkuID, item.Quantity)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 订单创建成功后，添加到超时队列
	// 设置30分钟支付超时时间
	if err := redis.OrderTimeoutQueue.AddOrderTimeout(ctx, orderInfo.ID, 30); err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectPay add order timeout queue failed", "orderID", orderInfo.ID)
		// 延迟队列失败，记录到数据库作为备用（可选实现）
		// TODO: 可以在订单表增加 timeout_fallback 字段，定时任务扫描处理
		log.Ctx(ctx).Warn("Order timeout will be handled by fallback mechanism", "orderID", orderInfo.ID)
	}

	switch payMethod {
	case 1: //微信支付
		wxPayRes, err := e.boxLotteryWechat(ctx, &BoxWxPayHandleReq{
			OrderInfo: orderInfo,
			UserInfo:  createOrderParams.UserInfo,
		})
		return wxPayRes, err
	default:
		err = errors.New(fmt.Sprintf("支付方式异常"))
		return
	}
}

// callPaySignOfApplet 调用微信SDK生成小程序支付签名
func (e *Entry) callPaySignOfApplet(ctx *gin.Context, prepayId string) (interface{}, error) {
	client, err := wechatOrderApi.GetApi().InitClientV3(ctx)
	if err != nil {
		return nil, err
	}
	appletParams, err := client.PaySignOfApplet(config.WechatCfg.AppletID, prepayId)
	if err != nil {
		return nil, err
	}
	return appletParams, nil
}

func (e *Entry) BoxPay(ctx *gin.Context, req *boxDto.BoxPayReq) (res interface{}, err error) {

	var (
		uc = GetUnderConstruct()
	)

	switch req.PurchaseType {
	case STRATEGY_DIRECT:
		// BoxPay 不处理直接购买类型
		return
	case STRATEGY_PURCHASE:
		if time.Now().After(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[0])*time.Hour)) && time.Now().Before(timeUtil.TruncateToDay(time.Now()).Add(time.Duration(uc[1])*time.Hour)) {
			errStr := fmt.Sprintf("%d-%d点为每日抽盒系统维护时间，无法操作", uc[0], uc[1])
			log.Ctx(ctx).Error(errStr)
			return
		}
	}

	if err = e.RedisCli.Lock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID), redis.DefaultLockTime); err != nil {
		return nil, ecode.SystemBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderUserIDLockKey(req.UserID))

	createOrderParams, err := e.checkCreateOrder(ctx, req)
	if err != nil {
		log.Ctx(ctx).Error("%+v CreateOrder CheckCreateOrder err is %s", req, err.Error())
		return
	}
	// 获取实际total值，处理指针类型
	var total uint32
	if req.PurchaseType == STRATEGY_DIRECT { // 直接购买
		if req.CartItems != nil {
			for _, item := range req.CartItems {
				total += item.Quantity
			}
		}
	} else { // 盲盒购买
		if req.Total != nil {
			total = *req.Total
		}
	}

	couponFee := uint64(0)
	if req.CouponID > 0 {
		couponInfo, err := cUserSrv.GetService().CheckWhenUseCoupon(
			ctx,
			req.UserID,
			req.CouponID,
			req.ActiveID,
			total)
		if err != nil {
			return nil, err
		}

		if couponInfo != nil {
			couponFee = couponInfo.DiscountAmount
		}
	}

	tradeNo := helper.GetAppNo(helper.AppNoBoxTrade)

	//总金额
	// 使用安全的乘法计算避免溢出
	totalPrice, err := decimalUtil.SafeMultiply(createOrderParams.ActiveInfo.ActPrice, total)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxPay price calculation overflow")
		return nil, fmt.Errorf("price calculation error: %w", err)
	}

	payMethod := req.PayMethod
	usedFee := totalPrice

	// 积分抵扣处理
	var pointsDiscount uint64
	var actualPointsUsed uint64
	if req.UsePoints == 1 {
		// 计算优惠券抵扣后的金额
		amountAfterCoupon, err := decimalUtil.SafeSubtract(totalPrice, couponFee)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxPay coupon discount calculation error")
			return nil, fmt.Errorf("coupon discount calculation error: %w", err)
		}

		pointsResult, err := e.calculateAndApplyPointsDiscount(ctx, &PointsDiscountRequest{
			UserID:      req.UserID,
			TotalAmount: amountAfterCoupon, // 使用优惠券后的金额作为基准
			MaxPercent:  95,                // 最大抵扣95%，确保至少支付5%
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxPay points discount calculation failed")
			return nil, err
		}

		if pointsResult != nil && pointsResult.DiscountAmount > 0 {
			pointsDiscount = pointsResult.DiscountAmount
			actualPointsUsed = pointsResult.ActualPointsUsed
			log.Ctx(ctx).Info("BoxPay applied points discount: UserID=%d, PointsDiscount=%d, PointsUsed=%d",
				req.UserID, pointsDiscount, actualPointsUsed)
		}
	}
	//sourcePlatform := checkSourcePlatform(req.ClientType)

	var boxSlot string

	if req.PurchaseType == STRATEGY_DIRECT { // 直接购买
		boxSlot = "" // 直接购买无槽位概念，设为空字符串
	} else {
		if req.Slot != nil {
			boxSlot = sliceUtil.UintSliJoin(*req.Slot, ",")
		}
	}

	//组装订单数据
	orderInfo := &orderDao.Model{
		OutTradeNo: tradeNo,
		ActiveID:   req.ActiveID,
		UserID:     req.UserID,
		BoxID:      req.BoxID,

		//BoxNo:          req.BoxNo,
		BoxSlot:      boxSlot,
		TotalFee:     totalPrice,
		LotteryTotal: total,
		LotteryLeft:  total,
		OrderStatus:  uint32(orderDao.OrderStatusCreated),
		FeeType:      "CNY",
		Fee:          createOrderParams.ActiveInfo.ActPrice,
		ConsumeType:  uint32(orderDao.ConsumeTypeBox),
		//SourcePlatform: sourcePlatform,
		ConsumeMsg:   createOrderParams.ActiveInfo.Title,
		OrderType:    uint32(orderDao.OrderTypeBox),
		UsedFee:      usedFee,
		PayMethod:    req.PayMethod,
		PurchaseType: req.PurchaseType,
	}

	if couponFee > 0 {
		orderInfo.CouponFee = couponFee
		// 使用安全减法避免下溢
		usedFeeAfterCoupon, err := decimalUtil.SafeSubtract(orderInfo.UsedFee, couponFee)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxPay(non-direct) UsedFee subtraction error")
			return nil, fmt.Errorf("coupon fee calculation error: %w", err)
		}
		orderInfo.UsedFee = usedFeeAfterCoupon
		orderInfo.CouponID = req.CouponID
	}

	// 处理积分抵扣
	if pointsDiscount > 0 {
		orderInfo.PointsFee = pointsDiscount
		orderInfo.PointsUse = actualPointsUsed // 保存消耗的积分数量
		// 使用安全减法计算积分抵扣后的金额
		usedFeeAfterPoints, err := decimalUtil.SafeSubtract(orderInfo.UsedFee, pointsDiscount)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxPay points discount calculation error")
			return nil, fmt.Errorf("points discount calculation error: %w", err)
		}
		orderInfo.UsedFee = usedFeeAfterPoints
	}

	switch payMethod {
	case 1: //微信支付
		wxPayRes, err := e.boxLotteryWechat(ctx, &BoxWxPayHandleReq{
			OrderInfo:        orderInfo,
			UserInfo:         createOrderParams.UserInfo,
			ActualPointsUsed: actualPointsUsed,
		})
		return wxPayRes, err
	default:
		return nil, ecode.BoxOrderPayMethodErr
	}
}

func (e *Entry) BoxPayTest(ctx *gin.Context, req *boxDto.BoxPayTestReq) (res interface{}, err error) {
	if err = e.RedisCli.Lock(ctx, redis.GetBoxOrderTradeNoLockKey(req.OutTradeNo), redis.DefaultLockTime); err != nil {
		return nil, ecode.BoxOrderAgainCreateOrder
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderTradeNoLockKey(req.OutTradeNo))

	var (
		orderParam = &boxCheckOrderInfoRes{}

		tradeDetails = make(orderDetailDao.ModelList, 0)
	)
	orderParam, err = e.boxCheckOrderInfo(ctx, req.OutTradeNo)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxWechatNotify boxCheckOrderInfo")
		return nil, err
	}

	tradeDetails, err = e.TradeRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderID: orderParam.OrderInfo.ID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxWechatNotify FindByFilter")
		return nil, err
	}

	if len(tradeDetails) > 0 ||
		lo.Contains([]uint32{uint32(orderDao.OrderStatusRefund), uint32(orderDao.OrderStatusPayOk)}, orderParam.OrderInfo.OrderStatus) {
		log.Ctx(ctx).Warn("BoxWechatNotify orderStatus is not correct")
		return
	}

	orderParam.OrderInfo.CashFee = req.TotalAmount
	orderParam.OrderInfo.TransactionId = req.TradeNo

	if err = e.RedisCli.RetryLock(ctx, redis.GetBoxOrderBoxIDLockKey(orderParam.OrderInfo.BoxID), redis.DefaultLockTime); err != nil {
		// TODO: 未获取到, 执行退款
	}

	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderBoxIDLockKey(orderParam.OrderInfo.BoxID))

	commonPayRes, err := e.BoxNotifyHandle(ctx, &BoxNotifyHandleReq{
		ActiveInfo: orderParam.ActiveInfo,
		BoxInfo:    orderParam.BoxInfo,
		OrderInfo:  orderParam.OrderInfo,
		UserInfo:   orderParam.UserInfo,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxWechatNotify BoxNotifyHandle")
		return
	}

	res = commonPayRes
	return res, nil
}

type BoxCheckCreateOrderRes struct {
	UserInfo   *userDao.UserAuth
	ActiveInfo *actDao.Model
	BoxInfo    *box_record.Model
}

func (e *Entry) checkCreateOrder(ctx *gin.Context, req *boxDto.BoxPayReq) (createOrderParams *BoxCheckCreateOrderRes, err error) {
	createOrderParams = &BoxCheckCreateOrderRes{}
	var (
		userModel   = &userDao.UserAuth{}
		activeModel = &actDao.Model{}
		boxModel    = &box_record.Model{}

		eg errgroup.Group
	)

	eg.Go(func() error {
		userModel, err = e.UserRepo.FetchAuthByUid(ctx, req.UserID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("CreateOrder FetchByID")
			err = ecode.SystemErr
			return err
		}
		if userModel == nil {
			return ecode.UserNotExistErr
		}

		return nil
	})

	if req.PurchaseType == STRATEGY_PURCHASE {
		eg.Go(func() error {

			if activeModel, err = e.checkActive(ctx, req.ActiveID); err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateOrder checkActive")
				return err
			}

			if err = e.checkActiveTime(ctx, activeModel); err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateOrder checkActiveTime")
				return err
			}

			return nil
		})

		eg.Go(func() error {
			boxModel, err = e.BoxRecordRepo.FetchByID(ctx, req.BoxID)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateOrder FetchByID")
				err = ecode.SystemErr
				return err
			}

			return nil
		})

		eg.Go(func() error {
			if _, err := e.AdminActiveRecommendCheck(ctx, &boxDto.AdminActiveRecommendCheckReq{
				ActiveID: req.ActiveID,
			}); err != nil {
				log.Ctx(ctx).Error("CreateOrder ActiveRecommendCheck err: %v", err)
				return ecode.SystemBusyErr
			}

			return nil
		})

		eg.Go(func() error {
			if boxModel, err = e.checkBox(ctx, req.BoxID); err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateOrder checkBox")
				return err
			}
			if boxModel.Len <= 0 {
				return ecode.BoxActiveNotExistErr
			}

			// 处理Slot字段的指针类型
			var slot []uint32
			if req.Slot != nil {
				slot = *req.Slot
			}

			if !e.checkAvaSlot(ctx, boxModel.AvaSlot, slot) {
				return ecode.BoxActiveErr
			}

			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateOrder errgroup.Wait")
		return nil, err
	}

	createOrderParams.UserInfo = userModel
	createOrderParams.ActiveInfo = activeModel
	createOrderParams.BoxInfo = boxModel

	return createOrderParams, nil
}

func (e *Entry) checkActive(ctx *gin.Context, activeID uint64) (activeModel *actDao.Model, err error) {
	activeModel, err = e.ActiveRepo.FetchByID(ctx, activeID)
	if err != nil {
		return nil, err
	}

	if activeModel == nil {
		return nil, ecode.ActNotExistErr
	}

	return activeModel, nil
}

func (e *Entry) checkActiveTime(ctx *gin.Context, activeModel *actDao.Model) (err error) {
	if activeModel == nil {
		return ecode.ActNotExistErr
	}

	tsToTime := func(timestamp int64) time.Time {
		return time.Unix(timestamp, 0)
	}

	if activeModel.StartTime > 0 {
		startTime := tsToTime(activeModel.StartTime)
		if time.Now().Before(startTime) {
			return ecode.BoxActiveStartTimeErr
		}
	}

	if activeModel.EndTime > 0 {
		endTime := tsToTime(activeModel.EndTime)
		if time.Now().After(endTime) {
			return ecode.BoxActiveEndTimeErr
		}
	}

	return nil
}

func (e *Entry) checkBox(ctx *gin.Context, boxID uint64) (boxModel *box_record.Model, err error) {
	boxModel, err = e.BoxRecordRepo.FetchByID(ctx, boxID)
	if err != nil {
		return nil, err
	}

	if boxModel == nil {
		return nil, ecode.BoxActiveNotExistErr
	}

	if boxModel.Cap <= 0 {
		return nil, ecode.BoxActiveNotExistErr
	}

	return boxModel, nil
}

func (e *Entry) checkAvaSlot(ctx *gin.Context, avaSlot string, slot []uint32) (check bool) {
	if len(avaSlot) == 0 || avaSlot == "" {
		return false
	}

	avaSlotSli := strings.Split(avaSlot, ",")
	//lockSlotSli := strings.Split(lockSlot, ",")
	//avaSlotSli = append(avaSlotSli, lockSlotSli...)
	setB := make(map[uint32]struct{})

	// 将数组B中的元素添加到集合setB中
	for _, s := range avaSlotSli {
		num, err := strconv.Atoi(s)
		if err != nil {
			continue
		}
		setB[uint32(num)] = struct{}{}
	}

	// 遍历数组A，如果有元素不在setB中，则返回false
	for _, num := range slot {
		if _, exists := setB[num]; !exists {
			return false
		}
	}

	return true
}

func (e *Entry) checkCount(ctx *gin.Context, req *boxDto.BoxItemUseReq, boxID uint64, boxActiveConf *box_active_config.Model) (err error) {
	if boxActiveConf == nil {
		return ecode.SystemBusyErr
	}

	var (
		count = int64(0)
	)

	if req.ICType == cardConfDao.CardType_yao {
		count, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:    req.UserID,
			CardType:  req.ICType,
			Status:    card_user.UserItemCardStatusUnused,
			StartTime: timeUtil.TruncateToDay(time.Now()).Unix(),
			EndTime:   timeUtil.TruncateToDayEnd(time.Now()).Unix(),
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("checkCount CountByFilter")
			return err
		}
		if t := 12; count > 12 {
			return fmt.Errorf("摇一摇每天最多使用%d次", t)
		}
	}

	switch req.ICType {
	case cardConfDao.CardType_yao, cardConfDao.CardType_tip:
		count, err = e.BoxActionDetailRepo.CountByFilter(ctx,
			&box_action_detail.Filter{
				UserID:     req.UserID,
				BoxID:      boxID,
				ActionType: box_action_detail.ACTION_TYPE_2,
				Slot:       req.Slot[0],
			})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("checkCount CountByFilter")
			return err
		}

		if count >= int64(boxActiveConf.LotteryNum-1) {
			t := fmt.Sprintf("一个位置下提示类道具最多使用%d次", boxActiveConf.LotteryNum-1)
			return errors.New(t)
		}

	case cardConfDao.CardType_tou:
		count, err = e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   req.UserID,
			CardType: req.ICType,
			Status:   card_user.UserItemCardStatusUsed,
			BoxID:    boxID,
			Slot:     req.Slot[0],
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("checkCount CountByFilter")
			return err
		}
		if count >= 1 {
			t := fmt.Sprintf("透视卡最多使用%d次", 1)
			return errors.New(t)
		}
	default:
		return ecode.BoxCardNotExistErr
	}

	return nil
}

func (e *Entry) checkItemCard(ctx *gin.Context, icId uint64, icType uint32, userId uint64) (ic *card_user.Model, err error) {
	ic = &card_user.Model{}
	if icType == cardConfDao.CardType_yao {
		ic.CardType = cardConfDao.CardType_yao
		return ic, nil
	}

	if icId == 0 {
		cnt, err := e.UserCardRepo.CountByFilter(ctx, &card_user.Filter{
			UserID:   userId,
			CardType: icType,
			Status:   card_user.UserItemCardStatusUnused,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("checkItemCard CountByFilter")
			return nil, err
		}
		if cnt <= 0 {
			return nil, ecode.BoxCardNotExistErr
		}

		ic, err = e.UserCardRepo.FetchUserCardExpireFirst(ctx, icType, userId)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("checkItemCard FetchUserCardExpireFirst")
			return nil, err
		}
		return ic, nil
	}

	var iccList = make([]*cardConfDao.Model, 0)

	ic, err = e.UserCardRepo.FetchByID(ctx, icId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkItemCard FetchByID")
		return nil, err
	}
	if ic == nil {
		return nil, ecode.BoxCardNotExistErr
	}
	if ic.UserID != userId {
		return nil, ecode.BoxCardNotExistErr
	}
	if ic.Status != card_user.UserItemCardStatusUnused {
		return nil, ecode.BoxCardNotExistErr
	}
	if ic.CardType != icType {
		return nil, ecode.BoxCardNotExistErr
	}

	iccList, err = e.CardConfRepo.FindByFilter(ctx, &cardConfDao.Filter{
		CardCode: ic.CardCode,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkItemCard FindByFilter")
		return nil, err
	}
	if len(iccList) <= 0 {
		return nil, ecode.BoxCardNotExistErr
	}
	if iccList[0].CardStatus != uint32(dbs.StatusEnable) {
		return nil, ecode.BoxCardNotExistErr
	}

	return ic, nil
}

type BoxWxPayHandleReq struct {
	CliType          int               `form:"cliType"`
	OrderInfo        *orderDao.Model   //订单信息
	UserInfo         *userDao.UserAuth //用户信息
	ClientIp         string
	ActualPointsUsed uint64 // 实际使用的积分数量
}

func (e *Entry) boxLotteryWechat(ctx *gin.Context, req *BoxWxPayHandleReq) (res *boxDto.BoxWxPayRes, err error) {
	res = &boxDto.BoxWxPayRes{}
	var (
		orderInfo = req.OrderInfo

		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	orderInfo.OrderStatus = uint32(orderDao.OrderStatusPayIng)

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			buf := make([]byte, 10<<10)
			runtime.Stack(buf, true)
			log.Ctx(ctx).WithField("stack", string(buf)).Error("BoxLotteryWechat panic")
			err = ecode.SystemErr
		}
	}()

	if orderInfo.CouponID > 0 {
		err = cUserSrv.GetService().UpdateUserCouponStatus(
			ctx, tx, orderInfo.CouponID, cUserDao.CUserUsing, orderInfo.OutTradeNo, orderInfo.CouponFee)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxLotteryWechat UpdateUserCouponStatus")
			tx.Rollback()
			return nil, err
		}
	}

	// 处理积分抵扣（在事务中扣减用户积分）
	if req.ActualPointsUsed > 0 {
		userSrv := user.GetService()
		if err := userSrv.UsePointsDiscountWithTx(ctx, tx, orderInfo.UserID, req.ActualPointsUsed, orderInfo.OutTradeNo); err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxLotteryWechat use points discount")
			tx.Rollback()
			return nil, fmt.Errorf("failed to use points discount: %w", err)
		}
		log.Ctx(ctx).Info("BoxLotteryWechat successfully deducted points: UserID=%d, Points=%d", orderInfo.UserID, req.ActualPointsUsed)
	}

	// 设置支付过期时间（15分钟后）
	expireTime := time.Now().Add(time.Duration(15) * time.Minute)
	orderInfo.PayExpireTime = expireTime.Unix()

	err = e.OrderRepo.CreateOrUpdateWithTx(ctx, tx, orderInfo)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxLotteryWechat CreateOrUpdateWithTx")
		tx.Rollback()
		return nil, err
	}

	payRes, err := order.GetApi().GopayOrderPay(
		ctx,
		tx,
		req.UserInfo,
		orderInfo.UsedFee,
		orderInfo.OutTradeNo,
		common2.WxpayOrderInfo{
			// TODO: 这里需要传入的参数
			TimeExpire: expireTime.Format(time.RFC3339),
		},
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxLotteryWechat GopayOrderPay")
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	// 订单创建成功后，添加到超时队列
	// 设置15分钟支付超时时间（与PayExpireTime保持一致）
	if err := redis.OrderTimeoutQueue.AddOrderTimeout(ctx, orderInfo.ID, 15); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxLotteryWechat add order timeout queue failed", "orderID", orderInfo.ID)
		// 延迟队列失败，记录到数据库作为备用（可选实现）
		// TODO: 可以在订单表增加 timeout_fallback 字段，定时任务扫描处理
		log.Ctx(ctx).Warn("Order timeout will be handled by fallback mechanism", "orderID", orderInfo.ID)
	}

	// string to int64
	timestamp, _ := strconv.ParseInt(payRes.TimeStamp, 10, 64)

	res = &boxDto.BoxWxPayRes{
		OrderID:   orderInfo.ID,
		TradeType: "",
		TimeStamp: timestamp,
		NonceStr:  payRes.NonceStr,
		Package:   payRes.Package,
		TradeNo:   "",
		SignType:  payRes.SignType,
		PaySign:   payRes.PaySign,
	}

	return res, nil
}

// 微信支付回调处理相关常量已移至ValidateStep内部

// WechatNotifyContext 微信回调处理上下文
type WechatNotifyContext struct {
	Request       *boxDto.BoxWechatNotifyReq
	OrderModel    *orderDao.Model
	OrderParam    *boxCheckOrderInfoRes
	TradeDetails  orderDetailDao.ModelList
	IsDirectOrder bool
	StartTime     time.Time              // 处理开始时间
	StepResults   map[string]interface{} // 步骤结果缓存
}

// WechatNotifyStep 微信回调处理步骤接口
type WechatNotifyStep interface {
	Name() string
	Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error
	ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool
}

// WechatNotifyPipeline 微信回调处理管道
type WechatNotifyPipeline struct {
	steps []WechatNotifyStep
}

// NewWechatNotifyPipeline 创建微信回调处理管道
func NewWechatNotifyPipeline() *WechatNotifyPipeline {
	return &WechatNotifyPipeline{
		steps: make([]WechatNotifyStep, 0),
	}
}

// AddStep 添加处理步骤
func (p *WechatNotifyPipeline) AddStep(step WechatNotifyStep) *WechatNotifyPipeline {
	p.steps = append(p.steps, step)
	return p
}

// Execute 执行管道
func (p *WechatNotifyPipeline) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	for _, step := range p.steps {
		if step.ShouldSkip(ctx, notifyCtx) {
			log.Ctx(ctx).Info("WechatNotifyPipeline skipping step: %s", step.Name())
			continue
		}

		log.Ctx(ctx).Info("WechatNotifyPipeline executing step: %s", step.Name())
		stepStartTime := time.Now()

		if err := step.Execute(ctx, notifyCtx); err != nil {
			log.Ctx(ctx).WithError(err).Error("WechatNotifyPipeline step failed: %s", step.Name())
			return fmt.Errorf("step %s failed: %w", step.Name(), err)
		}

		stepDuration := time.Since(stepStartTime)
		log.Ctx(ctx).Info("WechatNotifyPipeline step completed: %s, duration: %v", step.Name(), stepDuration)
	}
	return nil
}

func (e *Entry) BoxWechatNotify(ctx *gin.Context, req *boxDto.BoxWechatNotifyReq) (res interface{}, err error) {
	startTime := time.Now()

	// 初始化处理上下文
	notifyCtx := &WechatNotifyContext{
		Request:     req,
		StartTime:   startTime,
		StepResults: make(map[string]interface{}),
	}

	// 记录处理开始
	log.Ctx(ctx).Info("BoxWechatNotify started: OutTradeNo=%s", req.OutTradeNo)

	// 构建处理管道
	pipeline := NewWechatNotifyPipeline().
		AddStep(&TradeNoLockStep{entry: e}).
		AddStep(&ContextBuildStep{entry: e}).
		AddStep(&ValidateStep{entry: e}).
		AddStep(&PaymentInfoUpdateStep{entry: e}).
		AddStep(&BusinessLockStep{entry: e}).
		AddStep(&BusinessExecuteStep{entry: e})

	// 设置defer清理函数
	defer func() {
		e.cleanupNotifyContext(ctx, notifyCtx)

		// 记录处理结果和耗时
		duration := time.Since(startTime)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxWechatNotify failed: OutTradeNo=%s, Duration=%v",
				req.OutTradeNo, duration)
		} else {
			log.Ctx(ctx).Info("BoxWechatNotify completed: OutTradeNo=%s, Duration=%v",
				req.OutTradeNo, duration)
		}
	}()

	// 执行处理管道
	if err = pipeline.Execute(ctx, notifyCtx); err != nil {
		return nil, fmt.Errorf("wechat notify pipeline failed: %w", err)
	}

	// 返回业务处理结果
	if result, exists := notifyCtx.StepResults["businessResult"]; exists {
		return result, nil
	}

	// 如果没有业务结果（比如跳过了处理），返回nil
	return nil, nil
}

// cleanupNotifyContext 清理通知上下文的资源
func (e *Entry) cleanupNotifyContext(ctx *gin.Context, notifyCtx *WechatNotifyContext) {
	// 释放业务锁
	if unlockFunc, exists := notifyCtx.StepResults["businessLockUnlock"].(func()); exists {
		unlockFunc()
		log.Ctx(ctx).Info("BoxWechatNotify released business lock")
	}

	// 释放交易号锁
	if unlockFunc, exists := notifyCtx.StepResults["tradeLockUnlock"].(func()); exists {
		unlockFunc()
		log.Ctx(ctx).Info("BoxWechatNotify released trade lock")
	}
}

// 已移除旧的处理函数，现在使用Pipeline模式的步骤实现

type boxCheckOrderInfoRes struct {
	OrderInfo  *orderDao.Model
	UserInfo   *userDao.Model
	ActiveInfo *actDao.Model
	BoxInfo    *box_record.Model
}

func (e *Entry) boxCheckOrderInfo(ctx *gin.Context, tradeNo string) (res *boxCheckOrderInfoRes, err error) {
	res = &boxCheckOrderInfoRes{}

	var (
		orderModel  = &orderDao.Model{}
		userModel   = &userDao.Model{}
		activeModel = &actDao.Model{}
		boxModel    = &box_record.Model{}

		eg errgroup.Group
	)

	orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, tradeNo)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCheckOrderInfo FetchByOutTradeNo")
		return nil, err
	}
	if orderModel == nil {
		err = ecode.BoxOrderNotExistErr
		return res, nil
	}

	res.OrderInfo = orderModel

	eg.Go(func() error {
		userModel, err = e.UserRepo.FetchByID(ctx, orderModel.UserID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxCheckOrderInfo FetchByID")
			return err
		}
		if userModel == nil {
			err = ecode.UserNotExistErr
			return err
		}

		res.UserInfo = userModel
		return nil
	})

	if orderModel.OrderType == uint32(orderDao.OrderTypeBox) {
		eg.Go(func() error {
			activeModel, err = e.ActiveRepo.FetchByID(ctx, orderModel.ActiveID)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxCheckOrderInfo FetchByID")
				return err
			}
			if activeModel == nil {
				err = ecode.ActNotExistErr
				return err
			}

			res.ActiveInfo = activeModel
			return nil
		})

		eg.Go(func() error {
			boxModel, err = e.checkBox(ctx, orderModel.BoxID)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxCheckOrderInfo checkBox")
				return err
			}
			if boxModel == nil {
				err = ecode.BoxBagNotExistErr
				return err
			}

			if boxModel.Len <= 0 {
				err = ecode.BoxBagNotExistErr
				return err
			}

			res.BoxInfo = boxModel
			return nil
		})
	}

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCheckOrderInfo errgroup.Wait")
		return
	}

	return res, nil
}

type BoxNotifyHandleReq struct {
	ActiveInfo    *actDao.Model
	BoxInfo       *box_record.Model
	OrderInfo     *orderDao.Model
	UserInfo      *userDao.Model
	IsRetryFinish bool
	RetryID       uint64 // TODO: ?
}

type BoxNotifyHandleRes struct {
	OrderID   uint64
	SpOrderID []uint64
	TimeStamp int64
}

func (e *Entry) BoxNotifyHandle(ctx *gin.Context, req *BoxNotifyHandleReq) (commonPayRes *BoxNotifyHandleRes, err error) {
	commonPayRes = &BoxNotifyHandleRes{}

	var (
		// TODO: magic num = 1
		tradeOperationRecordChangeType = 1
		timeNow                        = time.Now()
		tx                             = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			buf := make([]byte, 10<<10)
			runtime.Stack(buf, true)
			log.Ctx(ctx).WithField("stack", string(buf)).Error("BoxNotifyHandle panic")
			err = ecode.SystemErr
		}
	}()

	// 0) 行级锁 + 幂等校验（确保锁顺序与取消一致）
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(req.OrderInfo, req.OrderInfo.ID).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if req.OrderInfo.OrderStatus != uint32(orderDao.OrderStatusPayIng) {
		tx.Rollback()
		log.Ctx(ctx).Info("BoxNotifyHandle idempotent: order already processed",
			"orderID", req.OrderInfo.ID, "status", req.OrderInfo.OrderStatus)
		return commonPayRes, nil
	}

	// 解析SKU参数（如果是直接购买）
	var skuID uint64
	var quantity uint32
	var slot []uint32

	if req.OrderInfo.PurchaseType == 3 { // 直接购买
		// 从BoxSlot解析数量信息
		if strings.HasPrefix(req.OrderInfo.BoxSlot, "DIRECT_") {
			quantityStr := strings.TrimPrefix(req.OrderInfo.BoxSlot, "DIRECT_")
			if q, err := strconv.ParseUint(quantityStr, 10, 32); err == nil {
				quantity = uint32(q)
			}
		}
		// 注意：这里需要从其他地方获取SPU/SKU信息，因为订单表可能还没有这些字段
		// 暂时使用0值，实际应用中需要根据订单查找对应的商品信息
		slot = []uint32{} // 直接购买无槽位
	} else {
		slot = sliceUtil.SplitStrToUintSli(req.OrderInfo.BoxSlot, ",")
	}

	selectRes, err := e.payBox(ctx, tx, &BoxSelectReq{
		ActiveInfo:   req.ActiveInfo,
		OrderInfo:    req.OrderInfo,
		Slot:         slot,
		PurchaseType: req.OrderInfo.PurchaseType,
		SkuID:        skuID,
		Quantity:     quantity,
	})
	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle payBox")
		return nil, ecode.SystemErr
	}

	if selectRes == nil {
		tx.Rollback()
		log.Ctx(ctx).Error("BoxNotifyHandle payBox selectRes is nil")
		return nil, ecode.SystemErr
	}

	orderInfo := req.OrderInfo
	updateMap := make(map[string]interface{})
	if selectRes.NoUsedTotal == req.OrderInfo.LotteryTotal {
		orderInfo.Remark = "该订单一发未抽, 已退款"
		updateMap["remark"] = orderInfo.Remark
		orderInfo.OrderStatus = uint32(orderDao.OrderStatusRefund)

	} else {
		orderInfo.OrderStatus = uint32(orderDao.OrderStatusPayOk)
	}
	updateMap["order_status"] = orderInfo.OrderStatus
	updateMap["pay_time"] = timeNow.Unix()

	lotteryLeft := selectRes.NoUsedTotal
	refundBalance := uint64(0)
	if lotteryLeft > 0 {
		noUsedAmount, err := e.BoxCheckRefundBalance(ctx, tx, orderInfo, selectRes.NoUsedTotal)
		if err != nil {
			tx.Rollback()
			return commonPayRes, err
		}

		if noUsedAmount > orderInfo.UsedFee {
			refundBalance = orderInfo.UsedFee
		} else {
			refundBalance = noUsedAmount
		}
	}

	orderInfo.LotteryLeft = lotteryLeft
	updateMap["lottery_left"] = lotteryLeft
	updateMap["pay_time"] = timeNow.Unix()
	orderInfo.TransactionId = req.OrderInfo.TransactionId
	updateMap["transaction_id"] = orderInfo.TransactionId

	if selectRes.IsLast {
		if err = e.ActiveRepo.UpdateMapByIDWithTx(ctx, tx, req.ActiveInfo.ID, map[string]interface{}{
			"act_status": actDao.ActStatusSaleout,
		}); err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle UpdateMapByIDWithTx")
			return nil, err
		}
	}

	err = e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, req.OrderInfo.ID, updateMap)
	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle UpdateMapByIDWithTx")
		return nil, err
	}

	if orderInfo.CouponID > 0 && orderInfo.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
		err = cUserSrv.GetService().UpdateUserCouponStatus(
			ctx, tx, orderInfo.CouponID, cUserDao.CUserUsed, orderInfo.OutTradeNo, orderInfo.CouponFee)
		if err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle UpdateUserCouponStatus")
			return nil, err
		}
	}

	// Redis 操作异步执行，避免影响主要业务流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Ctx(ctx).Error("goroutine panic in BoxNotifyHandle: %v", r)
			}
		}()
		err := tideDao.GetRepo().RedisSetUserTideTaskStatus(ctx, req.UserInfo.ID, tideDao.TideTaskTypeDraw, orderInfo.LotteryTotal)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle RedisSetUserTideTaskStatus")
		}
	}()

	var (
		userLotteryNum   = uint32(0)
		activeLotteryNum = uint32(0)
		normalLotteryNum = uint32(0)
		secretLotteryNum = uint32(0)

		actionDetail = &box_action_detail.Model{}
	)

	actionDetail, err = e.BoxActionDetailRepo.FindByFilterAtLast(ctx, &box_action_detail.Filter{
		BoxID: orderInfo.BoxID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle FindByFilterAtLast")
		return nil, err
	}
	if actionDetail != nil {
		activeLotteryNum = actionDetail.ActiveLotteryNum
		normalLotteryNum = actionDetail.NormalLotteryNum
		secretLotteryNum = actionDetail.SecretLotteryNum
	}

	userActionDetailCnt, err := e.BoxActionDetailRepo.CountByFilter(ctx, &box_action_detail.Filter{
		UserID:     orderInfo.UserID,
		ActiveID:   orderInfo.ActiveID,
		ActionType: box_action_detail.ACTION_TYPE_1,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle CountByFilter")
		return nil, err
	}
	if userActionDetailCnt > 0 {
		userLotteryNum = uint32(userActionDetailCnt)
	} else {
		userLotteryNum = 0
	}

	err = e.BatchBoxTrade(
		ctx,
		tx,
		uint32(tradeOperationRecordChangeType),
		orderInfo,
		selectRes.TradeInfos,
		req.ActiveInfo,
	)
	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle BatchBoxTrade")
		return nil, err
	}

	var (
		records = make(box_action_detail.ModelList, 0)
	)

	for _, info := range selectRes.TradeInfos {
		activeLotteryNum = activeLotteryNum + 1
		if info.GoodsLevel == box_level.BoxLevelNormal {
			normalLotteryNum = normalLotteryNum + 1
		} else {
			secretLotteryNum = secretLotteryNum + 1
		}

		userLotteryNum = userLotteryNum + 1

		temp := box_action_detail.NewBoxActionDetail(
			orderInfo.UserID,
			&box_record.Model{
				ModelWithDel: dbs.ModelWithDel{
					BaseModel: dbs.BaseModel{
						ID: orderInfo.BoxID,
					},
				},
			},
			info.BoxSlot,
			box_action_detail.ACTION_TYPE_1,
			&box_action_detail.BoxLotteryNum{
				UserLotteryNum:   userLotteryNum,
				ActiveLotteryNum: activeLotteryNum,
				NormalLotteryNum: normalLotteryNum,
				SecretLotteryNum: secretLotteryNum,
			},
			orderInfo, info,
		)

		// 使用安全除法避免精度损失和除零错误
		if orderInfo.LotteryTotal == 0 {
			log.Ctx(ctx).Error("BoxNotifyHandle: LotteryTotal is zero, cannot calculate average fee")
			temp.UsedFee = 0
		} else {
			temp.UsedFee = decimalUtil.Div(orderInfo.UsedFee, uint64(orderInfo.LotteryTotal))
		}
		records = append(records, temp)

		// 串行执行 ChangeBoxGoodsMapStock
		err = e.ChangeBoxGoodsMapStock(ctx, tx, info.GoodsID, &changeBoxGoodsStock{
			ChangeNumber:    -1,
			UserId:          orderInfo.UserID,
			TradeDetailList: []*orderDetailDao.Model{info}})
		if err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle ChangeBoxGoodsMapStock")
			return nil, err
		}
	}

	if len(records) > 0 {
		err = e.BoxActionDetailRepo.BatchCreateWithTx(ctx, tx, records)
		if err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("BoxNotifyHandle BatchCreateWithTx")
			return nil, err
		}
	}

	if refundBalance > 0 {
		// TODO: refund

	}

	tx.Commit()

	// TODO: set order LOCK?

	commonPayRes.OrderID = orderInfo.ID
	commonPayRes.TimeStamp = timeNow.Unix()

	return commonPayRes, nil
}

func (e *Entry) BoxCheckRefundBalance(ctx *gin.Context, tx *gorm.DB, orderInfo *orderDao.Model, noUsedTotal uint32) (refundBalance uint64, err error) {
	if orderInfo.CouponID <= 0 {
		// 使用安全乘法避免溢出
		refundBalance, err = decimalUtil.SafeMultiply(orderInfo.Fee, uint32(noUsedTotal))
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance simple refund calculation overflow")
			return 0, err
		}
		return
	}

	couponUser, err := e.UserCouponRepo.FetchByID(ctx, orderInfo.CouponID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance FetchByID")
		return 0, err
	}

	couponModel, err := e.CouponRepo.FetchByID(ctx, couponUser.CouponID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance FetchByID")
		return 0, err
	}

	if couponModel == nil {
		log.Ctx(ctx).Error("BoxCheckRefundBalance couponModel is nil")
		return 0, ecode.SystemBusyErr
	}

	if couponModel.DiscountType == uint32(couponDao.CouponDTWholeOrder) {
		// 使用安全计算避免溢出
		refundBalance, err = decimalUtil.SafeMultiply(orderInfo.UsedFee, uint32(noUsedTotal/orderInfo.LotteryTotal))
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance refund calculation overflow")
			return 0, err
		}

		couponFee, err := decimalUtil.SafeMultiply(orderInfo.CouponFee, uint32(orderInfo.LotteryTotal-noUsedTotal/orderInfo.LotteryTotal))
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance coupon fee calculation overflow")
			return 0, err
		}

		err = e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, orderInfo.ID, map[string]interface{}{
			"coupon_fee": couponFee,
		})
		if err != nil {
			return 0, err
		}

	}
	refundBalance, err = decimalUtil.SafeMultiply(orderInfo.Fee, uint32(noUsedTotal))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BoxCheckRefundBalance final refund calculation overflow")
		return 0, err
	}
	return
}

func (e *Entry) BatchBoxTrade(ctx *gin.Context, tx *gorm.DB, changeType uint32,
	orderInfo *orderDao.Model,
	tradeInfos orderDetailDao.ModelList,
	activeInfo *actDao.Model,
) error {
	if len(tradeInfos) <= 0 {
		return nil
	}
	var (
		goodsIDs       = tradeInfos.GetGoodsIDs()
		goodsModelList = make(box_goods.ModelList, 0)
		goodsMap       = make(map[uint64]*box_goods.Model)
		spuMap         = make(map[uint64]*spuDao.Model)

		err error
	)

	goodsModelList, err = e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
		IDs:      goodsIDs,
		ActiveID: activeInfo.ID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BatchBoxTrade FindByFilter")
		return err
	}

	if len(goodsModelList) <= 0 {
		log.Ctx(ctx).Error("BatchBoxTrade goodsModelList is nil")
		return ecode.SystemBusyErr
	}

	goodsMap = goodsModelList.GetIDMap()
	spuIDs := goodsModelList.GetSpuIDs()

	if len(spuIDs) > 0 {
		spuModelList, err := e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{
			Ids: spuIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("BatchBoxTrade FindByFilter")
			return err
		}
		spuMap = spuModelList.GetIdMap()
	}

	for _, v := range tradeInfos {
		if t, ok := goodsMap[v.GoodsID]; ok {
			v.GoodsLevel = t.LevelName
			if spu, ok := spuMap[t.SpuID]; ok {
				v.BatchID = spu.BatchId
			}
		}
		v.ActiveType = activeInfo.ActType
		v.OrderID = orderInfo.ID
	}

	if err = e.TradeRepo.BatchCreateWithTx(ctx, tx, tradeInfos); err != nil {
		log.Ctx(ctx).WithError(err).Error("BatchBoxTrade BatchCreateWithTx")
		return err
	}

	return nil
}

type changeBoxGoodsStock struct {
	ChangeNumber    int64  `json:"changeNumber"` //库存数量
	UserId          uint64 `json:"userId"`       //用户id
	TradeDetailList []*orderDetailDao.Model
}

func (e *Entry) ChangeBoxGoodsMapStock(
	ctx *gin.Context,
	tx *gorm.DB,
	goodsID uint64,
	changeGoodsStock *changeBoxGoodsStock,
) (err error) {
	if err = e.RedisCli.RetryLock(ctx, redis.GetBoxOrderGoodsIDLockKey(goodsID), redis.DefaultLockTime); err != nil {
		return ecode.BoxActiveGoodsNotEnoughErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderGoodsIDLockKey(goodsID))

	var (
		goodsStockLogModelList = make(box_goods_stock_log.ModelList, 0)
	)

	// ✅ 安全的库存扣减逻辑：先检查锁定库存，再决定扣减方式
	goodsModel, err := e.BoxGoodsRepo.FetchByID(ctx, goodsID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ChangeBoxGoodsMapStock FetchByID")
		return err
	}
	if goodsModel == nil {
		log.Ctx(ctx).Error("ChangeBoxGoodsMapStock goods not found")
		return ecode.BoxGoodsNotExistErr
	}

	// 根据锁定库存情况选择扣减方式，避免重复扣减
	if goodsModel.LockStock >= 1 {
		// 有锁定库存，从锁定库存扣减
		_, err = e.BoxGoodsRepo.ReduceGoodsLockStock(ctx, tx, goodsID, 1)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("ChangeBoxGoodsMapStock ReduceGoodsLockStock")
			return err
		}
	} else {
		// 无锁定库存，从当前库存原子扣减
		_, err = e.BoxGoodsRepo.AtomicDeductStock(ctx, tx, goodsID, 1)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("ChangeBoxGoodsMapStock AtomicDeductStock")
			return err
		}
	}

	// 创建库存日志
	for _, v := range changeGoodsStock.TradeDetailList {
		goodsStockLogModel := &box_goods_stock_log.Model{
			UserID:    changeGoodsStock.UserId,
			GoodsID:   v.GoodsID,
			ChangeNum: changeGoodsStock.ChangeNumber,
			TradeID:   v.ID,
		}
		goodsStockLogModelList = append(goodsStockLogModelList, goodsStockLogModel)
	}

	if len(goodsStockLogModelList) > 0 {
		err = e.BoxGoodsStockLogRepo.BatchCreateWithTx(ctx, tx, goodsStockLogModelList)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("ChangeBoxGoodsMapStock BatchCreateWithTx")
			return err
		}
	}

	return nil
}

func (e *Entry) ChangeBoxLockGoodsStock(ctx *gin.Context, tx *gorm.DB, goodsID uint64,
	changeGoodsStock *changeBoxGoodsStock) (err error) {
	if err = e.RedisCli.RetryLock(ctx, redis.GetBoxOrderGoodsIDLockKey(goodsID), redis.DefaultLockTime); err != nil {
		return ecode.BoxActiveGoodsNotEnoughErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetBoxOrderGoodsIDLockKey(goodsID))

	// ✅ 直接使用原子性操作，移除非原子性的先查询后更新逻辑
	_, err = e.BoxGoodsRepo.UpdateBoxGoodsLockStock(ctx, tx, goodsID, uint32(changeGoodsStock.ChangeNumber))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ChangeBoxLockGoodsStock UpdateBoxGoodsLockStock")
		return err
	}

	return nil
}

// DirectNotifyHandleReq 直接购买模式的微信回调处理请求
type DirectNotifyHandleReq struct {
	OrderInfo *orderDao.Model
	UserInfo  *userDao.Model
}

// DirectNotifyHandleRes 直接购买模式的微信回调处理响应
type DirectNotifyHandleRes struct {
	OrderID   uint64
	TimeStamp int64
}

// DirectNotifyHandle 处理直接购买模式的微信支付回调
// 直接购买模式在创建订单时已经创建了trade记录，这里只需要更新状态和处理库存
func (e *Entry) DirectNotifyHandle(ctx *gin.Context, req *DirectNotifyHandleReq) (res *DirectNotifyHandleRes, err error) {
	res = &DirectNotifyHandleRes{}

	var (
		timeNow = time.Now()
		tx      = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			buf := make([]byte, 10<<10)
			runtime.Stack(buf, true)
			log.Ctx(ctx).WithField("stack", string(buf)).Error("DirectNotifyHandle panic")
			err = ecode.SystemErr
		}
	}()

	// 0) 行级锁 + 幂等校验
	if err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(req.OrderInfo, req.OrderInfo.ID).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if req.OrderInfo.OrderStatus != uint32(orderDao.OrderStatusPayIng) {
		tx.Rollback()
		log.Ctx(ctx).Info("DirectNotifyHandle idempotent: order already processed",
			"orderID", req.OrderInfo.ID, "status", req.OrderInfo.OrderStatus)
		return res, nil
	}

	// 查找已存在的trade记录
	tradeDetails, err := e.TradeRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderID: req.OrderInfo.ID,
	})
	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("DirectNotifyHandle FindByFilter")
		return nil, err
	}

	if len(tradeDetails) == 0 {
		tx.Rollback()
		log.Ctx(ctx).Error("DirectNotifyHandle no trade details found")
		return nil, errors.New("no trade details found for direct purchase order")
	}

	// 更新订单状态为已支付
	orderUpdateMap := map[string]interface{}{
		"order_status": uint32(orderDao.OrderStatusPayOk),
		"pay_time":     timeNow.Unix(),
	}

	if err = e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, req.OrderInfo.ID, orderUpdateMap); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("DirectNotifyHandle update order status")
		return nil, err
	}

	// 处理每个trade记录的库存扣减
	for _, trade := range tradeDetails {
		if trade.SkuID > 0 {
			// 从锁定库存转为实际库存扣减
			err = e.processDirectPurchaseStock(ctx, tx, trade)
			if err != nil {
				tx.Rollback()
				log.Ctx(ctx).WithError(err).Error("DirectNotifyHandle processDirectPurchaseStock")
				return nil, err
			}
		}
	}

	// 添加订单积分奖励
	if req.OrderInfo.CashFee > 0 { // 只有实际支付金额大于0才给积分
		err = e.UserService.AddOrderPointsRewardWithTx(ctx, tx,
			req.OrderInfo.UserID,
			req.OrderInfo.CashFee,
			req.OrderInfo.ID,
			req.OrderInfo.OutTradeNo)
		if err != nil {
			// 积分奖励失败不回滚事务，记录错误日志
			log.Ctx(ctx).WithError(err).Error("DirectNotifyHandle AddOrderPointsReward failed")
		} else {
			log.Ctx(ctx).Info("DirectNotifyHandle AddOrderPointsReward success: UserID=%d, OrderAmount=%d, Points=%d",
				req.OrderInfo.UserID, req.OrderInfo.CashFee, req.OrderInfo.CashFee/100)
		}
	}

	if err = tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectNotifyHandle commit transaction")
		return nil, err
	}

	res.OrderID = req.OrderInfo.ID
	res.TimeStamp = timeNow.Unix()

	log.Ctx(ctx).Info("DirectNotifyHandle completed successfully: OrderID=%d", req.OrderInfo.ID)
	return res, nil
}

// processDirectPurchaseStock 处理直接购买的库存扣减
// 将之前锁定的库存转为实际扣减
// 直接购买模式下，trade记录的Quantity字段存储了购买数量
func (e *Entry) processDirectPurchaseStock(ctx *gin.Context, tx *gorm.DB, trade *orderDetailDao.Model) error {
	if trade.SkuID == 0 {
		log.Ctx(ctx).Warn("processDirectPurchaseStock: trade.SkuID is 0, skipping stock processing")
		return nil
	}

	// 使用Quantity字段获取数量
	quantity := trade.Quantity
	if quantity == 0 {
		log.Ctx(ctx).Warn("processDirectPurchaseStock: trade.Quantity is 0, using default quantity=1")
		quantity = 1 // 兜底处理，确保至少有数量1
	}

	// 获取SKU锁定，确保原子性操作
	lockKey := redis.GetBoxOrderGoodsIDLockKey(trade.SkuID)
	if err := e.RedisCli.RetryLock(ctx, lockKey, redis.DefaultLockTime); err != nil {
		return fmt.Errorf("failed to acquire lock for SkuID %d: %w", trade.SkuID, err)
	}
	defer e.RedisCli.Unlock(ctx, lockKey)

	// 获取当前SKU信息
	skuModel, err := e.SkuRepo.FetchByID(ctx, trade.SkuID)
	if err != nil {
		return fmt.Errorf("failed to fetch SKU %d: %w", trade.SkuID, err)
	}

	// 检查锁定库存是否足够
	if skuModel.LockNum < quantity {
		return fmt.Errorf("insufficient lock stock for SkuID %d: required %d, available %d",
			trade.SkuID, quantity, skuModel.LockNum)
	}

	// 将锁定库存转为实际扣减：减少锁定库存，增加已使用库存
	stockUpdates := map[string]interface{}{
		"lock_num": skuModel.LockNum - quantity,
		"used_num": skuModel.UsedNum + quantity,
	}

	if err := e.SkuRepo.UpdateMapByIDWithTx(ctx, tx, trade.SkuID, stockUpdates); err != nil {
		return fmt.Errorf("failed to update SKU stock for SkuID %d: %w", trade.SkuID, err)
	}

	log.Ctx(ctx).Info("processDirectPurchaseStock completed: SkuID=%d, Quantity=%d", trade.SkuID, quantity)
	return nil
}

// directCheckOrderInfo 专门处理直接购买订单的信息检查
// 直接购买不需要ActiveInfo和BoxInfo，只需要基本的订单和用户信息
func (e *Entry) directCheckOrderInfo(ctx *gin.Context, tradeNo string) (res *boxCheckOrderInfoRes, err error) {
	res = &boxCheckOrderInfoRes{}

	var (
		orderModel = &orderDao.Model{}
		userModel  = &userDao.Model{}
		eg         errgroup.Group
	)

	// 获取订单信息
	orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, tradeNo)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectCheckOrderInfo FetchByOutTradeNo")
		return nil, err
	}
	if orderModel == nil {
		err = ecode.BoxOrderNotExistErr
		return res, err
	}

	res.OrderInfo = orderModel

	// 获取用户信息
	eg.Go(func() error {
		userModel, err = e.UserRepo.FetchByID(ctx, orderModel.UserID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("DirectCheckOrderInfo FetchByID")
			return err
		}
		if userModel == nil {
			err = ecode.UserNotExistErr
			return err
		}

		res.UserInfo = userModel
		return nil
	})

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("DirectCheckOrderInfo errgroup.Wait")
		return
	}

	return res, nil
}

// PointsDiscountRequest 积分抵扣请求参数
type PointsDiscountRequest struct {
	UserID      uint64
	TotalAmount uint64
	MaxPercent  uint32
}

// PointsDiscountResult 积分抵扣结果
type PointsDiscountResult struct {
	DiscountAmount   uint64
	ActualPointsUsed uint64
}

// calculateAndApplyPointsDiscount 计算并应用积分抵扣的公共函数
func (e *Entry) calculateAndApplyPointsDiscount(ctx *gin.Context, req *PointsDiscountRequest) (*PointsDiscountResult, error) {
	if req.UserID == 0 || req.TotalAmount == 0 {
		return nil, nil
	}

	userSrv := user.GetService()

	// 计算积分抵扣
	pointsReq := &user.PointsDiscountRequest{
		UserID:      req.UserID,
		TotalAmount: req.TotalAmount,
		MaxPercent:  req.MaxPercent,
	}

	pointsResp, err := userSrv.CalculatePointsDiscount(ctx, pointsReq)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("calculateAndApplyPointsDiscount calculation failed")
		return nil, fmt.Errorf("points discount calculation error: %w", err)
	}

	if pointsResp == nil || pointsResp.DiscountAmount == 0 {
		return nil, nil
	}

	// 确保积分抵扣后至少还有0.01元需要支付
	minPaymentAmount := uint64(1) // 0.01元 = 1分
	maxPointsDiscount := req.TotalAmount
	if req.TotalAmount > minPaymentAmount {
		maxPointsDiscount = req.TotalAmount - minPaymentAmount
	} else {
		maxPointsDiscount = 0 // 如果订单金额小于0.01元，不允许积分抵扣
	}

	pointsDiscount := pointsResp.DiscountAmount
	if pointsDiscount > maxPointsDiscount {
		pointsDiscount = maxPointsDiscount
	}

	if pointsDiscount == 0 {
		return nil, nil
	}

	return &PointsDiscountResult{
		DiscountAmount:   pointsDiscount,
		ActualPointsUsed: pointsResp.CanUsePoints,
	}, nil
}

// TradeNoLockStep 交易号锁步骤
type TradeNoLockStep struct {
	entry *Entry
}

func (s *TradeNoLockStep) Name() string {
	return "TradeNoLock"
}

func (s *TradeNoLockStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	return false // 必须执行
}

func (s *TradeNoLockStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	lockKey := redis.GetBoxOrderTradeNoLockKey(notifyCtx.Request.OutTradeNo)
	if err := s.entry.RedisCli.Lock(ctx, lockKey, redis.DefaultLockTime); err != nil {
		log.Ctx(ctx).WithError(err).Error("TradeNoLockStep failed to acquire lock")
		return ecode.BoxOrderAgainCreateOrder
	}

	// 设置解锁函数到结果缓存
	unlockFunc := func() {
		s.entry.RedisCli.Unlock(ctx, lockKey)
	}
	notifyCtx.StepResults["tradeLockUnlock"] = unlockFunc

	log.Ctx(ctx).Info("TradeNoLockStep acquired lock for tradeNo: %s", notifyCtx.Request.OutTradeNo)
	return nil
}

// ContextBuildStep 上下文构建步骤
type ContextBuildStep struct {
	entry *Entry
}

func (s *ContextBuildStep) Name() string {
	return "ContextBuild"
}

func (s *ContextBuildStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	return false // 必须执行
}

func (s *ContextBuildStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	req := notifyCtx.Request

	// 1. 获取基本订单信息
	orderModel, err := s.entry.OrderRepo.FetchByOutTradeNo(ctx, req.OutTradeNo)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ContextBuildStep FetchByOutTradeNo failed")
		return fmt.Errorf("failed to fetch order: %w", err)
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("ContextBuildStep order not found: %s", req.OutTradeNo)
		return ecode.BoxOrderNotExistErr
	}

	notifyCtx.OrderModel = orderModel
	notifyCtx.IsDirectOrder = orderModel.OrderType == uint32(orderDao.OrderTypeDirect)

	log.Ctx(ctx).Info("ContextBuildStep found order: ID=%d, Type=%s",
		orderModel.ID,
		map[bool]string{true: "direct", false: "box"}[notifyCtx.IsDirectOrder])

	// 2. 根据订单类型获取详细信息
	var orderParam *boxCheckOrderInfoRes
	if notifyCtx.IsDirectOrder {
		orderParam, err = s.entry.directCheckOrderInfo(ctx, req.OutTradeNo)
	} else {
		orderParam, err = s.entry.boxCheckOrderInfo(ctx, req.OutTradeNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ContextBuildStep checkOrderInfo failed")
		return fmt.Errorf("failed to check order info: %w", err)
	}
	notifyCtx.OrderParam = orderParam

	// 3. 获取交易详情
	tradeDetails, err := s.entry.TradeRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderID: orderParam.OrderInfo.ID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ContextBuildStep FindByFilter failed")
		return fmt.Errorf("failed to fetch trade details: %w", err)
	}
	notifyCtx.TradeDetails = tradeDetails

	log.Ctx(ctx).Info("ContextBuildStep completed: TradeCount=%d", len(tradeDetails))
	return nil
}

// ValidateStep 验证步骤
type ValidateStep struct {
	entry *Entry
}

func (s *ValidateStep) Name() string {
	return "Validate"
}

func (s *ValidateStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	return false // 必须执行
}

func (s *ValidateStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	orderInfo := notifyCtx.OrderParam.OrderInfo

	// 检查订单状态
	skipProcessingStatuses := []uint32{
		uint32(orderDao.OrderStatusRefund),
		uint32(orderDao.OrderStatusPayOk),
	}

	if lo.Contains(skipProcessingStatuses, orderInfo.OrderStatus) {
		log.Ctx(ctx).Warn("ValidateStep order already processed: OrderID=%d, Status=%d",
			orderInfo.ID, orderInfo.OrderStatus)
		// 标记为跳过后续处理
		notifyCtx.StepResults["shouldSkipBusiness"] = true
		return nil
	}

	// 根据订单类型进行不同的检查
	if notifyCtx.IsDirectOrder {
		// 直接购买：创建订单时就有交易详情，不需要检查
		log.Ctx(ctx).Info("ValidateStep direct order validation passed")
	} else {
		// 盲盒：交易详情在回调时创建，如果已存在说明重复处理
		if len(notifyCtx.TradeDetails) > 0 {
			log.Ctx(ctx).Warn("ValidateStep box order already has trade details: Count=%d",
				len(notifyCtx.TradeDetails))
			notifyCtx.StepResults["shouldSkipBusiness"] = true
			return nil
		}
		log.Ctx(ctx).Info("ValidateStep box order validation passed")
	}

	notifyCtx.StepResults["shouldSkipBusiness"] = false
	return nil
}

// PaymentInfoUpdateStep 支付信息更新步骤
type PaymentInfoUpdateStep struct {
	entry *Entry
}

func (s *PaymentInfoUpdateStep) Name() string {
	return "PaymentInfoUpdate"
}

func (s *PaymentInfoUpdateStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	// 如果前面步骤标记了跳过业务处理，这一步也跳过
	if skip, exists := notifyCtx.StepResults["shouldSkipBusiness"].(bool); exists && skip {
		return true
	}
	return false
}

func (s *PaymentInfoUpdateStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	orderInfo := notifyCtx.OrderParam.OrderInfo
	req := notifyCtx.Request

	oldCashFee := orderInfo.CashFee
	oldTransactionId := orderInfo.TransactionId

	orderInfo.CashFee = req.TotalAmount
	orderInfo.TransactionId = req.TradeNo

	log.Ctx(ctx).Info("PaymentInfoUpdateStep updated: CashFee %d->%d, TransactionId %s->%s",
		oldCashFee, orderInfo.CashFee, oldTransactionId, orderInfo.TransactionId)
	return nil
}

// BusinessLockStep 业务锁步骤
type BusinessLockStep struct {
	entry *Entry
}

func (s *BusinessLockStep) Name() string {
	return "BusinessLock"
}

func (s *BusinessLockStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	// 直接购买不需要BoxID锁
	if notifyCtx.IsDirectOrder {
		return true
	}
	// 如果前面步骤标记了跳过业务处理，这一步也跳过
	if skip, exists := notifyCtx.StepResults["shouldSkipBusiness"].(bool); exists && skip {
		return true
	}
	return false
}

func (s *BusinessLockStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	boxID := notifyCtx.OrderParam.OrderInfo.BoxID
	lockKey := redis.GetBoxOrderBoxIDLockKey(boxID)

	if err := s.entry.RedisCli.RetryLock(ctx, lockKey, redis.DefaultLockTime); err != nil {
		log.Ctx(ctx).WithError(err).Error("BusinessLockStep failed to acquire box lock: BoxID=%d", boxID)
		// TODO: 处理锁获取失败，执行退款
		return fmt.Errorf("failed to acquire box lock: %w", err)
	}

	// 设置解锁函数到结果缓存
	unlockFunc := func() {
		s.entry.RedisCli.Unlock(ctx, lockKey)
	}
	notifyCtx.StepResults["businessLockUnlock"] = unlockFunc

	log.Ctx(ctx).Info("BusinessLockStep acquired box lock: BoxID=%d", boxID)
	return nil
}

// BusinessExecuteStep 业务执行步骤
type BusinessExecuteStep struct {
	entry *Entry
}

func (s *BusinessExecuteStep) Name() string {
	return "BusinessExecute"
}

func (s *BusinessExecuteStep) ShouldSkip(ctx *gin.Context, notifyCtx *WechatNotifyContext) bool {
	// 如果前面步骤标记了跳过业务处理，这一步也跳过
	if skip, exists := notifyCtx.StepResults["shouldSkipBusiness"].(bool); exists && skip {
		return true
	}
	return false
}

func (s *BusinessExecuteStep) Execute(ctx *gin.Context, notifyCtx *WechatNotifyContext) error {
	orderParam := notifyCtx.OrderParam

	var result interface{}
	var err error

	if notifyCtx.IsDirectOrder {
		log.Ctx(ctx).Info("BusinessExecuteStep executing direct purchase business")
		result, err = s.entry.DirectNotifyHandle(ctx, &DirectNotifyHandleReq{
			OrderInfo: orderParam.OrderInfo,
			UserInfo:  orderParam.UserInfo,
		})
	} else {
		log.Ctx(ctx).Info("BusinessExecuteStep executing box business")
		result, err = s.entry.BoxNotifyHandle(ctx, &BoxNotifyHandleReq{
			ActiveInfo: orderParam.ActiveInfo,
			BoxInfo:    orderParam.BoxInfo,
			OrderInfo:  orderParam.OrderInfo,
			UserInfo:   orderParam.UserInfo,
		})
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("BusinessExecuteStep business execution failed")
		return fmt.Errorf("business execution failed: %w", err)
	}

	notifyCtx.StepResults["businessResult"] = result

	// 支付成功后，从超时队列中移除订单
	orderID := orderParam.OrderInfo.ID
	if err := redis.OrderTimeoutQueue.RemoveOrderTimeout(ctx, orderID); err != nil {
		log.Ctx(ctx).WithError(err).Error("BusinessExecuteStep remove order timeout queue failed", "orderID", orderID)
		// 这里不返回错误，避免影响支付成功流程，只记录日志
	} else {
		log.Ctx(ctx).Info("BusinessExecuteStep removed order from timeout queue", "orderID", orderID)
	}

	log.Ctx(ctx).Info("BusinessExecuteStep business execution completed")
	return nil
}
