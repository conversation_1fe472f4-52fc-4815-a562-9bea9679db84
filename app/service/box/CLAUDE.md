# Box Service Architecture Documentation

## Overview

The box service is the core module of the blind box system, providing comprehensive functionality for blind box operations, direct purchases, and item card strategies. This service implements sophisticated algorithms for randomization, stock management, sliding window controls, and multi-strategy purchase patterns.

## Core Architecture

### Directory Structure
```
app/service/box/
├── interface.go           # Service interface and dependency injection
├── box.go                # Core blind box business logic
├── strategy.go           # Strategy pattern implementation (Purchase strategies)
├── core.go               # Core data structures and common methods
├── window.go             # Sliding window algorithm implementation
├── check.go              # Validation helper methods
├── box_goods.go          # Box goods management
├── box_active_config.go  # Activity configuration management
├── box_level.go          # Level system management
├── box_must_config.go    # Must-win configuration management
├── box_queue.go          # Queue lock mechanism
├── goods_shop.go         # Goods shop related functionality
├── box_test.go          # Unit tests
└── random_test.go       # Randomization algorithm tests
```

### Service Entry Dependencies
```go
type Entry struct {
    // Core DAO Dependencies (20+ repositories)
    UserRepo            *user.Entry
    BoxRecordRepo       box_record.Repo
    BoxLevelRepo        box_level.Repo
    BoxActiveConfigRepo box_active_config.Repo
    BoxMustConfigRepo   box_must_config.Repo
    BoxQueueLockRepo    box_queue_lock.Repo
    BoxGoodsRepo        box_goods.Repo
    BoxGoodsStockLogRepo box_goods_stock_log.Repo
    BoxActionDetailRepo box_action_detail.Repo
    
    // SKU/SPU Management (Direct Purchase Mode)
    SkuRepo *sku.Entry  // SKU as minimum purchase unit
    SpuRepo *spu.Entry
    
    // External Service Dependencies
    UserService  *userService.Entry
    CommonSrv    common.Server
    
    // Infrastructure Dependencies
    RedisCli     *redis.RedisClient
}
```

## Strategy Pattern Implementation

### Strategy Constants
```go
const (
    STRATEGY_PURCHASE = 1  // Blind box random strategy
    STRATEGY_ITEMCARD = 2  // Item card strategy
    STRATEGY_DIRECT   = 3  // Direct purchase strategy (SKU-level)
)
```

### Strategy Interface and Factory
```go
// Abstract operation interface
type Operator interface {
    Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error)
}

// Strategy factory method
func (e *Entry) StrategySelect(strategyType int) Operator {
    switch strategyType {
    case STRATEGY_PURCHASE:
        return &PurchaseStrategy{entry: e}
    case STRATEGY_ITEMCARD:
        return &ItemCardStrategy{entry: e}
    case STRATEGY_DIRECT:
        return &DirectPurchaseStrategy{entry: e}
    default:
        return nil
    }
}
```

### Strategy Implementations

#### 1. Purchase Strategy (Blind Box Random)
```go
type PurchaseStrategy struct {
    entry *Entry
}

func (p *PurchaseStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error) {
    // 1. Build selection request
    sgReq := &selectGoodsReq{
        UserID:    b.OrderInfo.UserID,
        BoxId:     b.BoxInfo.ID,
        Slot:      b.Slot,
        OrderInfo: b.OrderInfo,
        BoxInfo:   b.BoxInfo,
    }
    
    // 2. Get draw window and rules
    drawWindow, err := p.entry.getBoxRulesAndDrawWindow(ctx, b, sgReq)
    if err != nil {
        return nil, err
    }
    
    // 3. Execute random item selection
    res, actionType, err = p.entry.selectRandomItemProduce(ctx, sgReq, b.GoodsStockMap, b.BoxRecord, b.Active, drawWindow)
    
    // 4. Set action type as confirmed output
    res.ActionType = uint32(box_action_detail.ACTION_TYPE_1)
    res.OrderInfo = b.OrderInfo
    
    return res, err
}
```

#### 2. Direct Purchase Strategy (SKU-Level)
```go
type DirectPurchaseStrategy struct {
    entry *Entry
}

func (p *DirectPurchaseStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (res *ActionResult, err error) {
    // 1. Validate SKU parameters (SKU as minimum purchase unit)
    if b.SkuID == 0 || b.Quantity == 0 {
        return nil, errors.New("sku_id and quantity are required for direct purchase")
    }
    
    // 2. Query SKU information (bypassing box_goods table)
    skuModel, err := p.entry.SkuRepo.FetchByID(ctx, b.SkuID)
    if err != nil || !skuModel.JudgeCanOrdered() {
        return nil, fmt.Errorf("SKU not available: %w", err)
    }
    
    // 3. Query corresponding SPU information
    spuModel, err := p.entry.SpuRepo.FetchByID(ctx, skuModel.SpuId)
    if err != nil || !spuModel.JudgeCanOrdered() {
        return nil, fmt.Errorf("SPU not available: %w", err)
    }
    
    // 4. Check SKU stock availability
    availableStock := skuModel.GetUsableNum()
    if availableStock < b.Quantity {
        return nil, fmt.Errorf("insufficient stock: required %d, available %d", b.Quantity, availableStock)
    }
    
    // 5. Lock SKU stock (increase lock_num before payment)
    lockUpdates := map[string]interface{}{
        "lock_num": skuModel.LockNum + b.Quantity,
    }
    if err := p.entry.SkuRepo.UpdateMapByIDWithTx(ctx, tx, b.SkuID, lockUpdates); err != nil {
        return nil, fmt.Errorf("failed to lock stock: %w", err)
    }
    
    // 6. Create trade detail for direct purchase
    detail := &orderDetailDao.Model{
        OrderID:        b.OrderID,
        SkuID:          skuModel.ID,
        SpuID:          spuModel.ID,
        GoodsName:      skuModel.Title,
        Quantity:       b.Quantity,
        BoxSlot:        "",  // No slot concept for direct purchase
        GoodsID:        0,   // No box_goods for direct purchase
        TradeStatus:    1,   // Paid
        DeliveryStatus: 1,   // Pending delivery
        Remark:         fmt.Sprintf("Direct Purchase - SKU: %s, Quantity: %d", skuModel.Code, b.Quantity),
    }
    
    // 7. Build result item
    resItem := &ActionResultItem{
        BoxId:           b.BoxID,
        Slot:            0,   // No slot concept for direct purchase
        GoodsInfo:       nil, // No box_goods for direct purchase
        TradeDetailInfo: detail,
        ActionDetail: &box_action_detail.Model{
            UserID:     b.UserID,
            ActiveID:   b.ActiveID,
            BoxID:      b.BoxID,
            BoxSlot:    "",                              // No slot concept
            ActionType: box_action_detail.ACTION_TYPE_1, // Confirmed output
            GoodsID:    0,                               // No box_goods ID
        },
    }
    
    res.List = append(res.List, resItem)
    res.ActionType = uint32(box_action_detail.ACTION_TYPE_1)
    res.OrderInfo = b.OrderInfo
    
    return res, nil
}
```

#### 3. Item Card Strategy
```go
type ItemCardStrategy struct {
    entry *Entry
}

func (p *ItemCardStrategy) Operate(ctx *gin.Context, tx *gorm.DB, b *BoxModel, i *ItemCardModel) (result *ActionResult, err error) {
    // 1. Validate slot not already used
    details, err := p.entry.BoxActionDetailRepo.FindByFilter(ctx, &box_action_detail.Filter{
        BoxID: b.BoxInfo.ID,
        Slot:  b.Slot[0],
    })
    
    // 2. Check for conflicts
    for _, d := range details {
        if d.ActionType == box_action_detail.ACTION_TYPE_1 {
            return nil, errors.New("slot already sold")
        }
        if d.ActionType == box_action_detail.ACTION_TYPE_3 {
            return nil, errors.New("slot already used perspective card")
        }
    }
    
    // 3. Handle different card types
    switch i.ICType {
    case itemCardDao.CardType_yao:  // Shake card
    case itemCardDao.CardType_tip:  // Tip card
    case itemCardDao.CardType_tou:  // Perspective card
    default:
        return nil, errors.New("invalid card type")
    }
    
    // 4. Execute card strategy
    sgReq := &selectGoodsReq{
        BoxId:   b.BoxInfo.ID,
        Slot:    b.Slot,
        BoxInfo: b.BoxInfo,
    }
    
    if i.ICType == itemCardDao.CardType_tou {
        sgReq.ActionType = box_action_detail.ACTION_TYPE_3  // Pre-output lock
    } else {
        sgReq.ActionType = box_action_detail.ACTION_TYPE_2  // Pre-output
    }
    
    // 5. Get draw window and execute
    drawWindow, err := p.entry.getBoxRulesAndDrawWindow(ctx, b, sgReq)
    if err != nil {
        return nil, err
    }
    
    result, _, err = p.entry.selectRandomItemProduce(ctx, sgReq, b.GoodsStockMap, b.BoxRecord, b.Active, drawWindow)
    result.ActionType = sgReq.ActionType
    
    return result, err
}
```

## Core Data Models

### BoxModel - Comprehensive Box Model
```go
type BoxModel struct {
    // Blind box mode fields
    Active        *activeDao.Model        // Activity information
    Config        *box_active_config.Model // Activity configuration
    OrderInfo     *orderDao.Model         // Order information
    BoxInfo       *box_record.Model       // Box record
    Slot          []uint32                // Slot information
    GoodsStockMap goodsStockMap          // Goods stock mapping
    BoxRecord     *boxRecordInfo         // Box record details
    
    // Direct purchase mode fields (SKU-level)
    SkuID    uint64 // SKU product ID (minimum purchase unit)
    Quantity uint32 // Purchase quantity
    
    // Common fields
    UserID      uint64 // User ID
    ActiveID    uint64 // Activity ID
    BoxID       uint64 // Box ID
    BoxNo       string // Box number
    OrderID     uint64 // Order ID
    ActiveTitle string // Activity title
    ActiveType  uint32 // Activity type
}
```

### ActionResult - Operation Result
```go
type ActionResult struct {
    ActionType uint32                   // Action type
    OrderInfo  *orderDao.Model          // Order information
    List       []*ActionResultItem      // Result list
}

type ActionResultItem struct {
    BoxId           uint64                 // Box ID
    Slot            uint32                 // Box slot
    GoodsInfo       *boxGoodsDao.Model     // Goods information
    TradeDetailInfo *tradeDetailDao.Model  // Trade detail
    NotGoodsInfo    *boxGoodsDao.Model     // Non-goods information (for display)
    ActionDetail    *actionDetailDao.Model // Action detail
}
```

## Sliding Window Algorithm

### Window Data Structure
```go
type Window struct {
    limit         uint32                // Window size limit
    DrawQueue     *list.List            // Draw event queue (FIFO)
    ItemDrawCount map[uint64]uint32     // Item draw count
    mu            sync.Mutex            // Concurrency control lock
}

type DrawEvent struct {
    UserID  uint64  // User ID
    GoodsID uint64  // Goods ID
}
```

### Window Operations
```go
// Add historical draw events
func (w *Window) addHistoricalDrawEvents(events []DrawEvent) {
    w.mu.Lock()
    defer w.mu.Unlock()
    
    for _, event := range events {
        w.DrawQueue.PushBack(event)
        w.ItemDrawCount[event.GoodsID]++
    }
    
    // Remove old events if exceeding window size
    for w.DrawQueue.Len() > int(w.limit) {
        oldEvent := w.DrawQueue.Remove(w.DrawQueue.Front()).(DrawEvent)
        w.ItemDrawCount[oldEvent.GoodsID]--
        if w.ItemDrawCount[oldEvent.GoodsID] == 0 {
            delete(w.ItemDrawCount, oldEvent.GoodsID)
        }
    }
}

// Handle new draw event
func (w *Window) handleDrawEvent(event DrawEvent) {
    w.mu.Lock()
    defer w.mu.Unlock()
    
    // Add new draw event to queue end
    w.DrawQueue.PushBack(event)
    w.ItemDrawCount[event.GoodsID]++
    
    // Remove events outside window
    for w.DrawQueue.Len() > int(w.limit) {
        oldEvent := w.DrawQueue.Remove(w.DrawQueue.Front()).(DrawEvent)
        w.ItemDrawCount[oldEvent.GoodsID]--
        if w.ItemDrawCount[oldEvent.GoodsID] == 0 {
            delete(w.ItemDrawCount, oldEvent.GoodsID)
        }
    }
}
```

## Randomization Algorithm

### Weight-Based Random Selection
```go
func (e *Entry) getRandomItemUseRules(ctx *gin.Context, gSelect *goodsSelect) (goodsRes *goodsStock, err error) {
    var totalWeight uint32 = 0
    
    // Calculate total weight
    for _, stock := range gSelect.GoodsStockMap {
        totalWeight += stock.Weight
    }
    
    // Handle must-win logic
    tmpGoodsStockMap := make(goodsStockMap)
    if len(gSelect.MustRandomIn) > 0 {
        tmpTotalWeight := uint32(0)
        for _, i := range gSelect.MustRandomIn {
            if v, ok := gSelect.GoodsStockMap[i]; ok {
                tmpGoodsStockMap[i] = v
                tmpTotalWeight += v.GetWeight()
            }
        }
        if tmpTotalWeight > 0 {
            totalWeight = tmpTotalWeight
        }
    }
    
    if len(tmpGoodsStockMap) <= 0 {
        tmpGoodsStockMap = gSelect.GoodsStockMap
    }
    
    // Handle zero weight case
    if totalWeight <= 0 {
        for _, stock := range tmpGoodsStockMap {
            stock.Weight = stock.GetOverStockWeight()
            totalWeight += stock.GetOverStockWeight()
        }
    }
    
    // Generate safe random number
    randomNum := safe_random.Intn(int(totalWeight)) // Range [0, totalWeight-1]
    
    // Weight-based selection
    var cumulativeWeight uint32
    for _, g := range tmpGoodsStockMap {
        cumulativeWeight += g.Weight
        if randomNum < int(cumulativeWeight) {
            return g, nil
        }
    }
    
    return nil, errors.New("random selection failed")
}
```

### Must-Win Mechanism
```go
// Check must-win configuration
func (e *Entry) checkMustWin(ctx *gin.Context, userID uint64, activeID uint64) (mustGoodsIDs []uint64) {
    // 1. Check user-level must-win
    userMustConfig := e.BoxMustConfigRepo.GetUserMustConfig(ctx, userID, activeID)
    if userMustConfig != nil && userMustConfig.IsValid() {
        mustGoodsIDs = append(mustGoodsIDs, userMustConfig.GoodsIDs...)
    }
    
    // 2. Check activity-level must-win
    activeMustConfig := e.BoxMustConfigRepo.GetActiveMustConfig(ctx, activeID)
    if activeMustConfig != nil && activeMustConfig.ShouldTrigger() {
        mustGoodsIDs = append(mustGoodsIDs, activeMustConfig.GoodsIDs...)
    }
    
    return mustGoodsIDs
}
```

## Stock Management

### Stock Operation Principles
- All stock operations must use Redis distributed locks
- Stock changes must be logged to `box_goods_stock_log`
- Support batch stock operations for performance
- SKU-level stock management for direct purchases

### Stock Locking Process
```go
func (e *Entry) lockStock(ctx *gin.Context, goodsID uint64, num uint32) error {
    lockKey := fmt.Sprintf("box:stock:lock:%d", goodsID)
    
    return redis.RetryLock(ctx, lockKey, func() error {
        // 1. Check stock
        stock := e.BoxGoodsRepo.GetStock(ctx, goodsID)
        if stock.Available() < num {
            return ecode.StockNotEnoughErr
        }
        
        // 2. Lock stock
        if err := e.BoxGoodsRepo.LockStock(ctx, goodsID, num); err != nil {
            return err
        }
        
        // 3. Record log
        return e.BoxGoodsStockLogRepo.Create(ctx, &box_goods_stock_log.Model{
            GoodsID:    goodsID,
            ChangeType: "lock",
            ChangeNum:  int32(num),
        })
    })
}
```

### SKU Stock Management (Direct Purchase)
```go
// SKU stock locking for direct purchase
func (e *Entry) lockSkuStock(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32) error {
    // Get current SKU information
    skuModel, err := e.SkuRepo.FetchByID(ctx, skuID)
    if err != nil {
        return err
    }
    
    // Check available stock
    availableStock := skuModel.GetUsableNum()
    if availableStock < quantity {
        return fmt.Errorf("insufficient SKU stock: required %d, available %d", quantity, availableStock)
    }
    
    // Update lock_num in transaction
    lockUpdates := map[string]interface{}{
        "lock_num": skuModel.LockNum + quantity,
    }
    
    return e.SkuRepo.UpdateMapByIDWithTx(ctx, tx, skuID, lockUpdates)
}
```

## Performance Optimization

### Concurrent Data Fetching
```go
// Parallel data fetching with errgroup
func (e *Entry) fetchBoxData(ctx *gin.Context, activeID uint64) (*BoxData, error) {
    var (
        g           errgroup.Group
        activeInfo  *activeDao.Model
        configInfo  *box_active_config.Model
        goodsList   box_goods.ModelList
    )
    
    g.Go(func() error {
        var err error
        activeInfo, err = e.ActiveRepo.FetchByID(ctx, activeID)
        return err
    })
    
    g.Go(func() error {
        var err error
        configInfo, err = e.BoxActiveConfigRepo.FetchByActiveID(ctx, activeID)
        return err
    })
    
    g.Go(func() error {
        var err error
        goodsList, err = e.BoxGoodsRepo.FindByActiveID(ctx, activeID)
        return err
    })
    
    if err := g.Wait(); err != nil {
        return nil, err
    }
    
    return &BoxData{
        Active: activeInfo,
        Config: configInfo,
        Goods:  goodsList,
    }, nil
}
```

### Caching Strategy
```go
// Activity configuration caching (high frequency access, low change rate)
func (e *Entry) getActiveConfigWithCache(ctx *gin.Context, activeID uint64) (*box_active_config.Model, error) {
    cacheKey := fmt.Sprintf("box:active:config:%d", activeID)
    
    // 1. Try to get from cache
    var config box_active_config.Model
    if err := e.RedisCli.Get(ctx, cacheKey, &config); err == nil {
        return &config, nil
    }
    
    // 2. Cache miss, query database
    configModel, err := e.BoxActiveConfigRepo.FetchByActiveID(ctx, activeID)
    if err != nil {
        return nil, err
    }
    
    // 3. Asynchronously update cache (5 minutes expiry)
    go func() {
        e.RedisCli.Set(context.Background(), cacheKey, configModel, 5*time.Minute)
    }()
    
    return configModel, nil
}
```

## Business Method Naming Convention

### User-facing Methods
```go
func (e *Entry) BoxSearch(ctx *gin.Context, req *boxDto.BoxSearchReq) (*boxDto.BoxSearchRes, error)
func (e *Entry) BoxOpen(ctx *gin.Context, req *boxDto.BoxOpenReq) (*boxDto.BoxOpenRes, error)
func (e *Entry) Queue(ctx *gin.Context, req *boxDto.BoxQueueReq) (*boxDto.BoxQueueRes, error)
```

### Admin Methods - Admin Prefix
```go
func (e *Entry) AdminBoxGoodsAdd(ctx *gin.Context, req *boxDto.AdminBoxGoodsAddReq) (*boxDto.AdminBoxGoodsAddResp, error)
func (e *Entry) AdminBoxLevelUpdate(ctx *gin.Context, req *boxDto.AdminBoxLevelUpdateReq) (*boxDto.AdminBoxLevelUpdateResp, error)
func (e *Entry) AdminActiveConfigCreate(ctx *gin.Context, req *boxDto.AdminActiveConfigCreateReq) (*boxDto.AdminActiveConfigCreateResp, error)
```

### Internal Methods - Lowercase Start
```go
func (e *Entry) payBox(ctx *gin.Context, tx *gorm.DB, req *BoxSelectReq) (*BoxSelectRes, error)
func (e *Entry) selectGoods(ctx *gin.Context, tx *gorm.DB, req *selectGoodsReq) (map[slot]*goodsId, error)
func (e *Entry) calculateWeight(ctx *gin.Context, goods *goodsStock) uint32
```

## Transaction Management

### Core Transaction Method
```go
func (e *Entry) payBox(ctx *gin.Context, tx *gorm.DB, req *BoxSelectReq) (res *BoxSelectRes, err error) {
    // 1. Parameter validation
    if req.OrderInfo == nil {
        return nil, errors.New("order info is nil")
    }
    
    // 2. Strategy selection
    strategy := e.StrategySelect(int(req.PurchaseType))
    if strategy == nil {
        return nil, errors.New("invalid strategy type")
    }
    
    // 3. Build BoxModel
    boxModel := &BoxModel{
        OrderInfo: req.OrderInfo,
        SkuID:     req.SkuID,      // For direct purchase mode
        Quantity:  req.Quantity,   // For direct purchase mode
    }
    
    // 4. Execute strategy
    result, err := strategy.Operate(ctx, tx, boxModel, nil)
    if err != nil {
        return nil, err
    }
    
    // 5. Update stock (using passed transaction)
    if err := e.updateStockWithTx(ctx, tx, result); err != nil {
        return nil, err
    }
    
    return res, nil
}
```

## Error Handling Standards

### Business Error Definitions
```go
// Use ecode package defined errors
ecode.BoxNotExistErr      // Box does not exist
ecode.StockNotEnoughErr   // Insufficient stock
ecode.BoxAlreadyOpenErr   // Box already opened
ecode.ActNotExistErr      // Activity does not exist
ecode.BoxLevelNotExistErr // Level does not exist
```

### Error Handling Principles
```go
func (e *Entry) BoxOpen(ctx *gin.Context, req *boxDto.BoxOpenReq) (*boxDto.BoxOpenRes, error) {
    // 1. Parameter validation errors - return directly
    if req.BoxID == 0 {
        return nil, ecode.ParamErr
    }
    
    // 2. Database errors - log and return system error
    boxInfo, err := e.BoxRecordRepo.FetchByID(ctx, req.BoxID)
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("fetch box record failed")
        return nil, ecode.SystemErr
    }
    
    // 3. Business logic errors - return specific business error
    if boxInfo.Status == box_record.StatusOpened {
        return nil, ecode.BoxAlreadyOpenErr
    }
    
    // 4. Non-critical errors - log warning but continue execution
    if err := e.sendNotification(ctx, req.UserID); err != nil {
        log.Ctx(ctx).WithError(err).Warn("send notification failed")
        // Continue execution, don't affect main flow
    }
    
    return res, nil
}
```

## Security and Anti-Cheat Mechanisms

### Concurrency Control
- Use Redis distributed locks to control stock operations
- Window structure uses mutex for thread safety
- Queue lock mechanism prevents duplicate operations

### Anti-Cheat Mechanisms
- Use secure random number generator `safe_random.Intn`
- Sliding window controls output rate
- Must-win configuration prevents malicious farming

### Random Number Security
```go
// Use safe random number generator - fix boundary issues
randomNum := safe_random.Intn(int(totalWeight)) // Range [0, totalWeight-1]

// Weight-based cumulative selection
var cumulativeWeight uint32
for _, g := range tmpGoodsStockMap {
    cumulativeWeight += g.Weight
    if randomNum < int(cumulativeWeight) { // Use < instead of <=
        return g, nil
    }
}
```

## Testing Strategy

### Unit Test Organization
```go
// box_test.go
func TestPurchaseStrategy_Operate(t *testing.T) {
    // Arrange
    entry := setupTestEntry()
    strategy := &PurchaseStrategy{entry: entry}
    boxModel := &BoxModel{
        UserID:   1,
        ActiveID: 100,
    }
    
    // Act
    result, err := strategy.Operate(context.Background(), nil, boxModel, nil)
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Greater(t, len(result.List), 0)
}
```

### Randomness Testing
```go
// random_test.go
func TestRandomDistribution(t *testing.T) {
    // Test if random distribution meets expectations
    iterations := 10000
    distribution := make(map[uint64]int)
    
    for i := 0; i < iterations; i++ {
        selected := randomSelect(goodsMap)
        distribution[selected]++
    }
    
    // Verify distribution is within reasonable range
    for goodsID, count := range distribution {
        expectedRate := float64(goodsMap[goodsID].Weight) / totalWeight
        actualRate := float64(count) / float64(iterations)
        deviation := math.Abs(expectedRate - actualRate)
        
        assert.Less(t, deviation, 0.05) // Allow 5% deviation
    }
}
```

## Monitoring and Logging

### Key Operation Logging
```go
// Record box opening
log.Ctx(ctx).WithFields(logrus.Fields{
    "userID":   req.UserID,
    "boxID":    req.BoxID,
    "activeID": req.ActiveID,
    "result":   result.List,
}).Info("box opened successfully")

// Record stock changes
log.Ctx(ctx).WithFields(logrus.Fields{
    "goodsID":   goodsID,
    "changeNum": num,
    "remaining": stock.Available(),
}).Info("stock locked")

// Record direct purchase operations
log.Ctx(ctx).Info("DirectPurchaseStrategy executed successfully: SkuID=%d, SpuID=%d, Quantity=%d",
    b.SkuID, spuModel.ID, b.Quantity)
```

### Performance Monitoring Points
- Box opening duration
- Random algorithm execution time
- Stock lock wait time
- Window period calculation duration

## Development Guidelines

### Code Standards
- **Strategy Expansion**: When adding new purchase strategies, implement the Operator interface and register in factory method
- **Window Management**: Window size affects product distribution, adjust according to activity characteristics
- **Stock Consistency**: All stock operations must be completed within transactions or distributed locks
- **Error Propagation**: Service layer returns business errors, converted to HTTP responses by Handler layer
- **Performance Optimization**: Frequently accessed configuration data should use caching
- **Logging Standards**: Key business operations must record logs, including user ID, operation type, results, etc.

### Important Considerations

1. **SKU as Minimum Purchase Unit**: In direct purchase mode, users purchase specific SKUs, not SPUs
2. **Strategy Pattern Extension**: When adding purchase strategies, need to implement Operator interface and register in factory method
3. **Window Period Management**: Window size affects product distribution, needs adjustment based on activity characteristics
4. **Stock Consistency**: All stock operations must be completed within transactions or distributed locks
5. **Error Propagation**: Service layer returns business errors, converted to HTTP responses by Handler layer
6. **Performance Optimization**: Frequently accessed configuration data should use caching
7. **Logging Standards**: Key business operations must record logs, including user ID, operation type, results, etc.

### Development Checklist

- [ ] New dependencies registered in interface.go
- [ ] Transaction operations properly pass tx parameter
- [ ] Stock operations use distributed locks
- [ ] Errors use ecode package definitions
- [ ] Key operations record logs
- [ ] Concurrent operations are thread-safe
- [ ] Appropriate unit test coverage
- [ ] Random algorithms use safe random generators
- [ ] Strategy implementations follow Operator interface
- [ ] Window operations maintain state consistency

This documentation provides comprehensive guidelines for maintaining and extending the box service layer within the blind box project ecosystem, ensuring secure, performant, and maintainable blind box operations across all purchase strategies.