package box

import (
	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/box/box_active_config"
	boxGoodsDao "blind_box/app/dao/box/box_goods"
	boxDto "blind_box/app/dto/box"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

func (e *Entry) AdminActiveRecommendCheck(ctx *gin.Context, req *boxDto.AdminActiveRecommendCheckReq) (res *boxDto.AdminActiveRecommendCheckResp, err error) {
	res = &boxDto.AdminActiveRecommendCheckResp{}

	var (
		activeModel = &actDao.Model{}
		configsList = make([]*box_active_config.Model, 0)
		goodsList   = make([]*boxGoodsDao.Model, 0)

		num = uint32(0)

		eg errgroup.Group
	)
	eg.Go(func() error {
		activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminActiveRecommendCheck FetchByID")
			return ecode.SystemErr
		}
		if activeModel == nil {
			err = ecode.ActNotExistErr
			return err
		}
		return nil
	})
	eg.Go(func() error {
		configsList, err = e.BoxActiveConfigRepo.FindByFilter(ctx, &box_active_config.Filter{
			ActiveID: req.ActiveID,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminActiveRecommendCheck FindByFilter")
			return ecode.SystemErr
		}
		if len(configsList) <= 0 {
			err = ecode.BoxActiveConfigNotExistErr
			return err
		}

		return nil
	})
	eg.Go(func() error {
		goodsList, err = e.BoxGoodsRepo.FindByFilter(ctx, &boxGoodsDao.Filter{
			ActiveID: req.ActiveID,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminActiveRecommendCheck FindByFilter")
			return ecode.SystemErr
		}
		if len(goodsList) <= 0 {
			err = ecode.BoxGoodsNotExistErr
			return err
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveRecommendCheck err")
		return nil, err
	}

	for _, goods := range goodsList {
		// TODO: 数据库插入1条数据
		if goods.LevelID == 1 {
			num += 1
		}
	}

	if configsList[0].LotteryNum > num {
		err = ecode.BoxActiveRecommendGoodsErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminActiveConfigCreate(ctx *gin.Context, req *boxDto.AdminActiveConfigCreateReq) (res *boxDto.AdminActiveConfigCreateResp, err error) {
	res = &boxDto.AdminActiveConfigCreateResp{}

	var (
		active      = &actDao.Model{}
		configModel = &box_active_config.Model{}
	)
	active, err = e.ActiveRepo.FetchByID(ctx, req.ActiveId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigCreate FetchByID")
		return res, ecode.SystemErr
	}

	if active == nil {
		err = ecode.ActNotExistErr
		return
	}

	configs, err := e.BoxActiveConfigRepo.FindByFilter(ctx, &box_active_config.Filter{
		ActiveID: req.ActiveId,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigCreate FindByFilter")
		return res, ecode.SystemErr
	}
	if len(configs) > 0 {
		err = ecode.BoxActiveConfigExistErr
		return
	}

	configModel.ActiveID = req.ActiveId
	configModel.LotteryNum = req.LotteryNum
	configModel.HeadImage = req.HeadImage
	configModel.LocationImage = req.LocationImage
	configModel.BoxImage = req.BoxImage
	configModel.LevelText = req.LevelText
	configModel.ShakeNum = req.ShakeNum

	err = e.BoxActiveConfigRepo.CreateOrUpdate(ctx, configModel)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigCreate CreateOrUpdate")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminActiveConfigUpdate(ctx *gin.Context, req *boxDto.AdminActiveConfigUpdateReq) (res *boxDto.AdminActiveConfigUpdateResp, err error) {
	res = &boxDto.AdminActiveConfigUpdateResp{}

	var (
		active      = &actDao.Model{}
		configModel = &box_active_config.Model{}
		updateMap   = make(map[string]interface{})
	)
	active, err = e.ActiveRepo.FetchByID(ctx, req.ActiveId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigCreate FetchByID")
		return res, ecode.SystemErr
	}

	if active == nil {
		err = ecode.ActNotExistErr
		return
	}

	configs, err := e.BoxActiveConfigRepo.FindByFilter(ctx, &box_active_config.Filter{
		ActiveID: req.ActiveId,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigCreate FindByFilter")
		return res, ecode.SystemErr
	}
	if len(configs) <= 0 {
		err = ecode.BoxActiveConfigNotExistErr
		return
	}
	configModel = configs[0]

	if t := req.LotteryNum; configModel.LotteryNum != t {
		updateMap["lottery_num"] = t
	}
	if t := req.HeadImage; configModel.HeadImage != t {
		updateMap["head_image"] = t
	}
	if t := req.LocationImage; configModel.LocationImage != t {
		updateMap["location_image"] = t
	}
	if t := req.BoxImage; configModel.BoxImage != t {
		updateMap["box_image"] = t
	}
	if t := req.LevelText; configModel.LevelText != t {
		updateMap["level_text"] = t
	}
	if t := req.ShakeNum; configModel.ShakeNum != t {
		updateMap["shake_num"] = t
	}

	if len(updateMap) == 0 {
		return res, nil
	}

	err = e.BoxActiveConfigRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigUpdate UpdateMapByID")
		err = ecode.SystemErr
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminActiveConfigInfo(ctx *gin.Context, req *boxDto.AdminActiveConfigInfoReq) (res *boxDto.AdminActiveConfigInfoResp, err error) {
	res = &boxDto.AdminActiveConfigInfoResp{}

	var (
		activeModel = &actDao.Model{}
		configModel = &box_active_config.Model{}
	)
	activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigInfo FetchByID")
		return res, ecode.SystemErr
	}
	if activeModel == nil {
		err = ecode.ActNotExistErr
		return
	}
	configs, err := e.BoxActiveConfigRepo.FindByFilter(ctx, &box_active_config.Filter{
		ActiveID: req.ActiveID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveConfigInfo FindByFilter")
		return res, ecode.SystemErr
	}
	if len(configs) <= 0 {
		err = ecode.BoxActiveConfigNotExistErr
		return
	}
	configModel = configs[0]
	res.ID = configModel.ID
	res.ActiveID = configModel.ActiveID
	res.LotteryNum = configModel.LotteryNum
	res.HeadImage = configModel.HeadImage
	res.LocationImage = configModel.LocationImage
	res.BoxImage = configModel.BoxImage
	res.LevelText = configModel.LevelText
	res.ShakeNum = configModel.ShakeNum

	return res, nil
}
