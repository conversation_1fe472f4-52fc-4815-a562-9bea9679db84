package openapi

import (
	"encoding/json"
	"fmt"
	"time"

	"blind_box/app/dao/openapi/merchant"
	dto "blind_box/app/dto/openapi"
	"blind_box/pkg/utils"

	"github.com/gin-gonic/gin"
)

// MerchantManager 商户管理服务
type MerchantManager struct {
	merchantDao *merchant.Entry
}

// CreateMerchant 创建新商户
func (e *Entry) CreateMerchant(ctx *gin.Context, req *dto.CreateMerchantRequest) (*dto.CreateMerchantResponse, error) {
	// 检查商户编码是否已存在
	existing, err := e.MerchantRepo.FetchByMerchantCode(ctx, req.MerchantCode)
	if err != nil {
		return nil, fmt.Errorf("check merchant code failed: %w", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("商户编码已存在")
	}

	// 生成AppID和AppSecret
	appID := utils.GenerateAppID(req.MerchantCode)
	appSecret := utils.GenerateAppSecret()

	// 格式化白名单
	ipWhitelistStr := ""
	if len(req.IPWhitelist) > 0 {
		for i, ip := range req.IPWhitelist {
			if i > 0 {
				ipWhitelistStr += ","
			}
			ipWhitelistStr += ip
		}
	}

	skuWhitelistStr := utils.FormatSKUWhitelist(req.SKUWhitelist)

	// 设置默认值
	if req.QPSLimit == 0 {
		req.QPSLimit = 100
	}
	if req.DailyLimit == 0 {
		req.DailyLimit = 100000
	}
	if req.SingleAmountMax == 0 {
		req.SingleAmountMax = 10000
	}
	if req.DailyAmountMax == 0 {
		req.DailyAmountMax = 100000
	}

	// 创建商户记录
	model := &merchant.Model{
		AppID:             appID,
		AppSecret:         appSecret,
		MerchantName:      req.MerchantName,
		MerchantCode:      req.MerchantCode,
		ContactName:       req.ContactName,
		ContactMobile:     req.ContactMobile,
		ContactEmail:      req.ContactEmail,
		Status:            merchant.MerchantStatusEnable,
		CallbackUrl:       req.CallbackURL,
		IpWhitelist:       ipWhitelistStr,
		DailyLimit:        req.DailyLimit,
		QpsLimit:          req.QPSLimit,
		SingleAmountLimit: uint64(req.SingleAmountMax * 100), // 转换为分
		DailyAmountLimit:  uint64(req.DailyAmountMax * 100),  // 转换为分
		AllowedSkus:       skuWhitelistStr,
		Remark:            req.Remark,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	if err := e.MerchantRepo.Create(ctx, model); err != nil {
		return nil, fmt.Errorf("create merchant failed: %w", err)
	}

	// 生成配置
	config := e.generateConfig(model)

	return &dto.CreateMerchantResponse{
		MerchantID: model.ID,
		AppID:      appID,
		AppSecret:  appSecret,
		Config:     config,
	}, nil
}

// UpdateMerchantRequest 更新商户请求

// UpdateMerchant 更新商户信息
func (e *Entry) UpdateMerchant(ctx *gin.Context, req *dto.UpdateMerchantRequest) error {
	// 查询商户
	model, err := e.MerchantRepo.FetchByID(ctx, req.MerchantID)
	if err != nil {
		return fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return fmt.Errorf("商户不存在")
	}

	// 更新字段
	if req.MerchantName != "" {
		model.MerchantName = req.MerchantName
	}
	if req.ContactName != "" {
		model.ContactName = req.ContactName
	}
	if req.ContactMobile != "" {
		model.ContactMobile = req.ContactMobile
	}
	if req.ContactEmail != "" {
		model.ContactEmail = req.ContactEmail
	}
	if req.CallbackURL != "" {
		model.CallbackUrl = req.CallbackURL
	}
	if len(req.IPWhitelist) > 0 {
		ipWhitelistStr := ""
		for i, ip := range req.IPWhitelist {
			if i > 0 {
				ipWhitelistStr += ","
			}
			ipWhitelistStr += ip
		}
		model.IpWhitelist = ipWhitelistStr
	}
	if len(req.SKUWhitelist) > 0 {
		model.AllowedSkus = utils.FormatSKUWhitelist(req.SKUWhitelist)
	}
	if req.QPSLimit > 0 {
		model.QpsLimit = req.QPSLimit
	}
	if req.DailyLimit > 0 {
		model.DailyLimit = req.DailyLimit
	}
	if req.SingleAmountMax > 0 {
		model.SingleAmountLimit = uint64(req.SingleAmountMax * 100)
	}
	if req.DailyAmountMax > 0 {
		model.DailyAmountLimit = uint64(req.DailyAmountMax * 100)
	}
	if req.Status > 0 {
		model.Status = req.Status
	}
	if req.Remark != "" {
		model.Remark = req.Remark
	}

	model.UpdatedAt = time.Now()

	// 保存更新
	if err := e.MerchantRepo.Update(ctx, model); err != nil {
		return fmt.Errorf("update merchant failed: %w", err)
	}

	return nil
}

// GetMerchantConfig 获取商户配置
func (e *Entry) GetMerchantConfig(ctx *gin.Context, merchantID uint64) (*utils.MerchantConfig, error) {
	// 查询商户
	model, err := e.MerchantRepo.FetchByID(ctx, merchantID)
	if err != nil {
		return nil, fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return nil, fmt.Errorf("商户不存在")
	}

	return e.generateConfig(model), nil
}

// GetMerchantConfigByAppID 根据AppID获取商户配置
func (e *Entry) GetMerchantConfigByAppID(ctx *gin.Context, appID string) (*utils.MerchantConfig, error) {
	// 查询商户
	model, err := e.MerchantRepo.FetchByAppID(ctx, appID)
	if err != nil {
		return nil, fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return nil, fmt.Errorf("商户不存在")
	}

	return e.generateConfig(model), nil
}

// ExportConfig 导出配置文件
func (e *Entry) ExportConfig(ctx *gin.Context, merchantID uint64, format string) (string, error) {
	config, err := e.GetMerchantConfig(ctx, merchantID)
	if err != nil {
		return "", err
	}

	switch format {
	case "json":
		return utils.ExportConfigToJSON(config)
	case "yaml":
		// TODO: 实现YAML格式导出
		return "", fmt.Errorf("YAML格式暂未支持")
	default:
		return utils.ExportConfigToJSON(config)
	}
}

// ListMerchants 获取商户列表
func (e *Entry) ListMerchants(ctx *gin.Context, status uint32, keyword string) ([]*merchant.Model, error) {
	filter := &merchant.Filter{
		Status:       status,
		MerchantName: keyword,
	}

	list, err := e.MerchantRepo.FindByFilter(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("find merchants failed: %w", err)
	}

	return list, nil
}

// ResetAppSecret 重置AppSecret
func (e *Entry) ResetAppSecret(ctx *gin.Context, merchantID uint64) (string, error) {
	// 查询商户
	model, err := e.MerchantRepo.FetchByID(ctx, merchantID)
	if err != nil {
		return "", fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return "", fmt.Errorf("商户不存在")
	}

	// 生成新的AppSecret
	newSecret := utils.GenerateAppSecret()
	model.AppSecret = newSecret
	model.UpdatedAt = time.Now()

	// 保存更新
	if err := e.MerchantRepo.Update(ctx, model); err != nil {
		return "", fmt.Errorf("update merchant failed: %w", err)
	}

	return newSecret, nil
}

// ValidateMerchant 验证商户配置
func (e *Entry) ValidateMerchant(ctx *gin.Context, appID, appSecret string) (*merchant.Model, error) {
	// 查询商户
	model, err := e.MerchantRepo.FetchByAppID(ctx, appID)
	if err != nil {
		return nil, fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return nil, fmt.Errorf("商户不存在")
	}

	// 验证密钥
	if model.AppSecret != appSecret {
		return nil, fmt.Errorf("密钥错误")
	}

	// 验证状态
	if !model.IsEnabled() {
		return nil, fmt.Errorf("商户已禁用")
	}

	return model, nil
}

// TestConnection 测试连接
func (e *Entry) TestConnection(ctx *gin.Context, merchantID uint64) (bool, string, error) {
	// 查询商户
	model, err := e.MerchantRepo.FetchByID(ctx, merchantID)
	if err != nil {
		return false, "", fmt.Errorf("fetch merchant failed: %w", err)
	}
	if model == nil {
		return false, "", fmt.Errorf("商户不存在")
	}

	// 检查状态
	if !model.IsEnabled() {
		return false, "商户已禁用", nil
	}

	// 检查配置完整性
	if model.AppID == "" || model.AppSecret == "" {
		return false, "认证凭证不完整", nil
	}

	// TODO: 可以添加实际的连接测试，比如调用回调URL

	return true, "连接测试成功", nil
}

// generateConfig 生成商户配置
func (e *Entry) generateConfig(model *merchant.Model) *utils.MerchantConfig {
	// 解析IP白名单
	ipWhitelist := utils.ParseIPWhitelist(model.IpWhitelist)

	// 解析SKU白名单
	var skuWhitelist []uint64
	if model.AllowedSkus != "" {
		json.Unmarshal([]byte(model.AllowedSkus), &skuWhitelist)
	}

	// TODO: 从配置文件获取BaseURL
	baseURL := "https://api.example.com"

	config := &utils.MerchantConfig{
		Merchant: utils.MerchantInfo{
			MerchantID:   model.MerchantCode,
			MerchantName: model.MerchantName,
			ContactName:  model.ContactName,
			ContactPhone: model.ContactMobile,
			ContactEmail: model.ContactEmail,
		},
		Authentication: utils.AuthInfo{
			AppID:     model.AppID,
			AppSecret: model.AppSecret,
		},
		Endpoints: utils.EndpointInfo{
			BaseURL:         baseURL,
			ScanOrder:       "/api/v1/openapi/order/scan",
			PaymentCallback: "/api/v1/openapi/payment/callback",
			CallbackURL:     model.CallbackUrl,
		},
		Restrictions: utils.RestrictionInfo{
			IPWhitelist:  ipWhitelist,
			SKUWhitelist: skuWhitelist,
			QPSLimit:     int(model.QpsLimit),
			DailyLimit:   int(model.DailyLimit),
			AmountLimits: utils.AmountLimits{
				SingleMax: float64(model.SingleAmountLimit) / 100,
				DailyMax:  float64(model.DailyAmountLimit) / 100,
			},
		},
		Signature: utils.SignatureInfo{
			Algorithm: "SHA256",
			Format:    "JSON_BODY + '&key=' + APP_SECRET",
			Example:   "请参考集成文档获取示例代码",
		},
		CreatedAt: model.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	return config
}
