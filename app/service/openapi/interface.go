package openapi

import (
	"sync"

	skuDao "blind_box/app/dao/goods/sku"
	merchantDao "blind_box/app/dao/openapi/merchant"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	userDao "blind_box/app/dao/user"
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
)

// Entry 服务入口
type Entry struct {
	MerchantRepo     merchantDao.Repo
	OrderRepo        orderDao.Repo
	OrderDetailRepo  orderDetailDao.Repo
	SkuRepo          *skuDao.Entry
	UserRepo         userDao.Repo
	RedisCli         *redis.RedisClient
	MysqlEngine      *dbs.MysqlEngines
}

var (
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// GetService 获取服务实例
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

// newEntry 创建服务实例
func newEntry() *Entry {
	return &Entry{
		MerchantRepo:    merchantDao.GetRepo(),
		OrderRepo:       orderDao.GetRepo(),
		OrderDetailRepo: orderDetailDao.GetRepo(),
		SkuRepo:         skuDao.GetRepo(),
		UserRepo:        userDao.GetRepo(),
		RedisCli:        redis.GetRedisClient(),
		MysqlEngine:     dbs.NewMysqlEngines(),
	}
}
