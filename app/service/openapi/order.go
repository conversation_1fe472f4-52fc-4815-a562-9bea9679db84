package openapi

import (
	"fmt"
	"time"

	skuDao "blind_box/app/dao/goods/sku"
	merchantDao "blind_box/app/dao/openapi/merchant"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	"blind_box/app/dto/openapi"
	"blind_box/app/middleware"
	"blind_box/pkg/log"
	redisUtil "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	goredis "github.com/go-redis/redis/v8"
)

func (e *Entry) CreateScanOrder(ctx *gin.Context, req *openapi.CreateScanOrderReq) (*openapi.CreateScanOrderData, error) {
	merchant, err := middleware.GetMerchantFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// 检查幂等性
	orderKey := fmt.Sprintf("openapi:scan_order:%s:%s", merchant.AppID, req.OrderNo)
	existingOrderID, err := e.RedisCli.Get(ctx, orderKey).Result()
	if err == nil && existingOrderID != "" {
		existingOrder, err := e.OrderRepo.FetchByOutTradeNo(ctx, existingOrderID)
		if err == nil && existingOrder != nil {
			return e.buildScanOrderResponse(ctx, existingOrder, req.OrderNo)
		}
	}

	// 验证店铺权限
	if err := e.validateShopAccess(ctx, req.ShopID, merchant); err != nil {
		return nil, err
	}

	// 根据商品条码和数量查找SKU信息并计算总价
	skuList, quantityMap, totalAmount, err := e.getSkuListByGoods(ctx, req.Goods, merchant)
	if err != nil {
		return nil, err
	}

	// 验证金额限制
	if err := e.validateAmountLimits(ctx, totalAmount, merchant); err != nil {
		return nil, err
	}

	// 生成订单号
	orderID := e.generateOrderID()
	expireTime := time.Now().Add(30 * time.Minute).Unix()

	// 开启事务创建订单并锁定库存
	tx := e.MysqlEngine.UseWithGinCtx(ctx, true).Begin()
	var txErr error
	var txCommitted bool
	defer func() {
		if !txCommitted && txErr != nil {
			tx.Rollback()
		}
	}()

	// 创建订单记录
	orderModel := &orderDao.Model{
		OutTradeNo:     orderID,
		TotalFee:       totalAmount,
		Fee:            totalAmount, // 单价等于总价（OpenAPI订单）
		UserID:         0,           // OpenAPI订单暂时没有用户ID
		OrderStatus:    uint32(orderDao.OrderStatusCreated),
		SourcePlatform: uint32(orderDao.SourcePlatformWx),
		PayMethod:      0, // 待用户选择
		ConsumeType:    uint32(orderDao.ConsumeTypeDirect),
		OrderType:      uint32(orderDao.OrderTypeDirect),
		ShopID:         req.ShopID,
		MerchantID:     merchant.ID,
		PosOrderNo:     req.OrderNo,
		PayExpireTime:  expireTime,
		PurchaseType:   3, // 直接购买
	}

	if txErr = e.OrderRepo.CreateOrUpdateWithTx(ctx, tx, orderModel); txErr != nil {
		log.Ctx(ctx).WithError(txErr).Error("create order failed")
		return nil, fmt.Errorf("create order failed: %w", txErr)
	}

	// 创建订单详情并锁定库存
	orderDetails := make(orderDetailDao.ModelList, 0, len(skuList))
	for _, sku := range skuList {
		quantity := uint32(quantityMap[sku.BarCode])

		// 锁定库存
		if txErr = e.SkuRepo.LockStockWithTx(ctx, tx, sku.ID, quantity); txErr != nil {
			log.Ctx(ctx).WithError(txErr).Error("lock stock failed for sku: %d", sku.ID)
			return nil, fmt.Errorf("lock stock failed: %w", txErr)
		}

		// 创建订单详情
		orderDetail := &orderDetailDao.Model{
			OrderID:     orderModel.ID,
			UserID:      0,
			SkuID:       sku.ID,
			SpuID:       sku.SpuId,
			GoodsName:   sku.Title,
			TradeStatus: 0,        // 待支付状态（订单创建时还未支付）
			Quantity:    quantity, // 数量
		}

		orderDetails = append(orderDetails, orderDetail)
	}

	// 批量创建订单详情
	if txErr = e.OrderDetailRepo.BatchCreateWithTx(ctx, tx, orderDetails); txErr != nil {
		log.Ctx(ctx).WithError(txErr).Error("create order details failed")
		return nil, fmt.Errorf("create order details failed: %w", txErr)
	}

	// 提交事务
	if txErr = tx.Commit().Error; txErr != nil {
		log.Ctx(ctx).WithError(txErr).Error("commit transaction failed")
		return nil, fmt.Errorf("commit transaction failed: %w", txErr)
	}
	txCommitted = true // 标记事务已提交

	// 生成小程序链接和二维码
	miniProgramUrl := e.buildMiniProgramUrl(orderID, req.ShopID)
	qrCode := e.buildQrCodeContent(miniProgramUrl)

	// 保存幂等性映射
	err = e.RedisCli.Set(ctx, orderKey, orderID, 24*time.Hour).Err()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("save order mapping failed")
	}

	// 将订单加入超时队列（30分钟超时）
	if err := redisUtil.OrderTimeoutQueue.AddOrderTimeout(ctx, orderModel.ID, 30); err != nil {
		log.Ctx(ctx).WithError(err).Error("add order to timeout queue failed")
	}

	return &openapi.CreateScanOrderData{
		OrderID:        orderID,
		PosOrderNo:     req.OrderNo,
		MiniProgramUrl: miniProgramUrl,
		QrCode:         qrCode,
		TotalAmount:    totalAmount,
		ExpireTime:     expireTime,
		CreatedAt:      time.Now().Unix(),
	}, nil
}

func (e *Entry) QueryScanOrder(ctx *gin.Context, req *openapi.QueryScanOrderReq) (*openapi.QueryScanOrderData, error) {
	merchant, err := middleware.GetMerchantFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// 查找订单ID映射
	orderKey := fmt.Sprintf("openapi:scan_order:%s:%s", merchant.AppID, req.OrderNo)
	orderID, err := e.RedisCli.Get(ctx, orderKey).Result()
	if err == goredis.Nil {
		return nil, fmt.Errorf("order not found")
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("get order mapping failed")
		return nil, err
	}

	// 先从Redis获取订单信息
	orderInfoKey := fmt.Sprintf("openapi:scan_order_info:%s", orderID)
	orderInfo, err := e.RedisCli.HGetAll(ctx, orderInfoKey).Result()

	// 构建响应数据
	responseData := &openapi.QueryScanOrderData{
		OrderID:    orderID,
		PosOrderNo: req.OrderNo,
		CreatedAt:  time.Now().Unix(),
	}

	// 如果Redis中有数据
	if err == nil && len(orderInfo) > 0 {
		// 从Redis获取基本信息
		if totalAmount, ok := orderInfo["total_amount"]; ok {
			fmt.Sscanf(totalAmount, "%d", &responseData.TotalAmount)
		}
		if createdAt, ok := orderInfo["created_at"]; ok {
			var ts int64
			fmt.Sscanf(createdAt, "%d", &ts)
			responseData.CreatedAt = ts
			responseData.ExpireTime = ts + 1800 // 30分钟过期
		}

		// 默认状态为待支付
		responseData.Status = uint32(orderDao.OrderStatusCreated)

		// 尝试从数据库获取实际订单状态
		order, err := e.OrderRepo.FetchByOutTradeNo(ctx, orderID)
		if err == nil && order != nil {
			responseData.Status = order.OrderStatus
			responseData.TotalAmount = order.TotalFee
			responseData.ExpireTime = order.PayExpireTime
			responseData.CreatedAt = order.CreatedAt.Unix()

			// 如果已支付，添加支付信息
			if order.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
				responseData.PaidAmount = order.CashFee
				if order.PayTime > 0 {
					responseData.PaidTime = int64(order.PayTime)
				}
				responseData.PaymentMethod = e.getPaymentMethod(order.PayMethod)
				responseData.TransactionID = order.TransactionId
			}
		}
	} else {
		// Redis中没有数据，尝试从数据库获取
		order, err := e.OrderRepo.FetchByOutTradeNo(ctx, orderID)
		if err != nil || order == nil {
			return nil, fmt.Errorf("order not found")
		}

		responseData.Status = order.OrderStatus
		responseData.TotalAmount = order.TotalFee
		responseData.ExpireTime = order.PayExpireTime
		responseData.CreatedAt = order.CreatedAt.Unix()

		// 如果已支付，添加支付信息
		if order.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
			responseData.PaidAmount = order.CashFee
			if order.PayTime > 0 {
				responseData.PaidTime = int64(order.PayTime)
			}
			responseData.PaymentMethod = e.getPaymentMethod(order.PayMethod)
			responseData.TransactionID = order.TransactionId
		}
	}

	return responseData, nil
}

func (e *Entry) HandlePaymentCallback(ctx *gin.Context, req *openapi.PaymentCallback) error {
	// TODO: 实现支付回调处理
	return nil
}

// getPaymentMethod 获取支付方式描述
func (e *Entry) getPaymentMethod(payType uint32) string {
	switch payType {
	case 1:
		return "wechat_miniprogram"
	case 2:
		return "alipay"
	case 3:
		return "balance"
	default:
		return "unknown"
	}
}

func (e *Entry) validateAmountLimits(ctx *gin.Context, totalAmount uint64, merchant *merchantDao.Model) error {
	if totalAmount > merchant.SingleAmountLimit {
		return fmt.Errorf("order amount exceeds single limit")
	}

	dailyAmountKey := fmt.Sprintf("openapi:daily:amount:%s:%s", merchant.AppID, time.Now().Format("20060102"))
	dailyAmount, err := e.RedisCli.Get(ctx, dailyAmountKey).Uint64()
	if err != nil && err != goredis.Nil {
		log.Ctx(ctx).WithError(err).Error("get daily amount failed")
	}

	if dailyAmount+totalAmount > merchant.DailyAmountLimit {
		return fmt.Errorf("order amount exceeds daily limit")
	}

	err = e.RedisCli.IncrBy(ctx, dailyAmountKey, int64(totalAmount)).Err()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("update daily amount failed")
	}
	e.RedisCli.Expire(ctx, dailyAmountKey, 25*time.Hour)

	return nil
}

// validateShopAccess 验证店铺权限
func (e *Entry) validateShopAccess(ctx *gin.Context, shopID uint64, merchant *merchantDao.Model) error {
	// TODO: 验证商户是否有权访问该店铺
	// 可以检查商户的店铺白名单或合作关系
	return nil
}

// getSkuListByGoods 根据商品条码和数量获取SKU列表并计算总价
func (e *Entry) getSkuListByGoods(ctx *gin.Context, goods []openapi.GoodsItem, merchant *merchantDao.Model) ([]*skuDao.Model, map[string]int, uint64, error) {
	// 提取所有条码
	barCodes := make([]string, 0, len(goods))
	quantityMap := make(map[string]int)
	for _, item := range goods {
		barCodes = append(barCodes, item.Code)
		quantityMap[item.Code] = item.Num
	}

	// 根据条码查找SKU
	allSkuList, err := e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{
		BarCodeList: barCodes, // 按条码列表查询
	})
	if err != nil {
		return nil, nil, 0, err
	}

	if len(allSkuList) == 0 {
		return nil, nil, 0, fmt.Errorf("no valid goods found")
	}

	// 创建条码到SKU的映射
	skuMap := make(map[string]*skuDao.Model)
	for _, sku := range allSkuList {
		skuMap[sku.BarCode] = sku
	}

	// 按请求顺序构建sku列表，并验证商品
	orderedSkuList := make([]*skuDao.Model, 0, len(goods))
	var totalAmount uint64

	for _, item := range goods {
		sku, ok := skuMap[item.Code]
		if !ok {
			return nil, nil, 0, fmt.Errorf("goods with barcode %s not found", item.Code)
		}

		if !sku.JudgeCanOrdered() {
			return nil, nil, 0, fmt.Errorf("goods %s is not available", sku.BarCode)
		}

		if sku.GetUsableNum() < uint32(item.Num) {
			return nil, nil, 0, fmt.Errorf("goods %s stock not enough (need %d, available %d)", sku.BarCode, item.Num, sku.GetUsableNum())
		}

		// 检查商户权限
		if !merchant.IsSkuAllowed(sku.ID) {
			return nil, nil, 0, fmt.Errorf("goods %s not allowed for merchant", sku.BarCode)
		}

		// 按数量计算价格
		totalAmount += sku.SellPrice * uint64(item.Num)

		// 按顺序添加到结果列表
		orderedSkuList = append(orderedSkuList, sku)
	}

	return orderedSkuList, quantityMap, totalAmount, nil
}

// generateOrderID 生成订单ID
func (e *Entry) generateOrderID() string {
	// 生成唯一订单号，格式：OP + 时间戳 + 随机数
	return fmt.Sprintf("OP%d%06d", time.Now().Unix(), time.Now().Nanosecond()%1000000)
}

// buildMiniProgramUrl 构建小程序支付页面链接
func (e *Entry) buildMiniProgramUrl(orderID string, shopID uint64) string {
	// TODO: 根据实际小程序路径构建
	// qrcode/payment?source=popup_store&order_id=&store_id=
	return fmt.Sprintf("qrcode/payment?source=popup_store&order_id=%s&store_id=%d", orderID, shopID)
}

// buildQrCodeContent 构建二维码内容
func (e *Entry) buildQrCodeContent(miniProgramUrl string) string {
	// TODO: 根据实际小程序信息构建二维码
	// https://popup-store.youdian.cool/qrcode/payment?source=popup_store&order_id=&store_id=
	return fmt.Sprintf("https://pos.yuexia-goods.com/%s", miniProgramUrl)
}

// buildScanOrderResponse 构建扫码订单响应
func (e *Entry) buildScanOrderResponse(ctx *gin.Context, orderModel *orderDao.Model, posOrderNo string) (*openapi.CreateScanOrderData, error) {
	return &openapi.CreateScanOrderData{
		OrderID:        orderModel.OutTradeNo,
		PosOrderNo:     posOrderNo,
		MiniProgramUrl: e.buildMiniProgramUrl(orderModel.OutTradeNo, orderModel.ShopID), // 使用订单中的shop_id
		QrCode:         e.buildQrCodeContent(e.buildMiniProgramUrl(orderModel.OutTradeNo, orderModel.ShopID)),
		TotalAmount:    orderModel.TotalFee,
		ExpireTime:     orderModel.PayExpireTime,
		CreatedAt:      orderModel.CreatedAt.Unix(),
	}, nil
}
