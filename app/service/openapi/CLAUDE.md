# OpenAPI Service Architecture Documentation

## Overview

The OpenAPI service layer provides third-party integration capabilities for external partners, particularly POS systems and merchant integrations. This service implements scan-and-pay functionality, merchant management, and comprehensive authentication mechanisms for secure API access.

## Core Architecture

### Directory Structure
```
app/service/openapi/
├── interface.go          # Service interface and dependency injection
├── order.go             # Scan order creation and query functionality
└── merchant_manager.go  # Merchant configuration and management
```

### Service Entry Pattern
```go
type Entry struct {
    MerchantRepo     merchantDao.Repo         # Merchant data operations
    OrderRepo        orderDao.Repo            # Order data operations
    OrderDetailRepo  orderDetailDao.Repo      # Order detail operations
    SkuRepo          *skuDao.Entry            # SKU and stock management
    UserRepo         userDao.Repo             # User data operations
    RedisCli         *redis.RedisClient       # Redis cache operations
    MysqlEngine      *dbs.MysqlEngines        # Database transaction management
}
```

## Core Business Components

### 1. Scan-and-Pay Order System

#### Order Creation Flow
```go
func (e *Entry) CreateScanOrder(ctx *gin.Context, req *openapi.CreateScanOrderReq) (*openapi.CreateScanOrderData, error)
```

**Business Logic Flow**:
1. **Idempotency Check**: Redis-based duplicate order prevention
2. **Shop Access Validation**: Merchant authorization for shop operations
3. **SKU Resolution**: Barcode to SKU mapping and validation
4. **Amount Validation**: Single and daily limit enforcement
5. **Stock Management**: Atomic stock locking with transaction safety
6. **Order Generation**: Complete order and order detail creation
7. **Timeout Management**: 30-minute payment expiration with queue processing

#### Key Features
- **Idempotency**: `openapi:scan_order:{app_id}:{order_no}` Redis key mapping
- **Stock Locking**: Transaction-based inventory management
- **Merchant Validation**: Shop access and SKU whitelist enforcement
- **Timeout Handling**: Redis queue for order expiration management

### 2. Order Query System

#### Query Implementation
```go
func (e *Entry) QueryScanOrder(ctx *gin.Context, req *openapi.QueryScanOrderReq) (*openapi.QueryScanOrderData, error)
```

**Caching Strategy**:
- **Primary Cache**: Redis order info cache (`openapi:scan_order_info:{order_id}`)
- **Fallback Database**: Real-time order status from database
- **Hybrid Approach**: Cached basic info with live status updates

#### Response Data
- Order status and payment information
- Payment method and transaction details
- Expiration time and creation timestamps
- Real-time payment status updates

### 3. Merchant Management System

#### Merchant Lifecycle Management
```go
// Merchant creation with full configuration
func (e *Entry) CreateMerchant(ctx *gin.Context, req *dto.CreateMerchantRequest) (*dto.CreateMerchantResponse, error)

// Dynamic configuration updates
func (e *Entry) UpdateMerchant(ctx *gin.Context, req *dto.UpdateMerchantRequest) error

// Configuration export and validation
func (e *Entry) ValidateMerchant(ctx *gin.Context, appID, appSecret string) (*merchant.Model, error)
```

#### Merchant Configuration Features
- **Authentication**: AppID/AppSecret generation and management
- **Access Control**: IP whitelist with CIDR support
- **SKU Restrictions**: Merchant-specific product access control
- **Rate Limiting**: QPS and daily request limits
- **Amount Limits**: Single transaction and daily amount caps

## Authentication and Security

### 1. Signature Algorithm
```
Signature = SHA256(JSON_BODY + "&key=" + APP_SECRET)
```

**Implementation Notes**:
- Direct JSON string signing (simplified from complex flattening)
- Consistent request body handling
- Enhanced debugging support in development

### 2. Merchant Validation
```go
func (e *Entry) ValidateMerchant(ctx *gin.Context, appID, appSecret string) (*merchant.Model, error) {
    // 1. Merchant existence check
    // 2. AppSecret verification
    // 3. Status validation
    // 4. Return validated merchant model
}
```

### 3. Access Control Layers
- **IP Whitelist**: CIDR-based IP restriction
- **SKU Whitelist**: Product-level access control
- **Shop Authorization**: Store-specific permissions
- **Rate Limiting**: QPS and daily usage caps

## Business Rule Implementation

### 1. Amount Validation
```go
func (e *Entry) validateAmountLimits(ctx *gin.Context, totalAmount uint64, merchant *merchantDao.Model) error {
    // Single order amount check
    if totalAmount > merchant.SingleAmountLimit {
        return fmt.Errorf("order amount exceeds single limit")
    }
    
    // Daily cumulative amount check with Redis tracking
    dailyAmountKey := fmt.Sprintf("openapi:daily:amount:%s:%s", merchant.AppID, time.Now().Format("20060102"))
    // ... validation and increment logic
}
```

### 2. SKU Validation and Stock Management
```go
func (e *Entry) getSkuListByGoods(ctx *gin.Context, goods []openapi.GoodsItem, merchant *merchantDao.Model) ([]*skuDao.Model, map[string]int, uint64, error) {
    // 1. Barcode to SKU mapping
    // 2. Availability validation (status, stock)
    // 3. Merchant permission check
    // 4. Price calculation and aggregation
    // 5. Ordered result construction
}
```

### 3. Transaction Safety
```go
// Comprehensive transaction management with proper rollback
tx := e.MysqlEngine.UseWithGinCtx(ctx, true).Begin()
var txErr error
var txCommitted bool
defer func() {
    if !txCommitted && txErr != nil {
        tx.Rollback()
    }
}()

// Order creation and stock locking in single transaction
if txErr = e.OrderRepo.CreateOrUpdateWithTx(ctx, tx, orderModel); txErr != nil {
    return nil, fmt.Errorf("create order failed: %w", txErr)
}
```

## Redis Caching Strategy

### 1. Idempotency Management
```go
// Order mapping for duplicate prevention
orderKey := fmt.Sprintf("openapi:scan_order:%s:%s", merchant.AppID, req.OrderNo)
// 24-hour retention for order mapping
err = e.RedisCli.Set(ctx, orderKey, orderID, 24*time.Hour).Err()
```

### 2. Daily Limit Tracking
```go
// Daily amount accumulation
dailyAmountKey := fmt.Sprintf("openapi:daily:amount:%s:%s", merchant.AppID, time.Now().Format("20060102"))
// Atomic increment with 25-hour expiration
err = e.RedisCli.IncrBy(ctx, dailyAmountKey, int64(totalAmount)).Err()
e.RedisCli.Expire(ctx, dailyAmountKey, 25*time.Hour)
```

### 3. Order Information Cache
```go
// Fast order query support
orderInfoKey := fmt.Sprintf("openapi:scan_order_info:%s", orderID)
orderInfo, err := e.RedisCli.HGetAll(ctx, orderInfoKey).Result()
```

## Integration Points

### 1. Mini-Program Integration
```go
// Mini-program payment page URL generation
func (e *Entry) buildMiniProgramUrl(orderID string, shopID uint64) string {
    return fmt.Sprintf("qrcode/payment?source=popup_store&order_id=%s&store_id=%d", orderID, shopID)
}

// QR code content for scan-and-pay
func (e *Entry) buildQrCodeContent(miniProgramUrl string) string {
    return fmt.Sprintf("https://pos.yuexia-goods.com/%s", miniProgramUrl)
}
```

### 2. Order Timeout Management
```go
// Redis queue integration for order expiration
if err := redisUtil.OrderTimeoutQueue.AddOrderTimeout(ctx, orderModel.ID, 30); err != nil {
    log.Ctx(ctx).WithError(err).Error("add order to timeout queue failed")
}
```

### 3. Stock Management Integration
```go
// Atomic stock locking within transaction
if txErr = e.SkuRepo.LockStockWithTx(ctx, tx, sku.ID, quantity); txErr != nil {
    log.Ctx(ctx).WithError(txErr).Error("lock stock failed for sku: %d", sku.ID)
    return nil, fmt.Errorf("lock stock failed: %w", txErr)
}
```

## Merchant Configuration System

### 1. Configuration Generation
```go
func (e *Entry) generateConfig(model *merchant.Model) *utils.MerchantConfig {
    return &utils.MerchantConfig{
        Merchant: utils.MerchantInfo{
            MerchantID:   model.MerchantCode,
            MerchantName: model.MerchantName,
            // ... other merchant info
        },
        Authentication: utils.AuthInfo{
            AppID:     model.AppID,
            AppSecret: model.AppSecret,
        },
        Endpoints: utils.EndpointInfo{
            BaseURL:         baseURL,
            ScanOrder:       "/api/v1/openapi/order/scan",
            PaymentCallback: "/api/v1/openapi/payment/callback",
            CallbackURL:     model.CallbackUrl,
        },
        Restrictions: utils.RestrictionInfo{
            IPWhitelist:  ipWhitelist,
            SKUWhitelist: skuWhitelist,
            QPSLimit:     int(model.QpsLimit),
            DailyLimit:   int(model.DailyLimit),
            AmountLimits: utils.AmountLimits{
                SingleMax: float64(model.SingleAmountLimit) / 100,
                DailyMax:  float64(model.DailyAmountLimit) / 100,
            },
        },
        Signature: utils.SignatureInfo{
            Algorithm: "SHA256",
            Format:    "JSON_BODY + '&key=' + APP_SECRET",
            Example:   "请参考集成文档获取示例代码",
        },
    }
}
```

### 2. Dynamic Configuration Management
- **Real-time Updates**: Immediate configuration changes
- **Configuration Export**: JSON/YAML format support
- **Validation Testing**: Connection and configuration verification
- **Secret Management**: AppSecret reset and rotation

## Error Handling and Observability

### 1. Error Classification
```go
// Business validation errors
if !sku.JudgeCanOrdered() {
    return nil, nil, 0, fmt.Errorf("goods %s is not available", sku.BarCode)
}

// Merchant permission errors
if !merchant.IsSkuAllowed(sku.ID) {
    return nil, nil, 0, fmt.Errorf("goods %s not allowed for merchant", sku.BarCode)
}

// System operational errors
if txErr = tx.Commit().Error; txErr != nil {
    log.Ctx(ctx).WithError(txErr).Error("commit transaction failed")
    return nil, fmt.Errorf("commit transaction failed: %w", txErr)
}
```

### 2. Logging Strategy
```go
// Structured logging with context
log.Ctx(ctx).WithError(err).Error("create order failed")
log.Ctx(ctx).WithError(err).Error("lock stock failed for sku: %d", sku.ID)

// Non-critical operation warnings
if err := redisUtil.OrderTimeoutQueue.AddOrderTimeout(ctx, orderModel.ID, 30); err != nil {
    log.Ctx(ctx).WithError(err).Error("add order to timeout queue failed")
}
```

### 3. Monitoring Points
- **Order Creation Success Rate**: Transaction completion metrics
- **Stock Lock Failures**: Inventory management monitoring
- **Payment Timeout Rate**: Order expiration tracking
- **Merchant Authentication**: Security event monitoring

## Performance Optimization

### 1. Concurrent Processing
```go
// Parallel SKU processing for large orders
for _, item := range goods {
    // Process each item concurrently where possible
    // Validate permissions and calculate pricing
}
```

### 2. Batch Operations
```go
// Batch order detail creation
if txErr = e.OrderDetailRepo.BatchCreateWithTx(ctx, tx, orderDetails); txErr != nil {
    log.Ctx(ctx).WithError(txErr).Error("create order details failed")
    return nil, fmt.Errorf("create order details failed: %w", txErr)
}
```

### 3. Caching Optimization
- **Merchant Config Caching**: Reduce database queries for configuration
- **SKU Information Caching**: Fast product lookup and validation
- **Daily Limit Caching**: Efficient rate limiting tracking

## Security Considerations

### 1. Data Protection
- **Sensitive Information**: AppSecret encryption and secure storage
- **Request Validation**: Comprehensive input sanitization
- **SQL Injection Prevention**: Parameterized queries through ORM

### 2. Access Control
- **Multi-layer Authentication**: IP, signature, and merchant validation
- **Resource Isolation**: Merchant-specific data access
- **Audit Logging**: Comprehensive operation tracking

### 3. Rate Limiting
```go
// Multi-dimensional rate limiting
type MerchantLimits struct {
    QPSLimit:          uint32  // Requests per second
    DailyLimit:        uint32  // Requests per day
    SingleAmountLimit: uint64  // Single transaction limit
    DailyAmountLimit:  uint64  // Daily transaction amount limit
}
```

## Development Guidelines

### 1. Code Standards
- **Error Wrapping**: Comprehensive error context preservation
- **Transaction Safety**: Proper rollback and commit handling
- **Resource Cleanup**: Defer patterns for resource management

### 2. Testing Approach
- **Unit Testing**: Individual method validation with mocks
- **Integration Testing**: End-to-end order flow verification
- **Load Testing**: Performance validation under concurrent load

### 3. Documentation Requirements
- **API Documentation**: Complete endpoint specifications
- **Integration Guides**: Merchant onboarding documentation
- **Security Guidelines**: Authentication and authorization best practices

## Integration Workflow

### 1. Merchant Onboarding
1. **Registration**: Create merchant with initial configuration
2. **Configuration**: Set up IP whitelist, SKU permissions, and limits
3. **Testing**: Validate configuration and connection
4. **Go-Live**: Enable merchant for production use

### 2. Order Processing Flow
1. **Authentication**: Validate merchant credentials and permissions
2. **Order Creation**: Process scan order with stock locking
3. **Payment Flow**: Generate mini-program payment URL
4. **Status Tracking**: Provide real-time order status updates
5. **Completion**: Handle payment success and order fulfillment

### 3. Monitoring and Maintenance
- **Performance Monitoring**: Track API response times and success rates
- **Error Analysis**: Monitor and analyze failure patterns
- **Configuration Management**: Handle merchant configuration changes
- **Security Auditing**: Regular security review and updates

This documentation provides comprehensive guidelines for maintaining and extending the OpenAPI service layer within the blind box project ecosystem, ensuring secure and reliable third-party integrations.