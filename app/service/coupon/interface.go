package coupon

import (
	actDao "blind_box/app/dao/activity"
	couponDao "blind_box/app/dao/coupon"
	cIssueDao "blind_box/app/dao/coupon/coupon_issue"
	cRangeDao "blind_box/app/dao/coupon/coupon_range"
	cTargetDao "blind_box/app/dao/coupon/coupon_target"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	userDao "blind_box/app/dao/user"
	"blind_box/pkg/redis"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	UserRepo         *userDao.Entry
	CouponRepo       *couponDao.Entry
	CouponUserRepo   *cUserDao.Entry
	CouponRangeRepo  *cRangeDao.Entry
	CouponIssueRepo  *cIssueDao.Entry
	CouponTargetRepo *cTargetDao.Entry
	ActRepo          *actDao.Entry
	RedisCli         *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:         userDao.GetRepo(),
		CouponRepo:       couponDao.GetRepo(),
		CouponUserRepo:   cUserDao.GetRepo(),
		CouponRangeRepo:  cRangeDao.GetRepo(),
		CouponIssueRepo:  cIssueDao.GetRepo(),
		CouponTargetRepo: cTargetDao.GetRepo(),
		ActRepo:          actDao.GetRepo(),
		RedisCli:         redis.GetRedisClient(),
	}
}
