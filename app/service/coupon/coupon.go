package coupon

import (
	"blind_box/app/common/dbs"
	couponDao "blind_box/app/dao/coupon"
	cIssueDao "blind_box/app/dao/coupon/coupon_issue"
	cTargetDao "blind_box/app/dao/coupon/coupon_target"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	couponDto "blind_box/app/dto/coupon"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// UserRegCouponReward 新人注册优惠券奖励
func (e *Entry) RegCouponList(ctx *gin.Context, uid uint64) ([]*couponDto.HomepageCouponItem, error) {
	ret := []*couponDto.HomepageCouponItem{}

	validIssue, err := e.CouponIssueRepo.FindByFilter(ctx, &cIssueDao.Filter{IsValid: true, SourceID: uint64(couponDao.CouponSourceReg)})
	if err != nil {
		return nil, err
	}
	couponList, err := e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: validIssue.GetCids(), Status: uint32(dbs.StatusEnable)})
	if err != nil {
		return nil, err
	}
	couponMap := couponList.GetIDMap()

	for _, issue := range validIssue {
		if coupon, ok := couponMap[issue.CouponID]; ok {
			item := &couponDto.HomepageCouponItem{
				CouponID:        issue.CouponID,
				IssueID:         issue.ID,
				CouponName:      coupon.Name,
				DiscountType:    coupon.DiscountType,
				DiscountMethod:  coupon.DiscountMethod,
				DiscountAmount:  coupon.DiscountAmount,
				ThresholdType:   coupon.ThresholdType,
				ThresholdAmount: coupon.ThresholdAmount,
				ExpireDays:      coupon.ExpireDays,
				RangeType:       coupon.RangeType,
				Extra:           coupon.GetExtra(),
			}
			ret = append(ret, item)
		}
	}
	return ret, nil
}

// HomepageCouponList 首页弹出优惠券奖励 // TODO 缓存一分钟
func (e *Entry) HomepageCouponList(ctx *gin.Context, uid uint64) ([]*couponDto.HomepageCouponItem, error) {
	// 1. 获取全部用户的
	// 2. 获取指定用户的
	// 3. 去除用户已经领取的
	ret := []*couponDto.HomepageCouponItem{}
	filter := &cIssueDao.Filter{IsValid: true, SourceID: uint64(couponDao.CouponSourceHomepage)}
	if uid > 0 {
		var (
			eg        errgroup.Group
			usedIDS   = []uint64{}
			assignIDS = []uint64{}
		)
		eg.Go(func() (err error) {
			if usedIDS, err = e.CouponUserRepo.FindXIDSByFilter(ctx, &cUserDao.Filter{UID: uid}, cUserDao.PluckIID); err != nil {
				return err
			}
			if len(usedIDS) > 0 {
				filter.NotIDS = usedIDS
			}
			return
		})
		eg.Go(func() (err error) {
			filter.ByAssign = true
			if assignIDS, err = e.CouponTargetRepo.FindXIDSByFilter(ctx, &cTargetDao.Filter{UserID: uid}, cTargetDao.PluckIID); err != nil {
				return err
			}
			if len(assignIDS) > 0 {
				filter.AssignIDS = assignIDS
			}
			return
		})
		if err := eg.Wait(); err != nil {
			return nil, err
		}
	}

	validIssue, err := e.CouponIssueRepo.FindByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}
	couponList, err := e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: validIssue.GetCids(), Status: uint32(dbs.StatusEnable)})
	if err != nil {
		return nil, err
	}
	couponMap := couponList.GetIDMap()

	for _, issue := range validIssue {
		if coupon, ok := couponMap[issue.CouponID]; ok {
			item := &couponDto.HomepageCouponItem{
				CouponID:        issue.CouponID,
				IssueID:         issue.ID,
				CouponName:      coupon.Name,
				DiscountType:    coupon.DiscountType,
				DiscountMethod:  coupon.DiscountMethod,
				DiscountAmount:  coupon.DiscountAmount,
				ThresholdType:   coupon.ThresholdType,
				ThresholdAmount: coupon.ThresholdAmount,
				ExpireDays:      coupon.ExpireDays,
				RangeType:       coupon.RangeType,
				Extra:           coupon.GetExtra(),
			}
			ret = append(ret, item)
		}
	}
	return ret, nil
}
