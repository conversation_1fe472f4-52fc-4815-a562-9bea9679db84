package coupon

import (
	"sort"

	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	couponDao "blind_box/app/dao/coupon"
	cRangeDao "blind_box/app/dao/coupon/coupon_range"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	couponDto "blind_box/app/dto/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"blind_box/pkg/util/decimalUtil"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

// UserGetRegCoupon 用户领取新人注册奖励
func (e *Entry) UserGetRegCoupon(ctx *gin.Context, tx *gorm.DB, uid uint64) error {
	if err := e.RedisCli.Lock(ctx, redis.GetUserRegCouponRewardLockKey(uid), redis.DefaultLockTime); err != nil {
		return ecode.ActBusyLockErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserRegCouponRewardLockKey(uid))

	clist, err := e.RegCouponList(ctx, uid)
	if err != nil {
		return err
	}
	if len(clist) == 0 {
		return nil
	}
	data := []*cUserDao.Model{}
	for _, info := range clist {
		item := &cUserDao.Model{
			UserID:     uid,
			CouponID:   info.CouponID,
			IssueID:    info.IssueID,
			SourceID:   uint64(couponDao.CouponSourceReg),
			ExpireTime: carbon.Now().EndOfDay().AddDays(int(info.ExpireDays)).Timestamp(),
			Status:     uint32(cUserDao.CUserGet),
		}
		data = append(data, item)
	}

	return e.CouponUserRepo.BatchCreateWithTx(tx, data)
}

// UserGetHomepageCoupon 领取首页弹出优惠券
func (e *Entry) UserGetHomepageCoupon(ctx *gin.Context, uid uint64) error {
	if err := e.RedisCli.Lock(ctx, redis.GetHomePageCouponRewardLockKey(uid), redis.DefaultLockTime); err != nil {
		return ecode.ActBusyLockErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetHomePageCouponRewardLockKey(uid))

	clist, err := e.HomepageCouponList(ctx, uid)
	if err != nil {
		return err
	}
	if len(clist) == 0 {
		return nil
	}
	data := []*cUserDao.Model{}
	for _, info := range clist {
		item := &cUserDao.Model{
			UserID:     uid,
			CouponID:   info.CouponID,
			IssueID:    info.IssueID,
			SourceID:   uint64(couponDao.CouponSourceHomepage),
			ExpireTime: carbon.Now().EndOfDay().AddDays(int(info.ExpireDays)).Timestamp(),
			Status:     uint32(cUserDao.CUserGet),
		}
		data = append(data, item)
	}

	return e.CouponUserRepo.BatchCreate(ctx, data)
}

// UserCouponList 用户优惠券列表
func (e *Entry) UserCouponList(ctx *gin.Context, uid uint64, req couponDto.UserCouponListReq) (*couponDto.UserCouponListResp, error) {
	filter := &cUserDao.Filter{UID: uid, Sort: dbs.CommonSort{Field: dbs.SortFieldID, Method: dbs.SortMethodDesc}}
	switch req.Status {
	case string(cUserDao.CUserFilterWaiting):
		filter.Valid = true
		filter.Sort.Field = dbs.SortFieldExpireTime
		filter.Sort.Method = dbs.SortMethodAsc
	case string(cUserDao.CUserFilterUsed):
		filter.Status = uint32(cUserDao.CUserUsed)
		filter.Sort.Field = dbs.SortFieldUseTime
		filter.Sort.Method = dbs.SortMethodDesc
	case string(cUserDao.CUserFilterExpired):
		filter.Invalid = true
		filter.Sort.Field = dbs.SortFieldExpireTime
		filter.Sort.Method = dbs.SortMethodDesc
	default:
		return nil, ecode.ParamErr
	}
	invalidCouponIDS, err := e.CouponRepo.FindIdsByFilter(ctx, &couponDao.Filter{Status: uint32(dbs.StatusDisable)})
	if err != nil {
		return nil, err
	}
	if len(invalidCouponIDS) > 0 {
		filter.NotCouponIDS = invalidCouponIDS
	}

	total, list, err := e.CouponUserRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	couponList, err := e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: list.GetCids(), Status: uint32(dbs.StatusEnable)})
	if err != nil {
		return nil, err
	}
	couponMap := couponList.GetIDMap()

	retList := make([]*couponDto.UserCouponListItem, 0, len(list))
	for _, val := range list {
		if cInfo, ok := couponMap[val.CouponID]; ok {
			item := &couponDto.UserCouponListItem{
				CUID:           val.ID,
				CouponID:       val.CouponID,
				CouponCode:     cInfo.Code,
				CouponName:     cInfo.Name,
				UserID:         val.UserID,
				TradeNo:        val.TradeNo,
				DiscountAmount: val.DiscountAmount,
				SourceID:       val.SourceID,
				ExpireTime:     carbon.CreateFromTimestamp(val.ExpireTime).ToDateTimeString(),
				RangeType:      cInfo.RangeType,
				EntityIDS:      []uint64{},
				Extra:          cInfo.GetExtra(),
				Status:         val.Status,
				CreatedTime:    val.GetCreatedTime(),
			}
			if cInfo.RangeType == uint32(couponDao.CouponRangeAct) {
				entityIDS, err := e.CouponRangeRepo.FindEntityIDSByFilter(ctx, &cRangeDao.Filter{CouponID: cInfo.ID, EntityType: cInfo.RangeType})
				if err != nil {
					continue
				}
				actIDS, err := e.ActRepo.FindNoticeActIdsByFilter(ctx, &actDao.NoticeFilter{ActIDS: entityIDS})
				if err != nil {
					continue
				}
				item.EntityIDS = actIDS
			}
			retList = append(retList, item)
		}
	}
	return &couponDto.UserCouponListResp{
		List:  retList,
		Total: total,
	}, nil
}

// ActUserAvailableCoupon 查询活动-用户可使用的优惠券列表
func (e *Entry) ActUserAvailableCoupon(ctx *gin.Context, uid, actID uint64, drawNum uint32) (*couponDto.ActUserAvailableCouponResp, error) {
	var (
		eg              errgroup.Group
		userValidCoupon = cUserDao.ModelList{}
		couponIDS       = []uint64{}
		actInfo         = &actDao.Model{}
	)
	eg.Go(func() (err error) {
		if actInfo, err = e.ActRepo.FetchByID(ctx, actID); err != nil {
			return err
		}
		if actInfo.ID == 0 {
			return ecode.ActNotExistErr
		}
		return
	})
	eg.Go(func() (err error) {
		if userValidCoupon, err = e.CouponUserRepo.FindByFilter(ctx, &cUserDao.Filter{UID: uid, Enable: true}); err != nil {
			return err
		}
		couponIDS = userValidCoupon.GetCids()
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	var (
		couponList     = couponDao.ModelList{}
		couponMap      = make(map[uint64]*couponDao.Model)
		err            error
		availableRet   = couponDto.ActUserAvailableCouponItemSort{}
		unavailableRet = couponDto.ActUserAvailableCouponItemSort{}
	)

	if couponList, err = e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: couponIDS, Status: uint32(dbs.StatusEnable)}); err != nil {
		return nil, err
	}
	couponMap = couponList.GetIDMap()

	for _, val := range userValidCoupon {
		if cInfo, ok := couponMap[val.CouponID]; ok {
			item := &couponDto.ActUserAvailableCouponItem{
				CUID:           val.ID,
				CouponID:       val.CouponID,
				CouponCode:     cInfo.Code,
				CouponName:     cInfo.Name,
				DiscountAmount: cInfo.DiscountAmount,
				ExpireTime:     carbon.CreateFromTimestamp(val.ExpireTime).ToDateTimeString(),
				RangeType:      cInfo.RangeType,
				Extra:          cInfo.GetExtra(),
				Status:         val.Status,
				CreatedTime:    val.GetCreatedTime(),
			}

			// 门槛未达到 - 使用安全计算避免溢出
			if cInfo.ThresholdType == uint32(couponDao.CouponTTRealityPaySubtract) {
				totalAmount, err := decimalUtil.SafeMultiply(actInfo.ActPrice, drawNum)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("ActUserAvailableCoupon price calculation overflow")
					// 计算溢出时认为金额足够大，不加入不可用列表
				} else if totalAmount < cInfo.ThresholdAmount {
					unavailableRet = append(unavailableRet, item)
					continue
				}
			}

			// 判断是否限制活动
			if cInfo.RangeType == uint32(couponDao.CouponRangeAct) {
				num, err := e.CouponRangeRepo.CountByFilter(ctx, &cRangeDao.Filter{
					CouponID:   cInfo.ID,
					EntityType: cInfo.RangeType,
					EntityID:   actID,
				})
				if err != nil {
					continue
				}
				if num == 0 {
					unavailableRet = append(unavailableRet, item)
					continue
				}
			}
			availableRet = append(availableRet, item)
		}
	}
	sort.Sort(availableRet)
	return &couponDto.ActUserAvailableCouponResp{
		AvailableList:    availableRet,
		AvailableTotal:   len(availableRet),
		UnavailableList:  unavailableRet,
		UnavailableTotal: len(unavailableRet),
	}, nil
}

// CheckWhenUseCoupon 使用优惠券时校验优惠券
func (e *Entry) CheckWhenUseCoupon(ctx *gin.Context, uid, cuID, actID uint64, drawNum uint32) (*couponDto.ActUserAvailableCouponItem, error) {
	var (
		eg      errgroup.Group
		ucInfo  = &cUserDao.Model{}
		actInfo = &actDao.Model{}
	)
	eg.Go(func() (err error) {
		if ucInfo, err = e.CouponUserRepo.FetchByID(ctx, cuID); err != nil {
			return
		}
		if ucInfo.UserID != uid {
			return ecode.CouponStatusInvalidErr
		}
		if ucInfo.Status == uint32(cUserDao.CUserUsing) {
			return ecode.CouponUserUsingErr
		}
		if ucInfo.Status == uint32(cUserDao.CUserUsed) {
			return ecode.CouponUserUsedErr
		}
		if ucInfo.Status != uint32(cUserDao.CUserGet) || ucInfo.ExpireTime < carbon.Now().Timestamp() {
			return ecode.CouponStatusInvalidErr
		}
		return
	})
	eg.Go(func() (err error) {
		if actInfo, err = e.ActRepo.FetchByID(ctx, actID); err != nil {
			return err
		}
		if actInfo.ID == 0 {
			return ecode.ActNotExistErr
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	couponInfo, _ := e.CouponRepo.FetchByUdx(ctx, &couponDao.Filter{ID: ucInfo.CouponID, Status: uint32(dbs.StatusEnable)})
	if couponInfo.ID == 0 {
		return nil, ecode.CouponStatusInvalidErr
	}
	// 门槛未达到 - 使用安全计算避免溢出
	if couponInfo.ThresholdType == uint32(couponDao.CouponTTRealityPaySubtract) {
		totalAmount, err := decimalUtil.SafeMultiply(actInfo.ActPrice, drawNum)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("CheckWhenUseCoupon price calculation overflow")
			// 计算溢出时认为金额足够大，允许使用优惠券
		} else if totalAmount < couponInfo.ThresholdAmount {
			return nil, ecode.CouponNoUseConditionErr
		}
	}
	// 判断是否限制活动
	if couponInfo.RangeType == uint32(couponDao.CouponRangeAct) {
		num, _ := e.CouponRangeRepo.CountByFilter(ctx, &cRangeDao.Filter{
			CouponID:   couponInfo.ID,
			EntityType: couponInfo.RangeType,
			EntityID:   actID,
		})
		if num == 0 {
			return nil, ecode.CouponNoUseConditionErr
		}
	}
	availableInfo := &couponDto.ActUserAvailableCouponItem{
		CUID:           ucInfo.ID,
		CouponID:       ucInfo.CouponID,
		CouponCode:     couponInfo.Code,
		CouponName:     couponInfo.Name,
		DiscountAmount: couponInfo.DiscountAmount,
		ExpireTime:     carbon.CreateFromTimestamp(ucInfo.ExpireTime).ToDateTimeString(),
		RangeType:      couponInfo.RangeType,
		Extra:          couponInfo.GetExtra(),
		Status:         ucInfo.Status,
		CreatedTime:    ucInfo.GetCreatedTime(),
	}
	return availableInfo, nil
}

// UpdateUserCouponStatus 更新用户优惠券状态
func (e *Entry) UpdateUserCouponStatus(ctx *gin.Context, tx *gorm.DB, cuID uint64, status cUserDao.DFCUserStatus, tradeNo string, dctAmount uint64) error {
	m := &cUserDao.Model{
		Status: uint32(status),
	}

	switch status {
	case cUserDao.CUserUsing, cUserDao.CUserUsed:
		if tradeNo != "" {
			m.TradeNo = tradeNo
		}
		if dctAmount != 0 {
			m.DiscountAmount = dctAmount
		}
		m.UseTime = carbon.Now().Timestamp()
		return e.CouponUserRepo.UpdateByFilterWithTx(tx, &cUserDao.Filter{ID: cuID}, m)
	case cUserDao.CUserGet: // 订单取消时,修改优惠券状态
		m.TradeNo = tradeNo
		m.DiscountAmount = dctAmount
		m.UseTime = 0
		return e.CouponUserRepo.UpdateByFilterWithTx(tx, &cUserDao.Filter{ID: cuID}, m)
	}
	return nil
}

// CheckWhenUseDirectPurchaseCoupon 直接购买时校验优惠券
// 与CheckWhenUseCoupon的区别：
// 1. 不依赖活动信息，直接使用商品价格
// 2. 支持商品范围验证（可扩展为SKU、SPU范围）
// 3. 专为直接购买场景设计
func (e *Entry) CheckWhenUseDirectPurchaseCoupon(ctx *gin.Context, uid, cuID uint64, totalAmount uint64, skuID uint64) (*couponDto.ActUserAvailableCouponItem, error) {
	var (
		eg     errgroup.Group
		ucInfo = &cUserDao.Model{}
	)

	// 验证用户优惠券
	eg.Go(func() (err error) {
		if ucInfo, err = e.CouponUserRepo.FetchByID(ctx, cuID); err != nil {
			return
		}
		if ucInfo.UserID != uid {
			return ecode.CouponStatusInvalidErr
		}
		if ucInfo.Status == uint32(cUserDao.CUserUsing) {
			return ecode.CouponUserUsingErr
		}
		if ucInfo.Status == uint32(cUserDao.CUserUsed) {
			return ecode.CouponUserUsedErr
		}
		if ucInfo.Status != uint32(cUserDao.CUserGet) || ucInfo.ExpireTime < carbon.Now().Timestamp() {
			return ecode.CouponStatusInvalidErr
		}
		return
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	// 获取优惠券配置信息
	couponInfo, err := e.CouponRepo.FetchByUdx(ctx, &couponDao.Filter{ID: ucInfo.CouponID, Status: uint32(dbs.StatusEnable)})
	if err != nil {
		return nil, err
	}
	if couponInfo.ID == 0 {
		return nil, ecode.CouponStatusInvalidErr
	}

	// 验证门槛金额
	if couponInfo.ThresholdType == uint32(couponDao.CouponTTRealityPaySubtract) {
		if totalAmount < couponInfo.ThresholdAmount {
			log.Ctx(ctx).Info("CheckWhenUseDirectPurchaseCoupon threshold not met: totalAmount=%d, threshold=%d",
				totalAmount, couponInfo.ThresholdAmount)
			return nil, ecode.CouponNoUseConditionErr
		}
	}

	// 验证商品范围限制
	// 目前直接购买场景主要验证是否为全场通用
	// 后续可以扩展为验证具体的SKU或SPU范围
	if couponInfo.RangeType == uint32(couponDao.CouponRangeAct) {
		// 直接购买场景下，如果优惠券限制了活动范围，则不能使用
		// 因为直接购买不依赖于盲盒活动
		log.Ctx(ctx).Warn("CheckWhenUseDirectPurchaseCoupon: coupon %d is limited to activities, cannot use in direct purchase", couponInfo.ID)
		return nil, ecode.CouponNoUseConditionErr
	}

	// TODO: 后续可以添加SKU/SPU范围验证
	// if couponInfo.RangeType == uint32(couponDao.CouponRangeSku) {
	//     // 验证SKU范围
	// }
	// if couponInfo.RangeType == uint32(couponDao.CouponRangeSpu) {
	//     // 验证SPU范围
	// }

	// 构建返回信息
	availableInfo := &couponDto.ActUserAvailableCouponItem{
		CUID:           ucInfo.ID,
		CouponID:       ucInfo.CouponID,
		CouponCode:     couponInfo.Code,
		CouponName:     couponInfo.Name,
		DiscountAmount: couponInfo.DiscountAmount,
		ExpireTime:     carbon.CreateFromTimestamp(ucInfo.ExpireTime).ToDateTimeString(),
		RangeType:      couponInfo.RangeType,
		Extra:          couponInfo.GetExtra(),
		Status:         ucInfo.Status,
		CreatedTime:    ucInfo.GetCreatedTime(),
	}

	log.Ctx(ctx).Info("CheckWhenUseDirectPurchaseCoupon success: CouponID=%d, DiscountAmount=%d, TotalAmount=%d",
		couponInfo.ID, couponInfo.DiscountAmount, totalAmount)

	return availableInfo, nil
}
