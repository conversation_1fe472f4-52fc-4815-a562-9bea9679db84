package coupon

import (
	actDao "blind_box/app/dao/activity"
	couponDao "blind_box/app/dao/coupon"
	cRangeDao "blind_box/app/dao/coupon/coupon_range"
	couponDto "blind_box/app/dto/coupon"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// CouponRangeActList 优惠券使用范围-活动列表
func (e *Entry) CouponRangeActList(ctx *gin.Context, couponID uint64) (*couponDto.CouponRangeActListResp, error) {
	entityIDS, err := e.CouponRangeRepo.FindEntityIDSByFilter(ctx, &cRangeDao.Filter{CouponID: couponID, EntityType: uint32(couponDao.CouponRangeAct)})
	if err != nil {
		return nil, err
	}
	actIDS, err := e.ActRepo.FindNoticeActIdsByFilter(ctx, &actDao.NoticeFilter{ActIDS: entityIDS})
	if err != nil {
		return nil, err
	}
	retList := []*couponDto.CouponRangeActListItem{}
	if len(actIDS) == 0 {
		return &couponDto.CouponRangeActListResp{List: retList, Total: len(retList)}, nil
	}

	// TODO 活动标签获取先注释
	var (
		eg      errgroup.Group
		actList = actDao.ModelList{}
		// tagList = actDao.TagModelList{}
		// tagMap  = make(map[uint64]*actDao.TagModel)
	)
	eg.Go(func() (err error) {
		if actList, err = e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: actIDS}); err != nil {
			return err
		}
		return
	})
	// eg.Go(func() (err error) {
	// 	if tagList, err = e.ActRepo.RedisTagList(ctx); err != nil {
	// 		return
	// 	}
	// 	tagMap = tagList.GetIDMap()
	// 	return
	// })

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	for _, val := range actList {
		item := &couponDto.CouponRangeActListItem{
			ActID:    val.ID,
			ActTitle: val.Title,
			Cover:    val.Image,
			ActPrice: val.ActPrice,
			// TagNames:    []string{},
			CreatedTime: val.GetCreatedTime(),
		}

		// tagIdStrs := strings.Split(val.Tags, ",")
		// for _, tidStr := range tagIdStrs {
		// 	if tagInfo, ok := tagMap[gconv.Uint64(tidStr)]; ok {
		// 		item.TagNames = append(item.TagNames, tagInfo.TagName)
		// 	}
		// }

		retList = append(retList, item)
	}

	return &couponDto.CouponRangeActListResp{List: retList, Total: len(retList)}, nil
}
