package coupon

import (
	actDao "blind_box/app/dao/activity"
	couponDao "blind_box/app/dao/coupon"
	cRangeDao "blind_box/app/dao/coupon/coupon_range"
	couponDto "blind_box/app/dto/coupon"

	"github.com/gin-gonic/gin"
)

// GetCouponRangeEntityList .
func (e *Entry) GetCouponRangeEntityList(ctx *gin.Context, couponID uint64, eType couponDao.DFRangeType) ([]*couponDto.RangeEntity, error) {
	ret := []*couponDto.RangeEntity{}
	if eType == couponDao.CouponRangeAll {
		return ret, nil
	}
	entityIDS, err := e.CouponRangeRepo.FindEntityIDSByFilter(ctx, &cRangeDao.Filter{CouponID: couponID, EntityType: uint32(eType)})
	if err != nil {
		return ret, err
	}
	switch eType {
	case couponDao.CouponRangeAct:
		actList, err := e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: entityIDS})
		if err != nil {
			return ret, err
		}
		for _, val := range actList {
			item := &couponDto.RangeEntity{
				EntityID:   uint64(val.ID),
				EntityName: val.Title,
			}
			ret = append(ret, item)
		}
	}
	return ret, nil
}
