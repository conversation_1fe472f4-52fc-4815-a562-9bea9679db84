package coupon

import (
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	couponDao "blind_box/app/dao/coupon"
	cRangeDao "blind_box/app/dao/coupon/coupon_range"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	couponDto "blind_box/app/dto/coupon"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminCouponList .
func (e *Entry) AdminCouponList(ctx *gin.Context, req couponDto.AdminCouponListReq) (*couponDto.AdminCouponListResp, error) {
	total, list, err := e.CouponRepo.DataPageList(ctx, &couponDao.Filter{Name: req.Name, Code: req.Code}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*couponDto.AdminCouponListItem, 0, len(list))
	for _, val := range list {
		item := &couponDto.AdminCouponListItem{
			ID:              val.ID,
			Code:            val.Code,
			Name:            val.Name,
			DiscountType:    val.DiscountType,
			DiscountMethod:  val.DiscountMethod,
			DiscountAmount:  val.GetDiscountAmount(),
			ThresholdType:   val.ThresholdType,
			ThresholdAmount: val.GetThresholdAmount(),
			ExpireDays:      val.ExpireDays,
			RangeType:       val.RangeType,
			Status:          val.Status,
			CreatedTime:     val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}
	return &couponDto.AdminCouponListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminCouponDetail 优惠券详情
func (e *Entry) AdminCouponDetail(ctx *gin.Context, req couponDto.AdminCouponDetailReq) (*couponDto.AdminCouponDetailResp, error) {
	var (
		model         = &couponDao.Model{}
		couponUserNum int64
		err           error
	)
	if model, err = e.CouponRepo.FetchByUdx(ctx, &couponDao.Filter{ID: req.ID, Code: req.Code}); err != nil {
		return nil, err
	}
	if model.ID == 0 {
		return nil, ecode.CouponNotExistErr
	}
	ret := &couponDto.AdminCouponDetailResp{
		ID:              model.ID,
		Code:            model.Code,
		Name:            model.Name,
		DiscountType:    model.DiscountType,
		DiscountMethod:  model.DiscountMethod,
		DiscountAmount:  model.GetDiscountAmount(),
		ThresholdType:   model.ThresholdType,
		ThresholdAmount: model.GetThresholdAmount(),
		ExpireDays:      model.ExpireDays,
		RangeType:       model.RangeType,
		RangeEntity:     []*couponDto.RangeEntity{},
		Extra:           model.GetExtra(),
		Status:          model.Status,
		HasUserGet:      dbs.False,
		CreatedTime:     model.GetCreatedTime(),
	}
	ret.RangeEntity, _ = e.GetCouponRangeEntityList(ctx, model.ID, couponDao.DFRangeType(model.RangeType))

	if couponUserNum, err = e.CouponUserRepo.CountByFilter(ctx, &cUserDao.Filter{CouponID: model.ID}); err != nil {
		return nil, err
	}
	if couponUserNum > 0 {
		ret.HasUserGet = dbs.True
	}
	return ret, nil
}

// AdminSetCoupon 创建优惠券
func (e *Entry) AdminCreateCoupon(ctx *gin.Context, req couponDto.AdminSetCouponReq) error {
	var (
		eg          errgroup.Group
		couponCount int64
		actList     = actDao.ModelList{}
	)
	eg.Go(func() (err error) {
		if couponCount, err = e.CouponRepo.CountByFilter(ctx, &couponDao.Filter{Code: req.Code}); err != nil {
			return
		}
		if couponCount > 0 {
			return ecode.CouponCodeExistErr
		}
		return
	})

	if req.RangeType == uint32(couponDao.CouponRangeAct) {
		eg.Go(func() (err error) {
			if actList, err = e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: req.EntityIDS}); err != nil {
				return
			}
			if len(actList) == 0 {
				return ecode.CouponRangeActInvalidErr
			}
			return
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}

	var (
		m       = couponDao.SetCouponReqToModel(req)
		entityM = []*cRangeDao.Model{}
		tx      = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		cid     uint64
	)

	if err := func() (err error) {
		if cid, err = e.CouponRepo.CreateOrUpdateWithTx(tx, m); err != nil {
			return
		}
		if req.RangeType == uint32(couponDao.CouponRangeAct) {
			for _, val := range actList {
				entityM = append(entityM, &cRangeDao.Model{
					CouponID:   cid,
					EntityType: req.RangeType,
					EntityID:   uint64(val.ID),
				})
			}
			if err = e.CouponRangeRepo.BatchCreateWithTx(ctx, tx, entityM); err != nil {
				return
			}
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

// AdminSetCoupon 更新优惠券
func (e *Entry) AdminUpdateCoupon(ctx *gin.Context, req couponDto.AdminSetCouponReq) error {
	var (
		eg            errgroup.Group
		model         = &couponDao.Model{}
		actList       = actDao.ModelList{}
		couponUserNum int64
	)
	eg.Go(func() (err error) {
		if model, err = e.CouponRepo.FetchByID(ctx, req.ID); err != nil {
			return
		}
		if model.ID == 0 {
			return ecode.CouponNotExistErr
		}
		return
	})
	eg.Go(func() (err error) {
		if couponUserNum, err = e.CouponUserRepo.CountByFilter(ctx, &cUserDao.Filter{CouponID: req.ID}); err != nil {
			return
		}
		return
	})
	if req.RangeType == uint32(couponDao.CouponRangeAct) {
		eg.Go(func() (err error) {
			if actList, err = e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: req.EntityIDS}); err != nil {
				return
			}
			if len(actList) == 0 {
				return ecode.CouponRangeActInvalidErr
			}
			return
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}

	// 有明细产出,以下字段不可修改
	if couponUserNum > 0 {
		if model.Status == uint32(dbs.StatusEnable) {
			return ecode.CouponHasUserBillErr
		}
		if req.RangeType != model.RangeType {
			return ecode.CouponHasUserEditRangeErr
		}
		req.DiscountType = model.DiscountType
		req.DiscountMethod = model.DiscountMethod
		req.DiscountAmount = model.GetDiscountAmount()
		req.ThresholdType = model.ThresholdType
		req.ThresholdAmount = model.GetThresholdAmount()
		req.RangeType = model.RangeType
	}

	var (
		m       = couponDao.SetCouponReqToModel(req)
		entityM = []*cRangeDao.Model{}
		tx      = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		cid     uint64
		err     error
	)
	if err := func() error {
		if cid, err = e.CouponRepo.CreateOrUpdateWithTx(tx, m); err != nil {
			return err
		}
		if err = e.CouponRangeRepo.DelByCIDEntityType(ctx, cid, couponDao.DFRangeType(model.RangeType)); err != nil {
			return err
		}
		if req.RangeType == uint32(couponDao.CouponRangeAct) {
			for _, val := range actList {
				entityM = append(entityM, &cRangeDao.Model{
					CouponID:   cid,
					EntityType: req.RangeType,
					EntityID:   uint64(val.ID),
				})
			}
			if err = e.CouponRangeRepo.BatchCreateWithTx(ctx, tx, entityM); err != nil {
				return err
			}
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

// AdminOperateCoupon 操作优惠券
func (e *Entry) AdminOperateCoupon(ctx *gin.Context, id uint64, action dbs.OperateAction) error {
	switch action {
	case dbs.ActionOpen:
		return e.CouponRepo.UpdateStatus(ctx, id, dbs.StatusEnable)
	case dbs.ActionClose:
		return e.CouponRepo.UpdateStatus(ctx, id, dbs.StatusDisable)
	}
	return nil
}

// AdminSetCouponSource 设置优惠券来源
func (e *Entry) AdminSetCouponSource(ctx *gin.Context, id uint64, name string) error {
	m, err := e.CouponRepo.SourceFetchByID(ctx, id)
	if err != nil {
		return err
	}
	if m.ID == 0 {
		return ecode.CouponSourceNotExistErr
	}
	return e.CouponRepo.SourceCreateOrUpdate(ctx, &couponDao.SourceModel{ID: m.ID, Name: name})
}
