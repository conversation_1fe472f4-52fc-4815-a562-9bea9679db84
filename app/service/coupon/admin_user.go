package coupon

import (
	"blind_box/app/common/dbs"
	couponDao "blind_box/app/dao/coupon"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	userDao "blind_box/app/dao/user"
	couponDto "blind_box/app/dto/coupon"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

// AdminCouponUserList 优惠券领取列表
func (e *Entry) AdminCouponUserList(ctx *gin.Context, req couponDto.AdminCouponUserListReq) (*couponDto.AdminCouponUserListResp, error) {
	filter := &cUserDao.Filter{
		UID:       req.UserID,
		CouponID:  req.CouponID,
		SourceID:  req.SourceID,
		TradeNo:   req.TradeNo,
		Status:    req.Status,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	if req.CouponName != "" || req.CouponCode != "" {
		filter.CouponIDS, _ = e.CouponRepo.FindIdsByFilter(ctx, &couponDao.Filter{Name: req.CouponName, Code: req.CouponCode})
		if len(filter.CouponIDS) == 0 {
			return &couponDto.AdminCouponUserListResp{
				List:  []*couponDto.AdminCouponUserListItem{},
				Total: 0,
			}, nil
		}
	}
	total, list, err := e.CouponUserRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	var (
		eg         errgroup.Group
		couponList couponDao.ModelList
		couponMap  = make(map[uint64]*couponDao.Model)
		sourceList couponDao.SourceModelList
		sourceMap  = make(map[uint64]string)
		userList   userDao.ModelList
		userMap    = make(map[uint64]*userDao.Model)
	)
	eg.Go(func() (err error) {
		if couponList, err = e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: list.GetCids()}); err != nil {
			return
		}
		couponMap = couponList.GetIDMap()
		return
	})
	eg.Go(func() (err error) {
		if userList, err = e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: list.GetUids()}); err != nil {
			return
		}
		userMap = userList.GetIDMap()
		return
	})
	eg.Go(func() (err error) {
		if sourceList, err = e.CouponRepo.RedisSourceList(ctx); err != nil {
			return
		}
		sourceMap = sourceList.GetIDMap()
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	retList := make([]*couponDto.AdminCouponUserListItem, 0, len(list))
	for _, val := range list {
		var (
			couponCode, couponName, userName, sourceName string
		)
		if cInfo, ok := couponMap[val.CouponID]; ok {
			couponCode = cInfo.Code
			couponName = cInfo.Name
		}
		if uInfo, ok := userMap[val.UserID]; ok {
			userName = uInfo.Nickname
		}
		if sName, ok := sourceMap[val.SourceID]; ok {
			sourceName = sName
		}

		item := &couponDto.AdminCouponUserListItem{
			ID:             val.ID,
			CouponID:       val.CouponID,
			CouponCode:     couponCode,
			CouponName:     couponName,
			UserID:         val.UserID,
			UserName:       userName,
			TradeNo:        val.TradeNo,
			DiscountAmount: val.GetDiscountAmount(),
			SourceID:       val.SourceID,
			SourceName:     sourceName,
			ExpireTime:     carbon.CreateFromTimestamp(val.ExpireTime).ToDateTimeString(),
			IsExpired:      dbs.True,
			Status:         val.Status,
			Remark:         val.Remark,
			CreatedTime:    val.GetCreatedTime(),
		}
		if val.ExpireTime >= carbon.Now().Timestamp() {
			item.IsExpired = dbs.False
		}
		retList = append(retList, item)
	}
	return &couponDto.AdminCouponUserListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminOperateCouponUser 操作优惠券领取
func (e *Entry) AdminOperateCouponUser(ctx *gin.Context, id uint64, action dbs.OperateAction, remark string) error {
	model, err := e.CouponUserRepo.FetchByID(ctx, id)
	if err != nil {
		return err
	}
	switch action {
	case dbs.ActionAbandon:
		switch model.Status {
		case uint32(cUserDao.CUserUsing):
			return ecode.CouponUserAbandonUsingErr
		case uint32(cUserDao.CUserUsed):
			return ecode.CouponUserAbandonUsedErr
		case uint32(cUserDao.CUserAbandon):
			return ecode.CouponUserAbandonedErr
		}
		return e.CouponUserRepo.UpdateByFilter(ctx, &cUserDao.Filter{ID: id},
			&cUserDao.Model{Status: uint32(cUserDao.CUserAbandon), Remark: remark})
	case dbs.ActionRecover:
		return e.CouponUserRepo.UpdateByFilter(ctx, &cUserDao.Filter{ID: id}, &cUserDao.Model{Status: uint32(cUserDao.CUserGet)})
	}
	return nil
}
