package coupon

import (
	"blind_box/app/common/dbs"
	couponDao "blind_box/app/dao/coupon"
	issueDao "blind_box/app/dao/coupon/coupon_issue"
	cTargetDao "blind_box/app/dao/coupon/coupon_target"
	cUserDao "blind_box/app/dao/coupon/coupon_user"
	userDao "blind_box/app/dao/user"
	couponDto "blind_box/app/dto/coupon"
	"blind_box/pkg/ecode"
	"blind_box/pkg/util/sliceUtil"
	"fmt"
	"mime/multipart"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
)

const (
	ImportIssueTargetUserLimit = 2000
)

// AdminCouponIssueList 优惠券发放列表
func (e *Entry) AdminCouponIssueList(ctx *gin.Context, req couponDto.AdminCouponIssueListReq) (*couponDto.AdminCouponIssueListResp, error) {
	f := &issueDao.Filter{IssueType: req.IssueType}
	if req.CouponCode != "" {
		cInfo, _ := e.CouponRepo.FetchByUdx(ctx, &couponDao.Filter{Code: req.CouponCode})
		if cInfo.ID == 0 {
			return &couponDto.AdminCouponIssueListResp{
				List:  []*couponDto.AdminCouponIssueListItem{},
				Total: 0,
			}, nil
		}
		f.CouponID = cInfo.ID
	}

	total, list, err := e.CouponIssueRepo.DataPageList(ctx, f, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	var (
		eg         errgroup.Group
		couponList couponDao.ModelList
		couponMap  = make(map[uint64]*couponDao.Model)
		sourceList couponDao.SourceModelList
		sourceMap  = make(map[uint64]string)
		getCount   cUserDao.CountGroupIssueUserList
		getMap     = make(map[uint64]*cUserDao.CountGroupIssueUser)
		usedCount  cUserDao.CountGroupIssueUserList
		usedMap    = make(map[uint64]*cUserDao.CountGroupIssueUser)
	)

	eg.Go(func() (err error) {
		if couponList, err = e.CouponRepo.FindByFilter(ctx, &couponDao.Filter{IDS: list.GetCids()}); err != nil {
			return
		}
		couponMap = couponList.GetIDMap()
		return
	})
	eg.Go(func() (err error) {
		if sourceList, err = e.CouponRepo.RedisSourceList(ctx); err != nil {
			return
		}
		sourceMap = sourceList.GetIDMap()
		return
	})
	eg.Go(func() (err error) {
		issueIds := list.GetIDS()
		if getCount, err = e.CouponUserRepo.CountGroupIssueUser(ctx, issueIds, 0); err != nil {
			return
		}
		getMap = getCount.GetIDMap()

		if usedCount, err = e.CouponUserRepo.CountGroupIssueUser(ctx, issueIds, cUserDao.CUserUsed); err != nil {
			return
		}
		usedMap = usedCount.GetIDMap()
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	retList := make([]*couponDto.AdminCouponIssueListItem, 0, len(list))
	for _, val := range list {
		item := &couponDto.AdminCouponIssueListItem{
			ID:          val.ID,
			Name:        val.Name,
			StartTime:   carbon.CreateFromTimestamp(val.StartTime).ToDateTimeString(),
			EndTime:     carbon.CreateFromTimestamp(val.EndTime).ToDateTimeString(),
			Status:      val.Status,
			IssueType:   val.IssueType,
			Stock:       val.Stock,
			SourceID:    val.SourceID,
			CouponID:    val.CouponID,
			CreatedTime: val.GetCreatedTime(),
		}

		if cInfo, ok := couponMap[val.CouponID]; ok {
			item.CouponCode = cInfo.Code
			item.CouponName = cInfo.Name
			item.DiscountType = cInfo.DiscountType
			item.DiscountMethod = cInfo.DiscountMethod
			item.DiscountAmount = cInfo.GetDiscountAmount()
			item.ThresholdType = cInfo.ThresholdType
			item.ThresholdAmount = cInfo.GetThresholdAmount()
		}
		if sName, ok := sourceMap[val.SourceID]; ok {
			item.SourceName = sName
		}
		if countInfo, ok := getMap[val.ID]; ok {
			item.ReceivedStock = uint32(countInfo.Count)
		}
		if countInfo, ok := usedMap[val.ID]; ok {
			item.UsedStock = uint32(countInfo.Count)
		}

		retList = append(retList, item)
	}
	return &couponDto.AdminCouponIssueListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminCouponIssueDetail 优惠券发放详情
func (e *Entry) AdminCouponIssueDetail(ctx *gin.Context, id uint64) (*couponDto.AdminCouponIssueDetailResp, error) {
	var (
		model      = &issueDao.Model{}
		couponInfo = &couponDao.Model{}
		err        error
	)
	if model, err = e.CouponIssueRepo.FetchByID(ctx, id); err != nil {
		return nil, err
	}
	if model.ID == 0 {
		return nil, ecode.CouponIssueNotExistErr
	}
	if couponInfo, err = e.CouponRepo.FetchByUdx(ctx, &couponDao.Filter{ID: model.CouponID}); err != nil {
		return nil, err
	}
	if couponInfo.ID == 0 {
		return nil, ecode.CouponNotExistErr
	}

	ret := &couponDto.AdminCouponIssueDetailResp{
		ID:              model.ID,
		Name:            model.Name,
		StartTime:       carbon.CreateFromTimestamp(model.StartTime).ToDateTimeString(),
		EndTime:         carbon.CreateFromTimestamp(model.EndTime).ToDateTimeString(),
		Status:          model.Status,
		IssueType:       model.IssueType,
		SourceID:        model.SourceID,
		CouponID:        couponInfo.ID,
		CouponCode:      couponInfo.Code,
		CouponName:      couponInfo.Name,
		DiscountType:    couponInfo.DiscountType,
		DiscountMethod:  couponInfo.DiscountMethod,
		DiscountAmount:  couponInfo.GetDiscountAmount(),
		ThresholdType:   couponInfo.ThresholdType,
		ThresholdAmount: couponInfo.GetThresholdAmount(),
		ExpireDays:      couponInfo.ExpireDays,
		Extra:           couponInfo.GetExtra(),
	}

	return ret, nil
}

// AdminSetCouponIssue 设置优惠券发放
func (e *Entry) AdminSetCouponIssue(ctx *gin.Context, req couponDto.AdminSetCouponIssueReq) error {
	cNum, err := e.CouponRepo.CountByFilter(ctx, &couponDao.Filter{ID: req.CouponID})
	if err != nil {
		return err
	}
	if cNum == 0 {
		return ecode.CouponStatusInvalidErr
	}

	m := &issueDao.Model{
		ID:        req.ID,
		Name:      req.Name,
		IssueType: req.IssueType,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		CouponID:  req.CouponID,
		SourceID:  req.SourceID,
	}
	if req.ID > 0 {
		model, err := e.CouponIssueRepo.FetchByID(ctx, req.ID)
		if err != nil {
			return err
		}
		if model.ID == 0 {
			return ecode.CouponIssueNotExistErr
		}
		if _, err := e.CouponIssueRepo.CreateOrUpdate(ctx, m); err != nil {
			return err
		}
		return nil
	} else {
		m.Status = uint32(dbs.StatusDisable)
		var (
			tx      = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
			iid     uint64
			uids    = []uint64{}
			err     error
			targetM = []*cTargetDao.Model{}
		)

		if err := func() error {
			defer func() {
				if err := recover(); err != nil {
					return
				}
			}()
			if m.IssueType == uint32(issueDao.IssueTypeAssign) {
				if uids, err = e.CouponIssueRepo.RedisGetImportIssueTarget(ctx, req.UploadKey); err != nil {
					return err
				}
				if len(uids) == 0 {
					return ecode.CouponIssueImportFileInvalidErr
				}
				m.Stock = uint32(len(uids))
			}

			if iid, err = e.CouponIssueRepo.CreateOrUpdateWithTx(tx, m); err != nil {
				return err
			}
			if m.IssueType == uint32(issueDao.IssueTypeAssign) {
				for _, uid := range uids {
					targetM = append(targetM, &cTargetDao.Model{
						IssueID: iid,
						UserID:  uid,
					})
				}
				if err = e.CouponTargetRepo.BatchCreateWithTx(ctx, tx, targetM); err != nil {
					return err
				}
			}
			return tx.Commit().Error
		}(); err != nil {
			tx.Rollback()
			return err
		}
	}
	return nil
}

// AdminOperateCouponIssue 操作优惠券发放
func (e *Entry) AdminOperateCouponIssue(ctx *gin.Context, id uint64, action dbs.OperateAction) error {
	switch action {
	case dbs.ActionOpen:
		return e.CouponIssueRepo.UpdateStatus(ctx, id, dbs.StatusEnable)
	case dbs.ActionClose:
		return e.CouponIssueRepo.UpdateStatus(ctx, id, dbs.StatusDisable)
	}
	return nil
}

// AdminCouponIssueTargetSet 设置优惠券发放目标
func (e *Entry) AdminCouponIssueTargetSet(ctx *gin.Context, req couponDto.AdminCouponIssueTargetSetReq) error {
	var (
		eg    errgroup.Group
		model = &issueDao.Model{}
		uids  = []uint64{}
	)

	eg.Go(func() (err error) {
		if model, err = e.CouponIssueRepo.FetchByID(ctx, req.ID); err != nil {
			return err
		}
		if model.ID == 0 {
			return ecode.CouponIssueNotExistErr
		}
		if model.IssueType == uint32(issueDao.IssueTypeAll) {
			return ecode.CouponIssueImportTypeErr
		}
		now := carbon.Now().Timestamp()
		if model.Status == uint32(dbs.StatusEnable) && model.StartTime <= now && now <= model.EndTime {
			return ecode.CouponIssueStartForbidTaegetErr
		}
		return
	})

	eg.Go(func() (err error) {
		if uids, err = e.CouponIssueRepo.RedisGetImportIssueTarget(ctx, req.UploadKey); err != nil {
			return err
		}
		if len(uids) == 0 {
			return ecode.CouponIssueImportFileInvalidErr
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return err
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() error {
		var (
			num     int64
			err     error
			targetM = []*cTargetDao.Model{}
		)
		switch req.Action {
		case string(dbs.ActionAdd):
			for _, uid := range uids {
				targetM = append(targetM, &cTargetDao.Model{
					IssueID: req.ID,
					UserID:  uid,
				})
			}
			if num, err = e.CouponTargetRepo.BatchCreateIgnoreWithTx(tx, targetM); err != nil {
				return err
			}
		case string(dbs.ActionSubtract):
			// 已领取的不可减少
			getIds, _ := e.CouponUserRepo.FindXIDSByFilter(ctx, &cUserDao.Filter{IssueID: req.ID, UIDS: uids}, cUserDao.PluckUID)
			validIds := sliceUtil.SliceNotContainer(uids, getIds)
			if len(validIds) == 0 {
				return nil
			}
			if num, err = e.CouponTargetRepo.DelByIssueIDAndUidsWithTx(ctx, tx, req.ID, uids); err != nil {
				return err
			}
		}
		if num > 0 {
			model.Stock = model.GetStockByAction(num, dbs.OperateAction(req.Action))
			e.CouponIssueRepo.CreateOrUpdateWithTx(tx, model)
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Callback()
		return err
	}
	return nil
}

// AdminCouponIssueTargetUpload 上传优惠券发放附件
func (e *Entry) AdminCouponIssueTargetUpload(ctx *gin.Context, file multipart.File) (*couponDto.AdminCouponIssueTargetUploadResp, error) {
	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, err
	}
	sheetName := f.GetSheetName(f.GetActiveSheetIndex())
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}

	var (
		uids = []uint64{}
		fLen = len(rows) - 1
		key  string
	)
	if fLen > ImportIssueTargetUserLimit {
		return nil, ecode.CouponIssueImportOutErr
	}
	for i, row := range rows {
		if i == 0 {
			continue
		}
		uid, err := strconv.Atoi(row[0])
		if err != nil || uid == 0 {
			continue
		}
		uids = append(uids, uint64(uid))
	}
	uids = lo.Uniq(uids)
	validIds, _ := e.UserRepo.FindXidsByFilter(ctx, &userDao.Filter{IDS: uids}, dbs.PluckID)
	if len(validIds) == 0 {
		return nil, ecode.CouponIssueImportUserInvalidErr
	}
	if key, err = e.CouponIssueRepo.RedisSaveImportIssueTarget(ctx, validIds); err != nil {
		return nil, err
	}

	return &couponDto.AdminCouponIssueTargetUploadResp{
		UploadKey:  key,
		Total:      fLen,
		SuccessNum: len(validIds),
	}, nil
}

// AdminCouponIssueTargetExport 导出优惠券发放附件
func (e *Entry) AdminCouponIssueTargetExport(ctx *gin.Context, id uint64) ([]byte, error) {
	// TODO 校验issue_id 是否为指定用户
	uids, err := e.CouponTargetRepo.FindXIDSByFilter(ctx, &cTargetDao.Filter{IssueID: id}, cTargetDao.PluckUID)
	if err != nil {
		return nil, err
	}
	newFile := excelize.NewFile()
	defer func() {
		if err := newFile.Close(); err != nil {
			return
		}
	}()
	sheetName := "Sheet1"
	newFile.SetCellValue(sheetName, "A1", "用户ID")

	for idx, uid := range uids {
		cell := fmt.Sprintf("A%d", idx+2)
		newFile.SetCellValue(sheetName, cell, uid)
	}
	buf, err := newFile.WriteToBuffer()
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}
