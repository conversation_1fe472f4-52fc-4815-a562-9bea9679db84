package user

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	signDao "blind_box/app/dao/user/sign"
	tideDao "blind_box/app/dao/user/tide"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"context"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/errgroup"
	"github.com/golang-module/carbon/v2"
)

// GetUserSignConfigInfo 获取用户签到配置信息
func (e *Entry) GetUserSignConfigInfo(ctx *gin.Context, uid uint64) (*userDto.SignConfigResp, error) {
	configList, err := e.SignRepo.RedisGetSignConfigList(ctx)
	if err != nil {
		return nil, err
	}
	UserSignInfo, err := e.SignRepo.RedisGetUserSignInfo(ctx, uid)
	if err != nil {
		return nil, err
	}
	respList := []*userDto.SignConfig{}
	var todayIsSign = dbs.False

	for _, info := range configList {
		item := &userDto.SignConfig{
			Days:       info.Days,
			RewardType: info.RewardType,
			RewardVal:  info.RewardVal,
		}
		if info.Days <= UserSignInfo.ContinueSignDays {
			item.IsSign = dbs.True
		}
		if info.Days == UserSignInfo.ContinueSignDays && carbon.Now().ToDateString() == UserSignInfo.LastSignDate {
			todayIsSign = dbs.True
		}
		respList = append(respList, item)
	}

	return &userDto.SignConfigResp{
		ConfigList:       respList,
		ContinueSignDays: UserSignInfo.ContinueSignDays,
		TodayIsSign:      uint32(todayIsSign),
	}, nil
}

// UserSign 用户签到
func (e *Entry) UserSign(ctx *gin.Context, uid uint64) error {
	var (
		eg            errgroup.Group
		uInfo         = &userDao.Model{}
		today         = carbon.Now().ToDateString()
		cacheSignInfo = &signDao.UserSignCache{}
		signConfList  = []*signDao.SignConfig{}
		tideVal       uint32
	)
	eg.Go(func(context.Context) (err error) {
		if uInfo, err = e.getUserInfo(ctx, uid); err != nil {
			return
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if cacheSignInfo, err = e.SignRepo.RedisGetUserSignInfo(ctx, uid); err != nil {
			return
		}
		if today == cacheSignInfo.LastSignDate {
			return ecode.UserTodaySignedErr
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if signConfList, err = e.SignRepo.RedisGetSignConfigList(ctx); err != nil {
			return
		}

		return
	})
	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("UserSign err")
		return err
	}
	if cacheSignInfo.ContinueSignDays >= signDao.LimitDays {
		return nil
	}
	continueSignDays := cacheSignInfo.ContinueSignDays + 1
	for _, val := range signConfList {
		if val.Days == continueSignDays {
			tideVal = val.RewardVal
			break
		}
	}

	var (
		signLog = &signDao.SignLog{
			UserId:           uid,
			SignDate:         today,
			ContinueSignDays: continueSignDays,
		}
		tideLog = &tideDao.TideLog{
			UserId:      uid,
			TideValType: uint32(tideDao.TideValTypeAdd),
			Source:      uint32(tideDao.TideSourceTypeSign),
		}
	)

	// 更新用户潮气值+签到log+潮气值log
	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		// 签到: 潮气值超过100,任务完成,tide log 不记录, 未超过100,按100补差值
		if err = e.SignRepo.CreateSignLogWithTx(tx, signLog); err != nil {
			return
		}
		realTideVal := e.TideRepo.GetRealTideVal(int32(tideVal), int32(uInfo.TideVal), tideDao.TideValTypeAdd)
		if realTideVal > 0 {
			tideLog.TideVal = realTideVal
			tideLog.SourceId = signLog.Id

			if err = e.TideRepo.CreateTideLogWithTx(tx, tideLog); err != nil {
				return
			}

			if err = userDao.GetRepo().UpdateMapByIDWithTx(ctx, tx, uid, map[string]interface{}{
				"tide_val": uInfo.TideVal + realTideVal,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("UserSign err")
		return err
	}

	// 签到成功后，清理用户缓存（因为可能更新了潮气值）
	if err := e.UserRepo.RedisClearUserInfo(ctx, uid); err != nil {
		log.Ctx(ctx).WithError(err).Warn("UserSign: Failed to clear user cache after sign, userID=%d", uid)
	} else {
		log.Ctx(ctx).Info("UserSign: Successfully cleared user cache after sign, userID=%d", uid)
	}

	// 更新缓存
	if err := e.SignRepo.RedisUpdateUserSign(ctx, uid, &signDao.UserSignCache{
		ContinueSignDays: continueSignDays,
		LastSignDate:     today,
	}); err != nil {
		return err
	}

	return nil
}
