package user

import (
	"fmt"

	"blind_box/app/common/dbs"
	"blind_box/app/dao/card/card_user"
	tideDao "blind_box/app/dao/user/tide"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

// GetTideCardInfo 获取道具卡信息
func (e *Entry) GetTideCardInfo(ctx *gin.Context, uid uint64) (*userDto.TideCardConfigResp, error) {
	configList, err := e.TideRepo.RedisGetCardConfigList(ctx)
	if err != nil {
		return nil, err
	}
	respList := []*userDto.TideCardConfig{}
	for _, info := range configList {
		item := &userDto.TideCardConfig{
			Id:              info.Id,
			CardType:        info.CardType,
			CardNum:         info.CardNum,
			TideVal:         info.TideVal,
			OriginalTideVal: info.OriginalTideVal,
			ExpireHour:      info.ExpireHour,
			DayLimit:        info.DayLimit,
			IsChange:        dbs.False,
		}
		exchangeCount, _ := e.TideRepo.RedisGetCountUserExchangeCard(ctx, uid, info.Id)
		if info.DayLimit <= uint32(exchangeCount) {
			item.IsChange = dbs.True
		}
		respList = append(respList, item)
	}

	return &userDto.TideCardConfigResp{
		ConfigList: respList,
	}, nil
}

// UserTideExchangeCard 用户使用潮气值兑换道具卡
func (e *Entry) UserTideExchangeCard(ctx *gin.Context, uid, tcConfigId uint64) error {
	uInfo, _ := e.getUserInfo(ctx, uid)
	cardInfo, err := e.TideRepo.RedisGetCardConfigInfo(ctx, tcConfigId)
	if err != nil {
		return err
	}
	if uInfo.TideVal < cardInfo.TideVal {
		return ecode.UserTideNotEnoughErr
	}
	exchangeCount, err := e.TideRepo.CountExchangeLog(ctx, &tideDao.ExchangeLogFilter{
		UserId:     uid,
		TcConfigId: tcConfigId,
		Date:       carbon.Now().ToDateString(),
	})
	if err != nil {
		return err
	}
	if cardInfo.DayLimit <= uint32(exchangeCount) {
		return ecode.UserTideExchangeCardLimitErr
	}
	if err = e.userExchangeCard(ctx, cardInfo.CardCode, uid, cardInfo.Id, cardInfo.CardNum, cardInfo.ExpireHour); err != nil {
		return err
	}

	tideLog := &tideDao.TideLog{
		UserId:      uid,
		TideVal:     cardInfo.TideVal,
		TideValType: uint32(tideDao.TideValTypeSubtract),
		Source:      uint32(tideDao.TideSourceTypeExchange),
		SourceId:    tcConfigId,
	}
	exchangeLog := &tideDao.CardExchangeLog{
		UserId:       uid,
		TcConfigId:   tcConfigId,
		ExchangeDate: carbon.Now().ToDateString(),
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.TideRepo.CreateExchangeLogWithTx(ctx, tx, exchangeLog); err != nil {
			return
		}
		if err = e.TideRepo.CreateTideLogWithTx(tx, tideLog); err != nil {
			return
		}
		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uid, map[string]interface{}{
			"tide_val": uInfo.TideVal - cardInfo.TideVal,
		}); err != nil {
			tx.Rollback()
			return err
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("UserTideExchangeCard err")
		return err
	}

	// 兑换道具卡成功后，清理用户缓存（因为更新了潮气值）
	if err := e.UserRepo.RedisClearUserInfo(ctx, uid); err != nil {
		log.Ctx(ctx).WithError(err).Warn("UserTideExchangeCard: Failed to clear user cache after exchange, userID=%d", uid)
	} else {
		log.Ctx(ctx).Info("UserTideExchangeCard: Successfully cleared user cache after exchange, userID=%d", uid)
	}

	e.TideRepo.RedisClearCountUserExchangeCard(ctx, uid, tcConfigId)
	return nil
}

// userExchangeCard
func (e *Entry) userExchangeCard(ctx *gin.Context, cardCode string, uid, tcConfigId uint64, CardNum, expireHour uint32) (err error) {
	if err := e.RedisCli.Lock(ctx, redis.GetUserTideExchangeCardLockKey(uid), redis.DefaultLockTime); err != nil {
		return ecode.ActBusyLockErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserTideExchangeCardLockKey(uid))

	cardConfig, err := e.CardConfRepo.FetchByCode(ctx, cardCode)
	if err != nil {
		return err
	}
	if cardConfig.ID == 0 {
		err = ecode.UserTideCardTypeNotExistErr
		return err
	}

	userCardList := card_user.ModelList{}
	expireTime := carbon.Now().EndOfDay().AddHours(int(expireHour)).Timestamp()
	for i := 0; i < int(CardNum); i++ {
		item := &card_user.Model{
			UserID:     uid,
			CardCode:   cardConfig.CardCode,
			CardType:   cardConfig.CardType,
			CardName:   cardConfig.CardName,
			CardImg:    cardConfig.CardImg,
			DetailMsg:  cardConfig.DetailMsg,
			ExpireTime: expireTime,
			Status:     card_user.UserItemCardStatusUnused,
			SourceType: cardConfig.Source,
			CreateBy:   0,
			Remark:     fmt.Sprintf("潮气值兑换道具卡:%v", tcConfigId),
		}

		userCardList = append(userCardList, item)
	}

	// 道具卡
	if err = e.CardUserRepo.BatchCreate(ctx, userCardList); err != nil {
		return
	}

	return
}
