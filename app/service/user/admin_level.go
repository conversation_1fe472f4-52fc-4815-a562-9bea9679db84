package user

import (
	"blind_box/app/common/dbs"
	levelDao "blind_box/app/dao/user/level"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminLevelList .
func (e *Entry) AdminLevelList(ctx *gin.Context, req userDto.AdminLevelListReq) (*userDto.AdminLevelListResp, error) {
	total, list, err := e.LevelRepo.DataPageList(ctx, &levelDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*userDto.AdminLevelListItem, 0, len(list))
	for _, val := range list {
		item := &userDto.AdminLevelListItem{
			ID:        val.ID,
			Name:      val.Name,
			Icon:      val.Icon,
			Min:       val.Min,
			Max:       val.Max,
			Status:    val.Status,
			CreatedAt: val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}
	return &userDto.AdminLevelListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminLevelSet .
func (e *Entry) AdminLevelSet(ctx *gin.Context, req userDto.AdminLevelSetReq) (uint64, error) {
	m := &levelDao.Model{
		ID:     req.ID,
		Name:   req.Name,
		Icon:   req.Icon,
		Min:    req.Min,
		Max:    req.Max,
		Status: uint32(dbs.StatusEnable),
	}
	var (
		eg    errgroup.Group
		model = &levelDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.LevelRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.LevelRepo.CountByFilter(ctx, &levelDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.LevelRepo.CountByFilter(ctx, &levelDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.LevelRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminLevelOperate .
func (e *Entry) AdminLevelOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	return e.LevelRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
}

// UserLevelList .
func (e *Entry) UserLevelList(ctx *gin.Context) ([]*userDto.AdminLevelListItem, error) {
	list, err := e.LevelRepo.FindByFilter(ctx, &levelDao.Filter{
		Sort: dbs.CommonSort{
			Field:  levelDao.SortFieldMin,
			Method: dbs.SortMethodAsc,
		},
	})
	if err != nil {
		return nil, err
	}

	retList := make([]*userDto.AdminLevelListItem, 0, len(list))
	for _, val := range list {
		item := &userDto.AdminLevelListItem{
			ID:   val.ID,
			Name: val.Name,
			Icon: val.Icon,
			Min:  val.Min,
			Max:  val.Max,
		}
		retList = append(retList, item)
	}
	return retList, nil
}

func (e *Entry) GerUserLeveInfo(ctx *gin.Context, tideVal uint32) (*levelDao.Model, error) {
	return e.LevelRepo.FeatchByFilterSort(ctx, &levelDao.Filter{
		Status: uint32(dbs.StatusEnable),
		LtMin:  tideVal,
		Sort: dbs.CommonSort{
			Field:  levelDao.SortFieldMin,
			Method: dbs.SortMethodDesc,
		},
	})
}
