package user

import (
	"context"

	"blind_box/app/common/dbs"
	cardConfDao "blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_user"
	userDao "blind_box/app/dao/user"
	rewardDao "blind_box/app/dao/user/reward"
	couponDto "blind_box/app/dto/coupon"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/errgroup"
	"github.com/golang-module/carbon/v2"
)

// RewardNewUserInfo 新用户奖励信息

func (e *Entry) RewardNewUserInfo(ctx *gin.Context, uid uint64) (*userDto.RewardNewUserInfoResp, error) {
	resp := &userDto.RewardNewUserInfoResp{
		Title: rewardDao.NewUserRewardTitle,
		Desc:  rewardDao.NewUserRewardDesc,
		TideList: []*userDto.RewardNewUserTideItem{
			{
				Name: "潮气值",
				Img:  "https://cdn.mowantoy.com/mws_img/<EMAIL>",
				Num:  rewardDao.NewUserRewardTideVal,
			},
		},
		CardList:   []*userDto.RewardNewUserCardItem{},
		CouponList: []*couponDto.HomepageCouponItem{},
	}

	cardConfList, err := e.CardConfRepo.FindByFilter(ctx, &cardConfDao.Filter{CardCodes: rewardDao.CardCodes})
	if err != nil {
		return nil, err
	}

	for _, v := range cardConfList {
		tmp := &userDto.RewardNewUserCardItem{
			Name:       v.CardName,
			Type:       v.CardType,
			Img:        v.CardImg,
			Num:        rewardDao.CardCodeMap[v.CardCode].Num,
			ExpireDays: v.ExpireDay,
		}
		resp.CardList = append(resp.CardList, tmp)
	}

	couponList, err := e.CouponSrv.RegCouponList(ctx, uid)
	if err != nil {
		return nil, err
	}
	resp.CouponList = couponList

	return resp, nil
}

// RewardNewUserGet 领取新用户奖励
func (e *Entry) RewardNewUserGet(ctx *gin.Context, uid uint64) error {
	if err := e.RedisCli.Lock(ctx, redis.GetNewUserRewardLockKey(uid), redis.DefaultLockTime); err != nil {
		return ecode.ActBusyLockErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetNewUserRewardLockKey(uid))

	var (
		eg           errgroup.Group
		uInfo        = &userDao.Model{}
		cardConfList = cardConfDao.ModelList{}
		todayEndTime = carbon.Now().EndOfDay().Timestamp()

		cntNum int64
	)
	eg.Go(func(context.Context) (err error) {
		if cntNum, err = e.RewardRepo.CountByFilter(ctx, &rewardDao.Filter{
			UserID:   uid,
			RewardID: rewardDao.NewUserRewardID,
		}); err != nil {
			return
		}
		if cntNum > 0 {
			return ecode.BoxRegTideIsGetErr
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if uInfo, err = e.getUserInfo(ctx, uid); err != nil {
			return
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if cardConfList, err = e.CardConfRepo.FindByFilter(ctx, &cardConfDao.Filter{CardCodes: rewardDao.CardCodes}); err != nil {
			return
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return err
	}

	userCardList := []*card_user.Model{}
	for _, val := range cardConfList {
		item := &card_user.Model{
			UserID:     uid,
			CardCode:   val.CardCode,
			CardType:   val.CardType,
			CardName:   val.CardName,
			CardImg:    val.CardImg,
			DetailMsg:  val.DetailMsg,
			ExpireTime: todayEndTime + int64(val.ExpireDay)*24*3600,
			Status:     card_user.UserItemCardStatusUnused,
			SourceType: val.Source,
			CreateBy:   0,
			Remark:     "新人礼",
		}
		userCardList = append(userCardList, item)
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		// 潮气值
		if err = e.UserGetRegTide(ctx, tx, uInfo, rewardDao.NewUserRewardTideVal); err != nil {
			return
		}
		// 优惠券
		if err = e.CouponSrv.UserGetRegCoupon(ctx, tx, uid); err != nil {
			return
		}
		// 道具卡
		if err = e.CardUserRepo.BatchCreateWithTx(ctx, tx, userCardList); err != nil {
			return
		}
		// 记录领取日志
		if err = e.RewardRepo.CreateWithTx(tx, &rewardDao.Model{
			UserID:   uid,
			RewardID: rewardDao.NewUserRewardID,
		}); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("RewardNewUserGet err")
		return err
	}

	// 新用户奖励领取成功后，清理用户缓存（因为更新了潮气值）
	if err := e.UserRepo.RedisClearUserInfo(ctx, uid); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RewardNewUserGet: Failed to clear user cache after new user reward, userID=%d", uid)
	} else {
		log.Ctx(ctx).Info("RewardNewUserGet: Successfully cleared user cache after new user reward, userID=%d", uid)
	}

	return nil
}

// RewardNewUserStatus
func (e *Entry) RewardNewUserStatus(ctx *gin.Context, uid uint64) (*userDto.RewardNewUserStatusResp, error) {
	num, err := e.RewardRepo.CountByFilter(ctx, &rewardDao.Filter{
		UserID:   uid,
		RewardID: rewardDao.NewUserRewardID,
	})
	if err != nil {
		return nil, err
	}
	if num > 0 {
		return &userDto.RewardNewUserStatusResp{IsGet: dbs.True}, nil
	}

	return &userDto.RewardNewUserStatusResp{IsGet: dbs.False}, nil
}
