package login

import (
	"blind_box/app/api/wechat/applet"
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

type WxAppletLoginProvider struct{}

func (a *WxAppletLoginProvider) Login(ctx *gin.Context, req userDto.UserLoginCommonReq) (*userDao.UserAuth, error) {
	var (
		applet           = applet.GetApi().InitAppletClient(ctx)
		miniAuth         = applet.GetAuth()
		userSession, err = miniAuth.Code2Session(req.AuthToken)
	)
	if err != nil {
		log.Ctx(ctx).WithField("err", err).Error("Code2Session")
		return nil, err
	}
	phoneResp, err := miniAuth.GetPhoneNumber(req.Code)
	if err != nil {
		log.Ctx(ctx).WithField("err", err).Error("GetPhoneNumber")
		return nil, err
	}
	log.Ctx(ctx).WithField("phoneResp", phoneResp).WithField("userSession", userSession).Info("wxAppletResp")

	if phoneResp.ErrCode != 0 {
		return nil, err
	}

	userAuth := &userDao.UserAuth{
		Model: &userDao.Model{
			Mobile:      phoneResp.PhoneInfo.PurePhoneNumber,
			CountryCode: phoneResp.PhoneInfo.CountryCode,
		},
		AuthUid: userSession.OpenID,
		AuthPid: userSession.UnionID,
	}

	return userAuth, nil
}

func (a *WxAppletLoginProvider) GetAuthType() uint32 {
	return uint32(userDao.AtWxApplet)
}
