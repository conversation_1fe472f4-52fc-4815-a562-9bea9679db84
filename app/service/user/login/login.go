package login

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/app/service/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util"
	"fmt"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/sync/errgroup"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type LoginProvider interface {
	Login(ctx *gin.Context, req userDto.UserLoginCommonReq) (*userDao.UserAuth, error)
	GetAuthType() uint32
}

type LoginManager struct {
	providers map[userDao.AuthType]LoginProvider
}

func NewLoginManager() *LoginManager {
	return &LoginManager{
		providers: map[userDao.AuthType]LoginProvider{
			userDao.AtWxApplet: &WxAppletLoginProvider{},
		},
	}
}

func (lm *LoginManager) Login(ctx *gin.Context, authType userDao.AuthType, req userDto.UserLoginCommonReq) (*userDto.UserLoginResp, error) {
	var (
		loginProvider LoginProvider
		exists        bool
		loginAuth     = &userDao.UserAuth{}
		resp          = &userDto.UserLoginResp{}
		err           error
		userAuth      = &userDao.UserAuth{}
	)
	if loginProvider, exists = lm.providers[authType]; !exists {
		return nil, ecode.UserLoginAuthTypeErr
	}
	if loginAuth, err = loginProvider.Login(ctx, req); err != nil {
		return nil, err
	}
	if userAuth, err = lm.FindOrCreateUser(ctx, authType, loginAuth); err != nil {
		return nil, err
	}

	if userAuth.LastLoginTime == dbs.False {
		resp.IsFirst = dbs.True
	}
	resp.Token, _ = util.GenToken(userAuth.ID, dbs.False)
	if err = userDao.GetRepo().RedisSetUserToken(ctx, userAuth.ID, resp.Token); err != nil {
		return nil, err
	}
	resp.UserInfo = userAuth.Model.ToUserInfoResp(ctx)
	user.GetUserLogEntity().ProducerLogin(ctx, userAuth)

	return resp, nil
}

func (lm *LoginManager) FindOrCreateUser(ctx *gin.Context, authType userDao.AuthType, uaInfo *userDao.UserAuth) (*userDao.UserAuth, error) {
	var (
		eg       errgroup.Group
		userRepo = userDao.GetRepo()
		userAuth = &userDao.UserAuth{}
		userInfo = &userDao.Model{}
		err      error
	)

	eg.Go(func() (err error) {
		if userAuth, err = userRepo.FetchAuthByUdx(ctx, authType, uaInfo.AuthUid); err != nil {
			return
		}
		return
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	if userAuth.AuthID == dbs.False {
		if uaInfo.Mobile != "" {
			if userInfo, err = userRepo.FeatchByFilterSort(ctx, &userDao.Filter{Mobile: uaInfo.Mobile}); err != nil {
				return nil, err
			}
		}

		authInfo := &userDao.Auth{
			AuthType: uint32(authType),
			AuthUid:  uaInfo.AuthUid,
			AuthPid:  uaInfo.AuthPid,
		}

		tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		if err := func() (err error) {
			if userInfo.ID == dbs.False {
				userInfo = &userDao.Model{
					Nickname:      uaInfo.Nickname,
					Mobile:        uaInfo.Mobile,
					Email:         uaInfo.Email,
					Avatar:        uaInfo.Avatar,
					Status:        dbs.True,
					TideVal:       0,
					LastLoginTime: 0,
				}

				if err = userRepo.CreateWithTx(ctx, tx, userInfo); err != nil {
					return
				}
			}
			authInfo.UserID = userInfo.ID
			if err = userRepo.CreateAuthWithTx(ctx, tx, authInfo); err != nil {
				return
			}

			return tx.Commit().Error
		}(); err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("findOrCreateUser err")
			return nil, err
		}
		userAuth.Model = userInfo
		userAuth.AuthID = authInfo.ID
		userAuth.AuthType = authInfo.AuthType
		userAuth.AuthUid = authInfo.AuthUid
		userAuth.AuthPid = authInfo.AuthPid
	}

	return userAuth, nil
}

func GetLoginUserAuth(ctx *gin.Context, authType userDao.AuthType) *userDao.UserAuth {
	randInfo := helper.RandCode(8)
	return &userDao.UserAuth{
		Model: &userDao.Model{
			Nickname: fmt.Sprintf("用户-%v", randInfo),
			Email:    fmt.Sprintf("%<EMAIL>", randInfo),
		},
		AuthType: uint32(authType),
		AuthUid:  fmt.Sprintf("第三方id-%v", randInfo),
	}
}
