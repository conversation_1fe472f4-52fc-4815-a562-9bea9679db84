package login

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/util"

	"github.com/gin-gonic/gin"
)

// ScriptUserLogin .
func ScriptUserLogin(ctx *gin.Context, uid uint64) (*userDto.UserLoginResp, error) {
	var (
		err      error
		resp     = &userDto.UserLoginResp{}
		userRepo = userDao.GetRepo()
	)

	uAuth, err := userRepo.FetchAuthByUid(ctx, uid)
	if err != nil {
		return nil, err
	}
	if uAuth, err = NewLoginManager().FindOrCreateUser(ctx, userDao.AtWxApplet, uAuth); err != nil {
		return nil, err
	}

	if uAuth.LastLoginTime == dbs.False {
		resp.IsFirst = dbs.True
	}
	resp.Token, _ = util.GenToken(uAuth.ID, dbs.False)
	if err = userRepo.RedisSetUserToken(ctx, uAuth.ID, resp.Token); err != nil {
		return nil, err
	}
	resp.UserInfo = uAuth.Model.ToUserInfoResp(ctx)

	return resp, nil
}
