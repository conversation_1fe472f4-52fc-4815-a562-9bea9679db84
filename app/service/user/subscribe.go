package user

import (
	subDao "blind_box/app/dao/user/subscribe"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
)

// SubscribeList .
func (e *Entry) SubscribeList(ctx *gin.Context, uid uint64, entityType uint32, page, limit int) (*userDto.SubscribeListResp, error) {
	var (
		filter  = &subDao.Filter{UserId: uid, EntityType: entityType}
		retList = []*userDto.SubscribeListItem{}
	)

	total, subList, err := e.SubscribeRepo.DataPageList(ctx, filter, page, limit)
	if err != nil {
		return nil, err
	}
	if total == 0 || len(subList) == 0 {
		return &userDto.SubscribeListResp{List: retList, Total: total}, nil
	}

	for _, val := range subList {
		item := &userDto.SubscribeListItem{
			SubId:      val.Id,
			UserId:     val.UserId,
			EntityId:   val.EntityId,
			EntityType: val.EntityType,
			Status:     val.Status,
			CreatedAt:  val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}

	return &userDto.SubscribeListResp{List: retList, Total: total}, nil

	// var (
	// 	eg      errgroup.Group
	// 	scsList = subDao.ModelList{}
	// )
	// eg.Go(func() (err error) {
	// 	if toRemind, err = e.SubscribeRepo.CountUserSubByFilter(ctx, &subDao.UserSubFilter{
	// 		UID:      uid,
	// 		ByRemind: true,
	// 	}); err != nil {
	// 		return
	// 	}
	// 	return
	// })
	// eg.Go(func() (err error) {
	// 	if total, scsList, err = e.SubscribeRepo.DataPageList(ctx, uid, page, limit); err != nil {
	// 		return err
	// 	}
	// 	return
	// })
	// if err := eg.Wait(); err != nil {
	// 	return nil, err
	// }

	// actMap, err := e.ActRepo.FindMapByFilter(ctx, &actDao.Filter{IDS: scsList.GetActIDS()})
	// if err != nil {
	// 	return nil, err
	// }

	// retList := make([]*actDto.ActivitySubscribeListItem, 0, len(scsList))
	// for _, val := range scsList {
	// 	var (
	// 		actTitle, startTime, cover string
	// 		actPrice                   uint64
	// 	)
	// 	if temp, ok := actMap[val.ActID]; ok {
	// 		actTitle = temp.Title
	// 		startTime = temp.GetStartTime()
	// 		cover = temp.Image
	// 		actPrice = temp.ActPrice
	// 	}
	// 	item := &actDto.ActivitySubscribeListItem{
	// 		ID:          val.ID,
	// 		ActID:       val.ActID,
	// 		ActTitle:    actTitle,
	// 		StartTime:   startTime,
	// 		Cover:       cover,
	// 		ActPrice:    actPrice,
	// 		CreatedTime: val.GetCreatedTime(),
	// 	}

	// 	retList = append(retList, item)
	// }
}

// SubscribeAdd .
func (e *Entry) SubscribeAdd(ctx *gin.Context, uid, entityId uint64, entityType uint32) error {
	subInfo, err := e.SubscribeRepo.FetchByEntity(ctx, uid, entityId, subDao.EntityType(entityType))
	if err != nil {
		return err
	}
	if subInfo.Id != 0 && subInfo.Status == 1 {
		return nil
		// return ecode.ActAleardySubscribeErr
	}

	model := &subDao.Model{
		UserId:     uid,
		EntityId:   entityId,
		EntityType: entityType,
		Status:     1,
	}

	switch entityType {
	// TODO 校验订阅相关实体时间
	case uint32(subDao.EntityTypeBoxSaleStart), uint32(subDao.EntityTypeActStart):
		if _, err = e.CommonSrv.CheckActivityExist(ctx, entityId); err != nil {
			return err
		}
	case uint32(subDao.EntityTypeSkuHasStock):
		skuInfo, err := e.CommonSrv.CheckSkuExist(ctx, entityId)
		if err != nil {
			return err
		}

		model.EntityRelationId = skuInfo.SpuId
	default:
		return ecode.ParamInvalidErr
	}

	if err := e.SubscribeRepo.CreateOrUpdate(ctx, model); err != nil {
		return err
	}
	return nil
}

// SubscribeDel .
func (e *Entry) SubscribeDel(ctx *gin.Context, uid, entityId uint64, entityType uint32) error {
	return e.SubscribeRepo.DelByEntity(ctx, uid, entityId, subDao.EntityType(entityType))
}
