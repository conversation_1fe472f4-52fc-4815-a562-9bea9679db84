package user

import (
	actDao "blind_box/app/dao/activity"
	areaDao "blind_box/app/dao/admin/area"
	adminConfig "blind_box/app/dao/admin/config"
	cardConfDao "blind_box/app/dao/card/card_config"
	carduserDao "blind_box/app/dao/card/card_user"
	couponDao "blind_box/app/dao/coupon"
	skuDao "blind_box/app/dao/goods/sku"
	shopDao "blind_box/app/dao/resource/shop"
	userDao "blind_box/app/dao/user"
	addrDao "blind_box/app/dao/user/addr"
	userCart "blind_box/app/dao/user/cart"
	levelDao "blind_box/app/dao/user/level"
	rewardDao "blind_box/app/dao/user/reward"
	signDao "blind_box/app/dao/user/sign"
	subDao "blind_box/app/dao/user/subscribe"
	tideDao "blind_box/app/dao/user/tide"
	userLogDao "blind_box/app/dao/user/user_log"
	"blind_box/app/service/common"
	couponSrv "blind_box/app/service/coupon"
	"blind_box/pkg/redis"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	// UserRepo    userDao.Repo
	UserRepo     *userDao.Entry
	AddrRepo     *addrDao.Entry
	UserLogRepo  userLogDao.Repo
	RewardRepo   rewardDao.Repo
	SignRepo     *signDao.Entry
	CardConfRepo cardConfDao.Repo
	CardUserRepo carduserDao.Repo
	// TideRepo    tideDao.Repo
	TideRepo      *tideDao.Entry
	CouponRepo    *couponDao.Entry
	AreaRepo      areaDao.Repo
	ShopRepo      shopDao.Repo
	CartRepo      *userCart.Entry
	LevelRepo     levelDao.Repo
	CouponSrv     *couponSrv.Entry
	SubscribeRepo *subDao.Entry
	SkuRepo       *skuDao.Entry
	ActRepo       *actDao.Entry
	ConfigRepo    adminConfig.Repo
	CommonSrv     common.Server
	RedisCli      *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:      userDao.GetRepo(),
		AddrRepo:      addrDao.GetRepo(),
		UserLogRepo:   userLogDao.GetRepo(),
		RewardRepo:    rewardDao.GetRepo(),
		SignRepo:      signDao.GetRepo(),
		CardConfRepo:  cardConfDao.GetRepo(),
		CardUserRepo:  carduserDao.GetRepo(),
		TideRepo:      tideDao.GetRepo(),
		CouponRepo:    couponDao.GetRepo(),
		AreaRepo:      areaDao.GetRepo(),
		CartRepo:      userCart.GetRepo(),
		SkuRepo:       skuDao.GetRepo(),
		ActRepo:       actDao.GetRepo(),
		ShopRepo:      shopDao.GetRepo(),
		LevelRepo:     levelDao.GetRepo(),
		CouponSrv:     couponSrv.GetService(),
		ConfigRepo:    adminConfig.GetRepo(),
		CommonSrv:     common.GetService(),
		RedisCli:      redis.GetRedisClient(),
		SubscribeRepo: subDao.GetRepo(),
	}
}
