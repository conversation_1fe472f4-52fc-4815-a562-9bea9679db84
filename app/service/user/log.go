package user

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	userLog "blind_box/app/dao/user/user_log"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util/ctxUtil"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ua-parser/uap-go/uaparser"
	"gorm.io/gorm"
)

var (
	userLogInit   sync.Once
	userLogEntity *UserLogEntity
	logBatchSize  = 100
	logTickerTime = time.Second
)

type UserLogEntity struct {
	logChan      chan *operateLogChan
	loginLogChan chan *loginLog<PERSON>han
	done         chan bool
	mu           sync.Mutex
	isClosed     bool
	parser       *uaparser.Parser
}

type operateLogChan struct {
	*userLog.Model
	ctx *gin.Context
}

type loginLog<PERSON>han struct {
	*userLog.LoginLog
	ctx *gin.Context
}

// GetUserLogEntity
func GetUserLogEntity() *UserLogEntity {
	if userLogEntity == nil {
		userLogInit.Do(func() {
			userLogEntity = newUserLogEntity()
			newCtx := ctxUtil.NewRequestID(context.Background())
			userLogEntity.Start(newCtx)
			helper.GetAppSrvMgr(newCtx).Register(userLogEntity)
		})
	}
	return userLogEntity
}

func newUserLogEntity() *UserLogEntity {
	return &UserLogEntity{
		logChan:      make(chan *operateLogChan, 1000),
		loginLogChan: make(chan *loginLogChan, 1000),
		done:         make(chan bool),
		parser:       uaparser.NewFromSaved(),
	}
}

func (l *UserLogEntity) Start(ctx context.Context) error {
	helper.AsyncDo(func() {
		var (
			ticker  = time.NewTicker(logTickerTime)
			msgList = make(userLog.ModelList, 0, logBatchSize)
			ginC    *gin.Context
		)
		for {
			select {
			case msg := <-l.logChan:
				msgList = append(msgList, msg.Model)
				ginC = msg.ctx
				if len(msgList) >= logBatchSize {
					userLog.GetRepo().BatchCreateWithTx(dbs.NewMysqlEngines().UseWithGinCtx(ginC, true), msgList)
					msgList = msgList[:0]
					ticker.Reset(logTickerTime)
				}
			case logMsg := <-l.loginLogChan:
				log.Ctx(logMsg.ctx).Info("loginLogChan customer start")
				if err := dbs.NewMysqlEngines().UseWithGinCtx(logMsg.ctx, true).Transaction(func(tx *gorm.DB) (err error) {
					if err = userDao.GetRepo().UpdateMapByIDWithTx(nil, tx, logMsg.UserID, map[string]interface{}{"last_login_time": time.Now().Unix()}); err != nil {
						return
					}
					if err = userLog.GetRepo().CreateLoginLogWithTx(tx, logMsg.LoginLog); err != nil {
						return
					}
					return nil
				}); err != nil {
					log.Ctx(logMsg.ctx).WithError(err).Error("loginLogChan customer err")
				} else {
					// 登录时间更新成功后，清理用户缓存
					if err := userDao.GetRepo().RedisClearUserInfo(logMsg.ctx, logMsg.UserID); err != nil {
						log.Ctx(logMsg.ctx).WithError(err).Warn("loginLogChan: Failed to clear user cache after login time update, userID=%d", logMsg.UserID)
					} else {
						log.Ctx(logMsg.ctx).Info("loginLogChan: Successfully cleared user cache after login time update, userID=%d", logMsg.UserID)
					}
				}
				log.Ctx(logMsg.ctx).Info("loginLogChan customer end")
			case <-ticker.C:
				if len(msgList) > 0 {
					userLog.GetRepo().BatchCreateWithTx(dbs.NewMysqlEngines().UseWithGinCtx(ginC, true), msgList)
					msgList = msgList[:0]
				}
			case <-ctx.Done():
				l.mu.Lock()
				defer l.mu.Unlock()
				l.isClosed = true
				close(l.logChan)
				close(l.loginLogChan)
				l.done <- true
				ticker.Stop()
				return
			default:
				time.Sleep(200 * time.Millisecond)
			}
		}
	})

	return nil
}

func (l *UserLogEntity) Close(ctx context.Context) error {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
	return nil
}

func (l *UserLogEntity) Producer(ctx *gin.Context, headers, body []byte) (bool, error) {
	var rspCode string
	if val, ok := ctx.Get("rspCode"); ok && val != nil {
		rspCode = val.(string)
	}
	if rspCode != "" && rspCode != ecode.OK.Code() {
		return true, nil
	}

	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return true, nil
	}

	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		return false, err
	}

	c := ctx.Copy()
	c.Request = c.Request.Clone(context.WithoutCancel(c.Request.Context()))

	logModel := &userLog.Model{
		UserID:   ctxUser.UID,
		Method:   ctx.Request.Method,
		TraceID:  ctxUtil.GetRequestID(c.Request.Context()),
		Url:      strings.TrimPrefix(ctx.Request.URL.Path, "/"),
		ClientIp: ctx.ClientIP(),
		Header:   string(headers),
		Body:     string(body),
	}

	l.logChan <- &operateLogChan{
		Model: logModel,
		ctx:   c,
	}
	return true, nil
}

func (l *UserLogEntity) ProducerLogin(ctx *gin.Context, userAuth *userDao.UserAuth) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return true, nil
	}

	loginLog := &userLog.LoginLog{
		UserID:   userAuth.ID,
		ClientIp: ctx.ClientIP(),
		Device:   ctx.GetHeader("Client-Source"),
	}
	cli := l.parser.Parse(ctx.GetHeader("User-Agent"))
	loginLog.UserAgent = fmt.Sprintf("%s-%s-%s", cli.UserAgent.Family, cli.Os.Family, cli.Device.Family)

	headers, _ := json.Marshal(ctx.Request.Header)
	loginLog.Header = string(headers)

	c := ctx.Copy()
	c.Request = c.Request.Clone(context.WithoutCancel(c.Request.Context()))

	l.loginLogChan <- &loginLogChan{
		LoginLog: loginLog,
		ctx:      c,
	}

	return true, nil
}

func (l *UserLogEntity) Name() string {
	return "UserLog"
}
