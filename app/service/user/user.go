package user

import (
	"blind_box/app/common/dbs"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

// UserEdit .
func (e *Entry) UserEdit(ctx *gin.Context, UID uint64, req userDto.UserEditReq) error {
	uInfo, err := e.getUserInfo(ctx, UID)
	if err != nil {
		return err
	}
	if req.Nickname != "" {
		uInfo.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		uInfo.Avatar = dbs.CdnImg(req.Avatar)
	}
	if req.Email != "" {
		uInfo.Email = req.Email
	}

	if err = e.UserRepo.CreateOrUpdate(ctx, uInfo, true); err != nil {
		return err
	}

	// 用户信息更新成功后，清理用户缓存
	if err := e.UserRepo.RedisClearUserInfo(ctx, UID); err != nil {
		log.Ctx(ctx).WithError(err).Warn("UserEdit: Failed to clear user cache after edit, userID=%d", UID)
	} else {
		log.Ctx(ctx).Info("UserEdit: Successfully cleared user cache after edit, userID=%d", UID)
	}

	return nil
}

// UserEditPwd .
func (e *Entry) UserEditPwd(ctx *gin.Context, UID uint64, oldPwd, newPwd string) error {
	uInfo, err := e.UserRepo.FetchByID(ctx, UID)
	if err != nil {
		return err
	}
	if uInfo.ID == 0 {
		return ecode.ParamErr
	}
	// dePwd, _ := helper.AesCbcDecrypt(uInfo.Password, helper.AesKey)
	// if dePwd != oldPwd {
	// 	return fmt.Errorf("原密码不正确")
	// }

	enPwd, _ := helper.AesCbcEncrypt(newPwd)
	if err = e.UserRepo.UpdateMapByID(ctx, UID, map[string]interface{}{"password": enPwd}); err != nil {
		return err
	}

	// 用户密码更新成功后，清理用户缓存
	if err := e.UserRepo.RedisClearUserInfo(ctx, UID); err != nil {
		log.Ctx(ctx).WithError(err).Warn("UserEditPwd: Failed to clear user cache after password change, userID=%d", UID)
	} else {
		log.Ctx(ctx).Info("UserEditPwd: Successfully cleared user cache after password change, userID=%d", UID)
	}

	return nil
}
