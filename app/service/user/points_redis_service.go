package user

import (
	userDao "blind_box/app/dao/user"
	"blind_box/app/dao/user/points"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	redigoRedis "github.com/gomodule/redigo/redis"
	"gorm.io/gorm"
)

const (
	// Redis ZSet key for points expiration queue
	PointsExpireQueueKey = "points_expire_queue"
)

// PointsRedisService Redis优化版本的积分服务
type PointsRedisService struct {
	pointsRepo points.PointsLogRepo
	userRepo   *userDao.Entry
}

// NewPointsRedisService 创建Redis优化版积分服务
func NewPointsRedisService(pointsRepo points.PointsLogRepo, userRepo *userDao.Entry) *PointsRedisService {
	return &PointsRedisService{
		pointsRepo: pointsRepo,
		userRepo:   userRepo,
	}
}

// AddUserPointsWithExpireAndRedis 增加用户积分（Redis版本，自动添加到过期队列）
func (s *PointsRedisService) AddUserPointsWithExpireAndRedis(ctx *gin.Context, tx *gorm.DB, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string, expireAt *time.Time) (*points.PointsLog, error) {
	if addPoints == 0 {
		return nil, nil
	}

	// 首先使用基础FIFO服务创建积分记录
	fifoService := &PointsFIFOService{
		pointsRepo: s.pointsRepo,
		userRepo:   s.userRepo,
	}

	pointsLog, err := fifoService.AddUserPointsWithExpireAndTx(ctx, tx, userID, addPoints, sourceType, sourceID, remark, expireAt)
	if err != nil {
		return nil, err
	}

	// 如果有过期时间，添加到Redis过期队列
	if expireAt != nil && pointsLog != nil {
		// 使用实际的日志ID构建member，格式: "user_id:log_id:points"
		member := fmt.Sprintf("%d:%d:%d", userID, pointsLog.ID, addPoints)
		err = s.addToExpireQueueWithRetry(ctx, *expireAt, member)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("Failed to add points to Redis expire queue after retries, logID=%d, will add to compensation queue", pointsLog.ID)
			// 记录到补偿队列以便后续处理
			s.recordForCompensation(ctx, userID, pointsLog.ID, *expireAt)
		} else {
			log.Ctx(ctx).Info("Added points to Redis expire queue: userID=%d, logID=%d, expireAt=%v", userID, pointsLog.ID, expireAt)
		}
	}

	return pointsLog, nil
}

// addToExpireQueue 添加积分记录到Redis过期队列
func (s *PointsRedisService) addToExpireQueue(ctx *gin.Context, expireAt time.Time, member string) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	score := expireAt.Unix()
	key := redis.GetPrefixKey(PointsExpireQueueKey)

	_, err := conn.Do("ZADD", key, score, member)
	if err != nil {
		return fmt.Errorf("failed to add to Redis expire queue: %w", err)
	}

	log.Ctx(ctx).Info("Added points to Redis expire queue: member=%s, expireAt=%v", member, expireAt)
	return nil
}

// addToExpireQueueWithRetry 带重试机制的添加到过期队列
func (s *PointsRedisService) addToExpireQueueWithRetry(ctx *gin.Context, expireAt time.Time, member string) error {
	maxRetries := 3
	baseDelay := 50 * time.Millisecond

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := s.addToExpireQueue(ctx, expireAt, member)
		if err == nil {
			if attempt > 1 {
				log.Ctx(ctx).Info("Successfully added to expire queue on attempt %d: member=%s", attempt, member)
			}
			return nil
		}

		log.Ctx(ctx).WithError(err).Warn("Failed to add to expire queue on attempt %d: member=%s", attempt, member)

		if attempt < maxRetries {
			delay := baseDelay * time.Duration(attempt)
			time.Sleep(delay)
		}
	}

	return fmt.Errorf("failed to add to expire queue after %d attempts", maxRetries)
}

// recordForCompensation 记录失败的Redis操作到补偿队列（这里简化为日志记录）
func (s *PointsRedisService) recordForCompensation(ctx *gin.Context, userID, logID uint64, expireAt time.Time) {
	// 在实际生产环境中，这里应该记录到数据库的补偿表或消息队列中
	// 目前先用日志记录，可以通过日志分析工具来处理
	log.Ctx(ctx).Error("COMPENSATION_NEEDED: Redis operation failed, userID=%d, logID=%d, expireAt=%v",
		userID, logID, expireAt.Format("2006-01-02 15:04:05"))

	// TODO: 可以添加到专门的补偿队列表中
	// 定期扫描该表并重试失败的操作
}

// removeFromExpireQueue 从Redis过期队列移除记录
func (s *PointsRedisService) removeFromExpireQueue(ctx *gin.Context, member string) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	key := redis.GetPrefixKey(PointsExpireQueueKey)

	_, err := conn.Do("ZREM", key, member)
	if err != nil {
		return fmt.Errorf("failed to remove from Redis expire queue: %w", err)
	}

	log.Ctx(ctx).Info("Removed points from Redis expire queue: member=%s", member)
	return nil
}

// ProcessExpiredPointsWithRedis 使用Redis队列处理过期积分
func (s *PointsRedisService) ProcessExpiredPointsWithRedis(ctx *gin.Context, limit int64) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	now := time.Now()
	key := redis.GetPrefixKey(PointsExpireQueueKey)

	// 从Redis ZSet获取已过期的积分记录
	expiredMembers, err := redigoRedis.Strings(conn.Do("ZRANGEBYSCORE", key, 0, now.Unix(), "LIMIT", 0, limit))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to get expired points from Redis")
		return err
	}

	if len(expiredMembers) == 0 {
		log.Ctx(ctx).Info("No expired points found in Redis queue")
		return nil
	}

	log.Ctx(ctx).Info("Found %d expired points records in Redis queue", len(expiredMembers))

	// 记录处理结果
	var processedMembers []string
	var failedMembers []string

	// 处理每个过期记录
	for _, member := range expiredMembers {
		err := s.processExpiredMemberWithRedis(ctx, member)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("Failed to process expired member: %s", member)
			failedMembers = append(failedMembers, member)
			continue // 继续处理其他记录
		}

		processedMembers = append(processedMembers, member)
	}

	// 只移除成功处理的记录
	for _, member := range processedMembers {
		err = s.removeFromExpireQueue(ctx, member)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("Failed to remove processed member from queue: %s", member)
			// 记录清理失败，但不影响业务逻辑
		}
	}

	// 记录处理统计
	log.Ctx(ctx).Info("Processed expired points: total=%d, success=%d, failed=%d",
		len(expiredMembers), len(processedMembers), len(failedMembers))

	return nil
}

// processExpiredMemberWithRedis 处理单个过期的Redis队列成员
func (s *PointsRedisService) processExpiredMemberWithRedis(ctx *gin.Context, member string) error {
	// 解析member格式: "user_id:log_id:points"
	parts := strings.Split(member, ":")
	if len(parts) < 3 {
		return fmt.Errorf("invalid member format: %s, expected format: user_id:log_id:points", member)
	}

	userID, err := strconv.ParseUint(parts[0], 10, 64)
	if err != nil {
		return fmt.Errorf("invalid user ID in member: %s", member)
	}

	logID, err := strconv.ParseUint(parts[1], 10, 64)
	if err != nil {
		return fmt.Errorf("invalid log ID in member: %s", member)
	}

	expectedPoints, err := strconv.ParseUint(parts[2], 10, 64)
	if err != nil {
		return fmt.Errorf("invalid points in member: %s", member)
	}

	// 直接根据logID查询对应的积分记录
	filter := &points.PointsLogFilter{
		ID: logID,
	}
	pointsLogs, err := s.pointsRepo.FindByFilter(ctx, filter)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to get points log by ID: %d", logID)
		return err
	}

	if len(pointsLogs) == 0 {
		log.Ctx(ctx).Warn("Points log not found: ID=%d, member=%s", logID, member)
		return nil // 记录不存在，可能已被删除，直接跳过
	}

	pointsLog := pointsLogs[0]

	// 验证记录匹配性
	if pointsLog.UserID != userID {
		log.Ctx(ctx).Warn("User ID mismatch: expected=%d, actual=%d, member=%s", userID, pointsLog.UserID, member)
		return nil
	}

	if pointsLog.Points != expectedPoints {
		log.Ctx(ctx).Warn("Points mismatch: expected=%d, actual=%d, member=%s", expectedPoints, pointsLog.Points, member)
		// 继续处理，因为积分数可能在后续操作中被调整
	}

	// 检查记录是否确实已过期且未处理
	if pointsLog.ExpireAt == nil {
		log.Ctx(ctx).Info("Points log has no expire time, skipping: ID=%d", logID)
		return nil // 永不过期的积分
	}

	if pointsLog.ExpireAt.After(time.Now()) {
		log.Ctx(ctx).Info("Points log not yet expired, skipping: ID=%d, expireAt=%v", logID, pointsLog.ExpireAt)
		return nil // 还未过期
	}

	if pointsLog.IsExpired != 0 {
		log.Ctx(ctx).Info("Points log already processed as expired: ID=%d", logID)
		return nil // 已经处理过期
	}

	if pointsLog.GetRemainingPoints() == 0 {
		log.Ctx(ctx).Info("Points log has no remaining points, skipping: ID=%d", logID)
		return nil // 没有剩余积分
	}

	// 处理单个过期记录
	userExpiredLogs := []*points.PointsLog{pointsLog}

	// 使用基础FIFO服务处理过期积分
	fifoService := &PointsFIFOService{
		pointsRepo: s.pointsRepo,
		userRepo:   s.userRepo,
	}

	err = fifoService.ProcessUserExpiredPointsWithTx(ctx, userID, userExpiredLogs)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to process expired points for user %d, logID %d", userID, logID)
		return err
	}

	log.Ctx(ctx).Info("Successfully processed expired points: UserID=%d, LogID=%d, RemainingPoints=%d",
		userID, logID, pointsLog.GetRemainingPoints())

	return nil
}

// SyncExpireQueueFromDB 从数据库同步积分过期队列到Redis（初始化或修复用）
func (s *PointsRedisService) SyncExpireQueueFromDB(ctx *gin.Context) error {
	log.Ctx(ctx).Info("Starting to sync expire queue from database to Redis")

	// 先清空现有队列以确保数据一致性
	err := s.ClearExpireQueue(ctx)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("Failed to clear existing expire queue")
	}

	// 查询所有未过期且有过期时间的积分记录
	filter := &points.PointsLogFilter{
		ActionType:     uint32(points.PointsActionTypeAdd),
		OnlyWithExpire: true,
		IsExpired:      &[]bool{false}[0], // 未过期
		HasRemaining:   &[]bool{true}[0],  // 有剩余积分
	}

	pointsLogs, err := s.pointsRepo.FindByFilter(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to fetch points logs: %w", err)
	}

	syncCount := 0
	batchSize := 100
	conn := redis.RedisPool.Get()
	defer conn.Close()

	key := redis.GetPrefixKey(PointsExpireQueueKey)

	// 批量添加到队列
	for i, pointsLog := range pointsLogs {
		if pointsLog.ExpireAt == nil || pointsLog.GetRemainingPoints() == 0 {
			continue
		}

		score := pointsLog.ExpireAt.Unix()
		member := fmt.Sprintf("%d:%d:%d", pointsLog.UserID, pointsLog.ID, pointsLog.Points)

		_, err = conn.Do("ZADD", key, score, member)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("Failed to sync points log to Redis: ID=%d", pointsLog.ID)
			continue
		}
		syncCount++

		// 每批次提交一次
		if (i+1)%batchSize == 0 {
			log.Ctx(ctx).Info("Synced batch: %d/%d records", i+1, len(pointsLogs))
		}
	}

	log.Ctx(ctx).Info("Successfully synced %d points records to Redis expire queue", syncCount)
	return nil
}

// ClearExpireQueue 清空过期队列
func (s *PointsRedisService) ClearExpireQueue(ctx *gin.Context) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	key := redis.GetPrefixKey(PointsExpireQueueKey)

	_, err := conn.Do("DEL", key)
	if err != nil {
		return fmt.Errorf("failed to clear expire queue: %w", err)
	}

	log.Ctx(ctx).Info("Cleared Redis expire queue")
	return nil
}

// RemovePointsFromQueue 从队列中移除特定的积分记录
func (s *PointsRedisService) RemovePointsFromQueue(ctx *gin.Context, userID, logID uint64) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	key := redis.GetPrefixKey(PointsExpireQueueKey)

	// 查找所有可能的member格式，因为points数可能会变化
	pattern := fmt.Sprintf("%d:%d:*", userID, logID)

	// 使用ZSCAN查找匹配的member
	cursor := 0
	for {
		values, err := redigoRedis.Values(conn.Do("ZSCAN", key, cursor, "MATCH", pattern))
		if err != nil {
			return fmt.Errorf("failed to scan expire queue: %w", err)
		}

		if len(values) < 2 {
			break
		}

		cursor, _ = redigoRedis.Int(values[0], nil)
		members, _ := redigoRedis.Strings(values[1], nil)

		// 删除找到的members
		for i := 0; i < len(members); i += 2 { // ZSCAN返回的是member,score对
			member := members[i]
			_, err = conn.Do("ZREM", key, member)
			if err != nil {
				log.Ctx(ctx).WithError(err).Warn("Failed to remove member from queue: %s", member)
			} else {
				log.Ctx(ctx).Info("Removed points from Redis queue: UserID=%d, LogID=%d, Member=%s", userID, logID, member)
			}
		}

		if cursor == 0 {
			break
		}
	}

	return nil
}

// ProcessExpiredPointsBatch 批量处理过期积分（支持限制处理数量）
func (s *PointsRedisService) ProcessExpiredPointsBatch(ctx *gin.Context, batchSize int64, maxBatches int) error {
	processedTotal := 0
	batchCount := 0

	for batchCount < maxBatches {
		err := s.ProcessExpiredPointsWithRedis(ctx, batchSize)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("Failed to process expired points batch %d", batchCount+1)
			return err
		}

		// 检查队列中是否还有过期记录
		status, err := s.GetExpireQueueStatus(ctx)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("Failed to get queue status")
			break
		}

		expiredCount := status["expired_count"].(int64)
		if expiredCount == 0 {
			log.Ctx(ctx).Info("No more expired points to process")
			break
		}

		processedTotal += int(batchSize)
		batchCount++

		log.Ctx(ctx).Info("Processed batch %d, remaining expired: %d", batchCount, expiredCount)
	}

	log.Ctx(ctx).Info("Batch processing completed: %d batches processed, estimated total: %d records", batchCount, processedTotal)
	return nil
}

// CleanExpiredFromQueue 清理Redis队列中已经过期很久的记录（维护任务）
func (s *PointsRedisService) CleanExpiredFromQueue(ctx *gin.Context, beforeTime time.Time) error {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	key := redis.GetPrefixKey(PointsExpireQueueKey)
	maxScore := beforeTime.Unix()

	removedCount, err := redigoRedis.Int64(conn.Do("ZREMRANGEBYSCORE", key, 0, maxScore))
	if err != nil {
		return fmt.Errorf("failed to clean expired from queue: %w", err)
	}

	log.Ctx(ctx).Info("Cleaned %d expired records from Redis queue", removedCount)
	return nil
}

// GetExpireQueueStatus 获取Redis过期队列状态（监控用）
func (s *PointsRedisService) GetExpireQueueStatus(ctx *gin.Context) (map[string]interface{}, error) {
	conn := redis.RedisPool.Get()
	defer conn.Close()

	now := time.Now()
	key := redis.GetPrefixKey(PointsExpireQueueKey)

	// 总记录数
	totalCount, err := redigoRedis.Int64(conn.Do("ZCARD", key))
	if err != nil {
		return nil, err
	}

	// 已过期记录数
	expiredCount, err := redigoRedis.Int64(conn.Do("ZCOUNT", key, 0, now.Unix()))
	if err != nil {
		return nil, err
	}

	// 未来1小时内过期的记录数
	oneHourLater := now.Add(time.Hour)
	nearExpireCount, err := redigoRedis.Int64(conn.Do("ZCOUNT", key, now.Unix(), oneHourLater.Unix()))
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_count":       totalCount,
		"expired_count":     expiredCount,
		"near_expire_count": nearExpireCount,
		"queue_key":         PointsExpireQueueKey,
		"check_time":        now.Format("2006-01-02 15:04:05"),
	}, nil
}
