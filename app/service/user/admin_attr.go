package user

import (
	"blind_box/app/common/dbs"
	addrDao "blind_box/app/dao/user/addr"
	userLog "blind_box/app/dao/user/user_log"
	"blind_box/app/dto/common"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
)

// AdminUserLoginLogList .
func (e *Entry) AdminUserLoginLogList(ctx *gin.Context, UID uint64, req userDto.AdminUserLoginLogListReq) (*userDto.AdminUserLoginLogListResp, error) {
	total, list, err := e.UserLogRepo.LoginLogPageList(ctx, &userLog.LoginLogFilter{UserID: UID}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*userDto.AdminUserLoginLogListItem, 0, len(list))
	for _, val := range list {
		item := &userDto.AdminUserLoginLogListItem{
			ID:            val.ID,
			UserID:        val.UserID,
			ClientIp:      val.ClientIp,
			Source:        val.Source,
			Device:        val.Device,
			AgreeProtocol: val.AgreeProtocol,
			CreatedAt:     val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}
	return &userDto.AdminUserLoginLogListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminUserAddrList .
func (e *Entry) AdminUserAddrList(ctx *gin.Context, UID uint64, page, limit int) (*userDto.AdminUserAddrListResp, error) {
	total, list, err := e.AddrRepo.DatePageList(ctx, &addrDao.Filter{UID: UID}, page, limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*common.CommonUserAddrInfo, 0, len(list))
	for _, val := range list {
		retList = append(retList, val.ToItemResp())
	}
	return &userDto.AdminUserAddrListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSetUserAddr .
func (e *Entry) AdminSetUserAddr(ctx *gin.Context, UID uint64, req userDto.SetUserAddrReq) error {
	return e.SetUserAddr(ctx, UID, req)
}

// SetUserAddr .
func (e *Entry) SetUserAddr(ctx *gin.Context, UID uint64, req userDto.SetUserAddrReq) error {
	var (
		model = &addrDao.Model{}
		err   error
		m     = addrDao.SetUserAddrReqToModel(UID, req)
	)
	if req.ID > 0 {
		if model, err = e.AddrRepo.FetchByID(ctx, req.ID); err != nil {
			return err
		}
		if model.ID == 0 {
			return ecode.ParamErr
		}
	}
	if req.IsDefault > 0 {
		tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		if err := func() (err error) {
			if err = e.AddrRepo.UpdateMapByFilterWithTx(ctx, tx, &addrDao.Filter{UID: UID, IsDefault: dbs.True},
				map[string]interface{}{"is_default": dbs.False},
			); err != nil {
				return
			}
			if err = e.AddrRepo.CreateOrUpdateWithTx(ctx, tx, m); err != nil {
				return
			}

			return tx.Commit().Error
		}(); err != nil {
			tx.Callback()
			return err
		}
	} else {
		if err = e.AddrRepo.CreateOrUpdate(ctx, m); err != nil {
			return err
		}
	}
	return nil
}

// UserAddrInfo .
func (e *Entry) UserAddrInfo(ctx *gin.Context, UID, addrId uint64) (*common.CommonUserAddrInfo, error) {
	var (
		addr = &addrDao.Model{}
		err  error
	)
	if addr, err = e.AddrRepo.FetchByID(ctx, addrId); err != nil {
		return nil, err
	}
	if addr.UserID != UID {
		return nil, ecode.ParamErr
	}

	return addr.ToItemResp(), nil
}

// AdminOperateAddr .
func (e *Entry) AdminOperateAddr(ctx *gin.Context, ID uint64, action dbs.OperateAction, isAdmin bool) error {
	model, err := e.AddrRepo.FetchByID(ctx, ID)
	if err != nil {
		return err
	}
	if model.ID == 0 {
		return ecode.ParamErr
	}

	switch action {
	case dbs.ActionDel:
		return e.AddrRepo.UpdateMapByID(ctx, ID, map[string]interface{}{string(dbs.SoftDelField): dbs.True})
	}
	return nil
}
