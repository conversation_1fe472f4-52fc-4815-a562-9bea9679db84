package user

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	tideDao "blind_box/app/dao/user/tide"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

// GetTideTaskInfo 潮气值任务列表
func (e *Entry) GetTideTaskInfo(ctx *gin.Context, uid uint64) (*userDto.TideTaskConfigResp, error) {
	configList, err := e.TideRepo.RedisGetTideTaskConfigList(ctx)
	if err != nil {
		return nil, err
	}
	respList := []*userDto.TideTaskConfig{}
	for _, info := range configList {
		item := &userDto.TideTaskConfig{
			Id:       info.Id,
			Title:    info.Title,
			Cover:    info.Cover,
			TideVal:  info.TideVal,
			TaskType: info.TaskType,
			IsFinish: dbs.False,
			IsGet:    dbs.False,
		}
		taskStatus, err := e.TideRepo.RedisGetUserTideTaskStatus(ctx, uid, tideDao.TideTaskType(info.TaskType))
		if err != nil {
			respList = append(respList, item)
			continue
		}
		if info.TaskType == uint32(tideDao.TideTaskTypeShake) && taskStatus > 0 {
			item.IsFinish = dbs.True
		}
		if info.TaskType == uint32(tideDao.TideTaskTypeDraw) && taskStatus >= info.AccomplishNum {
			item.IsFinish = dbs.True
		}
		isGet, err := e.TideRepo.IsGetUserTaskTide(ctx, uid, info.Id)
		if err != nil {
			respList = append(respList, item)
			continue
		}
		if isGet {
			item.IsGet = dbs.True
		}
		respList = append(respList, item)
	}

	return &userDto.TideTaskConfigResp{
		ConfigList: respList,
	}, nil
}

// UserGetRegTide 用户领取新人注册潮气值奖励
func (e *Entry) UserGetRegTide(ctx *gin.Context, tx *gorm.DB, uInfo *userDao.Model, tideVal uint32) error {
	if uInfo.TideVal >= tideDao.UserTideValMax {
		return ecode.UserTideIsMaxErr
	}
	if err := e.UpdateUserTide(ctx, tx, tideVal, uInfo.TideVal, &tideDao.TideLog{
		UserId:      uint64(uInfo.ID),
		TideValType: uint32(tideDao.TideValTypeAdd),
		Source:      uint32(tideDao.TideSourceTypeReg),
	}); err != nil {
		return err
	}

	e.TideRepo.RedisClearUserGetRegTide(ctx, uint64(uInfo.ID))
	return nil
}

// UserGetTaskVal 用户领取完成任务潮气值奖励
func (e *Entry) UserGetTaskVal(ctx *gin.Context, uid, taskId uint64) error {
	uInfo, _ := e.getUserInfo(ctx, uid)
	if uInfo.TideVal >= tideDao.UserTideValMax {
		return ecode.UserTideIsMaxErr
	}
	isGet, err := e.TideRepo.IsGetUserTaskTide(ctx, uid, taskId)
	if err != nil {
		return err
	}
	if isGet {
		return ecode.UserGetedTaskRewardErr
	}
	taskInfo, err := e.TideRepo.RedisGetTideTaskConfigInfo(ctx, taskId)
	if err != nil {
		return err
	}
	taskStatus, err := e.TideRepo.RedisGetUserTideTaskStatus(ctx, uid, tideDao.TideTaskType(taskInfo.TaskType))
	if err != nil {
		return err
	}
	if taskInfo.TaskType == uint32(tideDao.TideTaskTypeShake) && taskStatus == 0 {
		return ecode.UserTaskNotFinishErr
	}
	if taskInfo.TaskType == uint32(tideDao.TideTaskTypeDraw) && taskStatus < taskInfo.AccomplishNum {
		return ecode.UserTaskNotFinishErr
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := e.UpdateUserTide(ctx, tx, taskInfo.TideVal, uInfo.TideVal, &tideDao.TideLog{
		UserId:      uid,
		TideValType: uint32(tideDao.TideValTypeAdd),
		Source:      uint32(tideDao.TideSourceTypeTask),
		SourceId:    taskId,
	}); err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	// 任务潮气值奖励成功后，清理用户缓存
	if err := e.UserRepo.RedisClearUserInfo(ctx, uid); err != nil {
		log.Ctx(ctx).WithError(err).Warn("UserGetTaskVal: Failed to clear user cache after task reward, userID=%d", uid)
	} else {
		log.Ctx(ctx).Info("UserGetTaskVal: Successfully cleared user cache after task reward, userID=%d", uid)
	}

	// e.TideRepo.RedisClearUserGetTaskTide(ctx, uid, taskId)
	return nil
}

// GetUserTideBill 用户潮气值明细账单(近7日)
func (e *Entry) GetUserTideBill(ctx *gin.Context, uid uint64, page, limit int) (*userDto.UserTideBillResp, error) {
	visibleDaysDate := carbon.Now().SubDays(tideDao.UserVisibleTideLogDays).StartOfDay().Timestamp()
	total, billList, err := e.TideRepo.DataPageList(ctx, &tideDao.LogFilter{UserId: uid, StartTime: visibleDaysDate}, page, limit)
	if err != nil {
		return nil, err
	}
	return e.dealTideBill(ctx, billList, total)
}

// UpdateUserTide 更新用户潮气值+潮气值log
func (e *Entry) UpdateUserTide(ctx *gin.Context, tx *gorm.DB, tideVal, uTideVal uint32, tideLog *tideDao.TideLog) (err error) {
	realTideVal := e.TideRepo.GetRealTideVal(int32(tideVal), int32(uTideVal), tideDao.TideValType(tideLog.TideValType))
	if realTideVal <= 0 {
		return nil
	}

	tideLog.TideVal = realTideVal
	userTideVal := uTideVal + realTideVal
	if tideLog.TideValType == uint32(tideDao.TideValTypeSubtract) {
		userTideVal = uTideVal - realTideVal
	}

	if err := func() (err error) {
		if err = e.TideRepo.CreateTideLogWithTx(tx, tideLog); err != nil {
			return
		}
		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, tideLog.UserId, map[string]interface{}{
			"tide_val": userTideVal,
		}); err != nil {
			return
		}
		return nil
	}(); err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateUserTide err")
		return err
	}

	return nil
}

func (e *Entry) dealTideBill(ctx *gin.Context, dataList tideDao.TideLogList, total int64) (*userDto.UserTideBillResp, error) {
	userList, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: dataList.GetUids()})
	if err != nil {
		return nil, err
	}
	userMap := userList.GetIDMap()

	respList := []*userDto.UserTideBillItem{}
	for _, info := range dataList {
		item := &userDto.UserTideBillItem{
			Id:          info.Id,
			UserId:      info.UserId,
			Source:      info.Source,
			TideVal:     info.TideVal,
			TideValType: info.TideValType,
			Remark:      info.Remark,
			CreatedTime: info.GetCreatedTime(),
		}
		if name, ok := tideDao.TideSourceMap[info.Source]; ok {
			item.SourceName = name
		}
		if v, ok := userMap[info.UserId]; ok {
			item.UserName = v.Nickname
		}
		respList = append(respList, item)
	}
	return &userDto.UserTideBillResp{
		List:  respList,
		Total: total,
	}, nil
}
