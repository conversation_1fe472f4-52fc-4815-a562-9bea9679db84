# User Service Architecture Documentation

## Overview

The user service layer provides comprehensive user management functionality including authentication, profile management, points system, shopping cart, and sign-in rewards. This service follows the project's layered architecture with proper dependency injection and transaction management.

## Core Architecture

### Directory Structure
```
app/service/user/
├── interface.go           # Service interface and dependency injection
├── login/                 # Authentication provider strategies
│   ├── interface.go       # Login manager interface
│   ├── login.go          # Core login management
│   └── wx_applet.go      # WeChat applet login provider
├── user.go               # User profile management
├── admin_user.go         # Admin user management
├── cart.go               # Shopping cart functionality
├── user_sign.go          # Sign-in reward system
├── points_*.go           # Points system (FIFO and Redis variants)
└── user_address.go       # User address management
```

### Service Entry Pattern
```go
type Entry struct {
    // Core dependencies (15+ repositories)
    UserRepo         *userDao.Entry
    CartRepo         *userCart.Entry
    PointsRepo       *pointsDao.Entry
    SignRepo         *signDao.Entry
    AddressRepo      *addressDao.Entry
    TideRepo         *tideDao.Entry
    LevelRepo        *levelDao.Entry
    ShopRepo         *shopDao.Entry
    DeliveryRepo     *deliveryDao.Entry
    CouponUserRepo   *couponUserDao.Entry
    BoxUserRepo      *boxUserDao.Entry
    ActivityRepo     *activityDao.Entry
    UserActRepo      *userActDao.Entry
    
    // Service dependencies
    CommonSrv        common.Entry
    LoginManager     *login.Manager
}
```

## Key Business Components

### 1. Authentication System (`login/` directory)

#### Strategy Pattern Implementation
- **LoginManager**: Central authentication coordinator
- **Provider Interface**: Pluggable authentication strategies
- **WeChat Applet Provider**: Primary login method

#### Key Features
- **FindOrCreateUser**: Concurrent user lookup and creation with errgroup
- **Session Management**: JWT tokens with Redis validation  
- **Profile Integration**: Automatic profile setup during first login

```go
// Example: Concurrent user operations
eg.Go(func() error {
    return provider.CheckUserExist(ctx, req)
})
eg.Go(func() error {
    return provider.FetchUserInfo(ctx, req)
})
```

### 2. Points System

#### FIFO-Based Points Management
- **PointsFIFOService**: Advanced points consumption using first-in-first-out principle
- **Expiration Handling**: Automatic processing of expired points
- **Transaction Safety**: Full ACID compliance with proper locking

#### Redis-Optimized Variant
- **High-Performance Calculations**: Redis-based points operations
- **Cache Management**: Consistent cache invalidation patterns
- **Batch Operations**: Optimized for high-frequency point transactions

#### Key Operations
```go
// Add points with expiration
AddUserPointsWithExpireAndTx(ctx, tx, userID, points, sourceType, sourceID, remark, expireAt)

// FIFO consumption with locking
UsePointsWithFIFOAndTx(ctx, tx, userID, usePoints, sourceType, sourceID, remark)

// Expire old points automatically
ProcessExpiredPoints(ctx, limit)
```

### 3. Shopping Cart System

#### Concurrent Data Loading
- **errgroup Pattern**: Parallel fetching of SKU and shop information
- **Batch Optimization**: Preventing N+1 query problems
- **Delivery Integration**: Support for different delivery methods

#### Cart Operations
- **Merge Logic**: Automatic quantity aggregation for same SKU+delivery combinations
- **Shop Binding**: Dynamic shop assignment based on delivery type
- **Transaction Management**: Proper rollback on failures

### 4. Sign-in Reward System

#### Daily Sign-in Logic
- **Continuation Tracking**: Redis-cached sign-in streaks
- **Reward Calculation**: Configurable rewards based on consecutive days
- **Tide Value Integration**: Automatic tide value updates with limits

#### Concurrency Handling
```go
// Parallel validation checks
eg.Go(func() error { return validateUser() })
eg.Go(func() error { return checkTodaySignStatus() })
eg.Go(func() error { return loadSignConfigs() })
```

### 5. User Profile Management

#### Cache-First Strategy
- **Redis Primary**: User info cached for performance
- **Database Fallback**: Transparent cache miss handling
- **Cache Invalidation**: Proactive cache clearing after updates

#### Profile Operations
- **Atomic Updates**: Transaction-wrapped profile changes
- **Password Security**: AES encryption for sensitive data
- **Avatar Processing**: CDN integration for image handling

## Technical Patterns

### 1. Dependency Injection
```go
// Singleton pattern with sync.Once
var (
    userSrvEntry *Entry
    userSrvOnce  sync.Once
)

func GetSrv() *Entry {
    userSrvOnce.Do(func() {
        userSrvEntry = newEntry()
    })
    return userSrvEntry
}
```

### 2. Transaction Management
```go
// Standard transaction pattern
tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
if err := func() error {
    // Business operations
    return tx.Commit().Error
}(); err != nil {
    tx.Rollback()
    return err
}
```

### 3. Concurrent Operations with errgroup
```go
var eg errgroup.Group
eg.Go(func() error { return operation1() })
eg.Go(func() error { return operation2() })
if err := eg.Wait(); err != nil {
    return err
}
```

### 4. Cache Management Pattern
```go
// Consistent cache clearing after updates
if err := e.UserRepo.RedisClearUserInfo(ctx, uid); err != nil {
    log.Ctx(ctx).WithError(err).Warn("Failed to clear user cache")
} else {
    log.Ctx(ctx).Info("Successfully cleared user cache")
}
```

## Data Flow Patterns

### 1. User Registration/Login Flow
```
Request → LoginManager → Provider Strategy → FindOrCreateUser → 
Profile Setup → Session Creation → Cache Warmup → Response
```

### 2. Points Transaction Flow
```
Request → Balance Check → FIFO Calculation → Lock Acquisition → 
Database Update → Log Creation → Cache Clear → Response
```

### 3. Cart Management Flow
```
Request → SKU Validation → Merge Logic → Shop Binding → 
Transaction Begin → Database Update → Commit → Response
```

## Performance Optimizations

### 1. Batch Query Prevention
- **Preload Related Data**: Load SKUs and shops in parallel before cart item processing
- **Map-Based Lookups**: Convert arrays to maps for O(1) access
- **Concurrent Fetching**: Use errgroup for independent data sources

### 2. Redis Caching Strategy
- **Hot Data Caching**: User profiles, sign-in status, shop information
- **TTL Management**: Appropriate expiration times for different data types
- **Cache Warming**: Proactive cache population during updates

### 3. Database Optimization
- **Transaction Scope**: Minimize transaction duration
- **Batch Updates**: Group related operations
- **Index Utilization**: Proper filtering in repository layer

## Error Handling Standards

### 1. Service Layer Errors
- **Business Logic Errors**: Return ecode package errors
- **Validation Errors**: Parameter validation at service entry
- **System Errors**: Log and wrap infrastructure errors

### 2. Transaction Error Recovery
```go
defer func() {
    if r := recover(); r != nil {
        if !committed {
            tx.Rollback()
        }
        panic(r)
    }
    if !committed {
        tx.Rollback()
    }
}()
```

### 3. Cache Error Handling
- **Non-Critical Operations**: Log cache errors but don't fail main flow
- **Graceful Degradation**: Continue operation even if cache operations fail
- **Monitoring Integration**: Proper error logging for observability

## Security Considerations

### 1. Authentication Security
- **JWT Validation**: Token verification with Redis blacklist support
- **Session Management**: Proper session timeout and cleanup
- **Authorization Checks**: User ownership validation for operations

### 2. Data Protection
- **Password Encryption**: AES encryption for sensitive data
- **Input Validation**: Comprehensive parameter checking
- **SQL Injection Prevention**: Parameterized queries through GORM

### 3. Business Rule Enforcement
- **Points Overflow Protection**: Maximum points limit enforcement
- **Concurrency Control**: Database locking for critical operations
- **Audit Logging**: Comprehensive operation logging for compliance

## Integration Points

### 1. External Services
- **WeChat API**: User profile and login integration
- **Redis**: Caching and session management
- **CDN**: Avatar and media file handling

### 2. Internal Services
- **Common Service**: Shared SKU and validation logic
- **Box Service**: Blind box opening and inventory
- **Order Service**: Purchase and transaction processing

### 3. Background Jobs
- **Points Expiration**: Scheduled processing of expired points
- **Cache Warming**: Periodic cache refresh for hot data
- **Data Synchronization**: Consistency checks and repairs

## Monitoring and Observability

### 1. Logging Standards
- **Structured Logging**: Consistent log format with context
- **Error Tracking**: Comprehensive error information
- **Performance Metrics**: Operation timing and success rates

### 2. Health Checks
- **Database Connectivity**: Repository health validation
- **Cache Availability**: Redis connection monitoring
- **External API Status**: WeChat API health checks

### 3. Business Metrics
- **User Activity**: Login frequency and retention
- **Points Usage**: Transaction volume and patterns
- **Cart Conversion**: Shopping cart to order conversion rates

## Development Guidelines

### 1. Code Standards
- **Interface-Driven Design**: All services implement interfaces
- **Dependency Injection**: Constructor-based dependency management
- **Error Propagation**: Proper error wrapping and context

### 2. Testing Approach
- **Unit Testing**: Individual method validation
- **Integration Testing**: End-to-end flow verification
- **Performance Testing**: Load testing for critical paths

### 3. Documentation Requirements
- **Code Comments**: Complex business logic explanation
- **API Documentation**: Request/response specifications
- **Deployment Guides**: Environment-specific configurations

This documentation serves as the definitive guide for understanding and maintaining the user service architecture within the blind box project ecosystem.