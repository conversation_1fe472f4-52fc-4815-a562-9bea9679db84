package user

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/util/sliceUtil"

	"github.com/gin-gonic/gin"
)

func (e *Entry) dealUserFilter(ctx *gin.Context, req userDto.AdminUserFilterReq) (uids []uint64, empty bool, err error) {
	var (
		searchUids []uint64
	)
	if req.UserID != 0 {
		uids = append(uids, req.UserID)
	}

	if req.UserName != "" || req.Account != "" || req.Mobile != "" || req.UserTypeID != 0 {
		if searchUids, err = e.UserRepo.FindXidsByFilter(ctx, &userDao.Filter{
			Nickname: req.UserName,
			Email:    req.Account,
			Mobile:   req.Mobile,
		}, dbs.PluckID); err != nil {
			return
		}
		if len(searchUids) == 0 {
			empty = true
			return
		}
		uids = sliceUtil.GetUintsIntersect(searchUids, uids)
		if len(uids) == 0 {
			empty = true
			return
		}
	}

	return
}

func (e *Entry) DealUserFilter(ctx *gin.Context, req userDto.AdminUserFilterReq) (uids []uint64, empty bool, err error) {
	return e.dealUserFilter(ctx, req)
}
