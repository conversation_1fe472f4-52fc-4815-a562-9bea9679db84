package user

import (
	userDao "blind_box/app/dao/user"
	"blind_box/app/dao/user/points"
	"blind_box/pkg/log"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PointsFIFOService 积分FIFO消费服务
type PointsFIFOService struct {
	pointsRepo points.PointsLogRepo
	userRepo   *userDao.Entry
}

// NewPointsFIFOService 创建积分FIFO服务实例
func NewPointsFIFOService(pointsRepo points.PointsLogRepo, userRepo *userDao.Entry) *PointsFIFOService {
	return &PointsFIFOService{
		pointsRepo: pointsRepo,
		userRepo:   userRepo,
	}
}

// clearUserCache 清理用户缓存，确保积分变动后缓存数据一致性
func (s *PointsFIFOService) clearUserCache(ctx *gin.Context, userID uint64) {
	err := s.userRepo.RedisClearUserInfo(ctx, userID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("clearUserCache: Failed to clear user cache, userID=%d", userID)
	} else {
		log.Ctx(ctx).Info("clearUserCache: Successfully cleared user cache, userID=%d", userID)
	}
}

// AddUserPointsWithExpireAndTx 增加用户积分（新版本，支持完整的日志系统）
func (s *PointsFIFOService) AddUserPointsWithExpireAndTx(ctx *gin.Context, tx *gorm.DB, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string, expireAt *time.Time) (*points.PointsLog, error) {
	if addPoints == 0 {
		return nil, nil
	}

	// 获取用户当前有效积分余额
	currentBalance, err := s.pointsRepo.GetUserValidPointsBalanceFromEarnLogs(ctx, userID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AddUserPointsWithExpireAndTx: Failed to get user valid points balance")
		return nil, err
	}

	// 记录操作前后余额
	balanceBefore := currentBalance
	balanceAfter := balanceBefore + addPoints

	// 防止积分溢出
	const MaxPoints = 999999999999 // 12位数字上限
	if balanceAfter > MaxPoints {
		log.Ctx(ctx).Warn("AddUserPointsWithExpireAndTx: points would overflow, capping at max value")
		balanceAfter = MaxPoints
		addPoints = MaxPoints - balanceBefore
	}

	// 创建积分增加日志（earn类型）
	pointsLog := &points.PointsLog{
		UserID:        userID,
		ActionType:    uint32(points.PointsActionTypeAdd),
		Points:        addPoints,
		BalanceBefore: balanceBefore,
		BalanceAfter:  balanceAfter,
		SourceType:    uint32(sourceType),
		SourceID:      sourceID,
		Remark:        remark,
		ExpireAt:      expireAt,
		IsExpired:     0,
	}

	// 设置初始剩余积分等于获得的积分数
	pointsLog.SetRemainingPoints(addPoints)

	err = s.pointsRepo.CreatePointsLogWithTx(ctx, tx, pointsLog)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AddUserPointsWithExpireAndTx: Failed to create points log")
		return nil, err
	}

	// 更新用户表中的积分余额
	err = s.updateUserPointsBalanceWithTx(ctx, tx, userID, balanceAfter)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AddUserPointsWithExpireAndTx: Failed to update user points balance")
		return nil, err
	}

	log.Ctx(ctx).Info("AddUserPointsWithExpireAndTx success: UserID=%d, AddPoints=%d, NewBalance=%d, SourceType=%d, ExpireAt=%v, Remark=%s",
		userID, addPoints, balanceAfter, sourceType, expireAt, remark)

	// 积分变动成功后，清理用户缓存
	s.clearUserCache(ctx, userID)

	return pointsLog, nil
}

// UsePointsWithFIFOAndTx 使用FIFO原则消费积分（新版本）
func (s *PointsFIFOService) UsePointsWithFIFOAndTx(ctx *gin.Context, tx *gorm.DB, userID, usePoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string) error {
	if usePoints == 0 {
		return nil
	}

	// 使用带锁的查询获取用户可用于FIFO消费的积分记录（防止并发问题）
	earnLogs, err := s.pointsRepo.GetUserEarnLogsForFIFOWithLock(ctx, tx, userID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UsePointsWithFIFOAndTx: Failed to get user earn logs for FIFO with lock")
		return err
	}

	// 从加锁的记录中计算当前有效余额
	var currentBalance uint64
	for _, earnLog := range earnLogs {
		currentBalance += earnLog.GetRemainingPoints()
	}

	// 检查积分余额
	if currentBalance < usePoints {
		return fmt.Errorf("用户积分余额不足，当前余额：%d，需要：%d（用户ID：%d）", currentBalance, usePoints, userID)
	}

	if len(earnLogs) == 0 {
		return fmt.Errorf("没有可用的积分记录进行消费")
	}

	// 执行FIFO消费
	remainingUsePoints := usePoints
	var offsetLogs []*points.PointsLog
	var updates []struct {
		LogID           uint64
		RemainingPoints uint64
	}

	for _, earnLog := range earnLogs {
		if remainingUsePoints == 0 {
			break
		}

		availablePoints := earnLog.GetRemainingPoints()
		if availablePoints == 0 {
			continue
		}

		// 计算本次消费的积分数
		consumePoints := remainingUsePoints
		if consumePoints > availablePoints {
			consumePoints = availablePoints
		}

		// 更新剩余积分
		newRemainingPoints := availablePoints - consumePoints
		updates = append(updates, struct {
			LogID           uint64
			RemainingPoints uint64
		}{
			LogID:           earnLog.ID,
			RemainingPoints: newRemainingPoints,
		})

		// 创建offset类型的日志记录
		offsetLog := &points.PointsLog{
			UserID:     userID,
			ActionType: uint32(points.PointsActionTypeOffset),
			Points:     consumePoints,
			SourceType: uint32(sourceType),
			SourceID:   sourceID,
			Remark:     fmt.Sprintf("FIFO消费积分，来源：%s，关联earn记录ID：%d", remark, earnLog.ID),
		}
		offsetLog.SetReferenceLogID(earnLog.ID)
		offsetLogs = append(offsetLogs, offsetLog)

		remainingUsePoints -= consumePoints

		log.Ctx(ctx).Info("FIFO consume: EarnLogID=%d, ConsumePoints=%d, NewRemaining=%d",
			earnLog.ID, consumePoints, newRemainingPoints)
	}

	if remainingUsePoints > 0 {
		return fmt.Errorf("积分消费失败，剩余需要消费：%d", remainingUsePoints)
	}

	// 批量更新剩余积分
	err = s.pointsRepo.BatchUpdateRemainingPointsWithTx(ctx, tx, updates)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UsePointsWithFIFOAndTx: Failed to batch update remaining points")
		return err
	}

	// 批量创建offset日志
	for _, offsetLog := range offsetLogs {
		err = s.pointsRepo.CreatePointsLogWithTx(ctx, tx, offsetLog)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("UsePointsWithFIFOAndTx: Failed to create offset log")
			return err
		}
	}

	// 创建总计spend类型日志（可选）
	spendLog := &points.PointsLog{
		UserID:        userID,
		ActionType:    uint32(points.PointsActionTypeSubtract),
		Points:        usePoints,
		BalanceBefore: currentBalance,
		BalanceAfter:  currentBalance - usePoints,
		SourceType:    uint32(sourceType),
		SourceID:      sourceID,
		Remark:        remark,
	}

	err = s.pointsRepo.CreatePointsLogWithTx(ctx, tx, spendLog)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UsePointsWithFIFOAndTx: Failed to create spend log")
		return err
	}

	// 更新用户表中的积分余额
	newBalance := currentBalance - usePoints
	err = s.updateUserPointsBalanceWithTx(ctx, tx, userID, newBalance)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UsePointsWithFIFOAndTx: Failed to update user points balance")
		return err
	}

	log.Ctx(ctx).Info("UsePointsWithFIFOAndTx success: UserID=%d, UsePoints=%d, NewBalance=%d, OffsetLogsCount=%d",
		userID, usePoints, newBalance, len(offsetLogs))

	// 积分变动成功后，清理用户缓存
	s.clearUserCache(ctx, userID)

	return nil
}

// ProcessExpiredPoints 处理过期积分
func (s *PointsFIFOService) ProcessExpiredPoints(ctx *gin.Context, limit int) error {
	// 获取已过期但未处理的积分记录
	expiredLogs, err := s.pointsRepo.GetExpiredPointsRecords(ctx, limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ProcessExpiredPoints: Failed to get expired points records")
		return err
	}

	if len(expiredLogs) == 0 {
		log.Ctx(ctx).Info("ProcessExpiredPoints: No expired points to process")
		return nil
	}

	log.Ctx(ctx).Info("ProcessExpiredPoints: Found %d expired points records to process", len(expiredLogs))

	// 按用户分组处理
	userLogMap := make(map[uint64][]*points.PointsLog)
	for _, expiredLog := range expiredLogs {
		userLogMap[expiredLog.UserID] = append(userLogMap[expiredLog.UserID], expiredLog)
	}

	// 逐个用户处理过期积分
	for userID, userExpiredLogs := range userLogMap {
		err := s.ProcessUserExpiredPointsWithTx(ctx, userID, userExpiredLogs)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("ProcessExpiredPoints: Failed to process user expired points, UserID=%d", userID)
			// 继续处理其他用户，不因单个用户失败而中断
			continue
		}
	}

	return nil
}

// ProcessUserExpiredPointsWithTx 处理单个用户的过期积分（事务版本）
func (s *PointsFIFOService) ProcessUserExpiredPointsWithTx(ctx *gin.Context, userID uint64, expiredLogs []*points.PointsLog) error {
	// 开启事务
	tx := s.pointsRepo.(*points.Entry).MysqlEngine.UseWithGinCtx(ctx, true)

	// 使用变量控制事务状态
	var committed bool
	defer func() {
		if r := recover(); r != nil {
			if !committed {
				tx.Rollback()
			}
			panic(r)
		}
		// 如果没有提交且没有panic，说明有错误返回，需要回滚
		if !committed {
			tx.Rollback()
		}
	}()

	var totalExpiredPoints uint64
	for _, expiredLog := range expiredLogs {
		remainingPoints := expiredLog.GetRemainingPoints()
		if remainingPoints == 0 {
			continue
		}

		// 标记为已过期
		err := s.pointsRepo.MarkPointsAsExpiredWithTx(ctx, tx, expiredLog.ID)
		if err != nil {
			return fmt.Errorf("failed to mark points as expired for log ID %d: %w", expiredLog.ID, err)
		}

		// 创建过期扣减日志
		expiredLogEntry := &points.PointsLog{
			UserID:     userID,
			ActionType: uint32(points.PointsActionTypeExpired),
			Points:     remainingPoints,
			SourceType: uint32(points.PointsSourceTypeExpire),
			Remark:     fmt.Sprintf("积分过期，原始记录ID：%d，过期时间：%s", expiredLog.ID, expiredLog.ExpireAt.Format("2006-01-02 15:04:05")),
		}
		expiredLogEntry.SetReferenceLogID(expiredLog.ID)

		err = s.pointsRepo.CreatePointsLogWithTx(ctx, tx, expiredLogEntry)
		if err != nil {
			return fmt.Errorf("failed to create expired log for user %d: %w", userID, err)
		}

		totalExpiredPoints += remainingPoints
		log.Ctx(ctx).Info("Expired points processed: UserID=%d, LogID=%d, ExpiredPoints=%d",
			userID, expiredLog.ID, remainingPoints)
	}

	// 更新用户积分余额
	if totalExpiredPoints > 0 {
		currentBalance, err := s.pointsRepo.GetUserValidPointsBalanceFromEarnLogs(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to get current balance for user %d: %w", userID, err)
		}

		newBalance := currentBalance
		if currentBalance >= totalExpiredPoints {
			newBalance = currentBalance - totalExpiredPoints
		} else {
			newBalance = 0
		}

		err = s.updateUserPointsBalanceWithTx(ctx, tx, userID, newBalance)
		if err != nil {
			return fmt.Errorf("failed to update user balance for user %d: %w", userID, err)
		}

		log.Ctx(ctx).Info("User points balance updated after expiration: UserID=%d, ExpiredPoints=%d, NewBalance=%d",
			userID, totalExpiredPoints, newBalance)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction for user %d: %w", userID, err)
	}

	// 标记已提交，避免defer中重复回滚
	committed = true

	// 积分过期处理成功后，清理用户缓存
	s.clearUserCache(ctx, userID)

	return nil
}

// updateUserPointsBalanceWithTx 更新用户表中的积分余额（事务版本）
func (s *PointsFIFOService) updateUserPointsBalanceWithTx(ctx *gin.Context, tx *gorm.DB, userID, newBalance uint64) error {
	result := tx.Model(&userDao.Model{}).
		Where("id = ?", userID).
		Update("points", newBalance)

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no user found with ID: %d", userID)
	}

	return nil
}

// GetUserValidPointsBalance 获取用户有效积分余额
func (s *PointsFIFOService) GetUserValidPointsBalance(ctx *gin.Context, userID uint64) (uint64, error) {
	return s.pointsRepo.GetUserValidPointsBalanceFromEarnLogs(ctx, userID)
}

// SyncUserPointsBalance 同步用户积分余额（从日志重新计算并更新到用户表）
func (s *PointsFIFOService) SyncUserPointsBalance(ctx *gin.Context, userID uint64) error {
	// 从日志计算真实余额
	realBalance, err := s.pointsRepo.GetUserValidPointsBalanceFromEarnLogs(ctx, userID)
	if err != nil {
		return err
	}

	// 更新用户表
	tx := s.pointsRepo.(*points.Entry).MysqlEngine.UseWithGinCtx(ctx, true)
	err = s.updateUserPointsBalanceWithTx(ctx, tx, userID, realBalance)
	if err != nil {
		tx.Rollback()
		return err
	}

	err = tx.Commit().Error
	if err != nil {
		return err
	}

	log.Ctx(ctx).Info("SyncUserPointsBalance success: UserID=%d, SyncedBalance=%d", userID, realBalance)

	// 积分同步成功后，清理用户缓存
	s.clearUserCache(ctx, userID)

	return nil
}
