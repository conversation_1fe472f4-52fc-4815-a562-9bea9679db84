package user

import (
	adminConfig "blind_box/app/dao/admin/config"
	userDao "blind_box/app/dao/user"
	"blind_box/app/dao/user/points"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/log"
	"blind_box/pkg/util/decimalUtil"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PointsDiscountRequest 积分抵扣请求
type PointsDiscountRequest struct {
	UserID      uint64 `json:"userId" binding:"required"`
	TotalAmount uint64 `json:"totalAmount" binding:"required"` // 订单总金额（分）
	UsePoints   uint64 `json:"usePoints"`                      // 用户想要使用的积分数
	MaxPercent  uint32 `json:"maxPercent,omitempty"`           // 最大抵扣比例（百分比，默认100%）
}

// PointsDiscountResponse 积分抵扣响应
type PointsDiscountResponse struct {
	CanUsePoints   uint64                          `json:"canUsePoints"`   // 实际可使用的积分数
	DiscountAmount uint64                          `json:"discountAmount"` // 抵扣金额（分）
	UserPoints     uint64                          `json:"userPoints"`     // 用户当前积分余额
	Rule           *adminConfig.PointsDiscountRule `json:"rule"`           // 积分抵扣规则
}

// 积分服务实例
func (e *Entry) getPointsFIFOService() *PointsFIFOService {
	return NewPointsFIFOService(points.GetRepo(), e.UserRepo)
}

// Redis优化版积分服务实例
func (e *Entry) getPointsRedisService() *PointsRedisService {
	return NewPointsRedisService(points.GetRepo(), e.UserRepo)
}

// 检查是否应该使用Redis优化（根据过期时间决定）
func (e *Entry) shouldUseRedisForExpiry(expireAt *time.Time) bool {
	// 如果有过期时间，使用Redis延迟调度优化
	return expireAt != nil
}

// GetPointsDiscountConfig 获取积分抵扣配置
func (e *Entry) GetPointsDiscountConfig(ctx *gin.Context) (*adminConfig.PointsDiscountRule, error) {
	// 注意：由于user服务的Entry没有AdminConfigRepo，我们需要直接使用repo
	// 或者在interface.go中添加AdminConfigRepo依赖

	// 暂时使用默认配置，后续可以通过添加AdminConfigRepo来实现动态配置
	return adminConfig.GetDefaultPointsDiscountRule(), nil
}

// CheckPointsDiscountEnable 检查积分抵扣功能是否启用
func (e *Entry) CheckPointsDiscountEnable(ctx *gin.Context) (bool, error) {
	// 暂时默认启用，后续可以通过配置表控制
	return true, nil
}

// CalculatePointsDiscount 计算积分抵扣
func (e *Entry) CalculatePointsDiscount(ctx *gin.Context, req *PointsDiscountRequest) (*PointsDiscountResponse, error) {
	// 检查积分抵扣功能是否启用
	enabled, err := e.CheckPointsDiscountEnable(ctx)
	if err != nil {
		return nil, err
	}
	if !enabled {
		return &PointsDiscountResponse{
			CanUsePoints:   0,
			DiscountAmount: 0,
		}, nil
	}

	// 获取积分抵扣规则
	rule, err := e.GetPointsDiscountConfig(ctx)
	if err != nil {
		return nil, err
	}

	if !rule.Enable {
		return &PointsDiscountResponse{
			CanUsePoints:   0,
			DiscountAmount: 0,
			Rule:           rule,
		}, nil
	}

	// 获取用户积分余额（使用新的FIFO服务）
	fifoService := e.getPointsFIFOService()
	userPoints, err := fifoService.GetUserValidPointsBalance(ctx, req.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to get user points balance")
		return nil, err
	}

	// 计算最大可抵扣金额
	maxDiscountAmount := req.TotalAmount
	if req.MaxPercent > 0 && req.MaxPercent < 100 {
		// 按百分比限制最大抵扣金额，使用安全计算避免溢出
		tempAmount, err := decimalUtil.SafeMultiply(req.TotalAmount, uint32(req.MaxPercent))
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("CalculatePointsDiscount: MaxPercent calculation overflow")
			return nil, fmt.Errorf("points discount calculation error: %w", err)
		}
		maxDiscountAmount = decimalUtil.Div(tempAmount, 100)
	}

	// 计算用户积分可以抵扣的最大金额
	maxPointsDiscount := adminConfig.CalculatePointsDiscount(userPoints, rule)

	// 取较小值作为实际可抵扣金额
	actualDiscountAmount := maxPointsDiscount
	if actualDiscountAmount > maxDiscountAmount {
		actualDiscountAmount = maxDiscountAmount
	}

	// 如果用户指定了使用积分数，计算对应的抵扣金额
	var canUsePoints uint64
	if req.UsePoints > 0 {
		// 用户指定积分数，计算对应抵扣金额
		userSpecifiedDiscount := adminConfig.CalculatePointsDiscount(req.UsePoints, rule)

		// 检查用户积分是否足够
		if req.UsePoints > userPoints {
			return nil, fmt.Errorf("用户积分余额不足，当前余额：%d，需要：%d", userPoints, req.UsePoints)
		}

		// 检查抵扣金额是否超过限制
		if userSpecifiedDiscount > maxDiscountAmount {
			// 按最大抵扣金额计算需要的积分数
			canUsePoints = adminConfig.CalculateRequiredPoints(maxDiscountAmount, rule)
			actualDiscountAmount = maxDiscountAmount
		} else {
			canUsePoints = req.UsePoints
			actualDiscountAmount = userSpecifiedDiscount
		}
	} else {
		// 自动计算最优积分使用数
		canUsePoints = adminConfig.CalculateRequiredPoints(actualDiscountAmount, rule)
	}

	// 确保不超过用户积分余额
	if canUsePoints > userPoints {
		canUsePoints = userPoints
		actualDiscountAmount = adminConfig.CalculatePointsDiscount(canUsePoints, rule)
	}

	log.Ctx(ctx).Info("CalculatePointsDiscount: UserID=%d, UserPoints=%d, UsePoints=%d, DiscountAmount=%d",
		req.UserID, userPoints, canUsePoints, actualDiscountAmount)

	return &PointsDiscountResponse{
		CanUsePoints:   canUsePoints,
		DiscountAmount: actualDiscountAmount,
		UserPoints:     userPoints,
		Rule:           rule,
	}, nil
}

// UsePointsDiscount 使用积分抵扣（使用新的FIFO逻辑）
func (e *Entry) UsePointsDiscount(ctx *gin.Context, userID, usePoints uint64, orderNo string) error {
	return e.UsePointsDiscountWithTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true), userID, usePoints, orderNo)
}

// UsePointsDiscountWithTx 使用事务扣减积分（使用新的FIFO逻辑）
func (e *Entry) UsePointsDiscountWithTx(ctx *gin.Context, tx *gorm.DB, userID, usePoints uint64, orderNo string) error {
	if usePoints == 0 {
		return nil
	}

	fifoService := e.getPointsFIFOService()
	return fifoService.UsePointsWithFIFOAndTx(ctx, tx, userID, usePoints, points.PointsSourceTypeDiscount, 0, fmt.Sprintf("积分抵扣，订单号：%s", orderNo))
}

// RefundPointsDiscount 退回积分抵扣（增加用户积分）
func (e *Entry) RefundPointsDiscount(ctx *gin.Context, userID, refundPoints uint64, orderNo string) error {
	return e.RefundPointsDiscountWithTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true), userID, refundPoints, orderNo)
}

// RefundPointsDiscountWithTx 使用事务退回积分（使用新的FIFO逻辑）
func (e *Entry) RefundPointsDiscountWithTx(ctx *gin.Context, tx *gorm.DB, userID, refundPoints uint64, orderNo string) error {
	if refundPoints == 0 {
		return nil
	}

	// 退回的积分设置为永不过期，或者可以设置为原来的过期时间
	// 这里我们设置为永不过期
	fifoService := e.getPointsFIFOService()
	_, err := fifoService.AddUserPointsWithExpireAndTx(ctx, tx, userID, refundPoints, points.PointsSourceTypeRefund, 0, fmt.Sprintf("积分抵扣退回，订单号：%s", orderNo), nil)
	return err
}

// AddUserPoints 增加用户积分（通用方法，使用新的FIFO逻辑）
func (e *Entry) AddUserPoints(ctx *gin.Context, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string) error {
	return e.AddUserPointsWithTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true), userID, addPoints, sourceType, sourceID, remark)
}

// AddUserPointsWithExpire 增加用户积分（带过期时间，使用新的FIFO逻辑）
func (e *Entry) AddUserPointsWithExpire(ctx *gin.Context, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string, expireAt *time.Time) error {
	return e.AddUserPointsWithExpireAndTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true), userID, addPoints, sourceType, sourceID, remark, expireAt)
}

// AddUserPointsWithTx 使用事务增加用户积分（使用新的FIFO逻辑）
func (e *Entry) AddUserPointsWithTx(ctx *gin.Context, tx *gorm.DB, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string) error {
	return e.AddUserPointsWithExpireAndTx(ctx, tx, userID, addPoints, sourceType, sourceID, remark, nil)
}

// AddUserPointsWithExpireAndTx 使用事务增加用户积分（带过期时间，自动选择最优实现）
func (e *Entry) AddUserPointsWithExpireAndTx(ctx *gin.Context, tx *gorm.DB, userID, addPoints uint64, sourceType points.PointsSourceType, sourceID uint64, remark string, expireAt *time.Time) error {
	if e.shouldUseRedisForExpiry(expireAt) {
		// 有过期时间时使用Redis优化版本
		redisService := e.getPointsRedisService()
		_, err := redisService.AddUserPointsWithExpireAndRedis(ctx, tx, userID, addPoints, sourceType, sourceID, remark, expireAt)
		log.Ctx(ctx).Info("AddUserPointsWithExpireAndTx: Used Redis version for expiry optimization, UserID=%d, ExpireAt=%v", userID, expireAt)
		return err
	} else {
		// 永不过期时使用传统FIFO版本
		fifoService := e.getPointsFIFOService()
		_, err := fifoService.AddUserPointsWithExpireAndTx(ctx, tx, userID, addPoints, sourceType, sourceID, remark, expireAt)
		log.Ctx(ctx).Info("AddUserPointsWithExpireAndTx: Used FIFO version for non-expiry points, UserID=%d", userID)
		return err
	}
}

// GetUserPointsBalance 获取用户积分余额（使用新的FIFO逻辑）
func (e *Entry) GetUserPointsBalance(ctx *gin.Context, userID uint64) (uint64, error) {
	fifoService := e.getPointsFIFOService()
	return fifoService.GetUserValidPointsBalance(ctx, userID)
}

// AddOrderPointsReward 订单完成积分奖励（1元=1积分，过期时间为一年后，使用新的FIFO逻辑）
func (e *Entry) AddOrderPointsReward(ctx *gin.Context, userID uint64, orderAmount uint64, orderID uint64, orderNo string) error {
	return e.AddOrderPointsRewardWithTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true), userID, orderAmount, orderID, orderNo)
}

// AddOrderPointsRewardWithTx 使用事务添加订单积分奖励（使用新的FIFO逻辑）
func (e *Entry) AddOrderPointsRewardWithTx(ctx *gin.Context, tx *gorm.DB, userID uint64, orderAmount uint64, orderID uint64, orderNo string) error {
	// 计算积分数：1元=1积分，订单金额以分为单位，所以除以100
	pointsToAdd := orderAmount / 100
	if pointsToAdd == 0 {
		log.Ctx(ctx).Info("AddOrderPointsReward: order amount too small, no points to add. UserID=%d, OrderAmount=%d", userID, orderAmount)
		return nil
	}

	// 设置过期时间为一年后
	expireAt := time.Now().AddDate(1, 0, 0) // 一年后过期

	remark := fmt.Sprintf("订单积分奖励，订单号：%s，订单金额：%.2f元", orderNo, float64(orderAmount)/100)

	// 使用统一的积分发放方法（自动选择Redis或FIFO版本）
	return e.AddUserPointsWithExpireAndTx(ctx, tx, userID, pointsToAdd, points.PointsSourceTypeOrder, orderID, remark, &expireAt)
}

// ProcessExpiredPoints 处理过期积分（新增方法）
func (e *Entry) ProcessExpiredPoints(ctx *gin.Context, limit int) error {
	// 优先使用Redis版本处理过期积分
	redisService := e.getPointsRedisService()
	err := redisService.ProcessExpiredPointsWithRedis(ctx, int64(limit))
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("ProcessExpiredPoints: Redis version failed, falling back to FIFO version")

		// 降级到FIFO版本
		fifoService := e.getPointsFIFOService()
		return fifoService.ProcessExpiredPoints(ctx, limit)
	}

	log.Ctx(ctx).Info("ProcessExpiredPoints: Successfully used Redis version")
	return nil
}

// SyncUserPointsBalance 同步用户积分余额（新增方法）
func (e *Entry) SyncUserPointsBalance(ctx *gin.Context, userID uint64) error {
	fifoService := e.getPointsFIFOService()
	return fifoService.SyncUserPointsBalance(ctx, userID)
}

// GetUserPointsBill 用户积分明细账单(近一年)
func (e *Entry) GetUserPointsBill(ctx *gin.Context, uid uint64, page, limit int) (*userDto.UserPointsBillResp, error) {
	// 近一年的时间戳
	oneYearAgo := time.Now().AddDate(-1, 0, 0).Unix()

	total, billList, err := points.GetRepo().DataPageList(ctx, &points.PointsLogFilter{
		UserID:    uid,
		StartTime: oneYearAgo,
	}, page, limit)
	if err != nil {
		return nil, err
	}
	return e.dealPointsBill(ctx, billList, total)
}

// dealPointsBill 处理积分明细数据
func (e *Entry) dealPointsBill(ctx *gin.Context, dataList points.PointsLogList, total int64) (*userDto.UserPointsBillResp, error) {
	userList, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: dataList.GetUserIDs()})
	if err != nil {
		return nil, err
	}
	userMap := userList.GetIDMap()

	respList := []*userDto.UserPointsBillItem{}
	for _, info := range dataList {
		item := &userDto.UserPointsBillItem{
			ID:          info.ID,
			UserID:      info.UserID,
			ActionType:  info.ActionType,
			Points:      info.Points,
			SourceType:  info.SourceType,
			Remark:      info.Remark,
			CreatedTime: info.GetCreatedTime(),
		}

		// 设置来源名称
		if name, ok := points.PointsSourceMap[info.SourceType]; ok {
			item.SourceName = name
		}

		// 设置用户名称
		if v, ok := userMap[info.UserID]; ok {
			item.UserName = v.Nickname
		}

		respList = append(respList, item)
	}
	return &userDto.UserPointsBillResp{
		List:  respList,
		Total: total,
	}, nil
}
