package user

import (
	"blind_box/app/common/dbs"
	tideDao "blind_box/app/dao/user/tide"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

// AdminTideBill 后台潮气值明细列表
func (e *Entry) AdminTideBill(ctx *gin.Context, req userDto.AdminTideBillReq) (*userDto.UserTideBillResp, error) {
	total, billList, err := e.TideRepo.DataPageList(ctx, &tideDao.LogFilter{
		UserId:    req.UserId,
		Source:    req.Source,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	return e.dealTideBill(ctx, billList, total)
}

// AdminSendTide 管理员发放or扣除潮气值
func (e *Entry) AdminSendTide(ctx *gin.Context, req userDto.AdminSendTideReq) error {
	uInfo, err := e.getUserInfo(ctx, req.UserId)
	if err != nil {
		return err
	}

	var (
		tideVal     = uint32(req.TideVal)
		tideValType = tideDao.TideValTypeAdd
	)
	if req.TideVal < 0 {
		tideVal = uint32(-req.TideVal)
		tideValType = tideDao.TideValTypeSubtract
	}
	var (
		realTideVal = e.TideRepo.GetRealTideVal(int32(tideVal), int32(uInfo.TideVal), tideValType)
		tideLog     = &tideDao.TideLog{
			UserId:      uInfo.ID,
			TideValType: uint32(tideValType),
			TideVal:     realTideVal,
			Source:      uint32(tideDao.TideSourceTypeSystem),
			Remark:      req.Remark,
		}
		userTideVal = uInfo.TideVal + realTideVal
	)
	if realTideVal <= 0 {
		return nil
	}
	if tideValType == tideDao.TideValTypeSubtract {
		userTideVal = uInfo.TideVal - realTideVal
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.TideRepo.CreateTideLogWithTx(tx, tideLog); err != nil {
			return
		}

		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, tideLog.UserId, map[string]interface{}{
			"tide_val": userTideVal,
		}); err != nil {
			return
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AdminSendTide err")
		return err
	}

	// 管理员发放潮气值成功后，清理用户缓存
	if err := e.UserRepo.RedisClearUserInfo(ctx, req.UserId); err != nil {
		log.Ctx(ctx).WithError(err).Warn("AdminSendTide: Failed to clear user cache after tide send, userID=%d", req.UserId)
	} else {
		log.Ctx(ctx).Info("AdminSendTide: Successfully cleared user cache after tide send, userID=%d", req.UserId)
	}

	return nil
}
