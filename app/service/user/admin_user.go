package user

import (
	"blind_box/app/common/dbs"
	userDao "blind_box/app/dao/user"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
)

// getUserInfo .
func (e *Entry) getUserInfo(ctx *gin.Context, UID uint64) (*userDao.Model, error) {
	uInfo, err := e.UserRepo.RedisUserInfo(ctx, UID)
	if err != nil {
		return nil, err
	}
	if uInfo.ID == 0 {
		return nil, ecode.ParamErr
	}

	return uInfo, nil
}

// AdminUserList .
func (e *Entry) AdminUserList(ctx *gin.Context, req userDto.AdminUserListReq) (*userDto.AdminUserListResp, error) {
	total, list, err := e.UserRepo.DataPageList(ctx, &userDao.Filter{
		ID:       req.ID,
		Nickname: req.Nickname,
		Mobile:   req.Mobile,
		Email:    req.Email,
		Status:   req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*userDto.AdminUserListItem, 0, len(list))
	for _, val := range list {
		item := val.ToUserInfoResp(ctx)
		retList = append(retList, item)
	}
	return &userDto.AdminUserListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminUserInfo .
func (e *Entry) AdminUserInfo(ctx *gin.Context, UID uint64) (*userDto.AdminUserListItem, error) {
	var (
		uInfo = &userDao.Model{}
		err   error
	)

	if uInfo, err = e.getUserInfo(ctx, UID); err != nil {
		return nil, err
	}

	resp := uInfo.ToUserInfoResp(ctx)
	levelInfo, err := e.GerUserLeveInfo(ctx, uInfo.TideVal)
	if err == nil && levelInfo.ID != 0 {
		resp.LevelInfo = &userDto.AdminLevelListItem{
			ID:   levelInfo.ID,
			Name: levelInfo.Name,
			Icon: levelInfo.Icon,
			Min:  levelInfo.Min,
			Max:  levelInfo.Max,
		}
	}

	return resp, nil
}

// AdminOperateUser .
func (e *Entry) AdminOperateUser(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		if err := e.UserRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]}); err != nil {
			return err
		}
		// 用户状态变更后，清理用户缓存
		return e.UserRepo.RedisClearUserInfo(ctx, id)
		// case dbs.ActionSort:
		// 	return e.UserRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}
