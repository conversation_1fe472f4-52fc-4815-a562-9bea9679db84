package user

import (
	"blind_box/app/common/dbs"
	skuDao "blind_box/app/dao/goods/sku"
	shopDao "blind_box/app/dao/resource/shop"
	userCart "blind_box/app/dao/user/cart"
	"blind_box/app/dto/goods"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

func (e *Entry) CheckCartAuth(ctx *gin.Context, uid, cartId uint64) (*userCart.Model, error) {
	cartInfo, err := e.CartRepo.FetchByID(ctx, cartId)
	if err != nil {
		return nil, err
	}
	if cartInfo.ID == 0 {
		return nil, ecode.ParamErr
	}
	if cartInfo.UserId != uid {
		return nil, ecode.NoAuthErr
	}

	return cartInfo, nil
}

// UserCartList .
func (e *Entry) UserCartList(ctx *gin.Context, uid uint64, req userDto.UserCartListReq, page, limit int) (*userDto.UserCartListResp, error) {
	total, list, err := e.CartRepo.DataPageList(ctx, &userCart.Filter{
		UserId:   uid,
		SellType: req.SellType,
		SpuTitle: req.SpuTitle,
	}, page, limit)
	if err != nil {
		return nil, err
	}
	var (
		eg             errgroup.Group
		retList        = []*userDto.UserCartListSkuItem{}
		spuIds, skuIds = list.GetSpuSkuIds()
		skuMap         = map[uint64]goods.CrSkuItem{}
		shopMap        = map[uint64]*shopDao.Model{}
	)
	if len(list) == dbs.False {
		return &userDto.UserCartListResp{Total: total, List: retList}, nil
	}

	eg.Go(func() (err error) {
		if skuMap, err = e.CommonSrv.GetCommonSkuResp(ctx, spuIds, skuIds); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if shopMap, err = e.ShopRepo.RedisShopMap(ctx); err != nil {
			return
		}
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuList err")
		return nil, err
	}

	for _, val := range list {
		item := &userDto.UserCartListSkuItem{
			CartId:     val.ID,
			UserId:     val.UserId,
			SpuId:      val.SpuId,
			SkuId:      val.SkuId,
			UnitNum:    val.UnitNum,
			DeliveryId: val.DeliveryId,
			ShopId:     val.ShopId,
			CreatedAt:  val.GetCreatedTime(),
		}
		if sku, ok := skuMap[val.SkuId]; ok {
			item.SkuInfo = sku
		}
		if shop, ok := shopMap[val.ShopId]; ok {
			item.ShopName = shop.Name
		}
		retList = append(retList, item)
	}
	return &userDto.UserCartListResp{Total: total, List: retList}, nil
}

// UserCartAdd .
func (e *Entry) UserCartAdd(ctx *gin.Context, uid uint64, req userDto.UserCartAddReq) error {
	cartSku, err := e.CartRepo.FeatchByFilterSort(ctx, &userCart.Filter{
		UserId:     uid,
		SkuId:      req.SkuId,
		DeliveryId: req.DeliveryId,
	})
	if err != nil {
		return err
	}
	sku, err := e.CommonSrv.CheckSkuExist(ctx, req.SkuId)
	if err != nil {
		return err
	}
	// TODO 校验sku 是否绑定 店铺
	if cartSku.ID != 0 {
		cartSku.UnitNum += req.UnitNum
		cartSku.ShopId = req.ShopId
	} else {
		cartSku = &userCart.Model{
			UserId:     uid,
			SpuId:      sku.SpuId,
			SkuId:      sku.ID,
			UnitNum:    req.UnitNum,
			DeliveryId: req.DeliveryId,
			ShopId:     req.ShopId,
		}
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.CartRepo.CreateOrUpdate(ctx, cartSku); err != nil {
			return
		}
		// 如果配送方式为店铺，则更新店铺id
		if req.DeliveryId == uint32(skuDao.DeliveryTypeShop) {
			if err = e.CartRepo.UpdateMapByFilterWithTx(tx, uid, &userCart.Filter{
				DeliveryId: req.DeliveryId,
			}, map[string]interface{}{
				"shop_id": req.ShopId,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("UserCartAdd err")
		return err
	}

	return nil
}

// UserCartEdit .
func (e *Entry) UserCartEdit(ctx *gin.Context, uid uint64, req userDto.UserCartEditReq) error {
	if _, err := e.CheckCartAuth(ctx, uid, req.CartId); err != nil {
		return err
	}
	dataMap := map[string]interface{}{
		"unit_num": req.UnitNum,
	}
	if req.DeliveryId != 0 {
		dataMap["delivery_id"] = req.DeliveryId
	}
	if req.ShopId != 0 {
		dataMap["shop_id"] = req.ShopId
	}
	if err := e.CartRepo.UpdateMapById(ctx, req.CartId, dataMap); err != nil {
		return err
	}

	return nil
}

// UserCartDel .
func (e *Entry) UserCartDel(ctx *gin.Context, uid uint64, cartIds []uint64) error {
	return e.CartRepo.DelByFilter(ctx, &userCart.Filter{UserId: uid, Ids: cartIds})
}

// UserCartCount .
func (e *Entry) UserCartCount(ctx *gin.Context, uid uint64, req userDto.UserCartListReq) (*userDto.UserCartCountResp, error) {
	var (
		eg                    errgroup.Group
		cartNum, CartGoodsNum int64
		filter                = &userCart.Filter{
			UserId:   uid,
			SellType: req.SellType,
			SpuTitle: req.SpuTitle,
		}
	)

	// 此处统计购物车内商品数
	eg.Go(func() (err error) {
		if cartNum, err = e.CartRepo.CountByFilter(ctx, filter); err != nil {
			return
		}
		return
	})

	eg.Go(func() (err error) {
		if CartGoodsNum, err = e.CartRepo.SumByFilter(ctx, filter); err != nil {
			return
		}
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("UserCartCount eg.Wait() err")
		return nil, err
	}

	return &userDto.UserCartCountResp{
		CartNum:      cartNum,
		CartGoodsNum: CartGoodsNum,
	}, nil
}
