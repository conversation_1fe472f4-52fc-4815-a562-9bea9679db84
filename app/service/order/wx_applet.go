package order

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/common"
	callbackLog "blind_box/app/dao/wechat/callback_log"
	boxDto "blind_box/app/dto/box"
	boxSrv "blind_box/app/service/box"
	"blind_box/config"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"
)

func (e *Entry) WxAppletPayNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error {
	log.Ctx(ctx).With<PERSON>ield("WxAppletPayNotify", notifyReq).Info("WxAppletPayNotify")

	resp, err := notifyReq.DecryptPayCipherText(config.WechatCfg.ApiV3Key)
	if err != nil {
		log.Ctx(ctx).With<PERSON>ield("notifyReq", notifyReq).WithError(err).Error("DecryptPayCipherText error")
		return err
	}

	log.Ctx(ctx).<PERSON><PERSON>ield("WxAppletPayNotifyResp", resp).Info("WxAppletPayNotifyResp")

	var (
		notityData, _ = json.Marshal(notifyReq)
		respData, _   = json.Marshal(resp)

		callbackLog = &callbackLog.Model{
			NotifyID:        notifyReq.Id,
			ReqID:           helper.GetGinRequestID(ctx),
			EntityType:      uint64(common.ActionPay),
			WxEntityID:      resp.TransactionId,
			OutEntityNo:     resp.OutTradeNo,
			NotifyStatus:    resp.TradeState,
			NotifyData:      string(notityData),
			ResourceDecrypt: string(respData),
			Status:          dbs.True,
		}
		payLogUpdate = map[string]interface{}{
			"wx_entity_id": resp.TransactionId,
		}
	)

	tx := dbs.NewMysqlEngines().Use(true).Begin()
	if err := func() (err error) {
		if notifyReq.EventType == "TRANSACTION.SUCCESS" {
			req := &boxDto.BoxWechatNotifyReq{
				OutTradeNo:  resp.OutTradeNo,
				TotalAmount: uint64(resp.Amount.PayerTotal), // 用户实际支付
				TradeNo:     resp.TransactionId,
			}

			// TODO: 判断是否是盲盒支付
			_, err = boxSrv.GetService().BoxWechatNotify(ctx, req)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("BoxWechatNotify error")
				return err
			}

			payLogUpdate["status"] = dbs.StatusEnable
		} else {
			payLogUpdate["status"] = dbs.StatusDisable
		}

		if err = e.WxCallbackLogRepo.CreateWithTx(ctx, tx, callbackLog); err != nil {
			return
		}
		if err = e.WxPayLogRepo.UpdateMapByUdxWithTx(ctx, tx, resp.OutTradeNo, payLogUpdate); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("WxAppletPayNotify err")
		return err
	}

	return nil
}

func (e *Entry) WxAppletRefundNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error {
	resp, err := notifyReq.DecryptRefundCipherText(config.WechatCfg.ApiV3Key)
	if err != nil {
		log.Ctx(ctx).WithField("notifyReq", notifyReq).WithError(err).Error("DecryptRefundCipherText error")
		return err
	}

	var (
		notityData, _ = json.Marshal(notifyReq)
		respData, _   = json.Marshal(resp)

		callbackLog = &callbackLog.Model{
			NotifyID:        notifyReq.Id,
			ReqID:           helper.GetGinRequestID(ctx),
			EntityType:      uint64(common.ActionRefund),
			WxEntityID:      resp.RefundId,
			OutEntityNo:     resp.OutTradeNo,
			NotifyStatus:    resp.RefundStatus,
			NotifyData:      string(notityData),
			ResourceDecrypt: string(respData),
			Status:          dbs.True,
		}
		payLogUpdate = map[string]interface{}{
			"wx_entity_id": resp.RefundId,
		}
	)

	tx := dbs.NewMysqlEngines().Use(true).Begin()
	if err := func() (err error) {
		if notifyReq.EventType == "REFUND.SUCCESS" {
			// TODO: refund

			payLogUpdate["status"] = dbs.StatusEnable
		} else {
			payLogUpdate["status"] = dbs.StatusDisable
		}

		if err = e.WxCallbackLogRepo.CreateWithTx(ctx, tx, callbackLog); err != nil {
			return
		}
		if err = e.WxPayLogRepo.UpdateMapByUdxWithTx(ctx, tx, resp.OutTradeNo, payLogUpdate); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("WxAppletRefundNotify err")
		return err
	}

	return nil
}
