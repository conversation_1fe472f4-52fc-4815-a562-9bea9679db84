package order

import (
	"errors"
	"strconv"
	"time"

	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/admin/account"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	batchDao "blind_box/app/dao/goods/batch"
	deliveryDao "blind_box/app/dao/order/delivery"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	shopDao "blind_box/app/dao/resource/shop"
	userDao "blind_box/app/dao/user"
	addrDao "blind_box/app/dao/user/addr"
	commonDto "blind_box/app/dto/common"
	goodsDto "blind_box/app/dto/goods"
	orderDto "blind_box/app/dto/order"
	"blind_box/app/service/common"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// AdminOrderList 管理后台订单列表 - 复用用户端的详细逻辑，优化批量查询性能
func (e *Entry) AdminOrderList(ctx *gin.Context, req *orderDto.AdminOrderListReq) (*orderDto.AdminOrderListRes, error) {
	// 构建订单查询条件
	orderFilter := &orderDao.Filter{}

	if req.OrderID > 0 {
		orderFilter.ID = req.OrderID
	}
	if req.OutTradeNo != "" {
		orderFilter.OutTradeNo = req.OutTradeNo
	}
	if req.UserID > 0 {
		orderFilter.UserID = req.UserID
	}
	if req.OrderType > 0 {
		orderFilter.OrderType = req.OrderType
	}
	if len(req.OrderStatusList) > 0 {
		orderFilter.OrderStatusList = req.OrderStatusList
	}
	if req.CreatedAtStart > 0 {
		orderFilter.CreatedAtStart = req.CreatedAtStart
	}
	if req.CreatedAtEnd > 0 {
		orderFilter.CreatedAtEnd = req.CreatedAtEnd
	}

	// 分页参数
	page := req.Page
	if page <= 0 {
		page = dbs.DefaultPage
	}
	limit := req.Limit
	if limit <= 0 {
		limit = dbs.DefaultLimit
	}

	// 查询订单列表
	total, orderList, err := e.OrderRepo.DataPageList(ctx, orderFilter, page, limit)
	if err != nil {
		return nil, err
	}

	// 构建响应数据
	res := &orderDto.AdminOrderListRes{
		List:  make([]*orderDto.AdminOrderListItem, 0, len(orderList)),
		Count: total,
	}

	if len(orderList) == 0 {
		return res, nil
	}

	// 使用批量查询优化性能 - 参考OrderList的实现
	orderDetailResList, err := e.batchGetOrderDetails(ctx, orderList)
	if err != nil {
		return nil, err
	}

	// 批量获取用户信息
	userIDs := orderList.GetUserIDs()
	userMap, err := e.getUserMap(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	// 构建结果列表
	for i, orderDetailRes := range orderDetailResList {
		order := orderList[i]

		// 构建管理后台订单列表项
		item := &orderDto.AdminOrderListItem{
			OrderDetailRes: orderDetailRes,
			UserName:       "",
		}

		// 设置用户昵称
		if user, ok := userMap[order.UserID]; ok {
			item.UserName = user.Nickname
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

// getUserMap 获取用户信息映射
func (e *Entry) getUserMap(ctx *gin.Context, userIDs []uint64) (map[uint64]*userDao.Model, error) {
	if len(userIDs) == 0 {
		return make(map[uint64]*userDao.Model), nil
	}

	userList, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{
		IDS: userIDs,
	})
	if err != nil {
		return nil, err
	}

	userMap := make(map[uint64]*userDao.Model, len(userList))
	for _, user := range userList {
		userMap[user.ID] = user
	}

	return userMap, nil
}

// batchGetOrderDetails 批量获取订单详情 - 复用OrderList的批量查询逻辑避免N+1问题
func (e *Entry) batchGetOrderDetails(ctx *gin.Context, orderList orderDao.ModelList) ([]*orderDto.OrderDetailRes, error) {
	if len(orderList) == 0 {
		return make([]*orderDto.OrderDetailRes, 0), nil
	}

	// 批量查询相关数据以避免 N+1 问题 - 参考OrderList实现
	orderIDs := orderList.GetIDs()
	activeIDs := orderList.GetActiveIDs()
	shopIDs := orderList.GetShopIDs()
	addressIDs := orderList.GetAddressIDs()

	// 批量查询交易详情
	tradeList, err := e.OrderDetailRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderIDs: orderIDs,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter trade error")
		return nil, err
	}

	// 批量查询活动信息
	var activeMap = make(map[uint64]*actDao.Model)
	if len(activeIDs) > 0 {
		activeList, err := e.ActivityRepo.FindByFilter(ctx, &actDao.Filter{
			Ids: activeIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter activity error")
			return nil, err
		}
		activeMap = activeList.GetIDMap()
	}

	// 批量查询盒子活动配置（只有盲盒订单需要）
	var boxActiveMap = make(map[uint64]*box_active_config.Model)
	blindBoxActiveIDs := make([]uint64, 0)
	for _, order := range orderList {
		if order.OrderType == uint32(orderDao.OrderTypeBox) {
			blindBoxActiveIDs = append(blindBoxActiveIDs, order.ActiveID)
		}
	}
	if len(blindBoxActiveIDs) > 0 {
		blindBoxActiveIDs = lo.Uniq(blindBoxActiveIDs)
		for _, activeID := range blindBoxActiveIDs {
			boxActive, err := e.BoxActiveRepo.FetchByActiveID(ctx, activeID)
			if err == nil && boxActive != nil {
				boxActiveMap[activeID] = boxActive
			}
		}
	}

	// 收集商品和SKU信息
	goodsIDs := tradeList.GetGoodsIDs()
	skuIDs := tradeList.GetSkuIDs()
	spuIDs := tradeList.GetSpuIDs()
	batchIDs := tradeList.GetBatchIDs()

	var shopMap = make(map[uint64]*shopDao.Model)
	if len(shopIDs) > 0 {
		// 批量查询店铺信息
		shopList, err := e.ShopRepo.FindByFilter(ctx, &shopDao.Filter{
			IDS: shopIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter shop error")
			return nil, err
		}
		shopMap = shopList.GetIDMap()
	}

	var addressMap = make(map[uint64]*addrDao.Model)
	if len(addressIDs) > 0 {
		// 批量查询地址信息
		addressList, err := e.AddrRepo.FindByFilter(ctx, &addrDao.Filter{
			IDS: addressIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter address error")
			return nil, err
		}
		addressMap = addressList.GetIDMap()
	}

	// 批量查询盲盒商品信息
	var goodsMap = make(map[uint64]*box_goods.Model)
	if len(goodsIDs) > 0 {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			IDs: goodsIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter goods error")
			return nil, err
		}

		// 合并 SPU/SKU 列表（盲盒模式需要）
		spuIDs = append(spuIDs, goodsList.GetSpuIDs()...)
		skuIDs = append(skuIDs, goodsList.GetSkuIDs()...)
		goodsMap = goodsList.GetIDMap()
	}

	// 批量查询SKU信息
	var skuMap = make(map[uint64]goodsDto.CrSkuItem)
	if len(skuIDs) > 0 {
		skuIDs = lo.Uniq(skuIDs)
		spuIDs = lo.Uniq(spuIDs)
		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails GetCommonSkuResp error")
			return nil, err
		}
	}

	// 批量查询发货信息
	var deliveryMap = make(map[uint64]*deliveryDao.Model)
	if len(orderIDs) > 0 {
		deliveryFilter := &deliveryDao.Filter{OrderIDs: orderIDs}
		deliveryList, err := e.DeliveryRepo.FindByFilter(ctx, deliveryFilter)
		if err == nil {
			for _, delivery := range deliveryList {
				deliveryMap[delivery.OrderID] = delivery
			}
		}
	}

	var batchMap = make(map[uint64]*batchDao.Model)
	if len(batchIDs) > 0 {
		batchList, err := e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{
			Ids: batchIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails FindByFilter batch error")
			return nil, err
		}
		batchMap = batchList.GetIDMap()
	}

	// 组织交易详情数据按订单ID分组
	tradeOrderMap := tradeList.GetOrderMap()

	// 构建返回结果
	orderDetailResList := make([]*orderDto.OrderDetailRes, 0, len(orderList))
	for _, order := range orderList {
		// 构建基本订单信息
		isDirect := order.OrderType == uint32(orderDao.OrderTypeDirect)

		var actModel *actDao.Model
		if activeModel, ok := activeMap[order.ActiveID]; ok {
			actModel = activeModel
		} else {
			actModel = &actDao.Model{}
		}

		orderRes := &orderDto.OrderInfo{
			ActiveId:       order.ActiveID,
			ActiveImage:    actModel.Image,
			ActiveType:     actModel.ActType,
			ActiveName:     actModel.Title,
			LotteryUsedNum: order.LotteryTotal - order.LotteryLeft,
			BagId:          order.BoxID,
			OrderId:        order.ID,               // 订单ID
			OrderNo:        order.OutTradeNo,       // 订单号
			OrderType:      order.OrderType,        // 订单类型
			OrderStatus:    order.OrderStatus,      // 订单状态
			OrderSubType:   0,                      // 订单子类型，暂时设为0
			CreatedAt:      order.CreatedAt.Unix(), // 创建时间
			PayAt:          int64(order.PayTime),   // 支付时间，直接使用时间戳
		}

		// 使用公共方法计算费用信息
		orderRes.UsedFee, orderRes.TotalFee, err = e.calculateOrderFee(ctx, order)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("batchGetOrderDetails fee calculation error")
			continue
		}

		// 构建商品信息
		goodsInfo := make([]*orderDto.TradeGood, 0)
		if trades, ok := tradeOrderMap[order.ID]; ok {
			for _, v := range trades {
				tmp := &orderDto.TradeGood{
					Id:       v.ID,
					GoodId:   v.GoodsID,
					GoodName: v.GoodsName,
					Remark:   v.Remark,
					Quantity: v.Quantity, // 商品购买数量
				}

				if isDirect {
					// 直接购买模式使用 SKU 信息
					if skuItem, ok := skuMap[v.SkuID]; ok {
						tmp.GoodId = skuItem.SkuID
						tmp.GoodName = skuItem.SkuTitle
						tmp.GoodImage = skuItem.SkuCover
						tmp.GoodStatus = skuItem.BatchSellType
						tmp.GoodDetail = &skuItem
					}
				} else {
					// 盲盒模式
					if goods, ok := goodsMap[v.GoodsID]; ok && v.GoodsID != 0 {
						tmp.GoodLevel = v.GoodsLevel
						tmp.GoodImage = goods.GoodsCover

						if skuItem, ok := skuMap[goods.SkuID]; ok {
							tmp.GoodStatus = skuItem.BatchSellType
							tmp.GoodDetail = &skuItem
						}

						if t, err := strconv.Atoi(v.BoxSlot); err == nil {
							tmp.BoxSlot = uint32(t)
						}
						tmp.BoxId = v.BoxID
						tmp.BoxNo = v.BoxNo
					}
				}

				if batch, ok := batchMap[v.BatchID]; ok && v.BatchID != 0 {
					tmp.BatchInfo = &goodsDto.AdminBatchListItem{
						ID:        batch.ID,
						Title:     batch.Title,
						SellType:  batch.SellType,
						Desc:      batch.Desc,
						Status:    batch.Status,
						CreatedAt: batch.CreatedAt.Format(dbs.TimeDateFormatFull),
					}
				}

				goodsInfo = append(goodsInfo, tmp)
			}
		}

		// 构建配送信息
		orderDelivery := &orderDto.OrderDelivery{
			Delivery: &goodsDto.CrSkuDeliveryItem{
				DeliveryID: order.DeliveryID,
			},
		}
		if order.DeliveryID == orderDao.DeliveryTypeExpress {
			if address, ok := addressMap[order.AddressID]; ok && order.AddressID != 0 {
				orderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
					DeliveryID: order.DeliveryID,
				}
				orderDelivery.Address = &commonDto.CommonUserAddrInfo{
					ID:        address.ID,
					UserID:    address.UserID,
					Consignee: address.Consignee,
					Mobile:    address.Mobile,
					Area:      address.Area,
					Address:   address.Address,
					IsDefault: address.IsDefault,
					CreatedAt: address.CreatedAt.Format(dbs.TimeDateFormatFull),
				}
			}

			// 查询发货信息填充快递单号和快递公司
			if delivery, ok := deliveryMap[order.ID]; ok && delivery != nil {
				orderDelivery.ExpressCode = delivery.TrackingNumber
				orderDelivery.ExpressCompany = delivery.ExpressCompany
			}
		} else if order.DeliveryID == orderDao.DeliveryTypeStore {
			if shop, ok := shopMap[order.AddressID]; ok && order.AddressID != 0 {
				orderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
					DeliveryID: order.DeliveryID,
					ShopList: []goodsDto.CrSkuDeliveryShopItem{
						{
							ID:      shop.ID,
							Name:    shop.Name,
							Address: shop.Address,
							Mobile:  shop.Mobile,
						},
					},
				}
			}

			// 获取取货码（仅针对到店取货订单）
			pickupCodeKey := redis.GetPickupCodeKey(order.OutTradeNo)
			if pickupCodeBytes, err := redis.Get(pickupCodeKey); err == nil && len(pickupCodeBytes) > 0 {
				orderDelivery.PickupCode = string(pickupCodeBytes)
			}
		}

		// 构建支付信息
		orderPayInfo := &orderDto.OrderPay{
			UsedFee:       order.UsedFee,
			PayMethod:     order.PayMethod,
			OrderNo:       order.OutTradeNo,
			TransactionId: order.TransactionId,
			OrderStatus:   order.OrderStatus,
			CreatedTime:   order.CreatedAt.Format(dbs.TimeDateFormatFull),
			CouponFee:     order.CouponFee,
			PointsFee:     order.PointsFee, // 积分抵扣金额
			PointsUse:     order.PointsUse, // 消耗的积分数额
		}

		if orderRes.TotalFee > orderRes.UsedFee {
			orderPayInfo.RefundId = order.ID
		}

		if order.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
			orderRes.TotalFee = e.getOrderTotalFee(order)
			orderPayInfo.UsedFee = orderRes.TotalFee
			orderRes.UsedFee = orderRes.TotalFee
		}

		// 构建完整的订单详情
		orderDetail := &orderDto.OrderDetailRes{
			OrderInfo:     orderRes,
			GoodsInfo:     goodsInfo,
			OrderPayInfo:  orderPayInfo,
			ActiveTitle:   orderRes.ActiveName,
			BoxActive:     boxActiveMap[order.ActiveID],
			OrderDelivery: orderDelivery,
		}

		orderDetailResList = append(orderDetailResList, orderDetail)
	}

	return orderDetailResList, nil
}

// AdminOrderDetail 管理后台订单详情 - 复用用户端的详细逻辑
func (e *Entry) AdminOrderDetail(ctx *gin.Context, req *orderDto.AdminOrderDetailReq) (*orderDto.AdminOrderDetailRes, error) {
	// 参数验证
	if err := req.Validate(); err != nil {
		return nil, ecode.ParamErr
	}

	// 构造OrderDetailReq
	orderDetailReq := &orderDto.OrderDetailReq{
		OrderID:    req.OrderID,
		OutTradeNo: req.OutTradeNo,
	}

	// 首先通过OrderID或OutTradeNo查找订单获取UserID
	var orderModel *orderDao.Model
	var err error
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OutTradeNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminOrderDetail fetch order failed")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 设置UserID
	orderDetailReq.UserID = orderModel.UserID

	// 调用OrderDetail获取完整的订单详情
	orderDetailRes, err := e.OrderDetail(ctx, orderDetailReq)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminOrderDetail get order detail failed")
		return nil, err
	}

	// 获取用户信息
	userMap, err := e.getUserMap(ctx, []uint64{orderModel.UserID})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminOrderDetail get user map failed")
		return nil, err
	}

	// 构建管理后台订单详情响应
	res := &orderDto.AdminOrderDetailRes{
		OrderDetailRes: orderDetailRes,
		UserName:       "",
	}

	// 设置用户昵称
	if user, ok := userMap[orderModel.UserID]; ok {
		res.UserName = user.Nickname
	}

	return res, nil
}

// AdminOrderDeliveryCreate 管理后台创建发货记录
func (e *Entry) AdminOrderDeliveryCreate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryCreateReq) (*orderDto.AdminOrderDeliveryCreateRes, error) {
	// 验证订单是否存在且状态正确
	orderInfo, err := e.OrderRepo.FetchByID(ctx, req.OrderID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ecode.BoxOrderNotExistErr
		}
		return nil, err
	}

	// 检查订单状态，只有支付成功的订单才能发货
	if orderInfo.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
		return nil, ecode.BoxOrderNotAllowPayErr
	}

	// 检查配送方式，只有快递配送才能填写快递单号
	if orderInfo.DeliveryID != orderDao.DeliveryTypeExpress {
		return nil, ecode.ParamErr
	}

	// 检查是否已经有发货记录
	existingDelivery, err := e.DeliveryRepo.FetchByOrderID(ctx, req.OrderID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if existingDelivery != nil {
		return nil, ecode.ParamErr
	}

	// 创建发货记录
	delivery := &deliveryDao.Model{
		OrderID:        req.OrderID,
		ExpressCompany: req.ExpressCompany,
		ExpressCode:    req.ExpressCode,
		TrackingNumber: req.TrackingNumber,
		DeliveryStatus: uint32(deliveryDao.DeliveryStatusShipped),
		ShippedAt:      time.Now().Unix(),
		Remark:         req.Remark,
		AdminID:        req.AccountID,
	}

	if err := e.DeliveryRepo.Create(ctx, delivery); err != nil {
		return nil, ecode.SystemErr
	}

	// 创建发货记录成功后更新订单状态为“已发货”
	orderUpdateData := map[string]interface{}{
		"order_status": uint32(orderDao.OrderStatusDelivery),
	}
	if err := e.OrderRepo.UpdateMapByID(ctx, orderInfo.ID, orderUpdateData); err != nil {
		return nil, ecode.SystemErr
	}

	return &orderDto.AdminOrderDeliveryCreateRes{
		ID:      delivery.ID,
		OrderID: delivery.OrderID,
		Message: "发货记录创建成功",
	}, nil
}

// AdminOrderDeliveryUpdate 管理后台更新发货记录
func (e *Entry) AdminOrderDeliveryUpdate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryUpdateReq) (*orderDto.AdminOrderDeliveryUpdateRes, error) {
	// 查询发货记录
	delivery, err := e.DeliveryRepo.FetchByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ecode.ParamErr
		}
		return nil, ecode.SystemErr
	}

	// 构建更新数据
	updateData := make(map[string]interface{})

	if req.ExpressCompany != "" {
		updateData["express_company"] = req.ExpressCompany
	}
	if req.ExpressCode != "" {
		updateData["express_code"] = req.ExpressCode
	}
	if req.TrackingNumber != "" {
		updateData["tracking_number"] = req.TrackingNumber
	}
	if req.DeliveryStatus > 0 {
		updateData["delivery_status"] = req.DeliveryStatus

		// 根据状态更新时间
		now := time.Now().Unix()
		switch deliveryDao.DeliveryStatus(req.DeliveryStatus) {
		case deliveryDao.DeliveryStatusShipped:
			updateData["shipped_at"] = now
		case deliveryDao.DeliveryStatusDelivered:
			updateData["delivered_at"] = now
		}
	}
	if req.Remark != "" {
		updateData["remark"] = req.Remark
	}

	// 更新管理员信息
	updateData["admin_id"] = req.AccountID

	if err := e.DeliveryRepo.UpdateMapByID(ctx, req.ID, updateData); err != nil {
		return nil, ecode.SystemErr
	}

	return &orderDto.AdminOrderDeliveryUpdateRes{
		ID:      delivery.ID,
		OrderID: delivery.OrderID,
		Message: "发货记录更新成功",
	}, nil
}

// AdminOrderDeliveryDetail 管理后台获取发货详情
func (e *Entry) AdminOrderDeliveryDetail(ctx *gin.Context, req *orderDto.AdminOrderDeliveryDetailReq) (*orderDto.AdminOrderDeliveryDetailRes, error) {
	var delivery *deliveryDao.Model
	var err error
	var adminModel = &account.Model{}

	if req.ID > 0 {
		delivery, err = e.DeliveryRepo.FetchByID(ctx, req.ID)
	} else if req.OrderID > 0 {
		delivery, err = e.DeliveryRepo.FetchByOrderID(ctx, req.OrderID)
	} else {
		return nil, ecode.ParamErr
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ecode.ParamErr
		}
		return nil, ecode.SystemErr
	}

	if delivery.AdminID > 0 {
		// 查询管理员信息
		adminModel, err = e.AccountRepo.FetchByID(ctx, delivery.AdminID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminOrderDeliveryDetail fetch admin failed")
			return nil, ecode.SystemErr
		}
	}

	res := &orderDto.AdminOrderDeliveryDetailRes{
		AdminDeliveryInfo: &orderDto.AdminDeliveryInfo{
			ID:             delivery.ID,
			OrderID:        delivery.OrderID,
			ExpressCompany: delivery.ExpressCompany,
			ExpressCode:    delivery.ExpressCode,
			TrackingNumber: delivery.TrackingNumber,
			DeliveryStatus: delivery.DeliveryStatus,
			ShippedAt:      "",
			DeliveredAt:    "",
			Remark:         delivery.Remark,
			AdminName:      adminModel.Name,
			CreatedAt:      delivery.CreatedAt.Format(dbs.TimeDateFormatFull),
			UpdatedAt:      delivery.UpdatedAt.Format(dbs.TimeDateFormatFull),
		},
	}

	if delivery.ShippedAt > 0 {
		res.ShippedAt = time.Unix(delivery.ShippedAt, 0).Format(dbs.TimeDateFormatFull)
	}
	if delivery.DeliveredAt > 0 {
		res.DeliveredAt = time.Unix(delivery.DeliveredAt, 0).Format(dbs.TimeDateFormatFull)
	}

	return res, nil
}

// AdminOrderDeliveryList 管理后台发货记录列表
func (e *Entry) AdminOrderDeliveryList(ctx *gin.Context, req *orderDto.AdminOrderDeliveryListReq) (*orderDto.AdminOrderDeliveryListRes, error) {
	// 构建查询条件
	filter := &deliveryDao.Filter{}

	if req.OrderID > 0 {
		filter.OrderID = req.OrderID
	}
	if req.DeliveryStatus > 0 {
		filter.DeliveryStatus = req.DeliveryStatus
	}
	if req.TrackingNumber != "" {
		filter.TrackingNumber = req.TrackingNumber
	}
	if req.ExpressCode != "" {
		filter.ExpressCode = req.ExpressCode
	}

	// 时间范围过滤
	if req.ShippedAtStart > 0 {
		filter.ShippedAtStart = req.ShippedAtStart
	}
	if req.ShippedAtEnd > 0 {
		filter.ShippedAtEnd = req.ShippedAtEnd
	}

	// 分页参数
	page := req.Page
	if page <= 0 {
		page = dbs.DefaultPage
	}
	limit := req.Limit
	if limit <= 0 {
		limit = dbs.DefaultLimit
	}

	// 查询发货记录列表
	total, deliveryList, err := e.DeliveryRepo.DataPageList(ctx, filter, page, limit)
	if err != nil {
		return nil, ecode.SystemErr
	}

	// 构建响应数据
	res := &orderDto.AdminOrderDeliveryListRes{
		List:  make([]*orderDto.AdminDeliveryInfo, 0, len(deliveryList)),
		Count: total,
	}

	var adminModelMap = make(map[uint64]*account.Model)

	if t := deliveryList.GetAdminIDs(); len(t) > 0 {
		// 批量查询管理员信息
		adminModelList, err := e.AccountRepo.FindByFilter(ctx, &account.Filter{
			IDS: t,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminOrderDeliveryList FindByFilter admin error")
			return nil, ecode.SystemErr
		}
		adminModelMap = adminModelList.GetIDMap()
	}

	for _, delivery := range deliveryList {
		item := &orderDto.AdminDeliveryInfo{
			ID:             delivery.ID,
			OrderID:        delivery.OrderID,
			ExpressCompany: delivery.ExpressCompany,
			ExpressCode:    delivery.ExpressCode,
			TrackingNumber: delivery.TrackingNumber,
			DeliveryStatus: delivery.DeliveryStatus,
			ShippedAt:      "",
			DeliveredAt:    "",
			Remark:         delivery.Remark,
			CreatedAt:      delivery.CreatedAt.Format(dbs.TimeDateFormatFull),
			UpdatedAt:      delivery.UpdatedAt.Format(dbs.TimeDateFormatFull),
		}
		if adminModel, ok := adminModelMap[delivery.AdminID]; ok {
			item.AdminName = adminModel.Name
		} else {
			item.AdminName = "系统"
		}

		if delivery.ShippedAt > 0 {
			item.ShippedAt = time.Unix(delivery.ShippedAt, 0).Format(dbs.TimeDateFormatFull)
		}
		if delivery.DeliveredAt > 0 {
			item.DeliveredAt = time.Unix(delivery.DeliveredAt, 0).Format(dbs.TimeDateFormatFull)
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

// AdminPickupCodeVerify 管理后台验证取货码
func (e *Entry) AdminPickupCodeVerify(ctx *gin.Context, req *orderDto.AdminPickupCodeVerifyReq) (*orderDto.AdminPickupCodeVerifyRes, error) {
	// 调用原有的取货码验证逻辑
	originalReq := &orderDto.PickupCodeVerifyReq{
		PickupCode: req.PickupCode,
		OrderID:    req.OrderID,
		OrderNo:    req.OrderNo,
	}

	originalRes, err := e.VerifyPickupCode(ctx, originalReq)
	if err != nil {
		return nil, err
	}

	// 构建增强响应
	res := &orderDto.AdminPickupCodeVerifyRes{
		OrderID:       originalRes.OrderID,
		OrderNo:       originalRes.OrderNo,
		PickupCode:    originalRes.PickupCode,
		IsValid:       originalRes.IsValid,
		OrderVerified: false, // 初始化为false，后续根据是否进行双重验证设置
		VerifyMessage: "取货码验证成功",
	}

	if !originalRes.IsValid {
		res.VerifyMessage = "取货码无效或已过期"
		return res, nil
	}

	// 查询订单详细信息
	orderInfo, err := e.OrderRepo.FetchByID(ctx, originalRes.OrderID)
	if err != nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 构建管理后台订单信息
	res.OrderInfo = &orderDto.AdminOrderInfo{
		OrderID:      orderInfo.ID,
		OrderNo:      orderInfo.OutTradeNo,
		UserID:       orderInfo.UserID,
		UserName:     "", // 需要查询用户信息填充
		TotalFee:     orderInfo.TotalFee,
		CashFee:      orderInfo.CashFee,
		OrderStatus:  orderInfo.OrderStatus,
		OrderType:    orderInfo.OrderType,
		DeliveryType: orderInfo.DeliveryID,
		PayMethod:    orderInfo.PayMethod,
		CreatedAt:    orderInfo.CreatedAt.Format(dbs.TimeDateFormatFull),
		PayTime:      "",
	}

	if orderInfo.PayTime > 0 {
		res.OrderInfo.PayTime = time.Unix(int64(orderInfo.PayTime), 0).Format(dbs.TimeDateFormatFull)
	}

	// 如果提供了订单信息，进行双重验证
	if req.OrderID > 0 || req.OrderNo != "" {
		res.OrderVerified = true

		if req.OrderID > 0 && orderInfo.ID != req.OrderID {
			res.VerifyMessage = "取货码与指定订单ID不匹配"
			res.IsValid = false
			return res, nil
		}

		if req.OrderNo != "" && orderInfo.OutTradeNo != req.OrderNo {
			res.VerifyMessage = "取货码与指定订单号不匹配"
			res.IsValid = false
			return res, nil
		}
	}

	// 验证配送方式，取货码只适用于到店取货
	if orderInfo.DeliveryID != orderDao.DeliveryTypeStore {
		res.VerifyMessage = "该订单不是到店取货订单，无法使用取货码"
		res.IsValid = false
		return res, nil
	}

	// 取货码验证成功，开始构建详细信息

	// 对于到店取货订单，查询发货信息（主要是为了获取状态和备注）
	delivery, err := e.DeliveryRepo.FetchByOrderID(ctx, originalRes.OrderID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, ecode.SystemErr
	}

	var adminModel = &account.Model{}
	if delivery.AdminID > 0 {
		// 查询管理员信息
		adminModel, err = e.AccountRepo.FetchByID(ctx, delivery.AdminID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminPickupCodeVerify fetch admin failed")
			return nil, ecode.SystemErr
		}
	}

	if delivery != nil {
		res.DeliveryInfo = &orderDto.AdminDeliveryInfo{
			ID:             delivery.ID,
			OrderID:        delivery.OrderID,
			ExpressCompany: "", // 到店取货无快递公司
			ExpressCode:    "", // 到店取货无快递编码
			TrackingNumber: "", // 到店取货无快递单号
			DeliveryStatus: delivery.DeliveryStatus,
			Remark:         delivery.Remark,
			AdminName:      adminModel.Name,
			CreatedAt:      delivery.CreatedAt.Format(dbs.TimeDateFormatFull),
			UpdatedAt:      delivery.UpdatedAt.Format(dbs.TimeDateFormatFull),
		}

		// 到店取货的状态说明
		prefix := "取货码验证成功"
		if res.OrderVerified {
			prefix = "取货码和订单双重验证成功"
		}

		switch deliveryDao.DeliveryStatus(delivery.DeliveryStatus) {
		case deliveryDao.DeliveryStatusPending:
			res.VerifyMessage = prefix + "，商品待准备"
		case deliveryDao.DeliveryStatusShipped:
			res.VerifyMessage = prefix + "，商品已准备好可取货"
		case deliveryDao.DeliveryStatusDelivered:
			res.VerifyMessage = prefix + "，订单已完成取货"
		default:
			res.VerifyMessage = prefix
		}
	} else {
		prefix := "取货码验证成功"
		if res.OrderVerified {
			prefix = "取货码和订单双重验证成功"
		}
		res.VerifyMessage = prefix + "，但未找到配送记录"
	}

	return res, nil
}

// AdminOrderComplete 管理后台完成订单+发货状态更新
func (e *Entry) AdminOrderComplete(ctx *gin.Context, req *orderDto.AdminOrderCompleteReq) (*orderDto.AdminOrderCompleteRes, error) {
	// 验证参数
	if err := req.Validate(); err != nil {
		return nil, ecode.ParamErr
	}

	// 查找订单
	var orderInfo *orderDao.Model
	var err error

	if req.OrderID > 0 {
		orderInfo, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else if req.OrderNo != "" {
		orderFilter := &orderDao.Filter{OutTradeNo: req.OrderNo}
		orderList, err2 := e.OrderRepo.FindByFilter(ctx, orderFilter)
		if err2 != nil {
			err = err2
		} else if len(orderList) == 0 {
			err = gorm.ErrRecordNotFound
		} else {
			orderInfo = orderList[0]
		}
	} else if req.PickupCode != "" {
		// 当仅提供取货码时，需要同时提供订单ID 或 订单号进行确认
		if req.OrderID > 0 {
			orderInfo, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
		} else if req.OrderNo != "" {
			orderFilter := &orderDao.Filter{OutTradeNo: req.OrderNo}
			orderList, err2 := e.OrderRepo.FindByFilter(ctx, orderFilter)
			if err2 != nil {
				err = err2
			} else if len(orderList) == 0 {
				err = gorm.ErrRecordNotFound
			} else {
				orderInfo = orderList[0]
			}
		} else {
			return nil, ecode.ParamErr
		}
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ecode.BoxOrderNotExistErr
		}
		return nil, ecode.SystemErr
	}

	// 检查订单状态，只有支付成功的订单才能完成
	if orderInfo.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
		return nil, ecode.BoxOrderNotAllowPayErr
	}

	res := &orderDto.AdminOrderCompleteRes{
		OrderID:         orderInfo.ID,
		OrderNo:         orderInfo.OutTradeNo,
		Message:         "订单完成成功",
		DeliveryUpdated: false,
		DeliveryMessage: "",
		CompletedAt:     time.Now().Format(dbs.TimeDateFormatFull),
	}

	// 如果需要更新发货状态
	if req.UpdateDelivery && req.TrackingNumber != "" {
		delivery, err := e.DeliveryRepo.FetchByOrderID(ctx, orderInfo.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ecode.SystemErr
		}

		if delivery != nil {
			// 验证快递单号是否匹配
			if delivery.TrackingNumber != req.TrackingNumber {
				res.DeliveryMessage = "快递单号不匹配，未更新发货状态"
			} else {
				// 更新发货状态
				updateData := make(map[string]interface{})
				if req.DeliveryStatus > 0 {
					updateData["delivery_status"] = req.DeliveryStatus
					// 根据状态更新时间
					now := time.Now().Unix()
					switch deliveryDao.DeliveryStatus(req.DeliveryStatus) {
					case deliveryDao.DeliveryStatusDelivered:
						updateData["delivered_at"] = now
					}
				}
				if req.DeliveryRemark != "" {
					updateData["remark"] = req.DeliveryRemark
				}
				updateData["admin_id"] = req.AccountID
				updateData["admin_name"] = req.AdminName

				if err := e.DeliveryRepo.UpdateMapByID(ctx, delivery.ID, updateData); err != nil {
					res.DeliveryMessage = "发货状态更新失败"
				} else {
					res.DeliveryUpdated = true
					res.DeliveryMessage = "发货状态更新成功"
				}
			}
		} else {
			res.DeliveryMessage = "未找到发货记录，无法更新发货状态"
		}
	}

	// 更新订单状态为已完成
	orderUpdateData := make(map[string]interface{})
	orderUpdateData["order_status"] = uint32(orderDao.OrderStatusDone)

	if err := e.OrderRepo.UpdateMapByID(ctx, orderInfo.ID, orderUpdateData); err != nil {
		return nil, ecode.SystemErr
	}

	return res, nil
}

// AdminTrackingNumberVerify 管理后台验证快递单号
func (e *Entry) AdminTrackingNumberVerify(ctx *gin.Context, req *orderDto.AdminTrackingNumberVerifyReq) (*orderDto.AdminTrackingNumberVerifyRes, error) {
	// 参数验证
	if err := req.Validate(); err != nil {
		return nil, ecode.ParamErr
	}

	// 构建基础响应
	res := &orderDto.AdminTrackingNumberVerifyRes{
		TrackingNumber: req.TrackingNumber,
		IsValid:        false,
		VerifyMessage:  "快递单号验证失败",
	}

	// 通过快递单号查找发货记录
	deliveryFilter := &deliveryDao.Filter{
		TrackingNumber: req.TrackingNumber,
	}
	deliveryList, err := e.DeliveryRepo.FindByFilter(ctx, deliveryFilter)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminTrackingNumberVerify fetch delivery error")
		return nil, ecode.SystemErr
	}

	if len(deliveryList) == 0 {
		res.VerifyMessage = "快递单号不存在"
		return res, nil
	}

	delivery := deliveryList[0]

	// 如果指定了订单信息，验证是否匹配
	if req.OrderID > 0 && delivery.OrderID != req.OrderID {
		res.VerifyMessage = "快递单号与指定订单不匹配"
		return res, nil
	}

	// 获取订单信息
	orderInfo, err := e.OrderRepo.FetchByID(ctx, delivery.OrderID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminTrackingNumberVerify fetch order error")
		return nil, ecode.SystemErr
	}

	if orderInfo == nil {
		res.VerifyMessage = "关联订单不存在"
		return res, nil
	}

	// 如果指定了订单号，验证是否匹配
	if req.OrderNo != "" && orderInfo.OutTradeNo != req.OrderNo {
		res.VerifyMessage = "快递单号与指定订单号不匹配"
		return res, nil
	}

	// 验证配送方式，快递单号只适用于快递配送
	if orderInfo.DeliveryID != orderDao.DeliveryTypeExpress {
		res.VerifyMessage = "该订单不是快递配送订单，无法使用快递单号"
		return res, nil
	}

	// 设置基础信息
	res.OrderID = orderInfo.ID
	res.OrderNo = orderInfo.OutTradeNo
	res.IsValid = true

	// 构建管理后台订单信息
	res.OrderInfo = &orderDto.AdminOrderInfo{
		OrderID:      orderInfo.ID,
		OrderNo:      orderInfo.OutTradeNo,
		UserID:       orderInfo.UserID,
		UserName:     "", // 需要查询用户信息填充
		TotalFee:     orderInfo.TotalFee,
		CashFee:      orderInfo.CashFee,
		OrderStatus:  orderInfo.OrderStatus,
		OrderType:    orderInfo.OrderType,
		DeliveryType: orderInfo.DeliveryID,
		PayMethod:    orderInfo.PayMethod,
		CreatedAt:    orderInfo.CreatedAt.Format(dbs.TimeDateFormatFull),
		PayTime:      "",
	}

	if orderInfo.PayTime > 0 {
		res.OrderInfo.PayTime = time.Unix(int64(orderInfo.PayTime), 0).Format(dbs.TimeDateFormatFull)
	}

	var adminModel = &account.Model{}
	if delivery.AdminID > 0 {
		// 查询管理员信息
		adminModel, err = e.AccountRepo.FetchByID(ctx, delivery.AdminID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminTrackingNumberVerify fetch admin failed")
			return nil, ecode.SystemErr
		}
	}

	// 构建发货信息
	res.DeliveryInfo = &orderDto.AdminDeliveryInfo{
		ID:             delivery.ID,
		OrderID:        delivery.OrderID,
		ExpressCompany: delivery.ExpressCompany,
		ExpressCode:    delivery.ExpressCode,
		TrackingNumber: delivery.TrackingNumber,
		DeliveryStatus: delivery.DeliveryStatus,
		Remark:         delivery.Remark,
		AdminName:      adminModel.Name,
		CreatedAt:      delivery.CreatedAt.Format(dbs.TimeDateFormatFull),
		UpdatedAt:      delivery.UpdatedAt.Format(dbs.TimeDateFormatFull),
	}

	if delivery.ShippedAt > 0 {
		res.DeliveryInfo.ShippedAt = time.Unix(delivery.ShippedAt, 0).Format(dbs.TimeDateFormatFull)
	}
	if delivery.DeliveredAt > 0 {
		res.DeliveryInfo.DeliveredAt = time.Unix(delivery.DeliveredAt, 0).Format(dbs.TimeDateFormatFull)
	}

	// 根据发货状态设置验证消息
	switch deliveryDao.DeliveryStatus(delivery.DeliveryStatus) {
	case deliveryDao.DeliveryStatusPending:
		res.VerifyMessage = "快递单号有效，待发货"
	case deliveryDao.DeliveryStatusShipped:
		res.VerifyMessage = "快递单号有效，已发货"
	case deliveryDao.DeliveryStatusDelivered:
		res.VerifyMessage = "快递单号有效，已送达"
	case deliveryDao.DeliveryStatusReturned:
		res.VerifyMessage = "快递单号有效，已退回"
	default:
		res.VerifyMessage = "快递单号验证成功"
	}

	log.Ctx(ctx).Info("AdminTrackingNumberVerify success",
		"trackingNumber", req.TrackingNumber,
		"orderNo", orderInfo.OutTradeNo,
		"orderID", orderInfo.ID)

	return res, nil
}
