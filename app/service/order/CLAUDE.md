# app/service/order 订单服务模块规范文档

本文档记录 `/app/service/order` 目录下的订单业务逻辑层代码规范和最佳实践。

## 目录结构

```
app/service/order/
├── interface.go     # 接口定义和依赖注入
├── order.go         # 用户端订单业务逻辑
├── admin_order.go   # 管理后台订单业务逻辑
├── refund.go        # 退款相关业务逻辑
└── wx_applet.go     # 微信小程序支付回调处理
```

## 核心架构设计

### 接口定义 (`interface.go`)

#### 业务接口分层
```go
type Server interface {
    WxAppletSrv    // 微信小程序相关
    Order          // 用户端订单操作
    AdminOrder     // 管理后台订单操作
}

// 用户端订单接口
type Order interface {
    OrderStatus(ctx *gin.Context, req *orderDto.OrderStatusReq) (*orderDto.OrderStatusRes, error)
    OrderDetail(ctx *gin.Context, req *orderDto.OrderDetailReq) (*orderDto.OrderDetailRes, error)
    GeneratePickupCode(ctx *gin.Context, req *orderDto.PickupCodeGenerateReq) (*orderDto.PickupCodeGenerateRes, error)
    QueryPickupCode(ctx *gin.Context, req *orderDto.PickupCodeQueryReq) (*orderDto.PickupCodeQueryRes, error)
    VerifyPickupCode(ctx *gin.Context, req *orderDto.PickupCodeVerifyReq) (*orderDto.PickupCodeVerifyRes, error)
}

// 管理后台订单接口
type AdminOrder interface {
    AdminOrderList(ctx *gin.Context, req *orderDto.AdminOrderListReq) (*orderDto.AdminOrderListRes, error)
    AdminOrderDetail(ctx *gin.Context, req *orderDto.AdminOrderDetailReq) (*orderDto.AdminOrderDetailRes, error)
    // 发货管理
    AdminOrderDeliveryCreate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryCreateReq) (*orderDto.AdminOrderDeliveryCreateRes, error)
    AdminOrderDeliveryUpdate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryUpdateReq) (*orderDto.AdminOrderDeliveryUpdateRes, error)
    // 验证功能
    AdminPickupCodeVerify(ctx *gin.Context, req *orderDto.AdminPickupCodeVerifyReq) (*orderDto.AdminPickupCodeVerifyRes, error)
    AdminTrackingNumberVerify(ctx *gin.Context, req *orderDto.AdminTrackingNumberVerifyReq) (*orderDto.AdminTrackingNumberVerifyRes, error)
    // 订单完成
    AdminOrderComplete(ctx *gin.Context, req *orderDto.AdminOrderCompleteReq) (*orderDto.AdminOrderCompleteRes, error)
}

// 微信小程序回调接口
type WxAppletSrv interface {
    WxAppletPayNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error
    WxAppletRefundNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error
}
```

#### 依赖注入结构
```go
type Entry struct {
    // 用户相关
    UserRepo    *userDao.Entry
    UserLogRepo userLogDao.Repo
    AccountRepo account.Repo

    // 支付相关
    WxpayRepo         *userWxpay.Entry
    WxPayLogRepo      *payLog.Entry
    WxCallbackLogRepo *callbackLog.Entry
    RefundLogRepo     refundLogDao.Repo

    // 订单相关
    OrderRepo       orderDao.Repo
    OrderDetailRepo orderDetailDao.Repo
    DeliveryRepo    deliveryDao.Repo
    
    // 盲盒相关
    ActivityRepo    *actDao.Entry
    BoxActiveRepo   box_active_config.Repo
    BoxGoodsRepo    box_goods.Repo
    
    // 商品相关
    SpuBatchRepo    *spuBatch.Entry
    BatchRepo       *batchDao.Entry
    SkuRepo         *skuDao.Entry
    StockLogRepo    *stockLogDao.Entry

    // 店铺地址相关
    ShopRepo shopDao.Repo
    AddrRepo *addrDao.Entry

    // Redis 客户端
    RedisCli *redis.RedisClient
}
```

### 单例模式实现
```go
var (
    defaultEntry         *Entry
    defaultEntryInitOnce sync.Once
)

func GetService() *Entry {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}

func newEntry() *Entry {
    return &Entry{
        // 初始化所有依赖
        OrderRepo:       orderDao.GetRepo(),
        OrderDetailRepo: orderDetailDao.GetRepo(),
        // ... 其他依赖
    }
}
```

## 核心业务逻辑实现

### 订单状态管理 (`order.go`)

#### 订单状态查询
```go
func (e *Entry) OrderStatus(ctx *gin.Context, req *orderDto.OrderStatusReq) (*orderDto.OrderStatusRes, error) {
    orderModel, err := e.OrderRepo.FetchByID(ctx, req.ID)
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("OrderStatus FetchByID error")
        return nil, err
    }
    if orderModel == nil {
        return nil, ecode.BoxOrderNotExistErr
    }

    res := &orderDto.OrderStatusRes{
        OrderStatus: orderModel.OrderStatus,
    }
    if orderModel.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
        res.IsSuccess = true
    }
    return res, nil
}
```

#### 订单详情查询 - 批量优化模式
```go
func (e *Entry) OrderDetail(ctx *gin.Context, req *orderDto.OrderDetailReq) (*orderDto.OrderDetailRes, error) {
    // 1. 获取订单基础信息
    var orderModel *orderDao.Model
    if req.OrderID > 0 {
        orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
    } else if req.OutTradeNo != "" {
        orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OutTradeNo)
    }

    // 2. 批量查询订单明细
    tradeList, err := e.OrderDetailRepo.FindByFilter(ctx, &orderDetailDao.Filter{
        OrderID: orderModel.ID,
    })

    // 3. 收集关联ID - 避免N+1查询
    var goodsIDs, skuIDs, spuIDs, batchIDs []uint64
    for _, detail := range tradeList {
        goodsIDs = append(goodsIDs, detail.GoodsID)
        skuIDs = append(skuIDs, detail.SkuID)
        spuIDs = append(spuIDs, detail.SpuID)
        batchIDs = append(batchIDs, detail.BatchID)
    }

    // 4. 批量查询关联数据
    goodsMap := e.getGoodsMap(ctx, goodsIDs)
    skuMap := e.getSkuMap(ctx, skuIDs)
    batchMap := e.getBatchMap(ctx, batchIDs)

    // 5. 组装结果
    res := &orderDto.OrderDetailRes{
        OrderInfo: e.buildOrderInfo(orderModel),
        GoodsInfo: e.buildGoodsInfo(tradeList, goodsMap, skuMap, batchMap),
    }

    return res, nil
}
```

### 取货码系统 - 双重缓存机制

#### 取货码生成
```go
func (e *Entry) GeneratePickupCode(ctx *gin.Context, req *orderDto.PickupCodeGenerateReq) (*orderDto.PickupCodeGenerateRes, error) {
    // 1. 验证订单
    orderModel, err := e.getOrderByUserID(ctx, req.OrderID, req.UserID)
    if err != nil {
        return nil, err
    }

    // 2. 验证订单状态和配送方式
    if orderModel.DeliveryID != orderDao.DeliveryTypeStore {
        return nil, ecode.BoxOrderPickupCodeErr
    }
    if orderModel.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
        return nil, ecode.BoxOrderPickupCodeGenErr
    }

    // 3. 生成取货码
    pickupCode := safe_random.GenerateRandomString(6)
    expireTime := 604800 // 7天
    codeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)

    // 4. 保存正向映射：订单号 -> 取货码
    if err := redis.SetString(codeKey, pickupCode, expireTime); err != nil {
        log.Ctx(ctx).WithError(err).Error("GeneratePickupCode failed to save pickup code")
        return nil, ecode.BoxOrderPickupCodeSaveErr
    }

    // 5. 返回结果
    res := &orderDto.PickupCodeGenerateRes{
        OrderID:    orderModel.ID,
        OrderNo:    orderModel.OutTradeNo,
        PickupCode: pickupCode,
        IsNew:      true,
    }

    log.Ctx(ctx).Info("GeneratePickupCode success", 
        "orderID", orderModel.ID, 
        "pickupCode", pickupCode)

    return res, nil
}
```

#### 取货码验证
```go
func (e *Entry) VerifyPickupCode(ctx *gin.Context, req *orderDto.PickupCodeVerifyReq) (*orderDto.PickupCodeVerifyRes, error) {
    // 1. 参数校验
    if err := req.Validate(); err != nil {
        return nil, ecode.ParamErr
    }

    // 2. 获取订单信息
    orderModel, err := e.getOrderByFilter(ctx, req.OrderID, req.OrderNo)
    if err != nil || orderModel == nil {
        res := &orderDto.PickupCodeVerifyRes{
            PickupCode: req.PickupCode,
            IsValid:    false,
        }
        return res, nil
    }

    // 3. 从 Redis 获取存储的取货码
    codeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)
    storedCode, err := redis.Get(codeKey)
    if err != nil || len(storedCode) == 0 {
        res := &orderDto.PickupCodeVerifyRes{
            PickupCode: req.PickupCode,
            IsValid:    false,
        }
        return res, nil
    }

    // 4. 比对取货码
    if string(storedCode) != req.PickupCode {
        res := &orderDto.PickupCodeVerifyRes{
            PickupCode: req.PickupCode,
            IsValid:    false,
        }
        return res, nil
    }

    // 5. 验证订单状态
    if orderModel.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
        res := &orderDto.PickupCodeVerifyRes{
            PickupCode: req.PickupCode,
            IsValid:    false,
        }
        return res, nil
    }

    // 6. 验证成功
    res := &orderDto.PickupCodeVerifyRes{
        OrderID:    orderModel.ID,
        OrderNo:    orderModel.OutTradeNo,
        PickupCode: req.PickupCode,
        IsValid:    true,
    }

    return res, nil
}
```

### 管理后台功能 (`admin_order.go`)

#### 订单列表 - 性能优化查询
```go
func (e *Entry) AdminOrderList(ctx *gin.Context, req *orderDto.AdminOrderListReq) (*orderDto.AdminOrderListRes, error) {
    // 1. 构建查询条件
    orderFilter := &orderDao.Filter{
        ID:                req.OrderID,
        OutTradeNo:        req.OutTradeNo,
        UserID:            req.UserID,
        OrderType:         req.OrderType,
        OrderStatusList:   req.OrderStatusList,
        CreatedAtStart:    req.CreatedAtStart,
        CreatedAtEnd:      req.CreatedAtEnd,
    }

    // 2. 分页查询订单
    total, orderList, err := e.OrderRepo.DataPageList(ctx, orderFilter, req.Page, req.Limit)
    if err != nil {
        return nil, err
    }

    // 3. 批量获取订单详情 - 避免N+1查询
    orderDetailResList, err := e.batchGetOrderDetails(ctx, orderList)
    if err != nil {
        return nil, err
    }

    // 4. 批量获取用户信息
    userIDs := orderList.GetUserIDs()
    userMap, err := e.getUserMap(ctx, userIDs)
    if err != nil {
        return nil, err
    }

    // 5. 构建结果
    res := &orderDto.AdminOrderListRes{
        List:  make([]*orderDto.AdminOrderListItem, 0, len(orderList)),
        Count: total,
    }

    for i, orderDetailRes := range orderDetailResList {
        order := orderList[i]
        user := userMap[order.UserID]
        
        item := &orderDto.AdminOrderListItem{
            OrderInfo:    orderDetailRes.OrderInfo,
            GoodsInfo:    orderDetailRes.GoodsInfo,
            UserNickname: user.Nickname,
            UserMobile:   user.Mobile,
        }
        res.List = append(res.List, item)
    }

    return res, nil
}
```

#### 取货码验证增强 - 双重验证模式
```go
func (e *Entry) AdminPickupCodeVerify(ctx *gin.Context, req *orderDto.AdminPickupCodeVerifyReq) (*orderDto.AdminPickupCodeVerifyRes, error) {
    // 1. 调用原有的取货码验证逻辑
    originalReq := &orderDto.PickupCodeVerifyReq{
        PickupCode: req.PickupCode,
        OrderID:    req.OrderID,
        OrderNo:    req.OrderNo,
    }

    originalRes, err := e.VerifyPickupCode(ctx, originalReq)
    if err != nil {
        return nil, err
    }

    // 2. 构建管理后台增强响应
    res := &orderDto.AdminPickupCodeVerifyRes{
        OrderID:       originalRes.OrderID,
        OrderNo:       originalRes.OrderNo,
        PickupCode:    originalRes.PickupCode,
        IsValid:       originalRes.IsValid,
        OrderVerified: false, // 初始化为false
        VerifyMessage: "取货码验证成功",
    }

    // 3. 如果取货码验证成功，进行双重验证
    if originalRes.IsValid {
        // 双重验证：管理员信息 + 订单信息匹配
        if req.AdminID > 0 {
            adminInfo, err := e.AccountRepo.FetchByID(ctx, req.AdminID)
            if err != nil {
                return nil, ecode.SystemErr
            }
            if adminInfo != nil {
                res.VerifyMessage = fmt.Sprintf("取货码验证成功，管理员：%s", adminInfo.Name)
            }
        }

        // 标记为双重验证成功
        res.OrderVerified = true
    } else {
        res.VerifyMessage = "取货码验证失败"
    }

    return res, nil
}
```

### 退款处理 (`refund.go`)

#### 多支付方式退款策略
```go
func (e *Entry) RefundCreate(ctx *gin.Context, req *orderDto.RefundCreateReq) (*orderDto.RefundCreateRes, error) {
    // 1. 参数验证和订单获取
    orderModel, userModel, err := e.validateRefundRequest(ctx, req)
    if err != nil {
        return nil, err
    }

    // 2. 验证订单状态
    if !e.canRefundOrder(orderModel.OrderStatus) {
        return nil, ecode.BoxOrderRefundErr
    }

    // 3. 检查重复退款
    if existingRefund := e.checkExistingRefund(ctx, orderModel.ID); existingRefund {
        return nil, ecode.BoxOrderRefundAlreadyErr
    }

    // 4. 执行退款 - 使用事务确保一致性
    res := &orderDto.RefundCreateRes{}
    
    err = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
        return e.executeRefund(ctx, tx, orderModel, userModel, req.Reason)
    })

    if err != nil {
        log.Ctx(ctx).WithError(err).Error("RefundCreate transaction failed")
        return nil, err
    }

    return res, nil
}

// 执行退款 - 策略模式处理不同支付方式
func (e *Entry) executeRefund(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model, 
                             userModel *userDao.Model, reason string) error {
    // 1. 创建退款记录
    outRefundNo := helper.GetAppNo("RF")
    refundAmount := orderModel.CashFee
    
    refundModel := &refundLogDao.Model{
        OrderID:      orderModel.ID,
        UserID:       orderModel.UserID,
        OutRefundNo:  outRefundNo,
        RefundAmount: refundAmount,
        RefundReason: reason,
        RefundStatus: uint32(refundLogDao.RefundStatusPending),
    }

    if err := e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
        return err
    }

    // 2. 根据支付方式选择退款策略
    switch orderModel.PayMethod {
    case 1: // 微信支付
        err = e.processWechatRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
    case 2: // 支付宝支付
        err = e.processAlipayRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
    case 3: // 余额支付
        err = e.processBalanceRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
    default:
        err = fmt.Errorf("不支持的支付方式: %d", orderModel.PayMethod)
    }

    if err != nil {
        return err
    }

    // 3. 更新订单状态
    return e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, orderModel.ID, map[string]interface{}{
        "order_status": uint32(orderDao.OrderStatusRefund),
    })
}
```

#### 微信退款处理
```go
func (e *Entry) processWechatRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
    userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {

    refundInfo := &order.RefundInfo{
        OutTradeNo:   orderModel.OutTradeNo,
        OutRefundNo:  outRefundNo,
        TotalAmount:  orderModel.CashFee,
        RefundAmount: refundAmount,
        Reason:       "用户申请退款",
    }

    // 调用微信退款 API
    if err := wechatOrderAPI.GopayOrderRefund(ctx, tx, userModel, outRefundNo, refundInfo); err != nil {
        log.Ctx(ctx).WithError(err).Error("processWechatRefundWithTx GopayOrderRefund error")
        return err
    }

    return nil
}

// 支付宝退款处理 - TODO 待实现
func (e *Entry) processAlipayRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
    userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {
    
    // TODO: 实现支付宝退款逻辑
    log.Ctx(ctx).Info("processAlipayRefundWithTx", "orderID", orderModel.ID, "amount", refundAmount)
    return nil
}

// 余额退款处理 - TODO 待实现
func (e *Entry) processBalanceRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
    userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {
    
    // TODO: 实现余额退款逻辑
    log.Ctx(ctx).Info("processBalanceRefundWithTx", "orderID", orderModel.ID, "amount", refundAmount)
    return nil
}
```

### 微信支付回调处理 (`wx_applet.go`)

#### 支付成功回调
```go
func (e *Entry) WxAppletPayNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error {
    log.Ctx(ctx).WithField("WxAppletPayNotify", notifyReq).Info("WxAppletPayNotify")

    // 1. 解密回调数据
    resp, err := notifyReq.DecryptPayCipherText(config.WechatCfg.ApiV3Key)
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("DecryptPayCipherText error")
        return err
    }

    // 2. 构建回调日志记录
    notityData, _ := json.Marshal(notifyReq)
    respData, _ := json.Marshal(resp)

    callbackLog := &callbackLog.Model{
        NotifyID:        notifyReq.Id,
        ReqID:           helper.GetGinRequestID(ctx),
        EntityType:      uint64(common.ActionPay),
        WxEntityID:      resp.TransactionId,
        OutEntityNo:     resp.OutTradeNo,
        NotifyStatus:    resp.TradeState,
        NotifyData:      string(notityData),
        ResourceDecrypt: string(respData),
        Status:          dbs.True,
    }

    // 3. 使用事务处理回调
    tx := dbs.NewMysqlEngines().Use(true).Begin()
    err = func() error {
        if notifyReq.EventType == "TRANSACTION.SUCCESS" {
            // 调用盲盒服务处理支付成功
            req := &boxDto.BoxWechatNotifyReq{
                OutTradeNo:  resp.OutTradeNo,
                TotalAmount: uint64(resp.Amount.PayerTotal),
                TradeNo:     resp.TransactionId,
            }

            if _, err = boxSrv.GetService().BoxWechatNotify(ctx, req); err != nil {
                log.Ctx(ctx).WithError(err).Error("BoxWechatNotify error")
                return err
            }
        }

        // 记录回调日志
        if err = e.WxCallbackLogRepo.CreateWithTx(ctx, tx, callbackLog); err != nil {
            return err
        }

        // 更新支付日志
        payLogUpdate := map[string]interface{}{
            "wx_entity_id": resp.TransactionId,
            "status": lo.Ternary(notifyReq.EventType == "TRANSACTION.SUCCESS", 
                                dbs.StatusEnable, dbs.StatusDisable),
        }
        if err = e.WxPayLogRepo.UpdateMapByUdxWithTx(ctx, tx, resp.OutTradeNo, payLogUpdate); err != nil {
            return err
        }

        return tx.Commit().Error
    }()

    if err != nil {
        tx.Rollback()
        log.Ctx(ctx).WithError(err).Error("WxAppletPayNotify err")
        return err
    }

    return nil
}
```

## 业务规范和最佳实践

### 1. 事务处理规范

#### 标准事务模式
```go
func (e *Entry) BusinessMethod(ctx *gin.Context, req *dto.Request) (*dto.Response, error) {
    var res *dto.Response
    
    err := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
        // 1. 数据验证和准备
        if err := e.validateData(ctx, req); err != nil {
            return err
        }
        
        // 2. 核心业务操作
        if err := e.coreBusinessWithTx(ctx, tx, req); err != nil {
            return err
        }
        
        // 3. 相关数据更新
        if err := e.updateRelatedDataWithTx(ctx, tx, req); err != nil {
            return err
        }
        
        // 4. 构建响应数据
        res = e.buildResponse(req)
        return nil
    })
    
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("BusinessMethod transaction failed")
        return nil, err
    }
    
    return res, nil
}
```

#### WithTx 方法命名规范
```go
// DAO层调用时传递事务
func (e *Entry) processOrderWithTx(ctx *gin.Context, tx *gorm.DB, orderID uint64) error {
    // 使用带事务的DAO方法
    if err := e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, orderID, updates); err != nil {
        return err
    }
    
    if err := e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
        return err
    }
    
    return nil
}
```

### 2. 批量查询优化

#### 避免N+1查询模式
```go
func (e *Entry) batchGetOrderDetails(ctx *gin.Context, orderList orderDao.ModelList) ([]*orderDto.OrderDetailRes, error) {
    // 1. 收集所有订单ID
    orderIDs := orderList.GetIDs()
    
    // 2. 批量查询订单明细
    orderDetailMap, err := e.batchGetOrderDetailMap(ctx, orderIDs)
    if err != nil {
        return nil, err
    }
    
    // 3. 收集关联数据ID
    var goodsIDs, skuIDs []uint64
    for _, details := range orderDetailMap {
        for _, detail := range details {
            goodsIDs = append(goodsIDs, detail.GoodsID)
            skuIDs = append(skuIDs, detail.SkuID)
        }
    }
    
    // 4. 批量查询关联数据
    goodsMap, err := e.getGoodsMap(ctx, goodsIDs)
    if err != nil {
        return nil, err
    }
    
    skuMap, err := e.getSkuMap(ctx, skuIDs)
    if err != nil {
        return nil, err
    }
    
    // 5. 组装结果
    results := make([]*orderDto.OrderDetailRes, 0, len(orderList))
    for _, order := range orderList {
        details := orderDetailMap[order.ID]
        res := e.buildOrderDetailRes(order, details, goodsMap, skuMap)
        results = append(results, res)
    }
    
    return results, nil
}
```

### 3. 缓存使用规范

#### Redis 取货码管理
```go
// 取货码键名生成
func getPickupCodeKey(outTradeNo string) string {
    return redis.GetPickupCodeKey(outTradeNo) // 实际调用 pkg/redis 包方法
}

// 取货码操作
func (e *Entry) handlePickupCode(ctx *gin.Context, outTradeNo, pickupCode string) error {
    codeKey := getPickupCodeKey(outTradeNo)
    expireTime := 604800 // 7天过期
    
    // 保存取货码
    if err := redis.SetString(codeKey, pickupCode, expireTime); err != nil {
        log.Ctx(ctx).WithError(err).Error("Failed to save pickup code")
        return ecode.BoxOrderPickupCodeSaveErr
    }
    
    // 获取取货码
    storedCode, err := redis.Get(codeKey)
    if err != nil || len(storedCode) == 0 {
        log.Ctx(ctx).Info("Pickup code not found or expired")
        return ecode.BoxOrderPickupCodeNotFoundErr
    }
    
    return nil
}
```

#### 缓存更新策略
```go
func (e *Entry) updateOrderWithCache(ctx *gin.Context, orderID uint64, updates map[string]interface{}) error {
    // 1. 更新数据库
    if err := e.OrderRepo.UpdateMapByID(ctx, orderID, updates); err != nil {
        return err
    }
    
    // 2. 异步清理相关缓存
    go func() {
        cacheKeys := []string{
            fmt.Sprintf("order:%d", orderID),
            fmt.Sprintf("user_orders:%d", updates["user_id"]),
        }
        
        for _, key := range cacheKeys {
            if err := redis.Del(key); err != nil {
                log.WithError(err).Warn("Failed to clear cache", "key", key)
            }
        }
    }()
    
    return nil
}
```

### 4. 错误处理规范

#### 业务错误分类
```go
// 订单相关错误
var (
    ErrOrderNotFound       = ecode.BoxOrderNotExistErr        // 订单不存在
    ErrOrderStatusInvalid  = ecode.BoxOrderStatusErr          // 订单状态错误
    ErrPickupCodeInvalid   = ecode.BoxOrderPickupCodeErr      // 取货码错误
    ErrRefundNotAllowed    = ecode.BoxOrderRefundErr          // 不允许退款
    ErrRefundAlreadyExists = ecode.BoxOrderRefundAlreadyErr   // 已存在退款记录
)

// 错误处理示例
func (e *Entry) handleOrderError(ctx *gin.Context, err error, orderID uint64) error {
    if err == gorm.ErrRecordNotFound {
        log.Ctx(ctx).Warn("Order not found", "orderID", orderID)
        return ErrOrderNotFound
    }
    
    if isBusinessError(err) {
        log.Ctx(ctx).Info("Business error", "error", err)
        return err // 直接返回业务错误
    }
    
    log.Ctx(ctx).WithError(err).Error("System error", "orderID", orderID)
    return ecode.SystemErr
}
```

#### 非关键操作处理
```go
func (e *Entry) processOrderWithNonCriticalOps(ctx *gin.Context, orderID uint64) error {
    // 1. 关键业务逻辑
    if err := e.updateOrderStatus(ctx, orderID, newStatus); err != nil {
        return err // 关键错误必须返回
    }
    
    // 2. 非关键操作 - 失败不影响主流程
    if err := e.sendOrderNotification(ctx, orderID); err != nil {
        log.Ctx(ctx).WithError(err).Warn("Failed to send notification", "orderID", orderID)
        // 不返回错误，继续执行
    }
    
    if err := e.updateOrderCache(ctx, orderID); err != nil {
        log.Ctx(ctx).WithError(err).Warn("Failed to update cache", "orderID", orderID)
        // 缓存更新失败不影响业务
    }
    
    return nil
}
```

### 5. 日志记录规范

#### 结构化日志
```go
// 订单操作日志
func (e *Entry) logOrderOperation(ctx *gin.Context, operation string, orderID uint64, details map[string]interface{}) {
    fields := log.Fields{
        "operation": operation,
        "orderID":   orderID,
    }
    
    for k, v := range details {
        fields[k] = v
    }
    
    log.Ctx(ctx).WithFields(fields).Info("Order operation completed")
}

// 使用示例
func (e *Entry) GeneratePickupCode(ctx *gin.Context, req *orderDto.PickupCodeGenerateReq) (*orderDto.PickupCodeGenerateRes, error) {
    // ... 业务逻辑
    
    // 记录操作日志
    e.logOrderOperation(ctx, "generate_pickup_code", orderModel.ID, map[string]interface{}{
        "pickupCode": pickupCode,
        "userID":     req.UserID,
        "expireTime": expireTime,
    })
    
    return res, nil
}
```

#### 错误日志增强
```go
func (e *Entry) logError(ctx *gin.Context, err error, operation string, orderID uint64) {
    log.Ctx(ctx).WithError(err).WithFields(log.Fields{
        "operation": operation,
        "orderID":   orderID,
        "stackTrace": string(debug.Stack()),  // 添加堆栈信息（调试时）
    }).Error("Order operation failed")
}
```

### 6. 性能优化建议

#### 数据库查询优化
```go
// 1. 使用索引友好的查询条件
func (e *Entry) findOrdersByUserID(ctx *gin.Context, userID uint64, limit int) (orderDao.ModelList, error) {
    filter := &orderDao.Filter{
        UserID: userID,
        // 使用索引字段排序
        OrderByCreatedAt: "DESC",
    }
    
    return e.OrderRepo.FindByFilter(ctx, filter, limit)
}

// 2. 分页查询使用游标而不是OFFSET（对于大数据量）
func (e *Entry) findOrdersWithCursor(ctx *gin.Context, cursor uint64, limit int) (orderDao.ModelList, error) {
    filter := &orderDao.Filter{
        IDLessThan: cursor,  // 使用主键作为游标
    }
    
    return e.OrderRepo.FindByFilter(ctx, filter, limit)
}
```

#### 并发处理优化
```go
func (e *Entry) batchProcessOrders(ctx *gin.Context, orderIDs []uint64) error {
    // 使用 errgroup 控制并发数量
    g, ctx := errgroup.WithContext(ctx)
    g.SetLimit(10) // 限制并发数
    
    for _, orderID := range orderIDs {
        orderID := orderID // 避免闭包问题
        g.Go(func() error {
            return e.processOrder(ctx, orderID)
        })
    }
    
    return g.Wait()
}
```

## 注意事项和限制

### 1. 已知技术债务
- **支付宝退款功能未实现**: `processAlipayRefundWithTx` 方法仅有 TODO 标记
- **余额退款功能未实现**: `processBalanceRefundWithTx` 方法仅有 TODO 标记
- **库存管理**: 需要与商品服务协调，确保库存一致性

### 2. 性能考量
- **取货码有效期**: Redis 中设置7天过期时间，避免内存泄漏
- **批量查询**: 在管理后台订单列表中使用批量查询避免 N+1 问题
- **事务范围**: 保持事务尽可能小，避免长时间锁定

### 3. 安全注意事项
- **用户权限验证**: 所有订单操作都验证 `order.UserID == request.UserID`
- **管理员权限**: 管理后台操作需要相应的角色权限验证
- **支付回调验证**: 微信支付回调使用官方解密方法验证签名

### 4. 扩展建议
- **订单状态机**: 可考虑实现状态机模式管理订单状态转换
- **事件驱动**: 订单状态变更可发送事件，便于其他服务监听
- **缓存预热**: 热门订单数据可预加载到缓存中
- **分库分表**: 订单量大时可按用户ID或时间进行分库分表

## 总结

订单服务是业务核心模块，包含了完整的订单生命周期管理、多支付方式处理、取货码系统等复杂业务逻辑。代码遵循了良好的分层架构，使用了依赖注入、事务管理、批量查询优化等最佳实践。在扩展新功能时，建议参考现有的代码模式，保持架构一致性。