package order

import (
	"sync"

	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/admin/account"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	spuBatch "blind_box/app/dao/goods/spu_batch"
	stockLogDao "blind_box/app/dao/goods/stock_log"
	deliveryDao "blind_box/app/dao/order/delivery"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	shopDao "blind_box/app/dao/resource/shop"
	userDao "blind_box/app/dao/user"
	addrDao "blind_box/app/dao/user/addr"
	userLogDao "blind_box/app/dao/user/user_log"
	userWxpay "blind_box/app/dao/user/wxpay"
	callbackLog "blind_box/app/dao/wechat/callback_log"
	refundLogDao "blind_box/app/dao/wechat/refund_log"
	orderDto "blind_box/app/dto/order"

	payLog "blind_box/app/dao/wechat/pay_log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/wechat/v3"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	WxAppletSrv

	Order
	AdminOrder
}

type Order interface {
	OrderStatus(ctx *gin.Context, req *orderDto.OrderStatusReq) (*orderDto.OrderStatusRes, error)
}

type AdminOrder interface {
	// 管理后台订单列表
	AdminOrderList(ctx *gin.Context, req *orderDto.AdminOrderListReq) (*orderDto.AdminOrderListRes, error)

	// 管理后台订单详情
	AdminOrderDetail(ctx *gin.Context, req *orderDto.AdminOrderDetailReq) (*orderDto.AdminOrderDetailRes, error)

	// 发货管理
	AdminOrderDeliveryCreate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryCreateReq) (*orderDto.AdminOrderDeliveryCreateRes, error)
	AdminOrderDeliveryUpdate(ctx *gin.Context, req *orderDto.AdminOrderDeliveryUpdateReq) (*orderDto.AdminOrderDeliveryUpdateRes, error)
	AdminOrderDeliveryDetail(ctx *gin.Context, req *orderDto.AdminOrderDeliveryDetailReq) (*orderDto.AdminOrderDeliveryDetailRes, error)
	AdminOrderDeliveryList(ctx *gin.Context, req *orderDto.AdminOrderDeliveryListReq) (*orderDto.AdminOrderDeliveryListRes, error)

	// 取货码验证 + 发货单号验证
	AdminPickupCodeVerify(ctx *gin.Context, req *orderDto.AdminPickupCodeVerifyReq) (*orderDto.AdminPickupCodeVerifyRes, error)
	AdminTrackingNumberVerify(ctx *gin.Context, req *orderDto.AdminTrackingNumberVerifyReq) (*orderDto.AdminTrackingNumberVerifyRes, error)

	// 订单完成 + 发货状态更新
	AdminOrderComplete(ctx *gin.Context, req *orderDto.AdminOrderCompleteReq) (*orderDto.AdminOrderCompleteRes, error)
}

type WxAppletSrv interface {
	WxAppletPayNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error
	WxAppletRefundNotify(ctx *gin.Context, notifyReq *wechat.V3NotifyReq) error
}

// TODO替换
type Entry struct {
	UserRepo    *userDao.Entry
	UserLogRepo userLogDao.Repo
	AccountRepo account.Repo

	WxpayRepo         *userWxpay.Entry
	WxPayLogRepo      *payLog.Entry
	WxCallbackLogRepo *callbackLog.Entry
	RefundLogRepo     refundLogDao.Repo

	OrderRepo       orderDao.Repo
	OrderDetailRepo orderDetailDao.Repo
	DeliveryRepo    deliveryDao.Repo
	ActivityRepo    *actDao.Entry
	BoxActiveRepo   box_active_config.Repo
	BoxGoodsRepo    box_goods.Repo
	SpuBatchRepo    *spuBatch.Entry
	BatchRepo       *batchDao.Entry
	SkuRepo         *skuDao.Entry
	StockLogRepo    *stockLogDao.Entry

	ShopRepo shopDao.Repo
	AddrRepo *addrDao.Entry

	RedisCli *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		OrderRepo:       orderDao.GetRepo(),
		OrderDetailRepo: orderDetailDao.GetRepo(),
		DeliveryRepo:    deliveryDao.GetRepo(),
		ActivityRepo:    actDao.GetRepo(),
		BoxActiveRepo:   box_active_config.GetRepo(),
		BoxGoodsRepo:    box_goods.GetRepo(),
		AccountRepo:     account.GetRepo(),
		UserRepo:        userDao.GetRepo(),
		UserLogRepo:     userLogDao.GetRepo(),
		RedisCli:        redis.GetRedisClient(),
		SpuBatchRepo:    spuBatch.GetRepo(),
		BatchRepo:       batchDao.GetRepo(),
		SkuRepo:         skuDao.GetRepo(),
		StockLogRepo:    stockLogDao.GetRepo(),

		ShopRepo: shopDao.GetRepo(),
		AddrRepo: addrDao.GetRepo(),

		WxpayRepo:         userWxpay.GetRepo(),
		WxPayLogRepo:      payLog.GetRepo(),
		WxCallbackLogRepo: callbackLog.GetRepo(),
		RefundLogRepo:     refundLogDao.GetRepo(),
	}
}
