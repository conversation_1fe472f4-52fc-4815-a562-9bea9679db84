package order

import (
	"fmt"
	"strconv"

	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/box/box_active_config"
	"blind_box/app/dao/box/box_goods"
	batchDao "blind_box/app/dao/goods/batch"
	stockLogDao "blind_box/app/dao/goods/stock_log"
	deliveryDao "blind_box/app/dao/order/delivery"
	orderDao "blind_box/app/dao/order/order"
	orderDetailDao "blind_box/app/dao/order/order_detail"
	shopDao "blind_box/app/dao/resource/shop"
	addrDao "blind_box/app/dao/user/addr"
	refundLogDao "blind_box/app/dao/wechat/refund_log"
	commonDto "blind_box/app/dto/common"
	goodsDto "blind_box/app/dto/goods"
	orderDto "blind_box/app/dto/order"
	"blind_box/app/service/common"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"blind_box/pkg/util/decimalUtil"
	"blind_box/pkg/util/safe_random"

	"github.com/golang-module/carbon/v2"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (e *Entry) OrderStatus(ctx *gin.Context, req *orderDto.OrderStatusReq) (*orderDto.OrderStatusRes, error) {
	var (
		res        = &orderDto.OrderStatusRes{}
		orderModel *orderDao.Model
		err        error
	)

	orderModel, err = e.OrderRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderStatus FetchByID error")
		return nil, err
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("OrderStatus FetchByID orderModel is nil")
		return nil, ecode.BoxOrderNotExistErr
	}

	res.OrderStatus = orderModel.OrderStatus
	if orderModel.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
		res.IsSuccess = true
	}

	return res, nil
}

func (e *Entry) OrderDetail(ctx *gin.Context, req *orderDto.OrderDetailReq) (*orderDto.OrderDetailRes, error) {
	var (
		res = &orderDto.OrderDetailRes{}
		err error

		orderModel     *orderDao.Model
		actModel       = &actDao.Model{}
		boxActiveModel *box_active_config.Model
		tradeList      = make(orderDetailDao.ModelList, 0)
		goodsIDs       = make([]uint64, 0)
		skuIDs         = make([]uint64, 0)
		spuIDs         = make([]uint64, 0)
		batchIDs       = make([]uint64, 0)
		shopIDs        = make([]uint64, 0)
		shopMap        = make(map[uint64]*shopDao.Model)
		addressIDs     = make([]uint64, 0)
		addressMap     = make(map[uint64]*addrDao.Model)
		goodsMap       = make(map[uint64]*box_goods.Model)
		skuMap         = make(map[uint64]goodsDto.CrSkuItem)
		batchMap       = make(map[uint64]*batchDao.Model)
	)
	res.OrderDelivery = &orderDto.OrderDelivery{}
	res.GoodsInfo = make([]*orderDto.TradeGood, 0)

	if req.OrderID <= 0 {
		if req.OutTradeNo != "" {
			orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OutTradeNo)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("OrderDetail FetchByOutTradeNo error")
				return nil, err
			}
		} else {
			log.Ctx(ctx).Error("OrderDetail OrderID and OutTradeNo are empty")
			return nil, ecode.ParamErr
		}
	} else {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FetchByID error")
			return nil, err
		}
	}

	if orderModel == nil {
		log.Ctx(ctx).Error("OrderDetail FetchByOutTradeNo orderModel is nil")
		return nil, ecode.BoxOrderNotExistErr
	}
	req.OrderID = orderModel.ID

	if orderModel.UserID != req.UserID {
		log.Ctx(ctx).Error("OrderDetail FetchByID orderModel UserID not match")
		return nil, ecode.BoxOrderNotExistErr
	}

	if orderModel.DeliveryID == orderDao.DeliveryTypeExpress {
		addressIDs = append(addressIDs, orderModel.AddressID)
	} else if orderModel.DeliveryID == orderDao.DeliveryTypeStore {
		shopIDs = append(shopIDs, orderModel.ShopID)
	}

	// 判断是否为直接购买订单
	isDirect := orderModel.OrderType == uint32(orderDao.OrderTypeDirect)

	// 只有盲盒订单才需要查询盒子活动配置
	if !isDirect {
		actModel, err = e.ActivityRepo.FetchByID(ctx, orderModel.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FetchByID error")
			return nil, err
		}
		if actModel == nil {
			log.Ctx(ctx).Error("OrderDetail FetchByID actModel is nil")
			return nil, ecode.BoxOrderNotExistErr
		}
		boxActiveModel, err = e.BoxActiveRepo.FetchByActiveID(ctx, orderModel.ActiveID)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FetchByID error")
			return nil, err
		}
		// 容忍为空，避免直接购买时为空引起错误
		if boxActiveModel == nil {
			log.Ctx(ctx).Warn("OrderDetail FetchByID boxActiveModel is nil, maybe direct purchase order")
		}
	}

	orderRes := &orderDto.OrderInfo{
		ActiveId:       actModel.ID,
		ActiveImage:    actModel.Image,
		ActiveType:     actModel.ActType,
		ActiveName:     actModel.Title,
		LotteryUsedNum: orderModel.LotteryTotal - orderModel.LotteryLeft,
		BagId:          orderModel.BoxID,
		OrderId:        orderModel.ID,               // 订单ID
		OrderNo:        orderModel.OutTradeNo,       // 订单号
		OrderType:      orderModel.OrderType,        // 订单类型
		OrderStatus:    orderModel.OrderStatus,      // 订单状态
		OrderSubType:   0,                           // 订单子类型，暂时设为0
		CreatedAt:      orderModel.CreatedAt.Unix(), // 创建时间
		PayAt:          int64(orderModel.PayTime),   // 支付时间，直接使用时间戳
		FreightFee:     orderModel.FreightFee,       // 运费
	}
	res.ActiveTitle = orderRes.ActiveName
	res.BoxActive = boxActiveModel

	// 使用公共方法计算费用
	orderRes.UsedFee, orderRes.TotalFee, err = e.calculateOrderFee(ctx, orderModel)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderDetail fee calculation error")
		return nil, err
	}

	res.OrderInfo = orderRes

	tradeList, err = e.OrderDetailRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderID: orderModel.ID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderDetail FindByFilter error")
		return nil, err
	}

	// 收集商品ID、SKU ID、SPU ID
	goodsIDs = tradeList.GetGoodsIDs()
	skuIDs = tradeList.GetSkuIDs()
	spuIDs = tradeList.GetSpuIDs()
	batchIDs = tradeList.GetBatchIDs()
	shopIDs = lo.Uniq(shopIDs)
	addressIDs = lo.Uniq(addressIDs)
	res.OrderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
		DeliveryID: orderModel.DeliveryID,
	}

	if len(shopIDs) > 0 {
		// 批量查询店铺信息
		shopList, err := e.ShopRepo.FindByFilter(ctx, &shopDao.Filter{
			IDS: shopIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FindByFilter shop error")
			return nil, err
		}
		shopMap = shopList.GetIDMap()
		if shop, ok := shopMap[orderModel.ShopID]; ok && orderModel.ShopID != 0 {
			res.OrderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
				DeliveryID: orderModel.DeliveryID,
				ShopList: []goodsDto.CrSkuDeliveryShopItem{
					{
						ID:      shop.ID,
						Name:    shop.Name,
						Address: shop.Address,
						Mobile:  shop.Mobile,
					},
				},
			}
		}
	}

	if len(addressIDs) > 0 {
		// 批量查询地址信息
		addressList, err := e.AddrRepo.FindByFilter(ctx, &addrDao.Filter{
			IDS: addressIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FindByFilter address error")
			return nil, err
		}
		addressMap = addressList.GetIDMap()
		if address, ok := addressMap[orderModel.AddressID]; ok && orderModel.AddressID != 0 {
			res.OrderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
				DeliveryID: orderModel.DeliveryID,
			}
			res.OrderDelivery.Address = &commonDto.CommonUserAddrInfo{
				ID:        address.ID,
				UserID:    address.UserID,
				Consignee: address.Consignee,
				Mobile:    address.Mobile,
				Area:      address.Area,
				Address:   address.Address,
				IsDefault: address.IsDefault,
				CreatedAt: address.CreatedAt.Format(dbs.TimeDateFormatFull),
			}
		}
	}

	// 查询发货信息（用于填充快递单号和快递公司信息）
	// 如果订单是快递配送且状态为已发货，则查询发货记录
	if orderModel.DeliveryID == orderDao.DeliveryTypeExpress && orderModel.OrderStatus == uint32(orderDao.OrderStatusDelivery) {
		delivery, err := e.DeliveryRepo.FetchByOrderID(ctx, orderModel.ID)
		if err == nil && delivery != nil {
			// 填充快递信息
			res.OrderDelivery.ExpressCode = delivery.TrackingNumber
			res.OrderDelivery.ExpressCompany = delivery.ExpressCompany
		}
	}

	// 获取取货码（仅针对到店取货订单）
	if orderModel.DeliveryID == orderDao.DeliveryTypeStore {
		pickupCodeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)
		if pickupCodeBytes, err := redis.Get(pickupCodeKey); err == nil && len(pickupCodeBytes) > 0 {
			res.OrderDelivery.PickupCode = string(pickupCodeBytes)
		}
	}

	if len(goodsIDs) > 0 {
		var goodsList box_goods.ModelList
		goodsList, err = e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			ActiveID: actModel.ID,
			IDs:      goodsIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FindByFilter error")
			return nil, err
		}

		// 合并 SPU/SKU 列表（盲盒模式需要）
		spuIDs = append(spuIDs, goodsList.GetSpuIDs()...)
		skuIDs = append(skuIDs, goodsList.GetSkuIDs()...)

		goodsMap = goodsList.GetIDMap()
	}

	skuIDs = lo.Uniq(skuIDs)
	spuIDs = lo.Uniq(spuIDs)

	if len(skuIDs) > 0 {
		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail GetCommonSkuResp error")
			return nil, err
		}
	}

	if len(batchIDs) > 0 {
		batchList, err := e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{
			Ids: batchIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderDetail FindByFilter batch error")
			return nil, err
		}
		batchMap = batchList.GetIDMap()
	}

	for _, v := range tradeList {
		tmp := &orderDto.TradeGood{
			Id:       v.ID,
			GoodId:   v.GoodsID,
			GoodName: v.GoodsName,
			Remark:   v.Remark,
			Quantity: v.Quantity, // 商品购买数量
		}

		if isDirect {
			// 直接购买模式使用 SKU 信息
			if skuItem, ok := skuMap[v.SkuID]; ok {
				tmp.GoodId = skuItem.SkuID
				tmp.GoodName = skuItem.SkuTitle
				tmp.GoodImage = skuItem.SkuCover
				tmp.GoodStatus = skuItem.BatchSellType
				tmp.GoodDetail = &skuItem
			} else {
				if goods, ok := goodsMap[v.GoodsID]; ok && v.GoodsID != 0 {
					tmp.GoodLevel = v.GoodsLevel
					tmp.GoodImage = goods.GoodsCover

					if skuItem, ok := skuMap[goods.SkuID]; ok {
						tmp.GoodStatus = skuItem.BatchSellType
						tmp.GoodDetail = &skuItem
					}

					if t, err := strconv.Atoi(v.BoxSlot); err == nil {
						tmp.BoxSlot = uint32(t)
					}
					tmp.BoxId = v.BoxID
					tmp.BoxNo = v.BoxNo
				}
			}
		}

		if batch, ok := batchMap[v.BatchID]; ok && v.BatchID != 0 {
			tmp.BatchInfo = &goodsDto.AdminBatchListItem{
				ID:        batch.ID,
				Title:     batch.Title,
				SellType:  batch.SellType,
				Desc:      batch.Desc,
				Status:    batch.Status,
				CreatedAt: batch.CreatedAt.Format(dbs.TimeDateFormatFull),
			}
		}

		res.GoodsInfo = append(res.GoodsInfo, tmp)
	}

	res.OrderPayInfo = &orderDto.OrderPay{
		UsedFee:          orderModel.UsedFee,
		CouponFee:        orderModel.CouponFee,
		PointsFee:        orderModel.PointsFee, // 积分抵扣金额
		PointsUse:        orderModel.PointsUse, // 消耗的积分数额
		PayMethod:        orderModel.PayMethod,
		OrderNo:          orderModel.OutTradeNo,
		OrderStatus:      orderModel.OrderStatus,
		TransactionId:    orderModel.TransactionId,
		CreatedTime:      orderModel.CreatedAt.Format(dbs.TimeDateFormatFull),
		PayExpireTime:    orderModel.PayExpireTime,
		PayExpireTimeStr: carbon.CreateFromTimestamp(orderModel.PayExpireTime).ToDateTimeString(),
	}

	if orderRes.TotalFee > orderRes.UsedFee {
		res.OrderPayInfo.RefundId = orderModel.ID
	}

	if !lo.Contains([]orderDao.OrderStatus{
		orderDao.OrderStatusCreated,
		orderDao.OrderStatusPayIng,
	}, orderDao.OrderStatus(orderModel.OrderStatus)) {
		orderRes.TotalFee = e.getOrderTotalFee(orderModel)
		res.OrderPayInfo.UsedFee = orderRes.TotalFee
		res.OrderInfo.UsedFee = orderRes.TotalFee
	}

	return res, nil
}

func (e *Entry) OrderList(ctx *gin.Context, req *orderDto.OrderListReq) (*orderDto.OrderListRes, error) {
	var (
		res = &orderDto.OrderListRes{}
		err error
	)

	// 构建查询过滤条件
	filter := &orderDao.Filter{
		UserID: req.UserID,
		Sort: []clause.OrderByColumn{
			{Column: clause.Column{Name: "created_at"}, Desc: true},
		},
	}

	// 根据前端传入的条件设置过滤器
	if req.OrderType > 0 {
		filter.OrderType = req.OrderType
	}

	// 处理订单状态过滤（支持单个或多个状态）
	if len(req.OrderStatusList) > 0 {
		filter.OrderStatusList = req.OrderStatusList
	}

	if req.OutTradeNo != "" {
		filter.OutTradeNo = req.OutTradeNo
	}
	if req.CreatedAtStart > 0 {
		filter.CreatedAtStart = req.CreatedAtStart
	}
	if req.CreatedAtEnd > 0 {
		filter.CreatedAtEnd = req.CreatedAtEnd
	}

	// 分页查询订单列表
	count, orderList, err := e.OrderRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderList DataPageList error")
		return nil, err
	}

	res.Count = count
	if count == 0 {
		res.List = make([]*orderDto.OrderListItem, 0)
		return res, nil
	}

	// 批量查询相关数据以避免 N+1 问题
	orderIDs := orderList.GetIDs()
	activeIDs := orderList.GetActiveIDs()
	shopIDs := orderList.GetShopIDs()
	addressIDs := orderList.GetAddressIDs()

	// 批量查询交易详情
	tradeList, err := e.OrderDetailRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderIDs: orderIDs,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter trade error")
		return nil, err
	}

	// 批量查询活动信息
	var activeMap = make(map[uint64]*actDao.Model)
	if len(activeIDs) > 0 {
		activeList, err := e.ActivityRepo.FindByFilter(ctx, &actDao.Filter{
			Ids: activeIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter activity error")
			return nil, err
		}
		activeMap = activeList.GetIDMap()
	}

	// 批量查询盒子活动配置（只有盲盒订单需要）
	var boxActiveMap = make(map[uint64]*box_active_config.Model)
	blindBoxActiveIDs := make([]uint64, 0)
	for _, order := range orderList {
		if order.OrderType == uint32(orderDao.OrderTypeBox) {
			blindBoxActiveIDs = append(blindBoxActiveIDs, order.ActiveID)
		}
	}
	if len(blindBoxActiveIDs) > 0 {
		blindBoxActiveIDs = lo.Uniq(blindBoxActiveIDs)
		for _, activeID := range blindBoxActiveIDs {
			boxActive, err := e.BoxActiveRepo.FetchByActiveID(ctx, activeID)
			if err == nil && boxActive != nil {
				boxActiveMap[activeID] = boxActive
			}
		}
	}

	// 收集商品和SKU信息
	goodsIDs := tradeList.GetGoodsIDs()
	skuIDs := tradeList.GetSkuIDs()
	spuIDs := tradeList.GetSpuIDs()
	batchIDs := tradeList.GetBatchIDs()

	var shopMap = make(map[uint64]*shopDao.Model)
	if len(shopIDs) > 0 {
		// 批量查询店铺信息
		shopList, err := e.ShopRepo.FindByFilter(ctx, &shopDao.Filter{
			IDS: shopIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter shop error")
			return nil, err
		}
		shopMap = shopList.GetIDMap()
	}

	var addressMap = make(map[uint64]*addrDao.Model)
	if len(addressIDs) > 0 {
		// 批量查询地址信息
		addressList, err := e.AddrRepo.FindByFilter(ctx, &addrDao.Filter{
			IDS: addressIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter address error")
			return nil, err
		}
		addressMap = addressList.GetIDMap()
	}

	// 批量查询盲盒商品信息
	var goodsMap = make(map[uint64]*box_goods.Model)
	if len(goodsIDs) > 0 {
		goodsList, err := e.BoxGoodsRepo.FindByFilter(ctx, &box_goods.Filter{
			IDs: goodsIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter goods error")
			return nil, err
		}

		// 合并 SPU/SKU 列表（盲盒模式需要）
		spuIDs = append(spuIDs, goodsList.GetSpuIDs()...)
		skuIDs = append(skuIDs, goodsList.GetSkuIDs()...)
		goodsMap = goodsList.GetIDMap()
	}

	// 批量查询SKU信息
	var skuMap = make(map[uint64]goodsDto.CrSkuItem)
	if len(skuIDs) > 0 {
		skuIDs = lo.Uniq(skuIDs)
		spuIDs = lo.Uniq(spuIDs)
		skuMap, err = common.GetService().GetCommonSkuResp(ctx, spuIDs, skuIDs)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList GetCommonSkuResp error")
			return nil, err
		}
	}

	// 批量查询发货信息
	var deliveryMap = make(map[uint64]*deliveryDao.Model)
	if len(orderIDs) > 0 {
		deliveryFilter := &deliveryDao.Filter{OrderIDs: orderIDs}
		deliveryList, err := e.DeliveryRepo.FindByFilter(ctx, deliveryFilter)
		if err == nil {
			for _, delivery := range deliveryList {
				deliveryMap[delivery.OrderID] = delivery
			}
		}
	}

	var batchMap = make(map[uint64]*batchDao.Model)
	if len(batchIDs) > 0 {
		batchList, err := e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{
			Ids: batchIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList FindByFilter batch error")
			return nil, err
		}
		batchMap = batchList.GetIDMap()
	}

	// 组织交易详情数据按订单ID分组
	tradeOrderMap := tradeList.GetOrderMap()

	// 构建返回结果
	res.List = make([]*orderDto.OrderListItem, 0, len(orderList))
	for _, order := range orderList {
		// 构建基本订单信息
		isDirect := order.OrderType == uint32(orderDao.OrderTypeDirect)

		orderRes := &orderDto.OrderInfo{
			ActiveId:       order.ActiveID,
			ActiveType:     0, // 默认值，稍后从活动信息中获取
			ActiveName:     "",
			LotteryUsedNum: order.LotteryTotal - order.LotteryLeft,
			BagId:          order.BoxID,
			OrderId:        order.ID,               // 订单ID
			OrderNo:        order.OutTradeNo,       // 订单号
			OrderType:      order.OrderType,        // 订单类型
			OrderStatus:    order.OrderStatus,      // 订单状态
			OrderSubType:   0,                      // 订单子类型，暂时设为0
			CreatedAt:      order.CreatedAt.Unix(), // 创建时间
			PayAt:          int64(order.PayTime),   // 支付时间，直接使用时间戳
			FreightFee:     order.FreightFee,       // 运费
		}

		// 设置活动信息
		if activeModel, ok := activeMap[order.ActiveID]; ok {
			orderRes.ActiveImage = activeModel.Image
			orderRes.ActiveType = activeModel.ActType
			orderRes.ActiveName = activeModel.Title
		}

		// 使用公共方法计算费用信息
		orderRes.UsedFee, orderRes.TotalFee, err = e.calculateOrderFee(ctx, order)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("OrderList fee calculation error")
			continue
		}

		// 构建商品信息
		goodsInfo := make([]*orderDto.TradeGood, 0)
		if trades, ok := tradeOrderMap[order.ID]; ok {
			for _, v := range trades {
				tmp := &orderDto.TradeGood{
					Id:       v.ID,
					GoodId:   v.GoodsID,
					GoodName: v.GoodsName,
					Remark:   v.Remark,
					Quantity: v.Quantity, // 商品购买数量
				}

				if isDirect {
					// 直接购买模式使用 SKU 信息
					if skuItem, ok := skuMap[v.SkuID]; ok {
						tmp.GoodId = skuItem.SkuID
						tmp.GoodName = skuItem.SkuTitle
						tmp.GoodImage = skuItem.SkuCover
						tmp.GoodStatus = skuItem.BatchSellType
						tmp.GoodDetail = &skuItem
					}
				} else {
					// 盲盒模式
					if goods, ok := goodsMap[v.GoodsID]; ok && v.GoodsID != 0 {
						tmp.GoodLevel = v.GoodsLevel
						tmp.GoodImage = goods.GoodsCover

						if skuItem, ok := skuMap[goods.SkuID]; ok {
							tmp.GoodStatus = skuItem.BatchSellType
							tmp.GoodDetail = &skuItem
						}

						if t, err := strconv.Atoi(v.BoxSlot); err == nil {
							tmp.BoxSlot = uint32(t)
						}
						tmp.BoxId = v.BoxID
						tmp.BoxNo = v.BoxNo
					}
				}

				if batch, ok := batchMap[v.BatchID]; ok && v.BatchID != 0 {
					tmp.BatchInfo = &goodsDto.AdminBatchListItem{
						ID:        batch.ID,
						Title:     batch.Title,
						SellType:  batch.SellType,
						Desc:      batch.Desc,
						Status:    batch.Status,
						CreatedAt: batch.CreatedAt.Format(dbs.TimeDateFormatFull),
					}
				}

				goodsInfo = append(goodsInfo, tmp)
			}
		}

		orderDelivery := &orderDto.OrderDelivery{
			Delivery: &goodsDto.CrSkuDeliveryItem{
				DeliveryID: order.DeliveryID,
			},
		}
		if order.DeliveryID == orderDao.DeliveryTypeExpress {
			if address, ok := addressMap[order.AddressID]; ok && order.AddressID != 0 {
				orderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
					DeliveryID: order.DeliveryID,
				}
				orderDelivery.Address = &commonDto.CommonUserAddrInfo{
					ID:        address.ID,
					UserID:    address.UserID,
					Consignee: address.Consignee,
					Mobile:    address.Mobile,
					Area:      address.Area,
					Address:   address.Address,
					IsDefault: address.IsDefault,
					CreatedAt: address.CreatedAt.Format(dbs.TimeDateFormatFull),
				}
			}

			// 查询发货信息填充快递单号和快递公司
			if delivery, ok := deliveryMap[order.ID]; ok && delivery != nil {
				orderDelivery.ExpressCode = delivery.TrackingNumber
				orderDelivery.ExpressCompany = delivery.ExpressCompany
			}
		} else if order.DeliveryID == orderDao.DeliveryTypeStore {
			if shop, ok := shopMap[order.ShopID]; ok && order.ShopID != 0 {
				orderDelivery.Delivery = &goodsDto.CrSkuDeliveryItem{
					DeliveryID: order.DeliveryID,
					ShopList: []goodsDto.CrSkuDeliveryShopItem{
						{
							ID:      shop.ID,
							Name:    shop.Name,
							Address: shop.Address,
							Mobile:  shop.Mobile,
						},
					},
				}
			}

			// 获取取货码（仅针对到店取货订单）
			pickupCodeKey := redis.GetPickupCodeKey(order.OutTradeNo)
			if pickupCodeBytes, err := redis.Get(pickupCodeKey); err == nil && len(pickupCodeBytes) > 0 {
				orderDelivery.PickupCode = string(pickupCodeBytes)
			}
		}

		// 构建支付信息
		orderPayInfo := &orderDto.OrderPay{
			UsedFee:          order.UsedFee,
			CouponFee:        order.CouponFee,
			PointsFee:        order.PointsFee, // 积分抵扣金额
			PointsUse:        order.PointsUse, // 消耗的积分数额
			PayMethod:        order.PayMethod,
			OrderNo:          order.OutTradeNo,
			OrderStatus:      order.OrderStatus,
			TransactionId:    order.TransactionId,
			CreatedTime:      order.CreatedAt.Format(dbs.TimeDateFormatFull),
			PayExpireTime:    order.PayExpireTime,
			PayExpireTimeStr: carbon.CreateFromTimestamp(order.PayExpireTime).String(),
		}

		if orderRes.TotalFee > orderRes.UsedFee {
			orderPayInfo.RefundId = order.ID
		}

		if order.OrderStatus == uint32(orderDao.OrderStatusPayOk) {
			orderRes.TotalFee = e.getOrderTotalFee(order)
			orderPayInfo.UsedFee = orderRes.TotalFee
			orderRes.UsedFee = orderRes.TotalFee
		}

		// 构建完整的订单详情
		orderDetail := &orderDto.OrderDetailRes{
			OrderInfo:     orderRes,
			GoodsInfo:     goodsInfo,
			OrderPayInfo:  orderPayInfo,
			ActiveTitle:   orderRes.ActiveName,
			BoxActive:     boxActiveMap[order.ActiveID],
			OrderDelivery: orderDelivery,
		}

		// 添加到结果列表
		res.List = append(res.List, &orderDto.OrderListItem{
			OrderDetailRes: orderDetail,
		})
	}

	return res, nil
}

// OrderCancel 订单取消（小程序端）
func (e *Entry) OrderCancel(ctx *gin.Context, req *orderDto.OrderCancelReq) (*orderDto.OrderCancelRes, error) {
	// 1. 参数校验
	if req.OrderID == 0 && req.OrderNo == "" {
		return nil, ecode.ParamErr
	}

	// 2. 查询订单
	var orderModel *orderDao.Model
	var err error
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("OrderCancel fetch order error")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 3. 权限校验
	if orderModel.UserID != req.UserID {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 4. 调用取消核心逻辑
	return e.cancelOrder(ctx, orderModel, req.IsRefund, req.IsReturn, req.Reason, false)
}

// AdminOrderCancel 订单取消（管理后台）
func (e *Entry) AdminOrderCancel(ctx *gin.Context, req *orderDto.AdminOrderCancelReq) (*orderDto.AdminOrderCancelRes, error) {
	// 1. 参数校验
	if req.OrderID == 0 && req.OrderNo == "" {
		return nil, ecode.ParamErr
	}

	// 2. 查询订单
	var orderModel *orderDao.Model
	var err error
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminOrderCancel fetch order error")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 3. 管理员直接调用取消逻辑
	res, err := e.cancelOrder(ctx, orderModel, req.IsRefund, req.IsReturn, req.Reason, true)
	if err != nil {
		return nil, err
	}

	return &orderDto.AdminOrderCancelRes{
		OrderID: res.OrderID,
		Message: res.Message,
	}, nil
}

// cancelOrder 订单取消核心逻辑（带行锁+状态二次校验）
func (e *Entry) cancelOrder(ctx *gin.Context, orderModel *orderDao.Model, isRefund, isReturn bool, reason string, isAdmin bool) (*orderDto.OrderCancelRes, error) {
	// 1. 启动事务并加行锁，确保订单状态一致
	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 行级锁获取最新状态
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(orderModel, orderModel.ID).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 2. 状态校验
	if !e.canCancelOrder(orderModel.OrderStatus) {
		tx.Rollback()
		return nil, ecode.BoxOrderCantCancelErr
	}

	// 3. 计算是否需要退款（即使上层未要求，当已支付/已完成时仍强制退款）
	needRefund := isRefund
	// 非管理员调用且订单已支付/完成时默认强制退款；管理员调用则尊重传参
	if !isAdmin && !needRefund && (orderModel.OrderStatus == uint32(orderDao.OrderStatusPayOk) || orderModel.OrderStatus == uint32(orderDao.OrderStatusDone)) {
		needRefund = true
	}

	// 4. 分支处理
	var (
		message string
		err     error
	)

	switch orderModel.OrderStatus {
	case uint32(orderDao.OrderStatusCreated), uint32(orderDao.OrderStatusPayIng):
		// 未支付订单
		message, err = e.handleUnpaidOrderCancel(ctx, tx, orderModel, isReturn, reason)
	case uint32(orderDao.OrderStatusPayOk), uint32(orderDao.OrderStatusDone):
		// 已支付/已完成订单
		message, err = e.handlePaidOrderCancel(ctx, tx, orderModel, needRefund, isReturn, reason)
	default:
		tx.Rollback()
		return nil, ecode.BoxOrderStatusErr
	}

	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("cancelOrder handle error")
		return nil, err
	}

	// 5. 更新订单状态为取消
	if err = e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, orderModel.ID, map[string]interface{}{
		"order_status": uint32(orderDao.OrderStatusCancel),
		"remark":       reason,
	}); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("cancelOrder update order status error")
		return nil, err
	}

	// 6. 提交事务
	if err = tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("cancelOrder commit transaction error")
		return nil, err
	}

	return &orderDto.OrderCancelRes{
		OrderID: orderModel.ID,
		Message: message,
	}, nil
}

// canCancelOrder 检查订单是否可以取消
func (e *Entry) canCancelOrder(orderStatus uint32) bool {
	// 只有以下状态的订单可以取消：
	// 1-订单生成, 2-支付中, 3-支付成功, 7-订单完成
	return orderStatus == uint32(orderDao.OrderStatusCreated) ||
		orderStatus == uint32(orderDao.OrderStatusPayIng) ||
		orderStatus == uint32(orderDao.OrderStatusPayOk) ||
		orderStatus == uint32(orderDao.OrderStatusDone) // 订单完成
}

// handleUnpaidOrderCancel 处理未支付订单取消
func (e *Entry) handleUnpaidOrderCancel(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model, isReturn bool, reason string) (string, error) {
	var message = "未支付订单取消成功"

	// 如果需要返还库存
	if isReturn {
		err := e.returnOrderStock(ctx, tx, orderModel)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("handleUnpaidOrderCancel return stock error")
			return "", err
		}
		message += "，库存已返还"
	}

	return message, nil
}

// handlePaidOrderCancel 处理已支付订单取消
func (e *Entry) handlePaidOrderCancel(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model, isRefund, isReturn bool, reason string) (string, error) {
	var message = "已支付订单取消成功"

	// 如果需要退款
	if isRefund {
		err := e.processOrderRefund(ctx, tx, orderModel)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("handlePaidOrderCancel refund error")
			return "", err
		}
		message += "，退款已处理"
	}

	// 如果需要返还库存
	if isReturn {
		err := e.returnOrderStock(ctx, tx, orderModel)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("handlePaidOrderCancel return stock error")
			return "", err
		}
		message += "，库存已返还"
	}

	return message, nil
}

// returnOrderStock 返还订单库存
func (e *Entry) returnOrderStock(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model) error {
	// 查询订单详情
	orderDetails, err := e.OrderDetailRepo.FindByFilter(ctx, &orderDetailDao.Filter{
		OrderID: orderModel.ID,
	})
	if err != nil {
		return err
	}

	// 根据订单类型处理库存返还
	if orderModel.OrderType == uint32(orderDao.OrderTypeBox) {
		// 盲盒订单：返还盲盒库存
		return e.returnBoxStock(ctx, tx, orderModel, orderDetails)
	} else if orderModel.OrderType == uint32(orderDao.OrderTypeDirect) {
		// 直购订单：返还商品库存
		return e.returnDirectStock(ctx, tx, orderModel, orderDetails)
	}

	return nil
}

// returnBoxStock 返还盲盒库存
func (e *Entry) returnBoxStock(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model, orderDetails orderDetailDao.ModelList) error {
	// 盲盒订单需要返还盲盒商品的库存
	log.Ctx(ctx).Info("returnBoxStock for box order", "orderID", orderModel.ID)

	for _, detail := range orderDetails {
		if detail.GoodsID > 0 && detail.Quantity > 0 {
			// 解锁库存：将 lock_stock 中的数量返还到 cur_stock
			updateData := map[string]interface{}{
				"lock_stock": gorm.Expr("lock_stock - ?", detail.Quantity),
				"cur_stock":  gorm.Expr("cur_stock + ?", detail.Quantity),
			}

			if err := e.BoxGoodsRepo.UpdateMapByIDWithTx(ctx, tx, detail.GoodsID, updateData); err != nil {
				log.Ctx(ctx).WithError(err).Error("returnBoxStock update stock failed",
					"orderID", orderModel.ID,
					"goodsID", detail.GoodsID,
					"quantity", detail.Quantity)
				return err
			}

			log.Ctx(ctx).Info("returnBoxStock success",
				"orderID", orderModel.ID,
				"goodsID", detail.GoodsID,
				"quantity", detail.Quantity)
		}
	}

	return nil
}

// changeBoxGoodsStock 这里需要定义和box service一样的结构体
type changeBoxGoodsStock struct {
	ChangeNumber    int64  `json:"changeNumber"` //库存数量
	UserId          uint64 `json:"userId"`       //用户id
	TradeDetailList []*orderDetailDao.Model
}

// returnDirectStock 返还直购商品库存
func (e *Entry) returnDirectStock(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model, orderDetails orderDetailDao.ModelList) error {
	// 直购订单返还商品库存
	for _, detail := range orderDetails {
		if detail.SkuID > 0 && detail.Quantity > 0 {
			err := e.returnSkuStock(ctx, tx, detail.SkuID, detail.Quantity, orderModel.UserID, orderModel.OrderStatus)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("returnDirectStock failed",
					"orderID", orderModel.ID,
					"skuID", detail.SkuID,
					"quantity", detail.Quantity)
				return err
			}

			log.Ctx(ctx).Info("returnDirectStock success",
				"orderID", orderModel.ID,
				"skuID", detail.SkuID,
				"quantity", detail.Quantity)
		}
	}

	return nil
}

// returnSkuStock 返还SKU库存
func (e *Entry) returnSkuStock(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32, userID uint64, orderStatus uint32) error {
	// 获取SKU锁定，确保原子性操作
	lockKey := fmt.Sprintf("sku_stock_lock_%d", skuID)
	// 3 秒过期时间，使用 Duration 传参避免纳秒误差
	if err := e.RedisCli.RetryLock(ctx, lockKey, redis.LockMaxRetryTime); err != nil {
		return fmt.Errorf("failed to acquire lock for SkuID %d: %w", skuID, err)
	}
	defer e.RedisCli.Unlock(ctx, lockKey)

	// 获取当前SKU信息
	skuModel, err := e.SkuRepo.FetchByID(ctx, skuID)
	if err != nil {
		return fmt.Errorf("failed to fetch SKU %d: %w", skuID, err)
	}
	if skuModel == nil {
		return fmt.Errorf("SKU %d not found", skuID)
	}

	// 根据订单状态区分库存返还逻辑
	var stockUpdates map[string]interface{}
	var stockType string

	// 记录原始请求数量，用于异常检测
	originalQuantity := quantity

	switch orderStatus {
	case 1, 2: // 订单生成、支付中 - 从锁定库存返还
		if skuModel.LockNum < quantity {
			log.Ctx(ctx).Warn("returnSkuStock: LockNum less than quantity - STOCK_INCONSISTENCY_DETECTED",
				"skuID", skuID,
				"lockNum", skuModel.LockNum,
				"requestedQuantity", quantity,
				"userID", userID,
				"orderStatus", orderStatus)
			// 如果锁定库存不足，按实际可返还数量处理
			quantity = skuModel.LockNum
		}

		if quantity == 0 {
			log.Ctx(ctx).Info("returnSkuStock: no locked stock to return", "skuID", skuID)
			return nil
		}

		stockUpdates = map[string]interface{}{
			"lock_num": skuModel.LockNum - quantity, // 减少锁定库存
		}
		stockType = "unlock"

	case 3, 7: // 支付成功、订单完成 - 从使用库存返还到退款库存
		if skuModel.UsedNum < quantity {
			log.Ctx(ctx).Warn("returnSkuStock: UsedNum less than quantity - STOCK_INCONSISTENCY_DETECTED",
				"skuID", skuID,
				"usedNum", skuModel.UsedNum,
				"requestedQuantity", quantity,
				"userID", userID,
				"orderStatus", orderStatus)
			// 如果已使用库存不足，按实际可返还数量处理
			quantity = skuModel.UsedNum
		}

		if quantity == 0 {
			log.Ctx(ctx).Info("returnSkuStock: no used stock to return", "skuID", skuID)
			return nil
		}

		stockUpdates = map[string]interface{}{
			"used_num":   skuModel.UsedNum - quantity,   // 减少已使用库存
			"refund_num": skuModel.RefundNum + quantity, // 增加退款库存
		}
		stockType = "refund"

	default:
		return fmt.Errorf("unsupported order status for stock return: %d", orderStatus)
	}

	if err := e.SkuRepo.UpdateMapByIDWithTx(ctx, tx, skuID, stockUpdates); err != nil {
		return fmt.Errorf("failed to update SKU stock for SkuID %d: %w", skuID, err)
	}

	// 执行库存一致性校验
	err = e.validateStockConsistency(ctx, skuID, userID)
	if err != nil {
		// 一致性校验失败不影响主流程，只记录错误
		log.Ctx(ctx).WithError(err).Error("returnSkuStock: stock consistency validation failed",
			"skuID", skuID, "userID", userID)
	}

	// 记录库存变更日志
	err = e.createStockLog(ctx, tx, skuID, quantity, userID, fmt.Sprintf("order_cancel_%s", stockType))
	if err != nil {
		// 库存日志失败不应该影响主流程，只记录错误
		log.Ctx(ctx).WithError(err).Error("returnSkuStock: failed to create stock log",
			"skuID", skuID, "quantity", quantity)
	}

	// 记录详细的完成日志，包含异常检测结果
	logLevel := "Info"
	if originalQuantity != quantity {
		logLevel = "Warn"
	}

	log.Ctx(ctx).WithField("level", logLevel).Info("returnSkuStock completed",
		"skuID", skuID,
		"originalQuantity", originalQuantity,
		"actualQuantity", quantity,
		"quantityAdjusted", originalQuantity != quantity,
		"stockType", stockType,
		"orderStatus", orderStatus,
		"userID", userID)

	return nil
}

// validateStockConsistency 验证库存数据一致性
func (e *Entry) validateStockConsistency(ctx *gin.Context, skuID uint64, userID uint64) error {
	skuModel, err := e.SkuRepo.FetchByID(ctx, skuID)
	if err != nil {
		return fmt.Errorf("failed to fetch SKU for consistency check: %w", err)
	}
	if skuModel == nil {
		return fmt.Errorf("SKU %d not found for consistency check", skuID)
	}

	// 基本一致性检查：总库存 + 退款库存 >= 锁定库存 + 已使用库存
	totalAvailable := skuModel.Total + skuModel.RefundNum
	totalUsed := skuModel.LockNum + skuModel.UsedNum

	if totalAvailable < totalUsed {
		log.Ctx(ctx).Error("CRITICAL_STOCK_INCONSISTENCY_DETECTED",
			"skuID", skuID,
			"userID", userID,
			"total", skuModel.Total,
			"refundNum", skuModel.RefundNum,
			"lockNum", skuModel.LockNum,
			"usedNum", skuModel.UsedNum,
			"totalAvailable", totalAvailable,
			"totalUsed", totalUsed,
			"deficit", totalUsed-totalAvailable)
		return fmt.Errorf("stock inconsistency: available %d < used %d", totalAvailable, totalUsed)
	}

	// 记录正常的一致性检查（调试级别）
	log.Ctx(ctx).Debug("stock consistency validated",
		"skuID", skuID,
		"total", skuModel.Total,
		"refundNum", skuModel.RefundNum,
		"lockNum", skuModel.LockNum,
		"usedNum", skuModel.UsedNum,
		"availableStock", totalAvailable-totalUsed)

	return nil
}

// createStockLog 创建库存变更日志
func (e *Entry) createStockLog(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32, userID uint64, changeType string) error {
	// 获取SKU信息用于记录变更前后的值
	skuModel, err := e.SkuRepo.FetchByID(ctx, skuID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("createStockLog: failed to fetch SKU", "skuID", skuID)
		// 库存日志失败不应影响主流程，只记录错误并返回
		return nil
	}

	// 创建库存变更日志记录
	stockLogModel := &stockLogDao.Model{
		SpuId:    skuModel.SpuId,
		SkuId:    skuID,
		UserId:   userID,
		LogType:  uint32(stockLogDao.LtOrderCancel),
		SubType:  0, // 可以根据changeType细分
		EntityId: 0, // 可以传入订单ID，这里暂时为0
		Value:    quantity,
		// BeforeVal 和 AfterVal 在这里难以准确计算，因为我们在变更后才记录
		// 但至少记录了变更的数量和类型
		CreateBy: userID,
	}

	// 批量创建日志记录（这里只有一条，但使用批量接口保持一致性）
	models := []*stockLogDao.Model{stockLogModel}
	if err := e.StockLogRepo.BatchCreateWithTx(ctx, tx, models); err != nil {
		// 库存日志失败不应影响主流程，只记录错误
		log.Ctx(ctx).WithError(err).Error("createStockLog: failed to create stock log",
			"skuID", skuID,
			"quantity", quantity,
			"changeType", changeType)
		return nil
	}

	log.Ctx(ctx).Info("stock change log created",
		"skuID", skuID,
		"quantity", quantity,
		"userID", userID,
		"changeType", changeType)
	return nil
}

// processOrderRefund 仅记录 "待退款" 并返回；真正的退款由异步任务处理
func (e *Entry) processOrderRefund(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model) error {
	// 1. 幂等检查：是否已有 Pending 退款
	existing, err := e.RefundLogRepo.FindByFilter(ctx, &refundLogDao.Filter{
		OrderID:      orderModel.ID,
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
	})
	if err != nil {
		return err
	}
	if len(existing) > 0 {
		log.Ctx(ctx).Info("processOrderRefund pending refund exists", "orderID", orderModel.ID, "refundID", existing[0].ID)
		return nil // 已存在，直接返回
	}

	// 2. 创建退款记录，状态置为 Pending
	outRefundNo := helper.GetAppNo(helper.AppNoBoxRefund)

	// 计算退款金额
	var refundAmount uint64
	if orderModel.PayMethod == uint32(orderDao.OrderInfoPayMethodMethodBalance) {
		refundAmount = orderModel.UsedFee
	} else {
		refundAmount = orderModel.CashFee
	}

	refundModel := &refundLogDao.Model{
		UserID:       orderModel.UserID,
		OrderID:      orderModel.ID,
		PayNo:        orderModel.OutTradeNo,
		OutRefundNo:  outRefundNo,
		TotalAmount:  refundAmount,
		RefundAmount: refundAmount,
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
		RefundReason: uint32(refundLogDao.RefundReasonOrderCancel),
		RefundDesc:   "订单取消待退款",
		AdminID:      0,
		Remark:       "Order cancellation auto refund (pending)",
	}

	if err := e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("processOrderRefund create refund log error")
		return err
	}

	log.Ctx(ctx).Info("processOrderRefund create pending refund", "orderID", orderModel.ID, "refundNo", outRefundNo)

	return nil
}

// processWechatRefund 处理微信支付退款
func (e *Entry) processWechatRefund(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model) error {
	// 生成退款单号
	outRefundNo := helper.GetAppNo(helper.AppNoBoxRefund)

	// 获取用户信息
	userModel, err := e.UserRepo.FetchByID(ctx, orderModel.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("processWechatRefund fetch user error")
		return err
	}
	if userModel == nil {
		return fmt.Errorf("user not found: %d", orderModel.UserID)
	}

	// 创建退款记录（订单取消退款）
	refundModel := &refundLogDao.Model{
		UserID:       orderModel.UserID,
		OrderID:      orderModel.ID,
		PayNo:        orderModel.OutTradeNo,
		OutRefundNo:  outRefundNo,
		TotalAmount:  orderModel.CashFee,
		RefundAmount: orderModel.CashFee, // 订单取消通常全额退款
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
		RefundReason: uint32(refundLogDao.RefundReasonOrderCancel), // 订单取消退款
		RefundDesc:   "订单取消自动退款",
		AdminID:      0, // 系统自动退款
		Remark:       "Order cancellation auto refund",
	}

	if err = e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("processWechatRefund create refund log error")
		return err
	}

	// 调用微信退款API
	return e.processWechatRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, orderModel.CashFee)
}

// processAlipayRefund 处理支付宝退款
func (e *Entry) processAlipayRefund(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model) error {
	// 生成退款单号
	outRefundNo := helper.GetAppNo(helper.AppNoBoxRefund)

	// 获取用户信息
	userModel, err := e.UserRepo.FetchByID(ctx, orderModel.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("processAlipayRefund fetch user error")
		return err
	}
	if userModel == nil {
		return fmt.Errorf("user not found: %d", orderModel.UserID)
	}

	// 创建退款记录（订单取消退款）
	refundModel := &refundLogDao.Model{
		UserID:       orderModel.UserID,
		OrderID:      orderModel.ID,
		PayNo:        orderModel.OutTradeNo,
		OutRefundNo:  outRefundNo,
		TotalAmount:  orderModel.CashFee,
		RefundAmount: orderModel.CashFee, // 订单取消通常全额退款
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
		RefundReason: uint32(refundLogDao.RefundReasonOrderCancel), // 订单取消退款
		RefundDesc:   "订单取消自动退款",
		AdminID:      0, // 系统自动退款
		Remark:       "Order cancellation auto refund",
	}

	if err = e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("processAlipayRefund create refund log error")
		return err
	}

	// 调用支付宝退款API
	return e.processAlipayRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, orderModel.CashFee)
}

// processBalanceRefund 处理余额退款
func (e *Entry) processBalanceRefund(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model) error {
	// 生成退款单号
	outRefundNo := helper.GetAppNo(helper.AppNoBoxRefund)

	// 获取用户信息
	userModel, err := e.UserRepo.FetchByID(ctx, orderModel.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("processBalanceRefund fetch user error")
		return err
	}
	if userModel == nil {
		return fmt.Errorf("user not found: %d", orderModel.UserID)
	}

	// 创建退款记录（订单取消退款）
	refundModel := &refundLogDao.Model{
		UserID:       orderModel.UserID,
		OrderID:      orderModel.ID,
		PayNo:        orderModel.OutTradeNo,
		OutRefundNo:  outRefundNo,
		TotalAmount:  orderModel.UsedFee, // 余额支付使用UsedFee
		RefundAmount: orderModel.UsedFee,
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
		RefundReason: uint32(refundLogDao.RefundReasonOrderCancel), // 订单取消退款
		RefundDesc:   "订单取消自动退款",
		AdminID:      0, // 系统自动退款
		Remark:       "Order cancellation auto refund",
	}

	if err = e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
		log.Ctx(ctx).WithError(err).Error("processBalanceRefund create refund log error")
		return err
	}

	// 调用用户余额退款逻辑
	return e.processBalanceRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, orderModel.UsedFee)
}

// generateQRCodeURL 生成二维码URL
func (e *Entry) generateQRCodeURL(pickupCode, orderNo string) string {
	// 这里可以根据实际需求生成二维码URL
	// 示例：可以是一个包含取货码和订单号的URL
	return fmt.Sprintf("qrcode://pickup?code=%s&order=%s&timestamp=%d",
		pickupCode, orderNo, carbon.Now().Timestamp())
}

// generateSixDigitCode 生成六位数字取货码
func (e *Entry) generateSixDigitCode() string {
	// 生成100000-999999范围内的随机数字
	code := safe_random.IntnRange(100000, 1000000)
	return fmt.Sprintf("%06d", code)
}

// GeneratePickupCode 生成取货码
func (e *Entry) GeneratePickupCode(ctx *gin.Context, req *orderDto.PickupCodeGenerateReq) (*orderDto.PickupCodeGenerateRes, error) {
	var (
		res        = &orderDto.PickupCodeGenerateRes{}
		orderModel *orderDao.Model
		err        error
	)

	// 获取订单信息
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else if req.OrderNo != "" {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	} else {
		return nil, ecode.ParamErr
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GeneratePickupCode fetch order error")
		return nil, err
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("GeneratePickupCode order not found")
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单权限
	if orderModel.UserID != req.UserID {
		log.Ctx(ctx).Error("GeneratePickupCode user not match")
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单是否为到店取货类型
	if orderModel.DeliveryID != orderDao.DeliveryTypeStore {
		log.Ctx(ctx).Error("GeneratePickupCode order is not store pickup type")
		return nil, ecode.BoxOrderPickupCodeErr
	}

	// 验证订单状态（只有已支付的订单才能生成取货码）
	if orderModel.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
		log.Ctx(ctx).Error("GeneratePickupCode order status not paid")
		return nil, ecode.BoxOrderPickupCodeGenErr
	}

	// 生成新的取货码
	pickupCode := e.generateSixDigitCode()

	// 保存取货码到Redis（使用双缓存机制）
	// 设置过期时间为7天（604800秒）
	expireTime := 604800
	codeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)

	// 保存正向映射：订单号 -> 取货码
	if err := redis.SetString(codeKey, pickupCode, expireTime); err != nil {
		log.Ctx(ctx).WithError(err).Error("GeneratePickupCode failed to save pickup code")
		return nil, ecode.BoxOrderPickupCodeSaveErr
	}

	res.OrderID = orderModel.ID
	res.OrderNo = orderModel.OutTradeNo
	res.PickupCode = pickupCode
	res.IsNew = true
	//res.QRCodeURL = e.generateQRCodeURL(pickupCode, orderModel.OutTradeNo)

	log.Ctx(ctx).Info("GeneratePickupCode success",
		"orderID", orderModel.ID,
		"orderNo", orderModel.OutTradeNo,
		"pickupCode", pickupCode,
		"forceRefresh", req.ForceRefresh)

	return res, nil
}

// QueryPickupCode 查询取货码
func (e *Entry) QueryPickupCode(ctx *gin.Context, req *orderDto.PickupCodeQueryReq) (*orderDto.PickupCodeQueryRes, error) {
	var (
		res        = &orderDto.PickupCodeQueryRes{}
		orderModel *orderDao.Model
		err        error
	)

	// 获取订单信息
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else if req.OrderNo != "" {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	} else {
		return nil, ecode.ParamErr
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("QueryPickupCode fetch order error")
		return nil, err
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("QueryPickupCode order not found")
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单权限
	if orderModel.UserID != req.UserID {
		log.Ctx(ctx).Error("QueryPickupCode user not match")
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单是否为到店取货类型
	if orderModel.DeliveryID != orderDao.DeliveryTypeStore {
		log.Ctx(ctx).Error("QueryPickupCode order is not store pickup type")
		return nil, ecode.BoxOrderTypeStoreErr
	}

	res.OrderID = orderModel.ID
	res.OrderNo = orderModel.OutTradeNo

	// 查询取货码
	codeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)
	pickupCodeBytes, err := redis.Get(codeKey)
	if err != nil || len(pickupCodeBytes) == 0 {
		// 取货码不存在或已过期
		res.IsValid = false
		log.Ctx(ctx).Info("QueryPickupCode pickup code not found or expired", "orderNo", orderModel.OutTradeNo)
		return res, nil
	}

	res.PickupCode = string(pickupCodeBytes)
	res.IsValid = true

	return res, nil
}

// VerifyPickupCode 验证取货码（门店端使用）
func (e *Entry) VerifyPickupCode(ctx *gin.Context, req *orderDto.PickupCodeVerifyReq) (*orderDto.PickupCodeVerifyRes, error) {
	// 参数校验
	if err := req.Validate(); err != nil {
		return nil, ecode.ParamErr
	}

	var (
		res        = &orderDto.PickupCodeVerifyRes{}
		orderModel *orderDao.Model
		err        error
	)

	res.PickupCode = req.PickupCode

	// 获取订单信息（优先使用 OrderID，其次 OrderNo）
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("VerifyPickupCode fetch order error")
		return nil, err
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("VerifyPickupCode order not found")
		res.IsValid = false
		return res, nil
	}

	res.OrderID = orderModel.ID
	res.OrderNo = orderModel.OutTradeNo

	// 从 Redis 获取该订单的取货码
	codeKey := redis.GetPickupCodeKey(orderModel.OutTradeNo)
	storedCode, err := redis.Get(codeKey)
	if err != nil || len(storedCode) == 0 {
		res.IsValid = false
		log.Ctx(ctx).Info("VerifyPickupCode pickup code not found or expired", "orderNo", orderModel.OutTradeNo)
		return res, nil
	}

	// 比对取货码
	if string(storedCode) != req.PickupCode {
		res.IsValid = false
		log.Ctx(ctx).Info("VerifyPickupCode code mismatch", "orderNo", orderModel.OutTradeNo, "pickupCode", req.PickupCode)
		return res, nil
	}

	// 验证订单状态（只有已支付的订单才有效）
	if orderModel.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
		log.Ctx(ctx).Error("VerifyPickupCode order status not paid")
		res.IsValid = false
		return res, nil
	}

	// 验证订单是否为到店取货类型
	if orderModel.DeliveryID != orderDao.DeliveryTypeStore {
		log.Ctx(ctx).Error("VerifyPickupCode order is not store pickup type")
		res.IsValid = false
		return res, nil
	}

	res.IsValid = true

	// 返回订单基本信息
	res.OrderInfo = &orderDto.OrderInfo{
		OrderId:     orderModel.ID,
		OrderNo:     orderModel.OutTradeNo,
		OrderType:   orderModel.OrderType,
		OrderStatus: orderModel.OrderStatus,
		CreatedAt:   orderModel.CreatedAt.Unix(),
		PayAt:       int64(orderModel.PayTime),
		TotalFee:    orderModel.TotalFee,
		FreightFee:  orderModel.FreightFee,
	}

	log.Ctx(ctx).Info("VerifyPickupCode success",
		"pickupCode", req.PickupCode,
		"orderNo", orderModel.OutTradeNo,
		"orderID", orderModel.ID)

	return res, nil
}

// CompletePickupOrder 完成取货（订单完成时删除取货码）
func (e *Entry) CompletePickupOrder(ctx *gin.Context, orderNo string) error {
	// 删除取货码相关的Redis键
	codeKey := redis.GetPickupCodeKey(orderNo)

	// 直接删除正向映射
	if _, err := redis.Delete(codeKey); err != nil {
		log.Ctx(ctx).WithError(err).Error("CompletePickupOrder failed to delete pickup code", "orderNo", orderNo)
		return err
	}

	log.Ctx(ctx).Info("CompletePickupOrder success", "orderNo", orderNo)
	return nil
}

// CompleteOrder 完成订单（门店端使用）
func (e *Entry) CompleteOrder(ctx *gin.Context, req *orderDto.OrderCompleteReq) (*orderDto.OrderCompleteRes, error) {
	var (
		res        = &orderDto.OrderCompleteRes{}
		orderModel *orderDao.Model
		err        error
	)

	// 根据不同参数获取订单信息
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else if req.OrderNo != "" {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	} else if req.PickupCode != "" {
		// 需要同时提供订单号或订单ID
		if req.OrderNo != "" {
			orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
		} else if req.OrderID > 0 {
			orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
		} else {
			return nil, ecode.ParamErr
		}
	} else {
		return nil, ecode.ParamErr
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CompleteOrder fetch order error")
		return nil, err
	}
	if orderModel == nil {
		log.Ctx(ctx).Error("CompleteOrder order not found")
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单状态（只有已支付的订单才能完成）
	if orderModel.OrderStatus != uint32(orderDao.OrderStatusPayOk) {
		log.Ctx(ctx).Error("CompleteOrder order status not paid")
		return nil, ecode.BoxOrderStatusErr
	}

	// 验证订单是否为到店取货类型
	if orderModel.DeliveryID != orderDao.DeliveryTypeStore {
		log.Ctx(ctx).Error("CompleteOrder order is not store pickup type")
		return nil, ecode.BoxOrderTypeStoreErr
	}

	// 开启事务更新订单状态
	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新订单状态为已完成
	updates := map[string]interface{}{
		"order_status": uint32(orderDao.OrderStatusDone),
	}

	if err := e.OrderRepo.UpdateMapByIDWithTx(ctx, tx, orderModel.ID, updates); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("CompleteOrder failed to update order status")
		return nil, ecode.BoxOrderUpdateStatusErr
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CompleteOrder failed to commit transaction")
		return nil, ecode.BoxOrderCompleteFailErr
	}

	// 删除取货码（事务外执行，避免影响主流程）
	if err := e.CompletePickupOrder(ctx, orderModel.OutTradeNo); err != nil {
		log.Ctx(ctx).WithError(err).Error("CompleteOrder failed to delete pickup code",
			"orderNo", orderModel.OutTradeNo)
		// 不返回错误，因为订单状态已更新成功
	}

	res.OrderID = orderModel.ID
	res.OrderNo = orderModel.OutTradeNo
	res.Message = "订单完成成功"

	log.Ctx(ctx).Info("CompleteOrder success",
		"orderID", orderModel.ID,
		"orderNo", orderModel.OutTradeNo)

	return res, nil
}

// calculateOrderFee 计算订单费用信息
// 对于盲盒订单：计算已使用费用 = 总费用 - (单价 * 剩余抽奖次数) - 优惠券费用
// 对于直接购买订单：已使用费用直接使用订单中的 UsedFee，总费用包含运费
func (e *Entry) calculateOrderFee(ctx *gin.Context, orderModel *orderDao.Model) (usedFee uint64, totalFee uint64, err error) {
	// 直接购买订单的特殊处理
	if orderModel.OrderType == uint32(orderDao.OrderTypeDirect) {
		// 对于直接购买订单，UsedFee 已经包含了运费
		usedFee = orderModel.UsedFee

		// 确定总费用
		if orderModel.CashFee > 0 {
			totalFee = orderModel.CashFee
		} else {
			totalFee = orderModel.UsedFee
		}

		return usedFee, totalFee, nil
	}

	// 盲盒订单的原有逻辑
	// 确定总费用
	if orderModel.CashFee > 0 {
		totalFee = orderModel.CashFee
	} else {
		totalFee = orderModel.UsedFee
	}

	// 使用安全计算避免溢出
	feeProduct, err := decimalUtil.SafeMultiply(orderModel.Fee, orderModel.LotteryLeft)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("calculateOrderFee fee calculation overflow")
		return 0, 0, err
	}

	usedFee, err = decimalUtil.SafeSubtract(orderModel.TotalFee, feeProduct, orderModel.CouponFee)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("calculateOrderFee used fee calculation error")
		return 0, 0, err
	}

	return usedFee, totalFee, nil
}

// getOrderTotalFee 获取订单总费用（现金费用优先，否则使用已使用费用）
func (e *Entry) getOrderTotalFee(orderModel *orderDao.Model) uint64 {
	if orderModel.CashFee > 0 {
		return orderModel.CashFee
	}
	return orderModel.UsedFee
}
