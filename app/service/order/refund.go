package order

import (
	"fmt"
	"time"

	"blind_box/app/api/wechat/order"
	"blind_box/app/common/dbs"
	commonDao "blind_box/app/dao/common"
	orderDao "blind_box/app/dao/order/order"
	userDao "blind_box/app/dao/user"
	refundLogDao "blind_box/app/dao/wechat/refund_log"
	orderDto "blind_box/app/dto/order"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RefundCreate 用户申请退款
func (e *Entry) RefundCreate(ctx *gin.Context, req *orderDto.RefundCreateReq) (*orderDto.RefundCreateRes, error) {
	var (
		orderModel *orderDao.Model
		userModel  *userDao.Model
		err        error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCreate validate error")
		return nil, ecode.ParamErr
	}

	// 获取订单信息
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCreate fetch order error")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 验证订单权限
	if orderModel.UserID != req.UserID {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 获取用户信息
	userModel, err = e.UserRepo.FetchByID(ctx, req.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCreate fetch user error")
		return nil, err
	}
	if userModel == nil {
		return nil, ecode.UserNotExistErr
	}

	// 验证订单状态
	if !e.canRefundOrder(orderModel.OrderStatus) {
		return nil, ecode.BoxOrderRefundErr
	}

	// 检查是否已有退款记录
	existingRefund, err := e.RefundLogRepo.FindByFilter(ctx, &refundLogDao.Filter{
		OrderID:      orderModel.ID,
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCreate check existing refund error")
		return nil, err
	}
	if len(existingRefund) > 0 {
		return nil, ecode.BoxOrderRefundAlreadyErr
	}

	// 执行退款
	return e.executeRefund(ctx, orderModel, userModel, req.RefundDesc,
		uint32(refundLogDao.RefundReasonUserRequest), 0, "")
}

// AdminRefundCreate 管理后台发起退款
func (e *Entry) AdminRefundCreate(ctx *gin.Context, req *orderDto.AdminRefundCreateReq) (*orderDto.AdminRefundCreateRes, error) {
	var (
		res        = &orderDto.AdminRefundCreateRes{}
		orderModel *orderDao.Model
		userModel  *userDao.Model
		err        error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundCreate validate error")
		return nil, ecode.ParamErr
	}

	// 获取订单信息
	if req.OrderID > 0 {
		orderModel, err = e.OrderRepo.FetchByID(ctx, req.OrderID)
	} else {
		orderModel, err = e.OrderRepo.FetchByOutTradeNo(ctx, req.OrderNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundCreate fetch order error")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 获取用户信息
	userModel, err = e.UserRepo.FetchByID(ctx, orderModel.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundCreate fetch user error")
		return nil, err
	}
	if userModel == nil {
		return nil, ecode.UserNotExistErr
	}

	// 验证订单状态
	if !e.canRefundOrder(orderModel.OrderStatus) {
		return nil, ecode.BoxOrderRefundErr
	}

	// 验证退款金额
	refundAmount := req.RefundAmount
	if refundAmount == 0 {
		// 全额退款
		refundAmount = orderModel.CashFee
	}
	if refundAmount > orderModel.CashFee {
		return nil, ecode.BoxOrderRefundFeeErr
	}

	// 执行退款
	refundRes, err := e.executeRefund(ctx, orderModel, userModel, req.RefundDesc,
		req.RefundReason, req.AccountID, req.Remark)
	if err != nil {
		return nil, err
	}

	res.RefundID = refundRes.RefundID
	res.OutRefundNo = refundRes.OutRefundNo
	res.RefundAmount = refundRes.RefundAmount
	res.RefundStatus = refundRes.RefundStatus
	res.Message = refundRes.Message

	return res, nil
}

// executeRefund 执行退款的核心逻辑
func (e *Entry) executeRefund(ctx *gin.Context, orderModel *orderDao.Model, userModel *userDao.Model,
	refundDesc string, refundReason uint32, adminID uint64, remark string) (*orderDto.RefundCreateRes, error) {

	var (
		res = &orderDto.RefundCreateRes{}
		err error
	)

	// 生成退款单号
	outRefundNo := helper.GetAppNo(helper.AppNoBoxRefund)

	// 确定退款金额
	refundAmount := orderModel.CashFee

	// 开启事务
	tx := dbs.NewMysqlEngines().Master.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建退款记录
	refundModel := &refundLogDao.Model{
		UserID:       orderModel.UserID,
		OrderID:      orderModel.ID,
		PayNo:        orderModel.OutTradeNo,
		OutRefundNo:  outRefundNo,
		TotalAmount:  orderModel.CashFee,
		RefundAmount: refundAmount,
		RefundStatus: uint32(refundLogDao.RefundStatusPending),
		RefundReason: refundReason,
		RefundDesc:   refundDesc,
		AdminID:      adminID,
		Remark:       remark,
	}

	if err = e.RefundLogRepo.CreateWithTx(ctx, tx, refundModel); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("executeRefund create refund log error")
		return nil, err
	}

	// 根据支付方式调用相应的退款接口
	switch orderModel.PayMethod {
	case 1: // 微信支付
		err = e.processWechatRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
	case 2: // 支付宝支付
		err = e.processAlipayRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
	case 3: // 余额支付
		err = e.processBalanceRefundWithTx(ctx, tx, orderModel, userModel, outRefundNo, refundAmount)
	default:
		err = fmt.Errorf("不支持的支付方式: %d", orderModel.PayMethod)
	}

	if err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("executeRefund process refund error")
		// 更新退款状态为失败
		failUpdateData := map[string]interface{}{
			"refund_status": uint32(refundLogDao.RefundStatusFailed),
			"fail_reason":   err.Error(),
		}
		e.RefundLogRepo.UpdateByOutRefundNo(ctx, outRefundNo, failUpdateData)
		return nil, err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("executeRefund commit transaction error")
		return nil, err
	}

	res.RefundID = refundModel.ID
	res.OutRefundNo = outRefundNo
	res.RefundAmount = refundAmount
	res.RefundStatus = uint32(refundLogDao.RefundStatusPending)
	res.EstimateTime = "1-3个工作日"
	res.Message = "退款申请已提交，预计1-3个工作日到账"

	log.Ctx(ctx).Info("executeRefund success",
		"orderID", orderModel.ID,
		"outRefundNo", outRefundNo,
		"refundAmount", refundAmount)

	return res, nil
}

// processWechatRefundWithTx 处理微信支付退款（带事务）
func (e *Entry) processWechatRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
	userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {

	// 创建微信退款请求信息
	refundInfo := commonDao.WxrefundOrderInfo{
		PayNo:        orderModel.OutTradeNo,
		TotalAmount:  orderModel.CashFee,
		RefundAmount: refundAmount,
	}

	// 调用微信退款API
	wechatOrderAPI := &order.Entry{
		WxPayLogRepo: e.WxPayLogRepo,
	}

	if err := wechatOrderAPI.GopayOrderRefund(ctx, tx, userModel, outRefundNo, refundInfo); err != nil {
		log.Ctx(ctx).WithError(err).Error("processWechatRefundWithTx GopayOrderRefund error")
		return err
	}

	return nil
}

// processAlipayRefundWithTx 处理支付宝退款（带事务）
func (e *Entry) processAlipayRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
	userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {

	// TODO: 实现支付宝退款逻辑
	log.Ctx(ctx).Info("processAlipayRefundWithTx", "orderID", orderModel.ID, "amount", refundAmount)
	return nil
}

// processBalanceRefundWithTx 处理余额退款（带事务）
func (e *Entry) processBalanceRefundWithTx(ctx *gin.Context, tx *gorm.DB, orderModel *orderDao.Model,
	userModel *userDao.Model, outRefundNo string, refundAmount uint64) error {

	// TODO: 实现余额退款逻辑
	log.Ctx(ctx).Info("processBalanceRefundWithTx", "orderID", orderModel.ID, "amount", refundAmount)
	return nil
}

// canRefundOrder 判断订单是否可以退款
func (e *Entry) canRefundOrder(orderStatus uint32) bool {
	// 只有已支付的订单才能退款
	return orderStatus == uint32(orderDao.OrderStatusPayOk)
}

// RefundDetail 获取退款详情
func (e *Entry) RefundDetail(ctx *gin.Context, req *orderDto.RefundDetailReq) (*orderDto.RefundDetailRes, error) {
	var (
		res         = &orderDto.RefundDetailRes{}
		refundModel *refundLogDao.Model
		err         error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundDetail validate error")
		return nil, ecode.ParamErr
	}

	// 查找退款记录
	if req.RefundID > 0 {
		refundModel, err = e.RefundLogRepo.FindByID(ctx, req.RefundID)
	} else if req.OutRefundNo != "" {
		refundModel, err = e.RefundLogRepo.FindByOutRefundNo(ctx, req.OutRefundNo)
	} else if req.OrderID > 0 {
		refundList, err := e.RefundLogRepo.FindByFilter(ctx, &refundLogDao.Filter{
			OrderID: req.OrderID,
		})
		if err != nil {
			return nil, err
		}
		if len(refundList) > 0 {
			refundModel = refundList[0]
		}
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundDetail find refund error")
		return nil, err
	}
	if refundModel == nil {
		return nil, ecode.BoxOrderRefundNotExistErr
	}

	// 验证权限
	if refundModel.UserID != req.UserID {
		return nil, ecode.BoxOrderRefundCantErr
	}

	// 填充响应数据
	res.RefundID = refundModel.ID
	res.OrderID = refundModel.OrderID
	res.OrderNo = refundModel.PayNo
	res.OutRefundNo = refundModel.OutRefundNo
	res.WxRefundID = refundModel.WxRefundID
	res.TotalAmount = refundModel.TotalAmount
	res.RefundAmount = refundModel.RefundAmount
	res.RefundStatus = refundModel.RefundStatus
	res.RefundReason = refundModel.RefundReason
	res.RefundDesc = refundModel.RefundDesc
	res.FailReason = refundModel.FailReason
	res.CreatedAt = refundModel.GetCreatedTime()
	res.EstimateTime = "1-3个工作日"

	if refundModel.SuccessTime != nil {
		res.SuccessTime = refundModel.SuccessTime.Format("2006-01-02 15:04:05")
	}

	return res, nil
}

// RefundList 用户退款记录列表
func (e *Entry) RefundList(ctx *gin.Context, req *orderDto.RefundListReq) (*orderDto.RefundListRes, error) {
	var (
		res = &orderDto.RefundListRes{}
		err error
	)

	filter := &refundLogDao.Filter{
		UserID: req.UserID,
	}
	if req.RefundStatus > 0 {
		filter.RefundStatus = req.RefundStatus
	}
	// 时间范围过滤
	if req.CreatedAtStart > 0 {
		filter.CreatedAtStart = req.CreatedAtStart
	}
	if req.CreatedAtEnd > 0 {
		filter.CreatedAtEnd = req.CreatedAtEnd
	}

	refundList, count, err := e.RefundLogRepo.FindPageByFilter(ctx, filter,
		req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundList find refunds error")
		return nil, err
	}

	res.List = make([]*orderDto.RefundListItem, 0, len(refundList))
	for _, refund := range refundList {
		item := &orderDto.RefundListItem{
			RefundID:     refund.ID,
			OrderID:      refund.OrderID,
			OrderNo:      refund.PayNo,
			OutRefundNo:  refund.OutRefundNo,
			RefundAmount: refund.RefundAmount,
			RefundStatus: refund.RefundStatus,
			RefundDesc:   refund.RefundDesc,
			CreatedAt:    refund.GetCreatedTime(),
		}
		if refund.SuccessTime != nil {
			item.SuccessTime = refund.SuccessTime.Format("2006-01-02 15:04:05")
		}
		res.List = append(res.List, item)
	}
	res.Count = count

	return res, nil
}

// RefundCancel 用户取消退款
func (e *Entry) RefundCancel(ctx *gin.Context, req *orderDto.RefundCancelReq) (*orderDto.RefundCancelRes, error) {
	var (
		res         = &orderDto.RefundCancelRes{}
		refundModel *refundLogDao.Model
		err         error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCancel validate error")
		return nil, ecode.ParamErr
	}

	// 查找退款记录
	if req.RefundID > 0 {
		refundModel, err = e.RefundLogRepo.FindByID(ctx, req.RefundID)
	} else {
		refundModel, err = e.RefundLogRepo.FindByOutRefundNo(ctx, req.OutRefundNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCancel find refund error")
		return nil, err
	}
	if refundModel == nil {
		return nil, ecode.BoxOrderRefundNotExistErr
	}

	// 验证权限
	if refundModel.UserID != req.UserID {
		return nil, ecode.BoxOrderRefundCantErr
	}

	// 验证退款状态
	if refundModel.RefundStatus != uint32(refundLogDao.RefundStatusPending) {
		return nil, ecode.BoxOrderRefundCancelErr
	}

	// 更新退款状态为已取消
	updateData := map[string]interface{}{
		"refund_status": uint32(refundLogDao.RefundStatusCancelled),
		"updated_at":    time.Now(),
	}

	if err = e.RefundLogRepo.UpdateByID(ctx, refundModel.ID, updateData); err != nil {
		log.Ctx(ctx).WithError(err).Error("RefundCancel update status error")
		return nil, err
	}

	res.RefundID = refundModel.ID
	res.Message = "退款已取消"

	return res, nil
}

// AdminRefundList 管理后台退款记录列表
func (e *Entry) AdminRefundList(ctx *gin.Context, req *orderDto.AdminRefundListReq) (*orderDto.AdminRefundListRes, error) {
	var (
		res = &orderDto.AdminRefundListRes{}
		err error
	)

	filter := &refundLogDao.Filter{}

	// 构建过滤条件
	if req.RefundID > 0 {
		filter.ID = req.RefundID
	}
	if req.OrderID > 0 {
		filter.OrderID = req.OrderID
	}
	if req.OutRefundNo != "" {
		filter.OutRefundNo = req.OutRefundNo
	}
	if req.WxRefundID != "" {
		filter.WxRefundID = req.WxRefundID
	}
	if req.UserID > 0 {
		filter.UserID = req.UserID
	}
	if req.RefundStatus > 0 {
		filter.RefundStatus = req.RefundStatus
	}
	if req.RefundReason > 0 {
		filter.RefundReason = req.RefundReason
	}
	if req.AccountID > 0 {
		filter.AdminID = req.AccountID
	}
	// 时间范围过滤
	if req.CreatedAtStart > 0 {
		filter.CreatedAtStart = req.CreatedAtStart
	}
	if req.CreatedAtEnd > 0 {
		filter.CreatedAtEnd = req.CreatedAtEnd
	}

	refundList, count, err := e.RefundLogRepo.FindPageByFilter(ctx, filter,
		req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundList find refunds error")
		return nil, err
	}

	res.List = make([]*orderDto.AdminRefundListItem, 0, len(refundList))
	for _, refund := range refundList {
		item := &orderDto.AdminRefundListItem{
			RefundID:     refund.ID,
			OrderID:      refund.OrderID,
			OrderNo:      refund.PayNo,
			OutRefundNo:  refund.OutRefundNo,
			WxRefundID:   refund.WxRefundID,
			UserID:       refund.UserID,
			TotalAmount:  refund.TotalAmount,
			RefundAmount: refund.RefundAmount,
			RefundStatus: refund.RefundStatus,
			RefundReason: refund.RefundReason,
			RefundDesc:   refund.RefundDesc,
			FailReason:   refund.FailReason,
			AccountID:    refund.AdminID,
			Remark:       refund.Remark,
			CreatedAt:    refund.GetCreatedTime(),
			UpdatedAt:    refund.UpdatedAt.Format(dbs.TimeDateFormatFull),
		}
		if refund.SuccessTime != nil {
			item.SuccessTime = refund.SuccessTime.Format("2006-01-02 15:04:05")
		}

		// TODO: 根据需要填充用户昵称和管理员姓名
		item.UserName = ""
		item.AdminName = ""

		res.List = append(res.List, item)
	}
	res.Count = count

	return res, nil
}

// AdminRefundDetail 管理后台退款详情
func (e *Entry) AdminRefundDetail(ctx *gin.Context, req *orderDto.AdminRefundDetailReq) (*orderDto.AdminRefundDetailRes, error) {
	var (
		res         = &orderDto.AdminRefundDetailRes{}
		refundModel *refundLogDao.Model
		err         error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundDetail validate error")
		return nil, ecode.ParamErr
	}

	// 查找退款记录
	if req.RefundID > 0 {
		refundModel, err = e.RefundLogRepo.FindByID(ctx, req.RefundID)
	} else {
		refundModel, err = e.RefundLogRepo.FindByOutRefundNo(ctx, req.OutRefundNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundDetail find refund error")
		return nil, err
	}
	if refundModel == nil {
		return nil, ecode.BoxOrderRefundNotExistErr
	}

	// 填充基础信息
	res.AdminRefundListItem = &orderDto.AdminRefundListItem{
		RefundID:     refundModel.ID,
		OrderID:      refundModel.OrderID,
		OrderNo:      refundModel.PayNo,
		OutRefundNo:  refundModel.OutRefundNo,
		WxRefundID:   refundModel.WxRefundID,
		UserID:       refundModel.UserID,
		TotalAmount:  refundModel.TotalAmount,
		RefundAmount: refundModel.RefundAmount,
		RefundStatus: refundModel.RefundStatus,
		RefundReason: refundModel.RefundReason,
		RefundDesc:   refundModel.RefundDesc,
		FailReason:   refundModel.FailReason,
		AccountID:    refundModel.AdminID,
		Remark:       refundModel.Remark,
		CreatedAt:    refundModel.GetCreatedTime(),
		UpdatedAt:    refundModel.UpdatedAt.Format(dbs.TimeDateFormatFull),
	}

	if refundModel.SuccessTime != nil {
		res.SuccessTime = refundModel.SuccessTime.Format("2006-01-02 15:04:05")
	}

	// TODO: 填充订单信息、用户信息、管理员信息等额外详情
	res.OrderInfo = &orderDto.OrderInfo{}
	res.RequestData = refundModel.RequestData
	res.ResponseData = refundModel.ResponseData

	return res, nil
}

// AdminRefundRetry 管理后台重试退款
func (e *Entry) AdminRefundRetry(ctx *gin.Context, req *orderDto.AdminRefundRetryReq) (*orderDto.AdminRefundRetryRes, error) {
	var (
		res         = &orderDto.AdminRefundRetryRes{}
		refundModel *refundLogDao.Model
		orderModel  *orderDao.Model
		userModel   *userDao.Model
		err         error
	)

	// 参数验证
	if err = req.Validate(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry validate error")
		return nil, ecode.ParamErr
	}

	// 查找退款记录
	if req.RefundID > 0 {
		refundModel, err = e.RefundLogRepo.FindByID(ctx, req.RefundID)
	} else {
		refundModel, err = e.RefundLogRepo.FindByOutRefundNo(ctx, req.OutRefundNo)
	}
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry find refund error")
		return nil, err
	}
	if refundModel == nil {
		return nil, ecode.BoxOrderRefundNotExistErr
	}

	// 验证退款状态，只有失败的退款才能重试
	if refundModel.RefundStatus != uint32(refundLogDao.RefundStatusFailed) {
		return nil, ecode.BoxOrderRefundRetryErr
	}

	// 获取订单信息
	orderModel, err = e.OrderRepo.FetchByID(ctx, refundModel.OrderID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry fetch order error")
		return nil, err
	}
	if orderModel == nil {
		return nil, ecode.BoxOrderNotExistErr
	}

	// 获取用户信息
	userModel, err = e.UserRepo.FetchByID(ctx, refundModel.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry fetch user error")
		return nil, err
	}
	if userModel == nil {
		return nil, ecode.UserNotExistErr
	}

	// 开启事务重新处理退款
	tx := dbs.NewMysqlEngines().Master.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新退款状态为处理中
	updateData := map[string]interface{}{
		"refund_status": uint32(refundLogDao.RefundStatusPending),
		"fail_reason":   "",
		"updated_at":    time.Now(),
	}

	if err = e.RefundLogRepo.UpdateByIDWithTx(ctx, tx, refundModel.ID, updateData); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry update status error")
		return nil, err
	}

	// 根据支付方式处理退款
	switch orderModel.PayMethod {
	case uint32(orderDao.OrderInfoPayMethodMethodWechat):
		if err = e.processWechatRefundWithTx(ctx, tx, orderModel, userModel,
			refundModel.OutRefundNo, refundModel.RefundAmount); err != nil {
			tx.Rollback()
			// 更新失败状态
			e.RefundLogRepo.UpdateByID(ctx, refundModel.ID, map[string]interface{}{
				"refund_status": uint32(refundLogDao.RefundStatusFailed),
				"fail_reason":   err.Error(),
				"updated_at":    time.Now(),
			})
			return nil, err
		}
	case uint32(orderDao.OrderInfoPayMethodMethodAli):
		if err = e.processAlipayRefundWithTx(ctx, tx, orderModel, userModel,
			refundModel.OutRefundNo, refundModel.RefundAmount); err != nil {
			tx.Rollback()
			// 更新失败状态
			e.RefundLogRepo.UpdateByID(ctx, refundModel.ID, map[string]interface{}{
				"refund_status": uint32(refundLogDao.RefundStatusFailed),
				"fail_reason":   err.Error(),
				"updated_at":    time.Now(),
			})
			return nil, err
		}
	case uint32(orderDao.OrderInfoPayMethodMethodBalance):
		if err = e.processBalanceRefundWithTx(ctx, tx, orderModel, userModel,
			refundModel.OutRefundNo, refundModel.RefundAmount); err != nil {
			tx.Rollback()
			// 更新失败状态
			e.RefundLogRepo.UpdateByID(ctx, refundModel.ID, map[string]interface{}{
				"refund_status": uint32(refundLogDao.RefundStatusFailed),
				"fail_reason":   err.Error(),
				"updated_at":    time.Now(),
			})
			return nil, err
		}
	default:
		tx.Rollback()
		return nil, ecode.BoxOrderUnsupportPayMethodErr
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminRefundRetry commit transaction error")
		return nil, err
	}

	res.RefundID = refundModel.ID
	res.RefundStatus = uint32(refundLogDao.RefundStatusPending)
	res.Message = "退款重试成功，正在处理中"

	return res, nil
}
