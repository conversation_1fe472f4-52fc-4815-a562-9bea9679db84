package goods

import (
	"blind_box/app/common/dbs"
	spuTagDao "blind_box/app/dao/goods/spu_tag"
	tagDao "blind_box/app/dao/goods/tag"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
)

func (e Entry) AdminTagList(ctx *gin.Context, req goodsDto.AdminTagListReq) (*goodsDto.AdminTagListResp, error) {
	total, list, err := e.TagRepo.DataPageList(ctx, &tagDao.Filter{
		GroupID:  req.GroupID,
		NameLike: req.Name,
		Status:   req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*goodsDto.AdminTagListItem, 0, len(list))
	for _, val := range list {
		item := &goodsDto.AdminTagListItem{
			ID:      val.ID,
			Name:    val.Name,
			GroupID: val.GroupID,
			Status:  val.Status,
		}
		retList = append(retList, item)
	}

	return &goodsDto.AdminTagListResp{
		List:  retList,
		Total: total,
	}, nil
}

func (e Entry) AdminTagCreate(ctx *gin.Context, req goodsDto.AdminTagCreateReq) (uint64, error) {
	var (
		_, ok = tagDao.GroupIDEmptyMap[tagDao.TagGroup(req.GroupID)]
		model = &tagDao.Model{
			Name:    req.Name,
			GroupID: req.GroupID,
			Status:  uint32(dbs.StatusEnable),
		}
		cnt, err = e.TagRepo.CountByFilter(ctx, &tagDao.Filter{
			GroupID: req.GroupID,
			Name:    req.Name,
		})
	)
	if !ok {
		return model.ID, ecode.TagGroupNotExistErr
	}
	if err != nil {
		return model.ID, err
	}
	if cnt > 0 {
		return model.ID, ecode.NameExistErr
	}

	if err = e.TagRepo.CreateOrUpdate(ctx, model); err != nil {
		return model.ID, err
	}

	return model.ID, nil
}

func (e Entry) AdminTagUpdate(ctx *gin.Context, req goodsDto.AdminTagUpdateReq) (uint64, error) {
	var (
		model = &tagDao.Model{}
		num   int64
		err   error

		m = &tagDao.Model{
			ID:     req.ID,
			Name:   req.Name,
			Status: uint32(dbs.StatusEnable),
		}
	)

	if model, err = e.TagRepo.FetchByID(ctx, req.ID); err != nil {
		return 0, err
	}
	if model.ID == 0 {
		return 0, ecode.ParamErr
	}

	if num, err = e.TagRepo.CountByFilter(ctx, &tagDao.Filter{Name: req.Name, GroupID: model.GroupID, NotID: req.ID}); err != nil {
		return 0, err
	}
	if num > 0 {
		return 0, ecode.NameExistErr
	}

	if err = e.TagRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

func (e Entry) AdminTagDelete(ctx *gin.Context, tagID uint64) error {
	model, err := e.TagRepo.FetchByID(ctx, tagID)
	if err != nil {
		return err
	}
	if model.ID == 0 {
		return ecode.TagNotExistErr
	}

	// 校验是否有商品使用该标签
	cnt, err := e.SpuTagRepo.CountByFilter(ctx, &spuTagDao.Filter{TagId: tagID})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return ecode.TagInUseErr
	}

	return e.TagRepo.DeleteByID(ctx, tagID, model.GroupID)
}

// AdminBrandOperate .
func (e *Entry) AdminTagOperate(ctx *gin.Context, tagID uint64, action dbs.OperateAction) error {
	model, err := e.TagRepo.FetchByID(ctx, tagID)
	if err != nil {
		return err
	}

	return e.TagRepo.UpdateMapByID(ctx, tagID, model.GroupID, map[string]interface{}{"status": dbs.OperateActionMap[action]})
}

func (e Entry) AdminTagCommonList(ctx *gin.Context, groupId, enable uint32) ([]*goodsDto.AdminTagListItem, error) {
	var (
		list = tagDao.ModelList{}
		err  error
	)
	if enable == dbs.True {
		if list, err = e.TagRepo.RedisEnableGroupTagList(ctx, groupId); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.TagRepo.RedisGroupTagList(ctx, groupId); err != nil {
			return nil, err
		}
	}

	retList := make([]*goodsDto.AdminTagListItem, 0, len(list))
	for _, val := range list {
		item := &goodsDto.AdminTagListItem{
			ID:      val.ID,
			Name:    val.Name,
			GroupID: val.GroupID,
			Status:  val.Status,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
