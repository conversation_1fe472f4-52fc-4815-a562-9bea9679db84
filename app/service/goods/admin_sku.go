package goods

import (
	"blind_box/app/common/dbs"
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	shopDao "blind_box/app/dao/resource/shop"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminSkuList .
func (e *Entry) AdminSkuList(ctx *gin.Context, req goodsDto.AdminSkuListReq) (*goodsDto.AdminSkuListResp, error) {
	var (
		fRet, err = FilterSpuID{}.DealFilterOption(ctx, e, req.CommonSpuSkuFilterReq)
		retList   = []goodsDto.CrSkuItem{}
	)
	if err != nil {
		return nil, err
	}
	if fRet.Empty {
		return &goodsDto.AdminSkuListResp{List: retList}, nil
	}

	total, list, err := e.SkuRepo.DataPageList(ctx, &skuDao.Filter{
		ID:        req.SkuID,
		Ids:       req.SkuIds,
		SpuIds:    fRet.SpuIds,
		TitleLike: req.SkuTitle,
		CodeLike:  req.SkuCode,
		Status:    req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	var (
		eg               errgroup.Group
		spuIds, batchIds = list.GetSpuBatchIds()
		skuIds           = list.GetIds()

		spuList        = spuDao.ModelList{}
		spuMap         = map[uint64]*spuDao.Model{}
		batchList      = batchDao.ModelList{}
		batchMap       = map[uint64]*batchDao.Model{}
		shopMap        = map[uint64]*shopDao.Model{}
		skuUnitList    = skuUnitDao.ModelList{}
		skuUnitListMap = map[uint64]skuUnitDao.ModelList{}
	)
	eg.Go(func() (err error) {
		if spuList, err = e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{Ids: spuIds}); err != nil {
			return
		}
		spuMap = spuList.GetIdMap()
		return
	})
	eg.Go(func() (err error) {
		if shopMap, err = e.ShopRepo.RedisShopMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if batchList, err = e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{Ids: batchIds}); err != nil {
			return
		}
		batchMap = batchList.GetIdMap()
		return
	})
	eg.Go(func() (err error) {
		if skuUnitList, err = e.SkuUnitRepo.FindByFilter(ctx, &skuUnitDao.Filter{SkuIds: skuIds}); err != nil {
			return
		}
		skuUnitListMap = skuUnitList.GetSkuUnitListMap()
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuList err")
		return nil, err
	}

	for _, sku := range list {
		spu, ok := spuMap[sku.SpuId]
		if !ok {
			return nil, ecode.SpuNotExistErr
		}
		batch, ok := batchMap[sku.BatchId]
		if !ok {
			return nil, ecode.BatchNotExist
		}
		item := goodsDto.ToSkuItemResp(ctx, sku, spu, batch, shopMap, skuUnitListMap)

		retList = append(retList, item)
	}

	return &goodsDto.AdminSkuListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSkuDetail ...
func (e Entry) AdminSkuDetail(ctx *gin.Context, skuId uint64) (*goodsDto.CrSkuItem, error) {
	sku, err := e.CommonSrv.CheckSkuExist(ctx, skuId)
	if err != nil {
		return nil, err
	}
	spu, err := e.CheckSpuExist(ctx, sku.SpuId)
	if err != nil {
		return nil, err
	}
	batch, err := e.BatchRepo.FetchByID(ctx, sku.BatchId)
	if err != nil {
		return nil, err
	}
	shopMap, err := e.ShopRepo.RedisShopMap(ctx)
	if err != nil {
		return nil, err
	}

	skuUnitList, err := e.SkuUnitRepo.FindByFilter(ctx, &skuUnitDao.Filter{SkuId: skuId})
	if err != nil {
		return nil, err
	}

	ret := goodsDto.ToSkuItemResp(ctx, sku, spu, batch, shopMap, skuUnitList.GetSkuUnitListMap())
	return &ret, nil
}

// AdminSkuOperate .
func (e *Entry) AdminSkuOperate(ctx *gin.Context, skuIds []uint64, action dbs.OperateAction) error {
	if err := e.SkuRepo.UpdateMapByFilter(ctx, &skuDao.Filter{Ids: skuIds}, map[string]interface{}{"status": dbs.OperateActionMap[action]}); err != nil {
		return err
	}

	return nil
}
