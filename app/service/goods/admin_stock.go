package goods

import (
	"blind_box/app/common/dbs"
	skuDao "blind_box/app/dao/goods/sku"
	stockLog "blind_box/app/dao/goods/stock_log"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AdminSkuStockLogList .
func (e *Entry) AdminSkuStockLogList(ctx *gin.Context, req goodsDto.AdminSkuStockLogListReq) (*goodsDto.AdminSkuStockLogListResp, error) {
	total, list, err := e.StockLogRepo.DataPageList(ctx, &stockLog.Filter{
		SkuId:   req.SkuId,
		LogType: req.LogType,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	spuIds, skuIds := list.GetSpuSkuIds()
	skuMap, err := e.CommonSrv.GetCommonSkuResp(ctx, spuIds, skuIds)
	if err != nil {
		return nil, err
	}

	retList := []goodsDto.AdminSkuStockLogListItem{}
	for _, log := range list {
		sku, ok := skuMap[log.SkuId]
		if !ok {
			return nil, ecode.SkuNotExistErr
		}
		item := goodsDto.AdminSkuStockLogListItem{
			ID:        log.ID,
			LogType:   log.LogType,
			UserId:    log.UserId,
			EntityId:  log.EntityId,
			Value:     log.Value,
			BeforeVal: log.BeforeVal,
			AfterVal:  log.AfterVal,
			SkuInfo:   sku,
			CreatedAt: log.GetCreatedTime(),
		}

		retList = append(retList, item)
	}

	return &goodsDto.AdminSkuStockLogListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSkuStockSet .
func (e *Entry) AdminSkuStockSet(ctx *gin.Context, skuIds []uint64, val uint32) error {
	var (
		paramList     = stockLog.LogParamList{}
		skuList, err  = e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{Ids: skuIds})
		tx            = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		ctxAccount, _ = helper.GetCtxAccount(ctx)
	)
	if err != nil {
		return err
	}

	for _, sku := range skuList {
		item := &stockLog.LogParam{
			SpuId:      sku.SpuId,
			SkuId:      sku.ID,
			LogType:    stockLog.LtSysSet,
			BeforeVal:  sku.GetUsableNum(),
			CurrentVal: val,
			CreateBy:   ctxAccount.AccountID,
		}
		paramList = append(paramList, item)
	}
	if err := func() (err error) {
		if err = e.ModifySKuStock(ctx, tx, paramList); err != nil {
			return
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AdminSkuStockSet err")
		return err
	}

	return nil
}

func (e *Entry) ModifySKuStock(ctx *gin.Context, tx *gorm.DB, paramList stockLog.LogParamList) error {
	if len(paramList) == 0 {
		return nil
	}
	log.Ctx(ctx).WithField("paramList", paramList).Info("ModifySKuStock param")

	LogList := stockLog.ModelList{}

	for _, log := range paramList {
		var (
			item = &stockLog.Model{
				SpuId:    log.SpuId,
				SkuId:    log.SkuId,
				UserId:   log.UserId,
				LogType:  uint32(log.LogType),
				SubType:  0,
				EntityId: log.EntityId,
				CreateBy: log.CreateBy,
			}
		)

		switch log.LogType {
		case stockLog.LtSysSet:
			item.BeforeVal = log.BeforeVal
			item.AfterVal = log.CurrentVal
			item.Value = log.CurrentVal

			if err := e.SkuRepo.UpdateMapByIDWithTx(ctx, tx, log.SkuId,
				map[string]interface{}{"total": log.CurrentVal}); err != nil {
				return err
			}

		case stockLog.LtBoxCreate, stockLog.LtBoxUpdate:
			result := tx.Model(&skuDao.Model{}).
				Where("id = ?", log.SkuId).
				Where("total + refund_num >= lock_num + used_num + ?", log.CurrentVal).
				Update("used_num", gorm.Expr("used_num + ?", log.CurrentVal))

			if result.Error != nil {
				return result.Error
			}

			if result.RowsAffected == 0 {
				return ecode.SkuStockNotEnoughErr
			}

			item.Value = log.CurrentVal
		}

		LogList = append(LogList, item)
	}

	if len(LogList) > 0 {
		if err := e.StockLogRepo.BatchCreateWithTx(ctx, tx, LogList); err != nil {
			return err
		}
	}

	return nil
}
