package goods

import (
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	spuBatchDao "blind_box/app/dao/goods/spu_batch"
	spuTagDao "blind_box/app/dao/goods/spu_tag"
	stockLog "blind_box/app/dao/goods/stock_log"
	tagDao "blind_box/app/dao/goods/tag"
	brandDao "blind_box/app/dao/resource/brand"
	imgDao "blind_box/app/dao/resource/image"
	shopDao "blind_box/app/dao/resource/shop"
	supplierDao "blind_box/app/dao/resource/supplier"
	vendorDao "blind_box/app/dao/resource/vendor"
	userCart "blind_box/app/dao/user/cart"
	"blind_box/app/service/common"

	"blind_box/pkg/redis"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	SpuRepo      *spuDao.Entry
	SkuRepo      *skuDao.Entry
	SkuShopRepo  *skuShopDao.Entry
	SkuUnitRepo  *skuUnitDao.Entry
	ShopRepo     shopDao.Repo
	BatchRepo    *batchDao.Entry
	SpuBatchRepo *spuBatchDao.Entry
	TagRepo      *tagDao.Entry
	SpuTagRepo   *spuTagDao.Entry
	StockLogRepo *stockLog.Entry
	ImgRepo      imgDao.Repo
	BrandRepo    brandDao.Repo
	SupplierRepo supplierDao.Repo
	VendorRepo   vendorDao.Repo
	CartRepo     *userCart.Entry
	CommonSrv    common.Server
	RedisCli     *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		SpuRepo:      spuDao.GetRepo(),
		SkuRepo:      skuDao.GetRepo(),
		SkuShopRepo:  skuShopDao.GetRepo(),
		SkuUnitRepo:  skuUnitDao.GetRepo(),
		ShopRepo:     shopDao.GetRepo(),
		BatchRepo:    batchDao.GetRepo(),
		SpuBatchRepo: spuBatchDao.GetRepo(),
		TagRepo:      tagDao.GetRepo(),
		SpuTagRepo:   spuTagDao.GetRepo(),
		StockLogRepo: stockLog.GetRepo(),
		ImgRepo:      imgDao.GetRepo(),
		BrandRepo:    brandDao.GetRepo(),
		SupplierRepo: supplierDao.GetRepo(),
		VendorRepo:   vendorDao.GetRepo(),
		CartRepo:     userCart.GetRepo(),
		CommonSrv:    common.GetService(),
		RedisCli:     redis.GetRedisClient(),
	}
}
