package goods

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/common"
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	spuBatchDao "blind_box/app/dao/goods/spu_batch"
	spuTag "blind_box/app/dao/goods/spu_tag"
	spuTagDao "blind_box/app/dao/goods/spu_tag"
	tagDao "blind_box/app/dao/goods/tag"
	"blind_box/app/dao/resource/brand"
	imgDao "blind_box/app/dao/resource/image"
	shopDao "blind_box/app/dao/resource/shop"
	supplierDao "blind_box/app/dao/resource/supplier"
	vendorDao "blind_box/app/dao/resource/vendor"
	userCart "blind_box/app/dao/user/cart"
	commonDto "blind_box/app/dto/common"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminSpuList
func (e *Entry) AdminSpuList(ctx *gin.Context, req goodsDto.AdminSpuListReq) (*goodsDto.AdminSpuListResp, error) {
	var (
		fRet, err = FilterSpuIDByAttr{}.DealFilterOption(ctx, e, req.CommonSpuSkuFilterReq)
		retList   = []goodsDto.AdminSpuListItem{}
	)
	if err != nil {
		return nil, err
	}
	if fRet.Empty {
		return &goodsDto.AdminSpuListResp{List: retList}, nil
	}

	var (
		eg          errgroup.Group
		total       int64
		spuList     = spuDao.ModelList{}
		spuIds      = []uint64{}
		brandMap    = make(map[uint64]*brand.Model)
		supplierMap = make(map[uint64]*supplierDao.Model)
		vendorMap   = make(map[uint64]*vendorDao.Model)
	)
	eg.Go(func() (err error) {
		if total, spuList, err = e.SpuRepo.DataPageList(ctx, &spuDao.Filter{
			ID:         req.SpuID,
			Ids:        fRet.SpuIds,
			CodeLike:   req.SpuCode,
			TitleLike:  req.SpuTitle,
			BrandId:    req.BrandId,
			SupplierId: req.SupplierId,
			VendorId:   req.VendorId,
			Status:     req.Status,
			SellType:   req.SellType,
		}, req.Page, req.Limit); err != nil {
			return
		}
		spuIds = spuList.GetIds()
		return
	})
	eg.Go(func() (err error) {
		if brandMap, err = e.BrandRepo.RedisBrandMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if supplierMap, err = e.SupplierRepo.RedisSupplierMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if vendorMap, err = e.VendorRepo.RedisVendorMap(ctx); err != nil {
			return
		}
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuList err")
		return nil, err
	}

	var (
		eg1          errgroup.Group
		tagMap       = make(map[uint64]*tagDao.Model)
		spuTagList   = spuTagDao.ModelList{}
		spuTagMap    = make(map[uint64]*spuTagDao.Model)
		spuImageList = imgDao.ModelList{}
		spuImageMap  = make(map[uint64]imgDao.ModelList)
		batchList    = batchDao.ModelList{}
		batchMap     = make(map[uint64]*batchDao.Model)
	)

	eg1.Go(func() (err error) {
		if batchList, err = e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{Ids: spuList.GetBatchIds()}); err != nil {
			return
		}
		batchMap = batchList.GetIdMap()
		return
	})
	eg1.Go(func() (err error) {
		if tagMap, err = e.TagRepo.RedisGroupTagMap(ctx, uint32(tagDao.GroupCateID)); err != nil {
			return
		}
		return
	})
	eg1.Go(func() (err error) {
		if spuTagList, err = e.SpuTagRepo.FindByFilter(ctx, &spuTag.Filter{
			SpuIds:  spuIds,
			GroupId: uint32(tagDao.GroupCateID),
		}); err != nil {
			return
		}
		spuTagMap = spuTagList.GetSpuIDMap()
		return
	})
	eg1.Go(func() (err error) {
		if spuImageList, err = e.ImgRepo.FindByFilter(ctx, &imgDao.Filter{
			MappingIDs:  spuIds,
			MappingType: imgDao.MappingSpuImages,
		}); err != nil {
			return
		}
		spuImageMap = spuImageList.GetMapppingIDKeyMap()
		return
	})

	if err := eg1.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuList eg1 err")
		return nil, err
	}

	for _, val := range spuList {
		batch, ok := batchMap[val.BatchId]
		if !ok {
			return nil, ecode.BatchNotExist
		}
		item := goodsDto.ToSpuItemResp(ctx, val, batch)

		if brand, ok := brandMap[val.BrandId]; ok {
			item.Brand = commonDto.GoodsTagResp{
				ID:   brand.ID,
				Name: brand.Name,
			}
		}
		if supplier, ok := supplierMap[val.SupplierId]; ok {
			item.Supplier = commonDto.GoodsTagResp{
				ID:   supplier.ID,
				Name: supplier.Name,
			}
		}
		if vendor, ok := vendorMap[val.VendorId]; ok {
			item.Vendor = commonDto.GoodsTagResp{
				ID:   vendor.ID,
				Name: vendor.Name,
			}
		}
		if cateTag, ok := spuTagMap[val.ID]; ok {
			if tag, ok := tagMap[cateTag.TagId]; ok {
				item.Category = commonDto.GoodsTagResp{
					ID:   tag.ID,
					Name: tag.Name,
				}
			}
		}
		if spuImages, ok := spuImageMap[val.ID]; ok {
			for _, img := range spuImages {
				item.ImageList = append(item.ImageList, helper.GetImageCdnUrl(ctx, img.URL))
			}
		}
		retList = append(retList, item)
	}

	return &goodsDto.AdminSpuListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSpuDetail .
func (e *Entry) AdminSpuDetail(ctx *gin.Context, spuID uint64, isAdmin bool) (*goodsDto.AdminSpuDetailResp, error) {
	var (
		ctxUser, _ = helper.GetCtxUser(ctx)
		retSkuList = []goodsDto.CrSkuItem{}
		ret        = &goodsDto.AdminSpuDetailResp{SkuList: retSkuList}

		eg                 errgroup.Group
		spuModel           = &spuDao.Model{}
		batchModel         = &batchDao.Model{}
		brandMap           = make(map[uint64]*brand.Model)
		supplierMap        = make(map[uint64]*supplierDao.Model)
		vendorMap          = make(map[uint64]*vendorDao.Model)
		tagMap             = make(map[uint64]*tagDao.Model)
		spuTagList         = spuTagDao.ModelList{}
		spuTagMap          = make(map[uint64]*spuTagDao.Model)
		spuImageList       = imgDao.ModelList{}
		spuImageMap        = make(map[uint64]imgDao.ModelList)
		skuList            = skuDao.ModelList{}
		shopMap            = map[uint64]*shopDao.Model{}
		skuUnitList        = skuUnitDao.ModelList{}
		skuUnitListMap     = map[uint64]skuUnitDao.ModelList{}
		cartList           = userCart.ModelList{}
		cartskuDeliveryMap = map[string]*userCart.Model{}
		userSubMap         = map[uint64]struct{}{}
	)

	eg.Go(func() (err error) {
		if skuList, err = e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{SpuID: spuID}); err != nil {
			return
		}

		return
	})
	if !isAdmin && ctxUser.UID > 0 {
		eg.Go(func() (err error) {
			if cartList, err = e.CartRepo.FindByFilter(ctx, &userCart.Filter{
				SpuId:  spuID,
				UserId: ctxUser.UID,
			}); err != nil {
				return
			}
			cartskuDeliveryMap = cartList.GetSkuDeliveryMap()
			return
		})
		eg.Go(func() (err error) {
			userSubMap = e.CommonSrv.JudgeUserSubscribeSpu(ctx, ctxUser.UID, spuID)
			return
		})
	}
	eg.Go(func() (err error) {
		if shopMap, err = e.ShopRepo.RedisShopMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if spuModel, err = e.CheckSpuExist(ctx, spuID); err != nil {
			return
		}
		if batchModel, err = e.BatchRepo.FetchByID(ctx, spuModel.BatchId); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if skuUnitList, err = e.SkuUnitRepo.FindByFilter(ctx, &skuUnitDao.Filter{SpuId: spuID}); err != nil {
			return
		}
		skuUnitListMap = skuUnitList.GetSkuUnitListMap()
		return
	})
	eg.Go(func() (err error) {
		if brandMap, err = e.BrandRepo.RedisBrandMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if supplierMap, err = e.SupplierRepo.RedisSupplierMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if vendorMap, err = e.VendorRepo.RedisVendorMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if tagMap, err = e.TagRepo.RedisGroupTagMap(ctx, uint32(tagDao.GroupCateID)); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if spuTagList, err = e.SpuTagRepo.FindByFilter(ctx, &spuTag.Filter{
			SpuId:   spuID,
			GroupId: uint32(tagDao.GroupCateID),
		}); err != nil {
			return
		}
		spuTagMap = spuTagList.GetSpuIDMap()
		return
	})
	eg.Go(func() (err error) {
		if spuImageList, err = e.ImgRepo.FindByFilter(ctx, &imgDao.Filter{
			MappingID:   spuID,
			MappingType: imgDao.MappingSpuImages,
		}); err != nil {
			return
		}
		spuImageMap = spuImageList.GetMapppingIDKeyMap()
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuDetail err")
		return nil, err
	}

	ret.AdminSpuListItem = goodsDto.ToSpuItemResp(ctx, spuModel, batchModel)
	for _, val := range skuList {
		skuItem := goodsDto.ToSkuItemResp(ctx, val, spuModel, batchModel, shopMap, skuUnitListMap)
		if !isAdmin {
			skuItem.Total = 0
			skuItem.LockNum = 0
			skuItem.UsedNum = 0
			skuItem.UsableNum = 0
			skuItem.RefundNum = 0
			for _, delivery := range skuItem.DeliveryList {
				key := fmt.Sprintf("%d_%d", val.ID, delivery.DeliveryID)
				if cartSku, ok := cartskuDeliveryMap[key]; ok {
					delivery.CartNum = cartSku.UnitNum
				}
			}
			if _, ok := userSubMap[val.ID]; ok {
				skuItem.IsSub = dbs.True
			}
		}
		retSkuList = append(retSkuList, skuItem)
	}
	ret.SkuList = retSkuList

	if brand, ok := brandMap[spuModel.BrandId]; ok {
		ret.AdminSpuListItem.Brand = commonDto.GoodsTagResp{
			ID:   brand.ID,
			Name: brand.Name,
		}
	}
	if supplier, ok := supplierMap[spuModel.SupplierId]; ok {
		ret.AdminSpuListItem.Supplier = commonDto.GoodsTagResp{
			ID:   supplier.ID,
			Name: supplier.Name,
		}
	}
	if vendor, ok := vendorMap[spuModel.VendorId]; ok {
		ret.AdminSpuListItem.Vendor = commonDto.GoodsTagResp{
			ID:   vendor.ID,
			Name: vendor.Name,
		}
	}
	if cateTag, ok := spuTagMap[spuModel.ID]; ok {
		if tag, ok := tagMap[cateTag.TagId]; ok {
			ret.AdminSpuListItem.Category = commonDto.GoodsTagResp{
				ID:   tag.ID,
				Name: tag.Name,
			}
		}
	}
	if spuImages, ok := spuImageMap[spuModel.ID]; ok {
		for _, img := range spuImages {
			ret.AdminSpuListItem.ImageList = append(ret.AdminSpuListItem.ImageList, helper.GetImageCdnUrl(ctx, img.URL))
		}
	}

	for _, val := range spuTagList {
		if tag, ok := tagMap[val.TagId]; ok {
			ret.AdminSpuListItem.Category = commonDto.GoodsTagResp{
				ID:   tag.ID,
				Name: tag.Name,
			}
		}
	}

	return ret, nil
}

// AdminSpuCreate .
func (e *Entry) AdminSpuCreate(ctx *gin.Context, req goodsDto.AdminSpuCreateReq) (uint64, error) {
	if err := req.SkuList.CheckSkuList(); err != nil {
		return 0, err
	}
	if err := e.checkSpuCode(ctx, req.Code, 0); err != nil {
		return 0, err
	}
	if err := e.checkSpuSkuCode(ctx, req.SkuList); err != nil {
		return 0, err
	}
	batchInfo, err := e.CheckBatch(ctx, req.BatchId)
	if err != nil {
		return 0, err
	}
	if batchInfo.SellType == uint32(common.SellPresell) && req.PreStartTime == 0 {
		return 0, fmt.Errorf("预购商品需设置开始时间")
	}

	var (
		newSpu      = req.ReqToSpuModel(0)
		tx          = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		skuShopList = skuShopDao.ModelList{}
		skuUnitList = skuUnitDao.ModelList{}
	)
	if err := func() (err error) {
		if err = e.SpuRepo.CreateOrUpdateWithTx(ctx, tx, newSpu); err != nil {
			return
		}
		if err = e.SpuBatchRepo.CreateOrUpdateWithTx(ctx, tx, &spuBatchDao.Model{
			SpuId:     newSpu.ID,
			BatchId:   newSpu.BatchId,
			IsDefault: 1,
		}); err != nil {
			return
		}
		skuList, skuShopMap, skuUnitMap := req.SkuList.GetSkuList(newSpu.ID, newSpu.BatchId)
		imageList, tagList := req.ReqToSpuAtrr(newSpu.ID)

		if err = e.SkuRepo.BatchCreateWithTx(ctx, tx, skuList); err != nil {
			return
		}

		for _, sku := range skuList {
			if valList, ok := skuShopMap[sku.Code]; ok {
				for _, val := range valList {
					skuShopList = append(skuShopList, &skuShopDao.Model{
						SpuId:  sku.SpuId,
						SkuId:  sku.ID,
						ShopId: val.ShopId,
					})
				}
			}
			if valList, ok := skuUnitMap[sku.Code]; ok {
				for _, val := range valList {
					skuUnitList = append(skuUnitList, &skuUnitDao.Model{
						ID:        val.ID,
						SpuId:     sku.SpuId,
						SkuId:     sku.ID,
						Title:     val.Title,
						IsPrimary: val.IsPrimary,
						Status:    val.Status,
						UnitRatio: val.UnitRatio,
					})
				}
			}
		}

		if err = e.SkuShopRepo.BatchCreateWithTx(ctx, tx, skuShopList); err != nil {
			return
		}

		if err = e.SkuUnitRepo.BatchCreateWithTx(ctx, tx, skuUnitList); err != nil {
			return
		}

		if err = e.SpuTagRepo.BatchCreateWithTx(ctx, tx, tagList); err != nil {
			return
		}

		if err = e.ImgRepo.BatchCreateWithTx(ctx, tx, imageList); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AdminSpuCreate err")
		return 0, err
	}
	return newSpu.ID, nil
}

// AdminSpuUpdate .
func (e *Entry) AdminSpuUpdate(ctx *gin.Context, req goodsDto.AdminSpuUpdateReq) error {
	if err := req.SkuList.CheckSkuList(); err != nil {
		return err
	}
	if err := e.checkSpuCode(ctx, req.Code, req.SpuId); err != nil {
		return err
	}
	if err := e.checkSpuSkuCode(ctx, req.SkuList); err != nil {
		return err
	}
	if _, err := e.CheckSpuExist(ctx, req.SpuId); err != nil {
		return err
	}

	batchInfo, err := e.CheckBatch(ctx, req.BatchId)
	if err != nil {
		return err
	}
	if batchInfo.SellType == uint32(common.SellPresell) && req.PreStartTime == 0 {
		return fmt.Errorf("预购商品需要设置开始时间")
	}

	var (
		newSpu                          = req.ReqToSpuModel(req.SpuId)
		skuList, skuShopMap, skuUnitMap = req.SkuList.GetSkuList(req.SpuId, req.BatchId)
		imageList, tagList              = req.ReqToSpuAtrr(req.SpuId)
		tx                              = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		skuShopList                     = skuShopDao.ModelList{}
		skuUnitList                     = skuUnitDao.ModelList{}
	)
	if err := func() (err error) {
		if err = e.ImgRepo.DeleteByIdx(ctx, req.SpuId, imgDao.MappingSpuImages); err != nil {
			return
		}

		// 目前只有category组
		if err = e.SpuTagRepo.DeleteBySpuID(ctx, req.SpuId, tagDao.GroupCateID); err != nil {
			return
		}

		// TODO: 需要对比sku新增与修改, 用户购物车选择了收货店铺时，sku不可删除与店铺的关联关系
		if err = e.SkuShopRepo.DeleteBySpuID(ctx, req.SpuId); err != nil {
			return
		}

		if err = e.SpuRepo.CreateOrUpdateWithTx(ctx, tx, newSpu); err != nil {
			return
		}

		if err = e.SpuBatchRepo.UpdateMapBySpuIdWithTx(ctx, tx, req.SpuId, map[string]interface{}{"is_default": dbs.False}); err != nil {
			return
		}

		if err = e.SpuBatchRepo.CreateOrUpdateWithTx(ctx, tx, &spuBatchDao.Model{
			SpuId:     req.SpuId,
			BatchId:   req.BatchId,
			IsDefault: 1,
		}); err != nil {
			return
		}

		if err = e.SkuRepo.BatchCreateOrUpdateWithTx(ctx, tx, skuList); err != nil {
			return
		}

		// TODO: 需要对比sku新增与修改, 用户购物车选择了收货店铺时，sku不可删除与店铺的关联关系
		for _, sku := range skuList {
			if valList, ok := skuShopMap[sku.Code]; ok {
				for _, val := range valList {
					skuShopList = append(skuShopList, &skuShopDao.Model{
						SpuId:  sku.SpuId,
						SkuId:  sku.ID,
						ShopId: val.ShopId,
					})
				}
			}
			if valList, ok := skuUnitMap[sku.Code]; ok {
				for _, val := range valList {
					skuUnitList = append(skuUnitList, &skuUnitDao.Model{
						ID:        val.ID,
						SpuId:     sku.SpuId,
						SkuId:     sku.ID,
						Title:     val.Title,
						IsPrimary: val.IsPrimary,
						Status:    val.Status,
						UnitRatio: val.UnitRatio,
					})
				}
			}
		}

		if err = e.SkuShopRepo.BatchCreateWithTx(ctx, tx, skuShopList); err != nil {
			return
		}
		log.Ctx(ctx).WithField("skuUnitList", skuUnitList).WithField("skuUnitMap", skuUnitMap).Info("skuUnitListBatchCreateOrUpdate")

		if err = e.SkuUnitRepo.BatchCreateOrUpdateWithTx(ctx, tx, skuUnitList); err != nil {
			return
		}

		if err = e.SpuTagRepo.BatchCreateWithTx(ctx, tx, tagList); err != nil {
			return
		}

		if err = e.ImgRepo.BatchCreateWithTx(ctx, tx, imageList); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AdminSpuUpdate err")
		return err
	}
	return nil
}

// AdminSpuOperate .
func (e *Entry) AdminSpuOperate(ctx *gin.Context, spuIds []uint64, action dbs.OperateAction) error {
	if err := e.SpuRepo.UpdateMapByFilter(ctx, &spuDao.Filter{Ids: spuIds}, map[string]interface{}{"status": dbs.OperateActionMap[action]}); err != nil {
		return err
	}

	return nil
}
