package goods

import (
	"blind_box/app/common/dbs"
	skuDao "blind_box/app/dao/goods/sku"
	spuDao "blind_box/app/dao/goods/spu"
	spuTag "blind_box/app/dao/goods/spu_tag"
	commonDto "blind_box/app/dto/common"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/util/sliceUtil"

	"github.com/gin-gonic/gin"
)

func (e *Entry) checkSpuCode(ctx *gin.Context, code string, spuID uint64) error {
	cnt, err := e.SpuRepo.CountByFilter(ctx, &spuDao.Filter{Code: code, NotID: spuID})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return ecode.SpuCodeExist
	}
	return nil
}

func (e *Entry) checkSpuSkuCode(ctx *gin.Context, skuList goodsDto.AdminSpuCreateSkuList) error {
	codeList, skuIds := skuList.GetSkuInfos()
	if len(codeList) != len(skuList) {
		return ecode.SkuCodeExist
	}

	cnt, err := e.SkuRepo.CountByFilter(ctx, &skuDao.Filter{CodeList: codeList, NotIds: skuIds})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return ecode.SkuCodeExist
	}

	return nil
}

func (e *Entry) CheckSpuExist(ctx *gin.Context, spuId uint64) (*spuDao.Model, error) {
	model, err := e.SpuRepo.FetchByID(ctx, spuId)
	if err != nil {
		return nil, err
	}

	if model.ID == 0 {
		return nil, ecode.SpuNotExistErr
	}
	return model, nil
}



func (e *Entry) checkSkuCode(ctx *gin.Context, code string, skuId uint64) error {
	cnt, err := e.SkuRepo.CountByFilter(ctx, &skuDao.Filter{Code: code, NotId: skuId})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return ecode.SkuCodeExist
	}
	return nil
}

type FilterSpuSkuSearch interface {
	DealFilterOption(ctx *gin.Context, entry *Entry, req commonDto.CommonSpuSkuFilterReq) (*DealFilterRet, error)
}
type DealFilterRet struct {
	UserIds    []uint64
	OrderIds   []uint64
	SkuIds     []uint64
	SpuIds     []uint64
	CreateAids []uint64
	CreateUids []uint64
	Empty      bool
	CreateBy   bool
}

type FilterSkuID struct{}

func (f FilterSkuID) DealFilterOption(ctx *gin.Context, e *Entry, req commonDto.CommonSpuSkuFilterReq) (*DealFilterRet, error) {
	var (
		ret          = &DealFilterRet{}
		searchSkuIds []uint64
		err          error
	)
	if req.SkuID > 0 {
		ret.SkuIds = append(ret.SkuIds, req.SkuID)
	} else if len(req.SkuIds) > 0 {
		ret.SkuIds = req.SkuIds
	}
	if req.SkuTitle != "" || req.SkuCode != "" {
		if searchSkuIds, err = e.SkuRepo.FindXidsByFilter(ctx, &skuDao.Filter{CodeLike: req.SkuCode, TitleLike: req.SkuTitle}, dbs.PluckID); err != nil {
			return nil, err
		}
		if len(searchSkuIds) == 0 {
			ret.Empty = true
			return ret, nil
		}
		ret.SkuIds = sliceUtil.GetUintsIntersect(searchSkuIds, ret.SkuIds)
		if len(ret.SkuIds) == 0 {
			ret.Empty = true
		}
	}
	return ret, nil
}

type FilterSpuID struct{}

func (f FilterSpuID) DealFilterOption(ctx *gin.Context, e *Entry, req commonDto.CommonSpuSkuFilterReq) (*DealFilterRet, error) {
	var (
		ret          = &DealFilterRet{}
		searchSpuIds = []uint64{}
		err          error
	)
	if req.SpuID > 0 {
		ret.SpuIds = append(ret.SpuIds, req.SpuID)
	} else if len(req.SpuIds) > 0 {
		ret.SpuIds = req.SpuIds
	}

	if req.SpuTitle != "" || req.SpuCode != "" {
		if ret.SpuIds, err = e.SpuRepo.FindXidsByFilter(ctx, &spuDao.Filter{CodeLike: req.SpuCode, TitleLike: req.SpuTitle}, dbs.PluckID); err != nil {
			return nil, err
		}
		if len(ret.SpuIds) == 0 {
			ret.Empty = true
		}
	}
	if req.CategoryID > 0 {
		if searchSpuIds, err = e.SpuTagRepo.FindXidsByFilter(ctx, &spuTag.Filter{TagId: req.CategoryID}, dbs.PluckSpuID); err != nil {
			return nil, err
		}

		if len(searchSpuIds) == 0 {
			ret.Empty = true
			return ret, nil
		}
		ret.SpuIds = sliceUtil.GetUintsIntersect(searchSpuIds, ret.SpuIds)
		if len(ret.SpuIds) == 0 {
			ret.Empty = true
		}
	}
	return ret, nil
}

type FilterSpuIDByAttr struct{}

func (f FilterSpuIDByAttr) DealFilterOption(ctx *gin.Context, e *Entry, req commonDto.CommonSpuSkuFilterReq) (*DealFilterRet, error) {
	var (
		ret          = &DealFilterRet{}
		searchSpuIds = []uint64{}
		err          error
	)

	if req.SkuTitle != "" || req.SkuCode != "" || req.SkuID > 0 || len(req.SkuIds) > 0 {
		if ret.SpuIds, err = e.SkuRepo.FindXidsByFilter(ctx, &skuDao.Filter{
			CodeLike:  req.SkuCode,
			TitleLike: req.SkuTitle,
			ID:        req.SkuID,
			Ids:       req.SkuIds,
		}, dbs.PluckSpuID); err != nil {
			return nil, err
		}
		if len(ret.SpuIds) == 0 {
			ret.Empty = true
		}
	}
	if req.CategoryID > 0 {
		if searchSpuIds, err = e.SpuTagRepo.FindXidsByFilter(ctx, &spuTag.Filter{TagId: req.CategoryID}, dbs.PluckSpuID); err != nil {
			return nil, err
		}

		if len(searchSpuIds) == 0 {
			ret.Empty = true
			return ret, nil
		}
		ret.SpuIds = sliceUtil.GetUintsIntersect(searchSpuIds, ret.SpuIds)
		if len(ret.SpuIds) == 0 {
			ret.Empty = true
		}
	}

	return ret, nil
}

func (e *Entry) dealSpuSkuSearchFilter(ctx *gin.Context, req commonDto.CommonSpuSkuFilterReq, getOrderIds bool) (*DealFilterRet, error) {
	filterMap := map[string]FilterSpuSkuSearch{
		"skuId": FilterSkuID{},
		"spuId": FilterSpuID{},
	}

	ret := &DealFilterRet{}
	for key, filter := range filterMap {
		filterRet, err := filter.DealFilterOption(ctx, e, req)
		if err != nil {
			return nil, err
		}
		if filterRet.Empty {
			ret.Empty = true
			return ret, nil
		}
		switch key {
		case "skuId":
			ret.SkuIds = filterRet.SkuIds
		case "spuId":
			ret.SpuIds = filterRet.SpuIds
		}
	}
	return ret, nil
}
