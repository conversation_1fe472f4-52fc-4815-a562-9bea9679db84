package goods

import (
	"blind_box/app/common/dbs"
	batchDao "blind_box/app/dao/goods/batch"
	spuDao "blind_box/app/dao/goods/spu"
	spuBatchDao "blind_box/app/dao/goods/spu_batch"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"

	"github.com/gin-gonic/gin"
)

// CheckBatch 检查批次
func (e *Entry) CheckBatch(ctx *gin.Context, batchID uint64) (*batchDao.Model, error) {
	model, err := e.BatchRepo.FetchByID(ctx, batchID)
	if err != nil {
		return nil, err
	}
	if model.ID == 0 {
		return nil, ecode.BatchNotExist
	}

	return model, nil
}

// AdminBatchList 批次列表
func (e *Entry) AdminBatchList(ctx *gin.Context, req goodsDto.AdminBatchListReq) (ret *goodsDto.AdminBatchListResp, err error) {
	total, list, err := e.BatchRepo.DataPageList(ctx, &batchDao.Filter{
		TitleLike: req.Title,
		SellType:  req.SellType,
		Status:    req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]goodsDto.AdminBatchListItem, 0, len(list))
	for _, val := range list {
		item := goodsDto.AdminBatchListItem{
			ID:        val.ID,
			Title:     val.Title,
			SellType:  val.SellType,
			Desc:      val.Desc,
			Status:    val.Status,
			CreatedAt: val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}

	return &goodsDto.AdminBatchListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminBatchDetail 批次详情
func (e Entry) AdminBatchDetail(ctx *gin.Context, batchID uint64) (ret *goodsDto.AdminBatchDetailResp, err error) {
	model, err := e.CheckBatch(ctx, batchID)
	if err != nil {
		return nil, err
	}

	batchSpuList, err := e.SpuBatchRepo.FindByFilter(ctx, &spuBatchDao.Filter{BatchId: batchID})
	if err != nil {
		return nil, err
	}
	spuList, err := e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{Ids: batchSpuList.GetSpuIds()})
	if err != nil {
		return nil, err
	}

	spuListResp := make([]goodsDto.AdminSpuListItem, 0, len(spuList))
	for _, val := range spuList {
		spuListResp = append(spuListResp, goodsDto.ToSpuItemResp(ctx, val, model))
	}

	return &goodsDto.AdminBatchDetailResp{
		AdminBatchListItem: goodsDto.AdminBatchListItem{
			ID:        model.ID,
			Title:     model.Title,
			SellType:  model.SellType,
			Desc:      model.Desc,
			Status:    model.Status,
			CreatedAt: model.GetCreatedTime(),
		},
		SpuList: spuListResp,
	}, nil
}

// AdminBatchCreate 创建批次
func (e Entry) AdminBatchCreate(ctx *gin.Context, req goodsDto.AdminBatchCreateReq) (uint64, error) {
	var (
		model = &batchDao.Model{
			Title:    req.Title,
			SellType: req.SellType,
			Desc:     req.Desc,
			Status:   uint32(dbs.StatusEnable),
		}
		cnt, err = e.BatchRepo.CountByFilter(ctx, &batchDao.Filter{
			Title: req.Title,
		})
	)
	if err != nil {
		return model.ID, err
	}
	if cnt > 0 {
		return model.ID, ecode.BatchTitleExist
	}

	if err = e.BatchRepo.CreateOrUpdate(ctx, model); err != nil {
		return model.ID, err
	}

	return model.ID, nil
}

// AdminBatchUpdate 更新批次
func (e Entry) AdminBatchUpdate(ctx *gin.Context, req goodsDto.AdminBatchUpdateReq) (uint64, error) {
	var (
		num int64
		err error

		m = &batchDao.Model{
			ID:       req.ID,
			Title:    req.Title,
			SellType: req.SellType,
			Desc:     req.Desc,
			Status:   req.Status,
		}
	)

	if _, err = e.CheckBatch(ctx, req.ID); err != nil {
		return 0, err
	}

	if num, err = e.BatchRepo.CountByFilter(ctx, &batchDao.Filter{Title: req.Title, NotID: req.ID}); err != nil {
		return 0, err
	}
	if num > 0 {
		return 0, ecode.BatchTitleExist
	}

	if req.Status == uint32(dbs.StatusDisable) {
		// 校验是否有spu使用该批次
		cnt, err := e.SpuBatchRepo.CountByFilter(ctx, &spuBatchDao.Filter{BatchId: req.ID})
		if err != nil {
			return 0, err
		}
		if cnt > 0 {
			return 0, ecode.BatchInUseErr
		}
	}

	if err = e.BatchRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminBatchDelete 删除批次
func (e Entry) AdminBatchDelete(ctx *gin.Context, batchID uint64) error {
	if _, err := e.CheckBatch(ctx, batchID); err != nil {
		return err
	}

	// 校验是否有spu使用该批次
	cnt, err := e.SpuBatchRepo.CountByFilter(ctx, &spuBatchDao.Filter{BatchId: batchID})
	if err != nil {
		return err
	}
	if cnt > 0 {
		return ecode.BatchInUseErr
	}

	return e.BatchRepo.DeleteByID(ctx, batchID)
}

// AdminBatchOperate 操作批次
func (e *Entry) AdminBatchOperate(ctx *gin.Context, batchID uint64, action dbs.OperateAction) error {
	if _, err := e.CheckBatch(ctx, batchID); err != nil {
		return err
	}

	if action == dbs.ActionClose {
		// 校验是否有spu使用该批次
		cnt, err := e.SpuBatchRepo.CountByFilter(ctx, &spuBatchDao.Filter{BatchId: batchID})
		if err != nil {
			return err
		}
		if cnt > 0 {
			return ecode.BatchInUseErr
		}
	}

	return e.BatchRepo.UpdateMapByID(ctx, batchID, map[string]interface{}{"status": dbs.OperateActionMap[action]})
}
