package admin

import (
	"blind_box/app/common/dbs"
	adminAccount "blind_box/app/dao/admin/account"
	adminRole "blind_box/app/dao/admin/role"
	adminDto "blind_box/app/dto/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// CheckOperationRoleAuth .
func (e *Entry) CheckOperationRoleAuth(ctx *gin.Context, roleID uint64) error {
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		return err
	}
	// 仅root可操作root
	if roleID == dbs.RoleRootID && !e.JudgeIsRoot(ctxAccount.RoleID) {
		return ecode.NoAuthErr
	}
	// 仅root or admin可操作admin
	if roleID == dbs.RoleAdminID && !e.EditableFirstAdmin(ctxAccount.AccountID) {
		return ecode.NoAuthErr
	}
	return nil
}

func (e *Entry) JudgeIsRoot(roleID uint64) bool {
	return roleID == dbs.RoleRootID
}

func (e *Entry) JudgeIsAdmin(roleID uint64) bool {
	return roleID == dbs.RoleRootID || roleID == dbs.RoleAdminID
}

func (e *Entry) EditableFirstAdmin(accountID uint64) bool {
	return accountID == dbs.RootUID || accountID == dbs.AdminUID
}

func (e *Entry) GetCommonRoleFilter(ctx *gin.Context) []uint64 {
	var notRoleIds []uint64
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.RoleID != dbs.RoleRootID {
		notRoleIds = append(notRoleIds, dbs.RoleRootID)
		if ctxAccount.RoleID != dbs.RoleAdminID {
			notRoleIds = append(notRoleIds, dbs.RoleAdminID)
		}
	}
	return notRoleIds
}

func (e *Entry) GetCommonAccountFilter(ctx *gin.Context) []uint64 {
	var notAccountIds []uint64
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		notAccountIds = append(notAccountIds, dbs.RootUID)
		if ctxAccount.RoleID != dbs.AdminUID {
			notAccountIds = append(notAccountIds, dbs.AdminUID)
		}
	}
	return notAccountIds
}

// AdminRoleList .
func (e *Entry) AdminRoleList(ctx *gin.Context, req *adminDto.AdminRoleListReq) (*adminDto.AdminRoleListResp, error) {
	total, list, err := e.RoleRepo.DataPageList(ctx, &adminRole.Filter{
		Name: req.Name, Status: req.Status, NotIds: req.NotRids,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*adminDto.AdminRoleListItem, 0, len(list))
	for _, val := range list {
		item := &adminDto.AdminRoleListItem{
			ID:        val.ID,
			Name:      val.Name,
			Status:    val.Status,
			IsAdmin:   e.JudgeIsAdmin(val.ID),
			CreatedAt: val.GetCreatedTime(),
		}
		retList = append(retList, item)
	}
	return &adminDto.AdminRoleListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSetRole .
func (e *Entry) AdminSetRole(ctx *gin.Context, req *adminDto.AdminSetRoleReq) (*adminRole.Model, error) {
	var (
		err   = e.CheckOperationRoleAuth(ctx, req.ID)
		eg    errgroup.Group
		model = &adminRole.Model{}
		num   int64
		m     = &adminRole.Model{
			ID:     req.ID,
			Name:   req.Name,
			Status: req.Status,
		}
	)
	if err != nil {
		return nil, err
	}
	if req.ID > 0 {
		if e.JudgeIsAdmin(req.ID) {
			m.Status = uint32(dbs.StatusEnable)
		}
		eg.Go(func() (err error) {
			if model, err = e.RoleRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.RoleRepo.CountByFilter(ctx, &adminRole.Filter{EqName: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.RoleExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.RoleRepo.CountByFilter(ctx, &adminRole.Filter{EqName: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.RoleExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	if err = e.RoleRepo.CreateOrUpdate(ctx, m); err != nil {
		return nil, err
	}

	return m, nil
}

// AdminSetRoleAuth .
func (e *Entry) AdminSetRoleAuth(ctx *gin.Context, roleID uint64, MenuIds []uint64) error {
	if e.JudgeIsAdmin(roleID) {
		return nil
	}

	model, err := e.RoleRepo.FetchByID(ctx, roleID)
	if err != nil {
		return err
	}
	if model.ID == 0 {
		return ecode.ParamErr
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		roleMenu := []*adminRole.RoleMenu{}
		for _, mid := range MenuIds {
			roleMenu = append(roleMenu, &adminRole.RoleMenu{
				RoleID: roleID,
				MenuID: mid,
			})
		}

		if err = e.RoleRepo.DelByRidWithTx(ctx, tx, roleID); err != nil {
			return
		}
		if err = e.RoleRepo.BatchCreateRoleMenuWithTx(tx, roleMenu); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Callback()
		return err
	}

	return nil
}

// AdminDelRole .
func (e *Entry) AdminDelRole(ctx *gin.Context, roleID uint64) error {
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if !e.JudgeIsAdmin(ctxAccount.RoleID) {
		return ecode.AccountNeedRootAuthErr
	}
	if e.JudgeIsAdmin(roleID) {
		return nil
	}
	var (
		tx  = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		num int64
		err error
	)
	if num, err = e.AccountRepo.CountByFilter(ctx, &adminAccount.Filter{RoleID: roleID}); err != nil {
		return err
	}
	if num > dbs.False {
		return ecode.RoleHasAccountErr
	}

	if err := func() (err error) {
		if err = e.RoleRepo.DelByID(ctx, tx, roleID); err != nil {
			return
		}
		if err = e.RoleRepo.DelByRidWithTx(ctx, tx, roleID); err != nil {
			return
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Callback()
		return err
	}

	return nil
}

// AdminRoleAll .
func (e *Entry) AdminRoleAll(ctx *gin.Context, req *adminDto.AdminRoleAllReq) ([]*adminDto.AdminRoleListItem, error) {
	var (
		ctxAccount = &helper.CtxAccount{}
		list       = adminRole.ModelList{}
		err        error
	)
	if req.Enable == dbs.True {
		if list, err = e.RoleRepo.RedisEnableRoleList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.RoleRepo.RedisRoleList(ctx); err != nil {
			return nil, err
		}
	}
	if ctxAccount, err = helper.GetCtxAccount(ctx); err != nil {
		return nil, err
	}

	retList := make([]*adminDto.AdminRoleListItem, 0, len(list))
	for _, val := range list {
		// 仅root可操作root
		if val.ID == dbs.RoleRootID && !e.JudgeIsRoot(ctxAccount.RoleID) {
			continue
		}
		// 仅root or admin可操作admin
		if val.ID == dbs.RoleAdminID && !e.EditableFirstAdmin(ctxAccount.AccountID) {
			continue
		}
		item := &adminDto.AdminRoleListItem{
			ID:   val.ID,
			Name: val.Name,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
