package admin

import (
	"blind_box/app/common/dbs"
	areaDao "blind_box/app/dao/admin/area"
	adminDto "blind_box/app/dto/admin"

	"github.com/gin-gonic/gin"
)

// AdminAreaList .
func (e *Entry) AdminAreaList(ctx *gin.Context) ([]*adminDto.AdminAreaListResp, error) {
	list, err := e.AreaRepo.RedisAreaList(ctx)
	if err != nil {
		return nil, err
	}

	retList := e.getArea(list, dbs.False)
	return retList, nil
}

func (e *Entry) getArea(list areaDao.ModelList, pid uint64) []*adminDto.AdminAreaListResp {
	ret := []*adminDto.AdminAreaListResp{}
	for _, area := range list {
		if area.Pid == pid {
			child := e.getArea(list, area.ID)
			item := &adminDto.AdminAreaListResp{
				ID:    area.ID,
				Pid:   area.Pid,
				Name:  area.Name,
				Child: child,
			}
			ret = append(ret, item)
		}
	}

	return ret
}
