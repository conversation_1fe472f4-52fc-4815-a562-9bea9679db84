package admin

import (
	"blind_box/app/common/dbs"
	adminMenu "blind_box/app/dao/admin/menu"
	adminMenuBackup "blind_box/app/dao/admin/menu_backup"
	adminRole "blind_box/app/dao/admin/role"
	adminDto "blind_box/app/dto/admin"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminMenuList .
func (e *Entry) AdminMenuList(ctx *gin.Context) ([]*adminDto.AdminMenuListResp, error) {
	list, err := e.MenuRepo.RedisMenuList(ctx)
	if err != nil {
		return nil, err
	}

	retList := e.getMenuTreeList(list, dbs.False)
	return retList, nil
}

// AdminMenuOption .
func (e *Entry) AdminMenuOption(ctx *gin.Context, req *adminDto.AdminMenuOptionReq) ([]*adminDto.AdminMenuOptionResp, error) {
	list, err := e.MenuRepo.RedisMenuList(ctx)
	if err != nil {
		return nil, err
	}

	var (
		roleAuthMenu    = adminRole.RoleAuthMenuIds{}
		roleAuthMenuMap = make(map[uint64]struct{})
	)
	if req.RoleID != 0 && !e.JudgeIsAdmin(req.RoleID) {
		if roleAuthMenu, err = e.RoleRepo.RedisRoleMenuIds(ctx, req.RoleID); err != nil {
			return nil, err
		}
		roleAuthMenuMap = roleAuthMenu.GetAuthMenuEmptyMap()
	}

	retList := e.getMenuOptionTreeList(list, dbs.False, req.RoleID, roleAuthMenuMap)
	return retList, nil
}

// AdminMenuRouter .
func (e *Entry) AdminMenuRouter(ctx *gin.Context, roleId uint64) ([]*adminDto.AdminMenuRouterResp, error) {
	var (
		menuList = adminMenu.ModelList{}
		err      error
		menuIds  = []uint64{}
	)
	if e.JudgeIsAdmin(roleId) {
		if menuList, err = e.MenuRepo.RedisMenuList(ctx); err != nil {
			return nil, err
		}
	} else {
		if menuIds, err = e.RoleRepo.RedisRoleMenuIds(ctx, roleId); err != nil {
			return nil, err
		}
		if len(menuIds) == dbs.False {
			return []*adminDto.AdminMenuRouterResp{}, nil
		}
		if menuList, err = e.MenuRepo.FindByFilter(ctx, &adminMenu.Filter{IDS: menuIds}); err != nil {
			return nil, err
		}
	}

	retList := e.getMenuRouterTreeList(menuList, dbs.False)
	return retList, nil
}

// AdminSetMenu .
func (e *Entry) AdminSetMenu(ctx *gin.Context, req *adminDto.AdminSetMenuReq) error {
	if req.ID == req.Pid && req.ID != 0 {
		return ecode.MenuPidSelfErr
	}

	var (
		model = &adminMenu.Model{}
		err   error
		m     = adminMenu.SetMenuReqToModel(req)
	)
	if req.Pid != dbs.False {
		if model, err = e.MenuRepo.FetchByID(ctx, req.Pid); err != nil {
			return err
		}
		if model.MenuType == uint32(adminMenu.MtButton) {
			return ecode.MenuPidButtonErr
		}
	}
	if req.ID > 0 {
		if model, err = e.MenuRepo.FetchByID(ctx, req.ID); err != nil {
			return err
		}
		if model.ID == 0 {
			return ecode.ParamErr
		}

		if req.Pid != model.Pid && e.chekMenuRelation(ctx, req.ID, req.Pid) {
			return ecode.MenuPidIsSelfChildErr
		}
	}

	if err = e.MenuRepo.CreateOrUpdate(ctx, m); err != nil {
		return err
	}

	return nil
}

// AdminOperateMenu .
func (e *Entry) AdminOperateMenu(ctx *gin.Context, id uint64, action dbs.OperateAction) error {
	var (
		model = &adminMenu.Model{}
		count int64
		err   error
	)
	if model, err = e.MenuRepo.FetchByID(ctx, id); err != nil {
		return err
	}
	if model.ID == 0 {
		return ecode.ParamErr
	}

	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.MenuRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionDel:
		if model.SystemMenu == uint32(dbs.StatusEnable) {
			return ecode.MenuDelSystemMenuErr
		}
		if count, err = e.MenuRepo.CountByFilter(ctx, &adminMenu.Filter{ByPid: true, Pid: id}); err != nil {
			return err
		}
		if count > 0 {
			return ecode.MenuDelHasChildErr
		}

		return e.MenuRepo.UpdateMapByID(ctx, id, map[string]interface{}{string(dbs.SoftDelField): dbs.True})
	}
	return nil
}

func (e *Entry) chekMenuRelation(ctx *gin.Context, mid, pid uint64) bool {
	if pid == dbs.False {
		return false
	}
	menuList, err := e.MenuRepo.RedisMenuList(ctx)
	if err != nil {
		return true
	}

	menuTree := e.getMenuTree(menuList, mid)
	for _, childID := range menuTree.ChildIds {
		if childID == pid {
			return true
		}
	}

	return false
}

func (e *Entry) getMenuTreeList(list adminMenu.ModelList, pid uint64) []*adminDto.AdminMenuListResp {
	ret := []*adminDto.AdminMenuListResp{}
	for _, menu := range list {
		if menu.Pid == pid {
			childIds := []uint64{}
			child := e.getMenuTreeList(list, menu.ID)
			for _, temp := range child {
				childIds = append(childIds, temp.ID)
				childIds = append(childIds, temp.ChildIds...)
			}
			item := &adminDto.AdminMenuListResp{
				ID:        menu.ID,
				Pid:       menu.Pid,
				Title:     menu.Title,
				Icon:      menu.Icon,
				MenuType:  menu.MenuType,
				Component: menu.Component,
				BeAuth:    menu.BeAuth,
				IsShow:    menu.IsShow,
				Sort:      menu.Sort,
				Path:      menu.Path,
				PathName:  menu.PathName,
				Redirect:  menu.Redirect,
				FeAuth:    menu.FeAuth,
				Status:    menu.Status,
				KeepAlive: menu.KeepAlive,
				Child:     child,
				ChildIds:  childIds,
			}

			ret = append(ret, item)
		}
	}

	return ret
}

func (e *Entry) getMenuTree(list adminMenu.ModelList, pid uint64) *adminDto.AdminMenuListResp {
	ret := &adminDto.AdminMenuListResp{}
	if pid == 0 {
		return ret
	}
	for _, menu := range list {
		if menu.ID == pid {
			ret.ID = menu.ID
			ret.Pid = menu.Pid
			ret.Title = menu.Title
			ret.ChildIds = []uint64{}
			ret.Child = []*adminDto.AdminMenuListResp{}
		}
		if menu.Pid == pid {
			child := e.getMenuTree(list, menu.ID)
			ret.ChildIds = append(ret.ChildIds, child.ID)
			ret.ChildIds = append(ret.ChildIds, child.ChildIds...)
			ret.Child = append(ret.Child, child)
		}
	}

	return ret
}

func (e *Entry) getMenuOptionTreeList(list adminMenu.ModelList, pid, rid uint64, roleAuth map[uint64]struct{}) []*adminDto.AdminMenuOptionResp {
	ret := []*adminDto.AdminMenuOptionResp{}
	for _, menu := range list {
		if menu.Pid == pid {
			child := e.getMenuOptionTreeList(list, menu.ID, rid, roleAuth)
			item := &adminDto.AdminMenuOptionResp{
				ID:     menu.ID,
				Pid:    menu.Pid,
				Title:  menu.Title,
				Status: menu.Status,
				Child:  child,
			}
			if e.JudgeIsAdmin(rid) {
				item.HasAuth = dbs.True
			} else if _, ok := roleAuth[menu.ID]; ok {
				item.HasAuth = dbs.True
			}

			ret = append(ret, item)
		}
	}

	return ret
}

func (e *Entry) getMenuRouterTreeList(list adminMenu.ModelList, pid uint64) []*adminDto.AdminMenuRouterResp {
	ret := []*adminDto.AdminMenuRouterResp{}
	for _, menu := range list {
		if menu.Status == uint32(dbs.StatusDisable) {
			continue
		}
		if menu.Pid == pid {
			child := e.getMenuRouterTreeList(list, menu.ID)
			item := &adminDto.AdminMenuRouterResp{
				ID:        menu.ID,
				Pid:       menu.Pid,
				Title:     menu.Title,
				Icon:      menu.Icon,
				MenuType:  menu.MenuType,
				Component: menu.Component,
				Path:      menu.Path,
				PathName:  menu.PathName,
				Redirect:  menu.Redirect,
				FeAuth:    menu.FeAuth,
				IsShow:    menu.IsShow,
				KeepAlive: menu.KeepAlive,
				Child:     child,
			}

			ret = append(ret, item)
		}
	}

	return ret
}

// AdminMenuInit .
func (e *Entry) AdminMenuInit(ctx *gin.Context) error {
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		return ecode.AccountNeedRootAuthErr
	}

	if err := e.insertNewMenu(ctx, 0, 1, nil); err != nil {
		return err
	}
	return nil
}

func (e *Entry) insertNewMenu(ctx *gin.Context, pid uint64, level uint32, originNowPidMap map[uint64]uint64) error {
	list, err := e.MenuRepo.FindByFilter(ctx, &adminMenu.Filter{ByPid: true, Pid: pid})
	if err != nil {
		return err
	}
	if len(list) == 0 {
		return nil
	}
	pidMap := map[uint64]uint64{}
	for _, val := range list {
		backup := &adminMenuBackup.Model{
			Title:      val.Title,
			Icon:       val.Icon,
			MenuType:   val.MenuType,
			Component:  val.Component,
			BeAuth:     val.BeAuth,
			IsShow:     val.IsShow,
			SystemMenu: val.SystemMenu,
			IsRoot:     val.IsRoot,
			Sort:       val.Sort,
			Status:     val.Status,
			Level:      level,
			KeepAlive:  val.KeepAlive,
			Path:       val.Path,
			PathName:   val.PathName,
			Redirect:   val.Redirect,
			FeAuth:     val.FeAuth,
			Desc:       val.Desc,
		}
		if tempId, ok := originNowPidMap[val.Pid]; ok {
			backup.Pid = tempId
		}
		if _, err = e.MenuBackupRepo.CreateOrUpdate(ctx, backup); err != nil {
			return err
		}
		pidMap[val.ID] = backup.ID
	}

	for _, val := range list {
		if err = e.insertNewMenu(ctx, val.ID, level+1, pidMap); err != nil {
			return err
		}
	}
	return nil
}
