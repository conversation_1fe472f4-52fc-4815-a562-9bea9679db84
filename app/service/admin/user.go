package admin

import (
	"fmt"
	"time"

	userDao "blind_box/app/dao/user"
	"blind_box/app/dao/user/points"
	adminDto "blind_box/app/dto/admin"
	userSrv "blind_box/app/service/user"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

// AdminEditUserPoints 管理员编辑用户积分
func (e *Entry) AdminEditUserPoints(ctx *gin.Context, req *adminDto.AdminEditUserPointsReq) (*adminDto.AdminEditUserPointsRes, error) {
	// 验证用户是否存在
	users, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: []uint64{req.UserID}})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminEditUserPoints: fetch user failed")
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if len(users) == 0 {
		return nil, fmt.Errorf("用户不存在")
	}
	userModel := users[0]

	// 获取用户当前积分余额
	userService := userSrv.GetService()
	currentBalance, err := userService.GetUserPointsBalance(ctx, req.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminEditUserPoints: get user valid points balance failed")
		return nil, fmt.Errorf("获取用户积分余额失败: %w", err)
	}

	// 构建操作备注
	operatorName := fmt.Sprintf("管理员ID:%d", req.OperatorID)
	adminRemark := fmt.Sprintf("管理员操作 - %s，备注：%s", operatorName, req.Remark)

	var pointsLog *points.PointsLog
	var newBalance uint64

	// 根据操作类型执行积分变更
	switch req.ActionType {
	case 1: // 增加积分
		// 设置积分过期时间为一年后
		expireAt := time.Now().AddDate(1, 0, 0)

		// 增加积分
		err = userService.AddUserPointsWithExpire(ctx, req.UserID, req.Points,
			points.PointsSourceTypeAdmin, req.OperatorID, adminRemark, &expireAt)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEditUserPoints: add user points failed")
			return nil, fmt.Errorf("增加积分失败: %w", err)
		}

		newBalance = currentBalance + req.Points

		// 获取最新创建的积分日志（用于返回详细信息）
		pointsLogs, err := points.GetRepo().FindByFilter(ctx, &points.PointsLogFilter{
			UserID:     req.UserID,
			ActionType: uint32(points.PointsActionTypeAdd),
			SourceType: uint32(points.PointsSourceTypeAdmin),
			SourceID:   req.OperatorID,
		})
		if err == nil && len(pointsLogs) > 0 {
			pointsLog = pointsLogs[len(pointsLogs)-1] // 获取最新的记录
		}

	case 2: // 减少积分
		// 检查积分余额是否足够
		if currentBalance < req.Points {
			return nil, fmt.Errorf("用户积分余额不足，当前余额：%d，尝试扣减：%d", currentBalance, req.Points)
		}

		// 使用FIFO原则扣减积分
		err = userService.UsePointsDiscountWithTx(ctx, e.UserRepo.MysqlEngine.UseWithGinCtx(ctx, true),
			req.UserID, req.Points, adminRemark)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEditUserPoints: subtract user points failed")
			return nil, fmt.Errorf("扣减积分失败: %w", err)
		}

		newBalance = currentBalance - req.Points

		// 创建管理员扣减记录（补充记录）
		pointsLog = &points.PointsLog{
			UserID:        req.UserID,
			ActionType:    uint32(points.PointsActionTypeSubtract),
			Points:        req.Points,
			BalanceBefore: currentBalance,
			BalanceAfter:  newBalance,
			SourceType:    uint32(points.PointsSourceTypeAdmin),
			SourceID:      req.OperatorID,
			Remark:        adminRemark,
		}

	default:
		return nil, fmt.Errorf("不支持的操作类型: %d", req.ActionType)
	}

	// 构建响应
	res := &adminDto.AdminEditUserPointsRes{
		UserID:        req.UserID,
		UserName:      userModel.Nickname,
		ActionType:    req.ActionType,
		Points:        req.Points,
		BalanceBefore: currentBalance,
		BalanceAfter:  newBalance,
		Remark:        req.Remark,
		CreatedTime:   time.Now().Format("2006-01-02 15:04:05"),
	}

	if pointsLog != nil {
		res.CreatedTime = pointsLog.GetCreatedTime()
	}

	log.Ctx(ctx).Info("AdminEditUserPoints success: UserID=%d, ActionType=%d, Points=%d, OperatorID=%d, NewBalance=%d",
		req.UserID, req.ActionType, req.Points, req.OperatorID, newBalance)

	return res, nil
}

// AdminUserPointsLog 管理员查询用户积分记录
func (e *Entry) AdminUserPointsLog(ctx *gin.Context, req *adminDto.AdminUserPointsLogReq) (*adminDto.AdminUserPointsLogRes, error) {
	// 构建查询过滤条件
	filter := &points.PointsLogFilter{}

	if req.UserID > 0 {
		filter.UserID = req.UserID
	}

	if req.ActionType > 0 {
		filter.ActionType = req.ActionType
	}

	if req.SourceType > 0 {
		filter.SourceType = req.SourceType
	}

	// 处理时间范围
	if req.StartTime != "" {
		if startTime := carbon.Parse(req.StartTime + " 00:00:00"); !startTime.IsZero() {
			filter.StartTime = startTime.Timestamp()
		}
	}

	if req.EndTime != "" {
		if endTime := carbon.Parse(req.EndTime + " 23:59:59"); !endTime.IsZero() {
			filter.EndTime = endTime.Timestamp()
		}
	}

	if req.Name != "" {
		userList, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{Nickname: req.Name})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminUserPointsLog: query users failed")
			return nil, fmt.Errorf("查询用户信息失败: %w", err)
		}

		if t := userList.GetIDS(); len(t) > 0 {
			filter.UserIDs = t // 如果有用户ID，则设置过滤条件
		}
	}
	// 获取积分记录
	total, pointsLogs, err := points.GetRepo().DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsLog: query points logs failed")
		return nil, fmt.Errorf("查询积分记录失败: %w", err)
	}

	// 获取相关用户信息
	userIDs := pointsLogs.GetUserIDs()
	users, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: userIDs})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsLog: query users failed")
		return nil, fmt.Errorf("查询用户信息失败: %w", err)
	}
	userMap := users.GetIDMap()

	// 构建响应数据
	items := make([]*adminDto.AdminUserPointsLogItem, 0, len(pointsLogs))
	for _, pointsLog := range pointsLogs {
		item := &adminDto.AdminUserPointsLogItem{
			ID:            pointsLog.ID,
			UserID:        pointsLog.UserID,
			ActionType:    pointsLog.ActionType,
			Points:        pointsLog.Points,
			BalanceBefore: pointsLog.BalanceBefore,
			BalanceAfter:  pointsLog.BalanceAfter,
			SourceType:    pointsLog.SourceType,
			SourceID:      pointsLog.SourceID,
			Remark:        pointsLog.Remark,
			CreatedTime:   pointsLog.GetCreatedTime(),
		}

		// 设置用户名
		if user, exists := userMap[pointsLog.UserID]; exists {
			item.UserName = user.Nickname
		}

		// 设置操作类型名称
		if name, exists := points.PointsActionMap[pointsLog.ActionType]; exists {
			item.ActionTypeName = name
		}

		// 设置来源类型名称
		if name, exists := points.PointsSourceMap[pointsLog.SourceType]; exists {
			item.SourceTypeName = name
		}

		// 设置剩余积分（仅对增加类型有效）
		if pointsLog.IsEarnType() {
			item.RemainingPoints = pointsLog.GetRemainingPoints()
		}

		// 设置过期时间和过期状态
		if pointsLog.ExpireAt != nil {
			item.ExpireAt = pointsLog.ExpireAt.Format("2006-01-02 15:04:05")
			item.IsExpired = pointsLog.IsExpired == 1 || pointsLog.ExpireAt.Before(time.Now())
		}

		items = append(items, item)
	}

	return &adminDto.AdminUserPointsLogRes{
		List:  items,
		Total: total,
	}, nil
}

// AdminUserPointsStats 管理员查询用户积分统计
func (e *Entry) AdminUserPointsStats(ctx *gin.Context, req *adminDto.AdminUserPointsStatsReq) (*adminDto.AdminUserPointsStatsRes, error) {
	// 验证用户是否存在
	users, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: []uint64{req.UserID}})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsStats: fetch user failed")
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if len(users) == 0 {
		return nil, fmt.Errorf("用户不存在")
	}
	userModel := users[0]

	// 获取用户积分总计（全部来源）
	addSum, subtractSum, err := points.GetRepo().GetUserPointsSum(ctx, req.UserID, []uint32{})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsStats: get user points sum failed")
		return nil, fmt.Errorf("查询积分总计失败: %w", err)
	}

	// 获取当前有效积分余额
	userService := userSrv.GetService()
	currentBalance, err := userService.GetUserPointsBalance(ctx, req.UserID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsStats: get current balance failed")
		return nil, fmt.Errorf("查询当前积分余额失败: %w", err)
	}

	// 计算已过期积分
	expiredSum, _, err := points.GetRepo().GetUserPointsSum(ctx, req.UserID, []uint32{uint32(points.PointsSourceTypeExpire)})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsStats: get expired points failed")
		return nil, fmt.Errorf("查询过期积分失败: %w", err)
	}

	// 查询即将过期的积分（30天内）
	thirtyDaysLater := time.Now().AddDate(0, 0, 30)
	expiringLogs, err := points.GetRepo().FindByFilter(ctx, &points.PointsLogFilter{
		UserID:         req.UserID,
		ActionType:     uint32(points.PointsActionTypeAdd),
		OnlyWithExpire: true,
		ExpireBefore:   &thirtyDaysLater,
		HasRemaining:   boolPtr(true),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUserPointsStats: get expiring points failed")
		return nil, fmt.Errorf("查询即将过期积分失败: %w", err)
	}

	// 计算即将过期的积分数量
	var expiringCount uint64
	for _, log := range expiringLogs {
		expiringCount += log.GetRemainingPoints()
	}

	res := &adminDto.AdminUserPointsStatsRes{
		UserID:        req.UserID,
		UserName:      userModel.Nickname,
		TotalEarned:   addSum,
		TotalSpent:    subtractSum,
		CurrentPoints: currentBalance,
		ExpiredPoints: expiredSum,
		ExpiringCount: expiringCount,
	}

	return res, nil
}

// contains 检查字符串是否包含子字符串（用于用户名模糊搜索）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(substr) > 0 && len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				func() bool {
					for i := 1; i < len(s)-len(substr)+1; i++ {
						if s[i:i+len(substr)] == substr {
							return true
						}
					}
					return false
				}())))
}

// boolPtr 返回bool指针
func boolPtr(b bool) *bool {
	return &b
}
