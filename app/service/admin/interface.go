package admin

import (
	"blind_box/app/common/dbs"
	adminAccount "blind_box/app/dao/admin/account"
	adminMenu "blind_box/app/dao/admin/menu"
	menuBackup "blind_box/app/dao/admin/menu_backup"
	adminRole "blind_box/app/dao/admin/role"
	areaDao "blind_box/app/dao/admin/area"
	userDao "blind_box/app/dao/user"
	adminDto "blind_box/app/dto/admin"
	"sync"

	"github.com/gin-gonic/gin"
)

type Server interface {
	RoleSrv
	AccountSrv
	MenuSrv
	AreaSrv
	UserSrv
}

type RoleSrv interface {
	CheckOperationRoleAuth(ctx *gin.Context, roleID uint64) error
	JudgeIsRoot(roleID uint64) bool
	JudgeIsAdmin(roleID uint64) bool
	EditableFirstAdmin(accountID uint64) bool
	GetCommonRoleFilter(ctx *gin.Context) []uint64
	GetCommonAccountFilter(ctx *gin.Context) []uint64
	AdminRoleList(ctx *gin.Context, req *adminDto.AdminRoleListReq) (*adminDto.AdminRoleListResp, error)
	AdminSetRole(ctx *gin.Context, req *adminDto.AdminSetRoleReq) (*adminRole.Model, error)
	AdminSetRoleAuth(ctx *gin.Context, roleID uint64, MenuIds []uint64) error
	AdminDelRole(ctx *gin.Context, roleID uint64) error
	AdminRoleAll(ctx *gin.Context, req *adminDto.AdminRoleAllReq) ([]*adminDto.AdminRoleListItem, error)
}

type AccountSrv interface {
	CheckOperationAccountAuth(ctx *gin.Context, accountID, roleID uint64) error
	AdminAccountList(ctx *gin.Context, req *adminDto.AdminAccountListReq) (*adminDto.AdminAccountListResp, error)
	AdminSetAccount(ctx *gin.Context, req *adminDto.AdminSetAccountReq) error
	AdminDelAccount(ctx *gin.Context, accountID uint64) error
	AdminSetAccountPwd(ctx *gin.Context, id uint64, pwd string) error
	AdminGetAccountInfo(ctx *gin.Context, aid, rid uint64) (*adminDto.AdminGetAccountInfoResp, error)
	AdminAccountAll(ctx *gin.Context, req *adminDto.AdminAccountAllReq) ([]*adminDto.AdminAccountAllItem, error)
	AdminAccountLogin(ctx *gin.Context, account, pwd string) (string, error)
}

type MenuSrv interface {
	AdminMenuList(ctx *gin.Context) ([]*adminDto.AdminMenuListResp, error)
	AdminMenuOption(ctx *gin.Context, req *adminDto.AdminMenuOptionReq) ([]*adminDto.AdminMenuOptionResp, error)
	AdminMenuRouter(ctx *gin.Context, roleId uint64) ([]*adminDto.AdminMenuRouterResp, error)
	AdminSetMenu(ctx *gin.Context, req *adminDto.AdminSetMenuReq) error
	AdminOperateMenu(ctx *gin.Context, id uint64, action dbs.OperateAction) error
	AdminMenuInit(ctx *gin.Context) error
}

type AreaSrv interface {
	AdminAreaList(ctx *gin.Context) ([]*adminDto.AdminAreaListResp, error)
}

type UserSrv interface {
	AdminEditUserPoints(ctx *gin.Context, req *adminDto.AdminEditUserPointsReq) (*adminDto.AdminEditUserPointsRes, error)
	AdminUserPointsLog(ctx *gin.Context, req *adminDto.AdminUserPointsLogReq) (*adminDto.AdminUserPointsLogRes, error)
	AdminUserPointsStats(ctx *gin.Context, req *adminDto.AdminUserPointsStatsReq) (*adminDto.AdminUserPointsStatsRes, error)
}

type Entry struct {
	UserRepo       *userDao.Entry
	MenuRepo       adminMenu.Repo
	AreaRepo       areaDao.Repo
	AccountRepo    adminAccount.Repo
	RoleRepo       adminRole.Repo
	MenuBackupRepo menuBackup.Repo
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RoleRepo:       adminRole.GetRepo(),
		AccountRepo:    adminAccount.GetRepo(),
		UserRepo:       userDao.GetRepo(),
		MenuRepo:       adminMenu.GetRepo(),
		MenuBackupRepo: menuBackup.GetRepo(),
		AreaRepo:       areaDao.GetRepo(),
	}
}
