package activity

import (
	"sync"

	actDao "blind_box/app/dao/activity"
	scDao "blind_box/app/dao/activity/sale_calendar"
	subDao "blind_box/app/dao/activity/subscribe"
	imageDao "blind_box/app/dao/resource/image"
	userDao "blind_box/app/dao/user"
	userLogDao "blind_box/app/dao/user/user_log"
	"blind_box/pkg/redis"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	UserRepo         *userDao.Entry
	UserLogRepo      userLogDao.Repo
	ActRepo          *actDao.Entry
	ImageRepo        imageDao.Repo
	SaleCalendarRepo *scDao.Entry
	SubscribeRepo    *subDao.Entry
	RedisCli         *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:         userDao.GetRepo(),
		UserLogRepo:      userLogDao.GetRepo(),
		ActRepo:          actDao.GetRepo(),
		ImageRepo:        imageDao.GetRepo(),
		SaleCalendarRepo: scDao.GetRepo(),
		SubscribeRepo:    subDao.GetRepo(),
		RedisCli:         redis.GetRedisClient(),
	}
}
