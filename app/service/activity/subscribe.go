package activity

import (
	actDao "blind_box/app/dao/activity"
	subDao "blind_box/app/dao/activity/subscribe"
	actDto "blind_box/app/dto/activity"
	"blind_box/pkg/ecode"
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

var (
	wg           sync.WaitGroup
	subAsyncOnce sync.Once
	subChan      = make(chan *subDao.SubChan, 1000)
)

// ActivitySubscribeList .
func (e *Entry) ActivitySubscribeList(ctx *gin.Context, uid uint64, page, limit int) (*actDto.ActivitySubscribeListResp, error) {
	var (
		eg              errgroup.Group
		toRemind, total int64
		scsList         = subDao.ModelList{}
	)
	eg.Go(func() (err error) {
		if toRemind, err = e.SubscribeRepo.CountUserSubByFilter(ctx, &subDao.UserSubFilter{
			UID:      uid,
			ByRemind: true,
		}); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if total, scsList, err = e.SubscribeRepo.DataPageList(ctx, uid, page, limit); err != nil {
			return err
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	actMap, err := e.ActRepo.FindMapByFilter(ctx, &actDao.Filter{Ids: scsList.GetActIDS()})
	if err != nil {
		return nil, err
	}

	retList := make([]*actDto.ActivitySubscribeListItem, 0, len(scsList))
	for _, val := range scsList {
		var (
			actTitle, startTime, cover string
			actPrice                   uint64
		)
		if temp, ok := actMap[val.ActID]; ok {
			actTitle = temp.Title
			startTime = temp.GetStartTime()
			cover = temp.Image
			actPrice = temp.ActPrice
		}
		item := &actDto.ActivitySubscribeListItem{
			ID:          val.ID,
			ActID:       val.ActID,
			ActTitle:    actTitle,
			StartTime:   startTime,
			Cover:       cover,
			ActPrice:    actPrice,
			CreatedTime: val.GetCreatedTime(),
		}

		retList = append(retList, item)
	}

	return &actDto.ActivitySubscribeListResp{
		List:     retList,
		Total:    total,
		ToRemind: toRemind,
	}, nil
}

// SubscribeActivity .
func (e *Entry) SubscribeActivity(ctx *gin.Context, uid, actID uint64) error {
	var (
		eg          errgroup.Group
		actInfo     *actDao.Model
		actSubCount int64
	)
	eg.Go(func() (err error) {
		if actSubCount, err = e.SubscribeRepo.CountUserSubByFilter(ctx, &subDao.UserSubFilter{ActID: actID, UID: uid}); err != nil {
			return
		}
		if actSubCount > 0 {
			return ecode.ActAleardySubscribeErr
		}
		return
	})
	eg.Go(func() (err error) {
		if actInfo, err = e.ActRepo.FetchByID(ctx, actID); err != nil {
			return
		}
		if actInfo.ID == 0 {
			return ecode.ActNotExistErr
		}
		if actInfo.StartTime < carbon.Now().Timestamp() {
			return ecode.ActFinishedErr
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return err
	}

	subAsyncOnce.Do(func() {
		c := ctx.Copy()
		c.Request = c.Request.Clone(context.WithoutCancel(c.Request.Context()))
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := e.subscribeActivity(c, subChan); err != nil {
				runtime.Goexit()
			}
		}()
	})

	subChan <- &subDao.SubChan{
		UID:   uid,
		ActID: actID,
	}
	return nil
}

// DelSubscribeActivity .
func (e *Entry) DelSubscribeActivity(ctx *gin.Context, uid, actID uint64) error {
	num, err := e.SubscribeRepo.CountUserSubByFilter(ctx, &subDao.UserSubFilter{UID: uid, ActID: actID})
	if err != nil {
		return err
	}
	if num == 0 {
		return ecode.ActNotSubscribeErr
	}
	return e.SubscribeRepo.DelSubscribe(ctx, uid, actID)
}

// subscribeActivity .
func (e *Entry) subscribeActivity(ctx *gin.Context, subChan chan *subDao.SubChan) error {
	for {
		// 监听订阅通道
		select {
		case msg := <-subChan:
			m := &subDao.Model{
				UserID: msg.UID,
				ActID:  msg.ActID,
			}
			if err := e.SubscribeRepo.Create(ctx, m); err != nil {
				return err
			}
		default:
			time.Sleep(time.Millisecond * 100)
		}
	}
}
