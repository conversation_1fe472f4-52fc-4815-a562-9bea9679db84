package activity

import (
	actDao "blind_box/app/dao/activity"
	scDao "blind_box/app/dao/activity/sale_calendar"
	subDao "blind_box/app/dao/activity/subscribe"
	actDto "blind_box/app/dto/activity"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminSaleCalendarList .
func (e *Entry) AdminSaleCalendarList(ctx *gin.Context, req actDto.AdminSaleCalendarListReq) (*actDto.AdminSaleCalendarListResp, error) {
	var (
		actIDS = []uint64{}
		err    error
	)
	if req.ActID > 0 {
		actIDS = append(actIDS, req.ActID)
	}
	if req.ActTitle != "" {
		actList, err := e.ActRepo.FindByFilter(ctx, &actDao.Filter{Title: req.ActTitle})
		if err != nil {
			return nil, err
		}
		if len(actList) != 0 {
			actIDS = append(actIDS, actList.GetIds()...)
		}
	}

	total, scList, err := e.SaleCalendarRepo.DataPageList(ctx, &scDao.Filter{ActIds: actIDS}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	actMap, err := e.ActRepo.FindMapByFilter(ctx, &actDao.Filter{Ids: scList.GetActIds()})
	if err != nil {
		return nil, err
	}
	retList := make([]*actDto.AdminSaleCalendarListItem, 0, len(scList))
	for _, val := range scList {
		var actTitle, startTime, cover string
		if temp, ok := actMap[val.ActID]; ok {
			actTitle = temp.Title
			startTime = temp.GetStartTime()
			cover = temp.Image
		}
		subNum, _ := e.SubscribeRepo.CountUserSubByFilter(ctx, &subDao.UserSubFilter{ActID: val.ActID})
		item := &actDto.AdminSaleCalendarListItem{
			ID:           val.ID,
			ActID:        val.ActID,
			ActTitle:     actTitle,
			StartTime:    startTime,
			Cover:        cover,
			Sort:         val.Sort,
			TimeDesc:     val.TimeDesc,
			SubscribeNum: uint32(subNum),
			CreatedTime:  val.GetCreatedTime(),
		}

		retList = append(retList, item)
	}

	return &actDto.AdminSaleCalendarListResp{
		Total: total,
		List:  retList,
	}, nil
}

// AdminSetSaleCalendar .
func (e *Entry) AdminSetSaleCalendar(ctx *gin.Context, req actDto.AdminSetSaleCalendarReq) error {
	m := &scDao.Model{
		ID:       req.ID,
		Sort:     req.Sort,
		TimeDesc: req.TimeDesc,
	}
	if req.ID > 0 {
		model, err := e.SaleCalendarRepo.FetchByID(ctx, req.ID)
		if err != nil {
			return err
		}
		if model.ID == 0 {
			return ecode.ActSaleCalendarNotExistErr
		}
	} else {
		var (
			eg                   errgroup.Group
			actCount, scActCount int64
		)
		eg.Go(func() (err error) {
			if actCount, err = e.ActRepo.CountByID(ctx, req.ActID); err != nil {
				return err
			}
			if actCount == 0 {
				return ecode.ActNotExistErr
			}
			return
		})
		eg.Go(func() (err error) {
			if scActCount, err = e.SaleCalendarRepo.CountByFilter(ctx, &scDao.Filter{ActId: req.ActID}); err != nil {
				return err
			}
			if scActCount > 0 {
				return ecode.ActSaleCalendarActExistErr
			}
			return
		})
		if err := eg.Wait(); err != nil {
			log.Ctx(ctx).WithError(err).Warn("AdminSetSaleCalendar err")
			return err
		}

		m.ActID = req.ActID
	}
	if err := e.SaleCalendarRepo.CreateOrUpdate(ctx, m); err != nil {
		return err
	}
	return nil
}
