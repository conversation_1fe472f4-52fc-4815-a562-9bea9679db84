package activity

import (
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	imageDao "blind_box/app/dao/resource/image"
	actDto "blind_box/app/dto/activity"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

// AdminActivitySearch .
func (e *Entry) AdminActivitySearch(ctx *gin.Context, actIDS []uint64) ([]*actDto.AdminActivitySearchResp, error) {
	list, err := e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: actIDS})
	if err != nil {
		return nil, err
	}
	retList := []*actDto.AdminActivitySearchResp{}
	for _, val := range list {
		retList = append(retList, &actDto.AdminActivitySearchResp{
			ActID:    val.ID,
			ActTitle: val.Title,
		})
	}
	return retList, nil
}

func (e *Entry) AdminActiveCreate(ctx *gin.Context, req *actDto.AdminActiveCreateReq) (res *actDto.AdminActiveCreateResp, err error) {
	res = &actDto.AdminActiveCreateResp{}
	r := []rune(req.ActiveTitle)
	if len(r) > 50 {
		return res, ecode.BoxActiveTitleLongErr
	}

	var (
		activeInfo = &actDao.Model{
			Title:         req.ActiveTitle,
			ActStatus:     req.ActiveStatus,
			Tips:          req.DeliveryExpect,
			Image:         req.ActiveImage,
			ActPrice:      req.ActivePrice,
			StartTime:     req.OpenTime,
			EndTime:       req.EndTime,
			OriginalPrice: req.ActivePrice,
		}
		imagesInfos = make(imageDao.ModelList, 0)
		tx          = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	if _, err := e.ActRepo.CreateWithTx(ctx, tx, activeInfo); err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateWithTx error")
		tx.Rollback()
		return res, err
	}

	res.ID = activeInfo.ID
	for _, val := range req.DetailImages {
		imagesInfos = append(imagesInfos, &imageDao.Model{
			URL:         dbs.CdnImg(val),
			MappingType: imageDao.MappingActiveDetailImages,
			MappingID:   activeInfo.ID,
		})
	}
	if len(imagesInfos) > 0 {
		if err := e.ImageRepo.BatchCreateWithTx(ctx, tx, imagesInfos); err != nil {
			log.Ctx(ctx).WithError(err).Error("BatchCreateWithTx error")
			tx.Rollback()
			return res, err
		}
	}

	tx.Commit()

	return res, nil
}

func (e *Entry) AdminActiveUpdate(ctx *gin.Context, req *actDto.AdminActiveUpdateReq) (res *actDto.AdminActiveUpdateResp, err error) {
	res = &actDto.AdminActiveUpdateResp{}
	res.ID = req.ID

	r := []rune(req.ActiveTitle)
	if len(r) > 50 {
		return res, ecode.BoxActiveTitleLongErr
	}

	var (
		activeInfo = &actDao.Model{}

		updateMap = map[string]interface{}{}

		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	if activeInfo, err = e.ActRepo.FetchByID(ctx, req.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByID error")
		return res, err
	}
	if activeInfo == nil {
		return res, ecode.ActNotExistErr
	}

	if activeInfo.Title != req.ActiveTitle {
		updateMap["title"] = req.ActiveTitle
	}
	if activeInfo.ActStatus != req.ActiveStatus {
		updateMap["act_status"] = req.ActiveStatus
	}
	if activeInfo.Tips != req.DeliveryExpect {
		updateMap["tips"] = req.DeliveryExpect
	}
	if activeInfo.Image != req.ActiveImage {
		updateMap["image"] = req.ActiveImage
	}
	if activeInfo.ActPrice != req.ActivePrice {
		updateMap["act_price"] = req.ActivePrice
	}
	if activeInfo.StartTime != req.OpenTime {
		updateMap["start_time"] = req.OpenTime
	}
	if activeInfo.EndTime != req.EndTime {
		updateMap["end_time"] = req.EndTime
	}

	if req.EndTime == 0 {
		updateMap["end_time"] = nil
	}
	if len(updateMap) > 0 {
		if err := e.ActRepo.UpdateMapByIDWithTx(ctx, tx, activeInfo.ID, updateMap); err != nil {
			log.Ctx(ctx).WithError(err).Error("UpdateMapByIDWithTx error")
			tx.Rollback()
			return res, err
		}
	}

	if len(req.DetailImages) > 0 {
		imagesInfos := make(imageDao.ModelList, 0)
		for _, val := range req.DetailImages {
			imagesInfos = append(imagesInfos, &imageDao.Model{
				URL:         dbs.CdnImg(val),
				MappingType: imageDao.MappingActiveDetailImages,
				MappingID:   activeInfo.ID,
			})
		}
		if err = e.ImageRepo.UpdateMapByFilterWithTx(ctx, tx, &imageDao.Filter{
			MappingType: imageDao.MappingActiveDetailImages,
			MappingID:   activeInfo.ID,
		}, map[string]interface{}{
			dbs.SoftDelField.String(): dbs.True,
		}); err != nil {
			log.Ctx(ctx).WithError(err).Error("UpdateMapByFilterWithTx error")
			tx.Rollback()
			return res, err
		}

		if err = e.ImageRepo.BatchCreateWithTx(ctx, tx, imagesInfos); err != nil {
			log.Ctx(ctx).WithError(err).Error("BatchCreateWithTx error")
			tx.Rollback()
			return res, err
		}
	}

	tx.Commit()

	return res, nil
}

func (e *Entry) AdminActiveList(ctx *gin.Context, req *actDto.AdminActiveListReq) (res *actDto.AdminActiveListResp, err error) {
	res = &actDto.AdminActiveListResp{}
	res.List = make([]*actDto.AdminActiveListItem, 0)

	var (
		activeInfos       = make(actDao.ModelList, 0)
		total             int64
		activeIds         = make([]uint64, 0)
		imageActiveKeyMap = make(map[uint64]imageDao.ModelList)
	)

	if total, activeInfos, err = e.ActRepo.DataPageList(ctx, &actDao.Filter{
		ID:        req.ActivityID,
		TitleLike: req.ActivityTitle,
		ActStatus: actDao.ActStatus(req.ActivityStatus),
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}, req.Page, req.Limit); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminActiveList error")
		return res, err
	}
	res.Count = total

	activeIds = activeInfos.GetIds()

	if len(activeIds) == 0 {
		images, err := e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			MappingType: imageDao.MappingActiveDetailImages,
			MappingIDs:  activeIds,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminActiveList error")
			return res, err
		}
		imageActiveKeyMap = images.GetMapppingIDKeyMap()
	}
	for _, val := range activeInfos {
		tmp := &actDto.AdminActiveListItem{
			ID:            val.ID,
			ActivityTitle: val.Title,
			//Store:         val.Store,
			ActiveImage:    val.Image,
			ActivePrice:    val.ActPrice,
			ActiveStatus:   val.ActStatus,
			OpenTime:       val.StartTime,
			EndTime:        val.EndTime,
			DeliveryExpect: val.Tips,
			//CurStock:      val.CurStock,
			//Stock:        val.Stock,
			//OverSell:     val.OverSell,
		}
		if images, ok := imageActiveKeyMap[val.ID]; ok {
			for _, image := range images {
				tmp.DetailImages = append(tmp.DetailImages, helper.GetImageCdnUrl(ctx, image.URL))
			}
		}

		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e *Entry) AdminActiveInfo(ctx *gin.Context, req *actDto.AdminActiveInfoReq) (res *actDto.AdminActiveInfoResp, err error) {
	res = &actDto.AdminActiveInfoResp{}
	res.AdminActiveListItem = &actDto.AdminActiveListItem{}
	var (
		activeInfo = &actDao.Model{}
		images     = make(imageDao.ModelList, 0)
	)

	if activeInfo, err = e.ActRepo.FetchByID(ctx, req.ActiveId); err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByID error")
		return res, err
	}
	if activeInfo == nil {
		return res, ecode.ActNotExistErr
	}

	if images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
		MappingType: imageDao.MappingActiveDetailImages,
		MappingID:   activeInfo.ID,
	}); err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return res, err
	}

	res.ID = activeInfo.ID
	res.ActivityTitle = activeInfo.Title
	res.ActiveImage = activeInfo.Image
	res.ActivePrice = activeInfo.ActPrice
	res.DeliveryExpect = activeInfo.Tips
	res.OpenTime = activeInfo.StartTime
	res.EndTime = activeInfo.EndTime
	res.ActiveStatus = activeInfo.ActStatus

	for _, image := range images {
		res.DetailImages = append(res.DetailImages, helper.GetImageCdnUrl(ctx, image.URL))
	}

	return res, nil
}
