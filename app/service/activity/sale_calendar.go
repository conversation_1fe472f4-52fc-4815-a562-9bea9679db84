package activity

import (
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	scDao "blind_box/app/dao/activity/sale_calendar"
	subDao "blind_box/app/dao/activity/subscribe"
	actDto "blind_box/app/dto/activity"
	commonDto "blind_box/app/dto/common"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// SaleCalendarList .
func (e *Entry) SaleCalendarList(ctx *gin.Context, uid uint64, req commonDto.DataListReq) (*actDto.SaleCalendarListResp, error) {
	var (
		eg        errgroup.Group
		total     int64
		scList    scDao.ModelList
		subList   subDao.ModelList
		subActMap = make(map[uint64]struct{})
	)

	eg.Go(func() (err error) {
		total, scList, err = e.SaleCalendarRepo.DataPageList(ctx, &scDao.Filter{}, req.Page, req.Limit)
		if err != nil {
			return err
		}
		return
	})
	eg.Go(func() (err error) {
		subList, err = e.SubscribeRepo.FindUserSubByFilter(ctx, &subDao.UserSubFilter{ByRemind: true, UID: uid})
		if err != nil {
			return err
		}
		subActMap = subList.GetActIDEmptyMap()
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	actMap, err := e.ActRepo.FindMapByFilter(ctx, &actDao.Filter{Ids: scList.GetActIds()})
	if err != nil {
		return nil, err
	}
	retList := make([]*actDto.SaleCalendarListItem, 0, len(scList))
	for _, val := range scList {
		var (
			actTitle, startTime, cover string
			acrPrice                   uint64
			isSub                      = dbs.False
		)
		if temp, ok := actMap[val.ActID]; ok {
			actTitle = temp.Title
			startTime = temp.GetStartTime()
			cover = temp.Image
			acrPrice = temp.ActPrice
		}
		if _, ok := subActMap[val.ActID]; ok {
			isSub = dbs.True
		}
		item := &actDto.SaleCalendarListItem{
			ID:          val.ID,
			ActID:       val.ActID,
			ActTitle:    actTitle,
			StartTime:   startTime,
			Cover:       cover,
			Sort:        val.Sort,
			TimeDesc:    val.TimeDesc,
			ActPrice:    acrPrice,
			IsSubscribe: uint32(isSub),
			CreatedTime: val.GetCreatedTime(),
		}

		retList = append(retList, item)
	}

	return &actDto.SaleCalendarListResp{
		Total: total,
		List:  retList,
	}, nil
}
