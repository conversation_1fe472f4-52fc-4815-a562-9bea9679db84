# Resource Service Architecture Documentation

## Overview

The resource service layer manages basic resource entities including shops, brands, suppliers, and vendors. This service follows a standardized CRUD pattern with Redis caching, concurrent validation, and business rule enforcement for resource management operations.

## Core Architecture

### Directory Structure
```
app/service/resource/
├── interface.go        # Service interface and dependency injection
├── admin_shop.go       # Shop resource management
├── admin_brand.go      # Brand resource management  
├── admin_supplier.go   # Supplier resource management
└── admin_vendor.go     # Vendor resource management
```

### Service Entry Pattern
```go
type Entry struct {
    ShopRepo     shopDao.Repo           # Shop data operations
    SkuShopRepo  *skuShopDao.Entry      # SKU-Shop relationships
    BrandRepo    brandDao.Repo          # Brand data operations
    SupplierRepo supplierDao.Repo       # Supplier data operations
    VendorRepo   vendorDao.Repo         # Vendor data operations
    CommonSrv    common.Server          # Common service dependency
}
```

## Resource Management Pattern

### Standard Resource Operations

All resource types (Shop, Brand, Supplier, Vendor) follow the same operation pattern:

#### 1. List Operations
```go
func (e *Entry) Admin[Resource]List(ctx *gin.Context, req resourceDto.Admin[Resource]ListReq) (*resourceDto.Admin[Resource]ListResp, error)
```
- **Pagination Support**: Standard page/limit parameters
- **Filter Support**: Name search and status filtering
- **Data Transformation**: Model to DTO conversion with CDN URL processing

#### 2. Set Operations (Create/Update)
```go
func (e *Entry) Admin[Resource]Set(ctx *gin.Context, req resourceDto.Admin[Resource]SetReq) (uint64, error)
```
- **Concurrent Validation**: Parallel existence check and name uniqueness validation
- **CDN Integration**: Automatic image URL processing
- **Default Status**: New resources created with enabled status

#### 3. Operate Operations (Status/Sort Management)
```go
func (e *Entry) Admin[Resource]Operate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error
```
- **Status Control**: Enable/disable operations
- **Sort Management**: Display order modification
- **Business Rule Validation**: Dependency checks before operations

#### 4. All Operations (List All Resources)
```go
func (e *Entry) Admin[Resource]All(ctx *gin.Context, req resourceDto.Admin[Resource]AllReq) ([]*resourceDto.Admin[Resource]ListItem, error)
```
- **Redis Caching**: High-performance resource listing
- **Filter Support**: Optional enabled-only filtering
- **Optimized Response**: Minimal field selection for dropdown/select usage

## Concurrent Validation Pattern

### errgroup Implementation
All Set operations use errgroup for concurrent validation:

```go
var (
    eg    errgroup.Group
    model = &[Resource]Dao.Model{}
    num   int64
    err   error
)

// For updates: parallel existence and uniqueness check
if req.ID > 0 {
    eg.Go(func() (err error) {
        // Check resource exists
        if model, err = e.[Resource]Repo.FetchByID(ctx, req.ID); err != nil {
            return
        }
        if model.ID == 0 {
            return ecode.ParamErr
        }
        return
    })
    eg.Go(func() (err error) {
        // Check name uniqueness (excluding current record)
        if num, err = e.[Resource]Repo.CountByFilter(ctx, &[Resource]Dao.Filter{
            Name: req.Name, 
            NotID: req.ID
        }); err != nil {
            return
        }
        if num > 0 {
            return ecode.NameExistErr
        }
        return
    })
} else {
    // For creates: check name uniqueness only
    eg.Go(func() (err error) {
        if num, err = e.[Resource]Repo.CountByFilter(ctx, &[Resource]Dao.Filter{
            Name: req.Name
        }); err != nil {
            return
        }
        if num > 0 {
            return ecode.NameExistErr
        }
        return
    })
}

if err := eg.Wait(); err != nil {
    return 0, err
}
```

### Validation Benefits
- **Performance**: Parallel execution reduces response time
- **Data Integrity**: Prevents duplicate names and invalid updates
- **Error Handling**: Early termination on validation failures

## Resource-Specific Business Rules

### Shop Management
```go
// Special validation: Check SKU usage before disabling
if action == dbs.ActionClose {
    cnt, err := e.SkuShopRepo.CountByFilter(ctx, &skuShopDao.Filter{ShopId: id})
    if err != nil {
        return err
    }
    if cnt > 0 {
        return ecode.ResourceShopInUseErr  // Prevent deletion if in use
    }
}
```

**Business Logic**:
- Shops cannot be disabled if they have associated SKUs
- Extended fields: Address, Mobile, Description
- Critical for order fulfillment and pickup locations

### Brand Management
```go
// Standard operations with sort support
switch action {
case dbs.ActionOpen, dbs.ActionClose:
    return e.BrandRepo.UpdateMapByID(ctx, id, map[string]interface{}{
        "status": dbs.OperateActionMap[action]
    })
case dbs.ActionSort:
    return e.BrandRepo.UpdateMapByID(ctx, id, map[string]interface{}{
        "sort": val
    })
}
```

**Business Logic**:
- Supports display order management
- Used for product categorization and branding
- Minimal validation requirements

### Supplier & Vendor Management
```go
// Identical pattern for supplier and vendor operations
switch action {
case dbs.ActionOpen, dbs.ActionClose:
    return e.[Resource]Repo.UpdateMapByID(ctx, id, map[string]interface{}{
        "status": dbs.OperateActionMap[action]
    })
case dbs.ActionSort:
    return e.[Resource]Repo.UpdateMapByID(ctx, id, map[string]interface{}{
        "sort": val
    })
}
```

**Business Logic**:
- Standard resource management operations
- Used for supply chain management
- Support status and sort operations

## Redis Caching Strategy

### Cache Utilization Pattern
```go
// Conditional caching based on requirements
if req.Enable == dbs.True {
    // Get only enabled resources from cache
    if list, err = e.[Resource]Repo.RedisEnable[Resource]List(ctx); err != nil {
        return nil, err
    }
} else {
    // Get all resources from cache
    if list, err = e.[Resource]Repo.Redis[Resource]List(ctx); err != nil {
        return nil, err
    }
}
```

### Cache Design Benefits
- **Performance**: Fast access to frequently used resource lists
- **Flexibility**: Separate caches for enabled vs all resources
- **Consistency**: DAO layer manages cache invalidation

## Data Transformation Patterns

### Model to DTO Conversion
```go
// Standard transformation with CDN processing
retList := make([]*resourceDto.Admin[Resource]ListItem, 0, len(list))
for _, val := range list {
    item := &resourceDto.Admin[Resource]ListItem{
        ID:     val.ID,
        Name:   val.Name,
        Cover:  helper.GetImageCdnUrl(ctx, val.Cover),  // CDN URL generation
        Status: val.Status,
        // Resource-specific fields...
    }
    retList = append(retList, item)
}
```

### CDN Integration
- **Automatic Processing**: `helper.GetImageCdnUrl()` for response data
- **Storage Processing**: `dbs.CdnImg()` for incoming request data
- **Consistent URLs**: Uniform CDN URL handling across all resources

## Interface Design Pattern

### Interface Segregation
```go
type Server interface {
    AdminShop      # Shop management operations
    AdminBrand     # Brand management operations  
    AdminSupplier  # Supplier management operations
    AdminVendor    # Vendor management operations
}

// Each resource type has its own interface
type AdminShop interface {
    AdminShopList(*gin.Context, resourceDto.AdminShopListReq) (*resourceDto.AdminShopListResp, error)
    AdminShopSet(*gin.Context, resourceDto.AdminShopSetReq) (uint64, error)
    AdminShopOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
    AdminShopAll(*gin.Context, resourceDto.AdminShopAllReq) ([]*resourceDto.AdminShopListItem, error)
}
```

### Method Naming Convention
- **List**: Paginated listing with filters
- **Set**: Create or update operations
- **Operate**: Status and sort management
- **All**: Complete listing for dropdown/select usage

## Dependency Injection Pattern

### Repository Dependencies
```go
type Entry struct {
    ShopRepo     shopDao.Repo           # Interface-based dependency
    SkuShopRepo  *skuShopDao.Entry      # Concrete type for relationship management
    BrandRepo    brandDao.Repo          # Interface-based dependency
    SupplierRepo supplierDao.Repo       # Interface-based dependency
    VendorRepo   vendorDao.Repo         # Interface-based dependency
    CommonSrv    common.Server          # Service layer dependency
}

func newEntry() *Entry {
    return &Entry{
        ShopRepo:     shopDao.GetRepo(),
        SkuShopRepo:  skuShopDao.GetRepo(),
        BrandRepo:    brandDao.GetRepo(),
        SupplierRepo: supplierDao.GetRepo(),
        VendorRepo:   vendorDao.GetRepo(),
        CommonSrv:    common.GetService(),
    }
}
```

### Singleton Management
```go
var (
    defaultEntry         Server
    defaultEntryInitOnce sync.Once
)

func GetService() Server {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}
```

## Error Handling Standards

### Business Error Codes
```go
// Resource validation errors
if model.ID == 0 {
    return ecode.ParamErr           # Resource not found
}

if num > 0 {
    return ecode.NameExistErr       # Duplicate name validation
}

if cnt > 0 {
    return ecode.ResourceShopInUseErr  # Business rule violation
}
```

### Error Propagation
- **DAO Errors**: Propagate database errors directly
- **Business Errors**: Return specific business error codes
- **Validation Errors**: Use standard validation error codes

## Performance Optimization

### Concurrent Operations
```go
// Parallel validation reduces response time
eg.Go(func() error { return validateExistence() })
eg.Go(func() error { return validateUniqueness() })
if err := eg.Wait(); err != nil {
    return 0, err
}
```

### Memory Optimization
```go
// Pre-allocate slices with known capacity
retList := make([]*resourceDto.AdminResourceListItem, 0, len(list))
```

### Redis Caching
- **Frequent Access**: Cache commonly accessed resource lists
- **Conditional Caching**: Separate caches for enabled vs all resources
- **DAO Management**: Repository layer handles cache operations

## Development Patterns

### Code Duplication Strategy
The resource service intentionally uses similar patterns across all resource types to:
- **Consistency**: Uniform behavior across all resource management
- **Maintainability**: Predictable code structure for all resources
- **Performance**: Optimized patterns without over-abstraction
- **Business Logic**: Easy customization per resource type

### Template Pattern Implementation
```go
// Each resource follows the same method structure:
// 1. AdminResourceList   - Paginated listing
// 2. AdminResourceSet    - Create/Update with validation
// 3. AdminResourceOperate - Status/Sort management  
// 4. AdminResourceAll    - Complete listing for selects
```

## Integration Points

### External Dependencies
- **CDN Service**: Image URL processing through helper package
- **Redis**: Caching layer for performance optimization
- **Database**: GORM-based data persistence

### Internal Dependencies
- **Common Service**: Shared utilities and helper functions
- **DAO Layer**: Data access through repository pattern
- **DTO Layer**: Request/response structure definitions

## Security Considerations

### Data Validation
- **Name Uniqueness**: Prevent duplicate resource names
- **Existence Validation**: Verify resource exists before operations
- **Business Rules**: Enforce relationship constraints

### Access Control
- **Admin Only**: All operations require admin authentication
- **Operation Logging**: Implicit through request context
- **Status Management**: Controlled enable/disable operations

## Monitoring and Observability

### Performance Metrics
- **Concurrent Validation**: Monitor errgroup execution times
- **Cache Hit Rates**: Redis cache performance for resource lists
- **Database Query Performance**: Monitor DAO operation times

### Business Metrics
- **Resource Usage**: Track which resources are actively used
- **Operation Frequency**: Monitor CRUD operation patterns
- **Error Rates**: Track validation and business rule failures

## Best Practices

### 1. Validation Strategy
- Use errgroup for parallel validation operations
- Implement business rule checks before data modification
- Return specific error codes for different failure scenarios

### 2. Caching Strategy
- Leverage Redis caching for frequently accessed data
- Maintain separate caches for different data subsets
- Let DAO layer manage cache lifecycle

### 3. Interface Design
- Segregate interfaces by resource type
- Use consistent naming conventions across all resources
- Maintain uniform method signatures

### 4. Data Transformation
- Process CDN URLs consistently in responses
- Pre-allocate slices with known capacity
- Convert models to DTOs at service layer

### 5. Error Handling
- Propagate DAO errors without modification
- Use business-specific error codes for validation failures
- Handle concurrent operation errors properly

## Testing Strategy

### Unit Testing
- Test validation logic with various input scenarios
- Verify error handling for business rule violations
- Test concurrent validation behavior

### Integration Testing
- Verify Redis cache integration
- Test DAO layer interactions
- Validate complete CRUD workflows

### Performance Testing
- Benchmark concurrent validation operations
- Test cache performance under load
- Measure response times for different operation types

This documentation provides comprehensive guidelines for maintaining and extending the resource service layer within the blind box project ecosystem.