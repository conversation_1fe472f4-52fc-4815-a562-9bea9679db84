package resource

import (
	"blind_box/app/common/dbs"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	shopDao "blind_box/app/dao/resource/shop"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminShopList .
func (e *Entry) AdminShopList(ctx *gin.Context, req resourceDto.AdminShopListReq) (*resourceDto.AdminShopListResp, error) {
	total, list, err := e.ShopRepo.DataPageList(ctx, &shopDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*resourceDto.AdminShopListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminShopListItem{
			ID:          val.ID,
			Name:        val.Name,
			Cover:       helper.GetImageCdnUrl(ctx, val.Cover),
			Address:     val.Address,
			Mobile:      val.Mobile,
			Description: val.Description,
			Status:      val.Status,
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminShopListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminShopSet .
func (e *Entry) AdminShopSet(ctx *gin.Context, req resourceDto.AdminShopSetReq) (uint64, error) {
	m := &shopDao.Model{
		ID:          req.ID,
		Name:        req.Name,
		Cover:       dbs.CdnImg(req.Cover),
		Address:     req.Address,
		Mobile:      req.Mobile,
		Description: req.Description,
		Status:      uint32(dbs.StatusEnable),
	}
	var (
		eg    errgroup.Group
		model = &shopDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.ShopRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.ShopRepo.CountByFilter(ctx, &shopDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.ShopRepo.CountByFilter(ctx, &shopDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.ShopRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminShopOperate .
func (e *Entry) AdminShopOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	if action == dbs.ActionClose {
		// 校验是否有sku使用该店铺
		cnt, err := e.SkuShopRepo.CountByFilter(ctx, &skuShopDao.Filter{ShopId: id})
		if err != nil {
			return err
		}
		if cnt > 0 {
			return ecode.ResourceShopInUseErr
		}
	}

	return e.ShopRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
}

// AdminShopAll .
func (e *Entry) AdminShopAll(ctx *gin.Context, req resourceDto.AdminShopAllReq) ([]*resourceDto.AdminShopListItem, error) {
	var (
		list = shopDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.ShopRepo.RedisEnableShopList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.ShopRepo.RedisShopList(ctx); err != nil {
			return nil, err
		}
	}

	retList := make([]*resourceDto.AdminShopListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminShopListItem{
			ID:          val.ID,
			Name:        val.Name,
			Cover:       helper.GetImageCdnUrl(ctx, val.Cover),
			Address:     val.Address,
			Mobile:      val.Mobile,
			Description: val.Description,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
