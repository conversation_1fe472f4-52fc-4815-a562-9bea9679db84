package resource

import (
	"blind_box/app/common/dbs"
	supplierDao "blind_box/app/dao/resource/supplier"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminSupplierList .
func (e *Entry) AdminSupplierList(ctx *gin.Context, req resourceDto.AdminSupplierListReq) (*resourceDto.AdminSupplierListResp, error) {
	total, list, err := e.SupplierRepo.DataPageList(ctx, &supplierDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*resourceDto.AdminSupplierListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminSupplierListItem{
			ID:     val.ID,
			Name:   val.Name,
			Cover:  helper.GetImageCdnUrl(ctx, val.Cover),
			Status: val.Status,
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminSupplierListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSupplierSet .
func (e *Entry) AdminSupplierSet(ctx *gin.Context, req resourceDto.AdminSupplierSetReq) (uint64, error) {
	m := &supplierDao.Model{
		ID:     req.ID,
		Name:   req.Name,
		Cover:  dbs.CdnImg(req.Cover),
		Status: uint32(dbs.StatusEnable),
	}
	var (
		eg    errgroup.Group
		model = &supplierDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.SupplierRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.SupplierRepo.CountByFilter(ctx, &supplierDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.SupplierRepo.CountByFilter(ctx, &supplierDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.SupplierRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminSupplierOperate .
func (e *Entry) AdminSupplierOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.SupplierRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionSort:
		return e.SupplierRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminSupplierAll .
func (e *Entry) AdminSupplierAll(ctx *gin.Context, req resourceDto.AdminSupplierAllReq) ([]*resourceDto.AdminSupplierListItem, error) {
	var (
		list = supplierDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.SupplierRepo.RedisEnableSupplierList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.SupplierRepo.RedisSupplierList(ctx); err != nil {
			return nil, err
		}
	}

	retList := make([]*resourceDto.AdminSupplierListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminSupplierListItem{
			ID:   val.ID,
			Name: val.Name,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
