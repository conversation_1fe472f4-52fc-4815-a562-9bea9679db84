package resource

import (
	"blind_box/app/common/dbs"
	brandDao "blind_box/app/dao/resource/brand"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminBrandList .
func (e *Entry) AdminBrandList(ctx *gin.Context, req resourceDto.AdminBrandListReq) (*resourceDto.AdminBrandListResp, error) {
	total, list, err := e.BrandRepo.DataPageList(ctx, &brandDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*resourceDto.AdminBrandListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminBrandListItem{
			ID:     val.ID,
			Name:   val.Name,
			Cover:  helper.GetImageCdnUrl(ctx, val.Cover),
			Status: val.Status,
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminBrandListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminBrandSet .
func (e *Entry) AdminBrandSet(ctx *gin.Context, req resourceDto.AdminBrandSetReq) (uint64, error) {
	var (
		eg    errgroup.Group
		model = &brandDao.Model{}
		num   int64
		err   error
		m     = &brandDao.Model{
			ID:     req.ID,
			Name:   req.Name,
			Cover:  dbs.CdnImg(req.Cover),
			Status: uint32(dbs.StatusEnable),
		}
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.BrandRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.BrandRepo.CountByFilter(ctx, &brandDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.BrandRepo.CountByFilter(ctx, &brandDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.BrandRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminBrandOperate .
func (e *Entry) AdminBrandOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.BrandRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionSort:
		return e.BrandRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminBrandAll .
func (e *Entry) AdminBrandAll(ctx *gin.Context, req resourceDto.AdminBrandAllReq) ([]*resourceDto.AdminBrandListItem, error) {
	var (
		list = brandDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.BrandRepo.RedisEnableBrandList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.BrandRepo.RedisBrandList(ctx); err != nil {
			return nil, err
		}
	}

	retList := make([]*resourceDto.AdminBrandListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminBrandListItem{
			ID:   val.ID,
			Name: val.Name,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
