package resource

import (
	"blind_box/app/common/dbs"
	vendorDao "blind_box/app/dao/resource/vendor"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/pkg/ecode"
	"blind_box/pkg/helper"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminVendorList .
func (e *Entry) AdminVendorList(ctx *gin.Context, req resourceDto.AdminVendorListReq) (*resourceDto.AdminVendorListResp, error) {
	total, list, err := e.VendorRepo.DataPageList(ctx, &vendorDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*resourceDto.AdminVendorListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminVendorListItem{
			ID:     val.ID,
			Name:   val.Name,
			Cover:  helper.GetImageCdnUrl(ctx, val.Cover),
			Status: val.Status,
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminVendorListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminVendorSet .
func (e *Entry) AdminVendorSet(ctx *gin.Context, req resourceDto.AdminVendorSetReq) (uint64, error) {
	m := &vendorDao.Model{
		ID:     req.ID,
		Name:   req.Name,
		Cover:  dbs.CdnImg(req.Cover),
		Status: uint32(dbs.StatusEnable),
	}
	var (
		eg    errgroup.Group
		model = &vendorDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.VendorRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.VendorRepo.CountByFilter(ctx, &vendorDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.VendorRepo.CountByFilter(ctx, &vendorDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.VendorRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminVendorOperate .
func (e *Entry) AdminVendorOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.VendorRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionSort:
		return e.VendorRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminVendorAll .
func (e *Entry) AdminVendorAll(ctx *gin.Context, req resourceDto.AdminVendorAllReq) ([]*resourceDto.AdminVendorListItem, error) {
	var (
		list = vendorDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.VendorRepo.RedisEnableVendorList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.VendorRepo.RedisVendorList(ctx); err != nil {
			return nil, err
		}
	}

	retList := make([]*resourceDto.AdminVendorListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminVendorListItem{
			ID:   val.ID,
			Name: val.Name,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
