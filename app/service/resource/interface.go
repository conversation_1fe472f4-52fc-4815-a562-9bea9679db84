package resource

import (
	"blind_box/app/common/dbs"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	brandDao "blind_box/app/dao/resource/brand"
	shopDao "blind_box/app/dao/resource/shop"
	supplierDao "blind_box/app/dao/resource/supplier"
	vendorDao "blind_box/app/dao/resource/vendor"
	resourceDto "blind_box/app/dto/resource"
	"blind_box/app/service/common"
	"sync"

	"github.com/gin-gonic/gin"
)

type Server interface {
	AdminShop
	AdminBrand
	AdminSupplier
	AdminVendor
}

type AdminShop interface {
	AdminShopList(*gin.Context, resourceDto.AdminShopListReq) (*resourceDto.AdminShopListResp, error)
	AdminShopSet(*gin.Context, resourceDto.AdminShopSetReq) (uint64, error)
	AdminShopOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminShopAll(*gin.Context, resourceDto.AdminShopAllReq) ([]*resourceDto.AdminShopListItem, error)
}

type AdminBrand interface {
	AdminBrandList(*gin.Context, resourceDto.AdminBrandListReq) (*resourceDto.AdminBrandListResp, error)
	AdminBrandSet(*gin.Context, resourceDto.AdminBrandSetReq) (uint64, error)
	AdminBrandOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminBrandAll(*gin.Context, resourceDto.AdminBrandAllReq) ([]*resourceDto.AdminBrandListItem, error)
}

type AdminSupplier interface {
	AdminSupplierList(*gin.Context, resourceDto.AdminSupplierListReq) (*resourceDto.AdminSupplierListResp, error)
	AdminSupplierSet(*gin.Context, resourceDto.AdminSupplierSetReq) (uint64, error)
	AdminSupplierOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminSupplierAll(*gin.Context, resourceDto.AdminSupplierAllReq) ([]*resourceDto.AdminSupplierListItem, error)
}

type AdminVendor interface {
	AdminVendorList(*gin.Context, resourceDto.AdminVendorListReq) (*resourceDto.AdminVendorListResp, error)
	AdminVendorSet(*gin.Context, resourceDto.AdminVendorSetReq) (uint64, error)
	AdminVendorOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminVendorAll(*gin.Context, resourceDto.AdminVendorAllReq) ([]*resourceDto.AdminVendorListItem, error)
}

type Entry struct {
	ShopRepo     shopDao.Repo
	SkuShopRepo  *skuShopDao.Entry
	BrandRepo    brandDao.Repo
	SupplierRepo supplierDao.Repo
	VendorRepo   vendorDao.Repo
	CommonSrv    common.Server
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		ShopRepo:     shopDao.GetRepo(),
		SkuShopRepo:  skuShopDao.GetRepo(),
		BrandRepo:    brandDao.GetRepo(),
		SupplierRepo: supplierDao.GetRepo(),
		VendorRepo:   vendorDao.GetRepo(),
		CommonSrv:    common.GetService(),
	}
}
