package card

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_source"
	cardDto "blind_box/app/dto/card"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
)

func (e *Entry) CardConfigList(ctx *gin.Context) (res *cardDto.AdminCardConfigListResp, err error) {
	res = &cardDto.AdminCardConfigListResp{}
	res.List = make([]*cardDto.CardConfigListNameItem, 0)

	var (
		configs = card_config.ModelList{}
	)

	configs, err = e.CardConfigRepo.FindByFilter(ctx, &card_config.Filter{
		CardStatus: uint32(dbs.StatusEnable),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardConfigList FindByFilter")
		err = ecode.SystemErr
		return
	}
	if len(configs) == 0 {
		return
	}

	res.List = toCardConfigList(configs, res.List)

	return
}

func toCardConfigList(m card_config.ModelList, resp []*cardDto.CardConfigListNameItem) (ret []*cardDto.CardConfigListNameItem) {

	for _, model := range m {
		tmp := &cardDto.CardConfigListNameItem{
			ID:       model.ID,
			CardName: model.CardName,
		}
		resp = append(resp, tmp)
	}

	return resp
}

func (e *Entry) CardConfigAdd(ctx *gin.Context, req *cardDto.AdminCardConfigAddReq) (res *cardDto.AdminCardConfigAddResp, err error) {
	res = &cardDto.AdminCardConfigAddResp{}
	var (
		cardConfig = &card_config.Model{}
		sourceInfo = &card_source.Model{}
		m          = &card_config.Model{}
	)

	cardConfig, err = e.checkCardConfigCode(ctx, req.Code)
	if err != nil {
		return res, err
	}
	if cardConfig != nil {
		err = ecode.BoxCardCodeExistErr
		return res, err
	}

	sourceInfo, err = e.CardSourceRepo.FetchByID(ctx, req.SourceID)
	if err != nil {
		err = ecode.SystemErr
		log.Ctx(ctx).WithError(err).Error("CardConfigAdd")
		return res, err
	}
	if sourceInfo == nil {
		err = ecode.BoxCardSourceNotExistErr
		return res, err
	}

	m = e.toItemCardConfigModel(ctx, &req.CardConfig)

	_, err = e.CardConfigRepo.Create(ctx, m)
	if err != nil {
		return nil, err
	}
	res.ID = m.ID

	return
}

func (e *Entry) toItemCardConfigModel(ctx *gin.Context, req *cardDto.CardConfig) (model *card_config.Model) {
	model = &card_config.Model{
		CardCode:   req.Code,
		CardType:   req.Type,
		CardDesc:   req.Desc,
		CardName:   req.Name,
		CardImg:    req.Image,
		DetailMsg:  req.DetailMsg,
		ExpireDay:  req.ExpireDay,
		CardStatus: uint32(dbs.StatusEnable),
	}

	return
}

func (e *Entry) checkCardConfigCode(ctx *gin.Context, code string) (cardConfig *card_config.Model, err error) {
	var (
		cardConfigs = make(card_config.ModelList, 0)
	)
	cardConfigs, err = e.CardConfigRepo.FindByFilter(ctx, &card_config.Filter{
		CardCode: code,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkCardConfigCode FindByFilter")
		err = ecode.SystemErr
		return
	}
	if len(cardConfigs) == 0 {
		return nil, nil
	}
	cardConfig = cardConfigs[0]
	return cardConfig, nil
}

func (e *Entry) checkCardConfigID(ctx *gin.Context, id uint64) (cardConfig *card_config.Model, err error) {
	var (
		cardConfigs = make(card_config.ModelList, 0)
	)
	cardConfigs, err = e.CardConfigRepo.FindByFilter(ctx, &card_config.Filter{
		IDs: []uint64{id},
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkCardConfigID FindByFilter")
		err = ecode.SystemErr
		return
	}
	if len(cardConfigs) == 0 {
		return nil, nil
	}
	cardConfig = cardConfigs[0]
	return cardConfig, nil
}

func (e *Entry) CardConfigUpdate(ctx *gin.Context, req *cardDto.AdminCardConfigUpdateReq) (res *cardDto.AdminCardConfigUpdateResp, err error) {
	res = &cardDto.AdminCardConfigUpdateResp{}
	res.ID = req.ID

	var (
		cardConfig = &card_config.Model{}
		sourceInfo = &card_source.Model{}
		updateMap  = make(map[string]interface{})
	)

	cardConfig, err = e.checkCardConfigID(ctx, req.ID)
	if err != nil {
		return res, err
	}
	if cardConfig == nil {
		err = ecode.BoxCardNotExistErr
		return
	}

	sourceInfo, err = e.CardSourceRepo.FetchByID(ctx, req.SourceID)
	if err != nil {
		err = ecode.SystemErr
		log.Ctx(ctx).WithError(err).Error("CardConfigAdd")
		return res, err
	}
	if sourceInfo == nil {
		err = ecode.BoxCardSourceNotExistErr
		return res, err
	}
	if cardConfig.CardCode != req.Code {
		updateMap["card_code"] = req.Code
	}
	if cardConfig.CardName != req.Name {
		updateMap["card_name"] = req.Name
	}
	if cardConfig.CardDesc != req.Desc {
		updateMap["card_desc"] = req.Desc
	}
	if cardConfig.CardImg != req.Image {
		updateMap["card_img"] = req.Image
	}
	if cardConfig.DetailMsg != req.DetailMsg {
		updateMap["detail_msg"] = req.DetailMsg
	}
	if cardConfig.ExpireDay != req.ExpireDay {
		updateMap["expire_day"] = req.ExpireDay
	}
	if cardConfig.Source != req.SourceID {
		updateMap["source"] = req.SourceID
	}
	if cardConfig.CardType != req.Type {
		updateMap["card_type"] = req.Type
	}
	err = e.CardConfigRepo.UpdateMapByID(ctx, cardConfig.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardConfigUpdate")
		return nil, err
	}

	return res, nil
}

func (e *Entry) CardConfigInfo(ctx *gin.Context, req *cardDto.AdminCardConfigInfoReq) (res *cardDto.AdminCardConfigInfoResp, err error) {
	res = &cardDto.AdminCardConfigInfoResp{}

	var (
		cardConfig = &card_config.Model{}
		sources    = make(card_source.ModelList, 0)
		sourceMap  = make(map[uint64]*card_source.Model)
	)

	cardConfig, err = e.checkCardConfigID(ctx, req.ID)
	if err != nil {
		return res, err
	}
	if cardConfig == nil {
		return res, nil
	}

	sources, err = e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{
		ID: cardConfig.Source,
	})
	if err != nil {
		return res, err
	}
	sourceMap = sources.GetIDMap()

	res.CardConfigListInfoItem = e.toCardConfigListInfoItem(cardConfig, sourceMap)

	return res, nil
}

func (e *Entry) toCardConfigListInfoItem(cardConfig *card_config.Model, sourceMap map[uint64]*card_source.Model) (item cardDto.CardConfigListInfoItem) {
	item = cardDto.CardConfigListInfoItem{
		ID: cardConfig.ID,
		CardConfig: cardDto.CardConfig{
			Code:      cardConfig.CardCode,
			Desc:      cardConfig.CardDesc,
			Name:      cardConfig.CardName,
			Type:      cardConfig.CardType,
			ExpireDay: cardConfig.ExpireDay,
			SourceID:  cardConfig.Source,
			Image:     cardConfig.CardImg,
			DetailMsg: cardConfig.DetailMsg,
		},
	}
	if source, ok := sourceMap[cardConfig.Source]; ok {
		item.SourceName = source.SourceName
	}
	item.CreateTime = cardConfig.CreatedAt.Format(dbs.TimeDateFormatFull)

	return
}

func (e *Entry) CardConfigListInfo(ctx *gin.Context, req *cardDto.AdminCardConfigListInfoReq) (res *cardDto.AdminCardConfigListInfoResp, err error) {
	res = &cardDto.AdminCardConfigListInfoResp{}
	res.List = make([]*cardDto.CardConfigListInfoItem, 0)

	var (
		cardConfigList = make(card_config.ModelList, 0)
		sourceIDs      = make([]uint64, 0)
		sources        = make(card_source.ModelList, 0)
		sourceMap      = make(map[uint64]*card_source.Model)
		total          = int64(0)
	)

	total, cardConfigList, err = e.CardConfigRepo.DataPageList(ctx, &card_config.Filter{
		NameLike: req.Name,
		//SourceName: req.SourceName, // TODO: join
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardConfigListInfo")
		return res, err
	}
	if total == 0 {
		return res, nil
	}
	res.Total = total
	if sourceIDs = cardConfigList.GetSourceIDs(); len(sourceIDs) > 0 {
		sources, err = e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{
			IDs: sourceIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("CardConfigListInfo")
			return res, err
		}
		sourceMap = sources.GetIDMap()
	}

	for _, model := range cardConfigList {
		tmp := e.toCardConfigListInfoItem(model, sourceMap)
		res.List = append(res.List, &tmp)
	}
	return res, nil
}
