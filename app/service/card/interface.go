package card

import (
	activeDao "blind_box/app/dao/activity"
	adminAccount "blind_box/app/dao/admin/account"
	"blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_source"
	"blind_box/app/dao/card/card_user"
	userDao "blind_box/app/dao/user"
	signDao "blind_box/app/dao/user/sign"
	cardDto "blind_box/app/dto/card"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
)

type Server interface {
	AdminCardConfig

	AdminCardSource

	AdminCardUser
}

type AdminCardConfig interface {
	CardConfigList(ctx *gin.Context) (*cardDto.AdminCardConfigListResp, error)

	CardConfigAdd(ctx *gin.Context, req *cardDto.AdminCardConfigAddReq) (*cardDto.AdminCardConfigAddResp, error)

	CardConfigUpdate(ctx *gin.Context, req *cardDto.AdminCardConfigUpdateReq) (*cardDto.AdminCardConfigUpdateResp, error)

	CardConfigListInfo(ctx *gin.Context, req *cardDto.AdminCardConfigListInfoReq) (*cardDto.AdminCardConfigListInfoResp, error)

	CardConfigInfo(ctx *gin.Context, req *cardDto.AdminCardConfigInfoReq) (*cardDto.AdminCardConfigInfoResp, error)
}

type AdminCardSource interface {
	CardSourceList(ctx *gin.Context, req *cardDto.AdminCardSourceListReq) (*cardDto.AdminCardSourceListResp, error)

	CardSourceAdd(ctx *gin.Context, req *cardDto.AdminCardSourceAddReq) (*cardDto.AdminCardSourceAddResp, error)

	CardSourceUpdate(ctx *gin.Context, req *cardDto.AdminCardSourceUpdateReq) (*cardDto.AdminCardSourceUpdateResp, error)
}

type AdminCardUser interface {
	CardSend(ctx *gin.Context, req *cardDto.AdminCardSendReq) (*cardDto.AdminCardSendResp, error)

	UserCardList(ctx *gin.Context, req *cardDto.AdminUserCardReq) (*cardDto.AdminUserCardResp, error)

	UserCardDel(ctx *gin.Context, req *cardDto.AdminUserCardDelReq) (*cardDto.AdminUserCardDelResp, error)
}

// TODO替换
type Entry struct {
	UserRepo    *userDao.Entry
	SignRepo    *signDao.Entry
	ActiveRepo  *activeDao.Entry
	AccountRepo adminAccount.Repo

	CardConfigRepo card_config.Repo
	CardSourceRepo card_source.Repo
	CardUserRepo   card_user.Repo

	RedisCli *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:    userDao.GetRepo(),
		SignRepo:    signDao.GetRepo(),
		ActiveRepo:  activeDao.GetRepo(),
		AccountRepo: adminAccount.GetRepo(),

		CardConfigRepo: card_config.GetRepo(),
		CardSourceRepo: card_source.GetRepo(),
		CardUserRepo:   card_user.GetRepo(),

		RedisCli: redis.GetRedisClient(),
	}
}
