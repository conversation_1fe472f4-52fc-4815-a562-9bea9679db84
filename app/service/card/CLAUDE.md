# Card Service Architecture Documentation

## 概览

卡牌服务是盲盒系统的核心辅助模块，提供卡牌配置管理、卡牌来源管理以及用户卡牌发放和使用功能。此服务实现了完整的卡牌生命周期管理，包括卡牌类型配置、发放策略、使用限制和管理后台功能。

## 目录结构

```
app/service/card/
├── interface.go           # 服务接口定义和依赖注入
├── admin_card_config.go   # 卡牌配置管理
├── admin_card_source.go   # 卡牌来源管理  
└── admin_card_user.go     # 用户卡牌发放和管理
```

## 架构设计

### 服务接口架构

采用接口隔离原则，将卡牌服务分为三个独立的功能接口：

```go
type Server interface {
    AdminCardConfig   // 卡牌配置管理接口
    AdminCardSource   // 卡牌来源管理接口
    AdminCardUser     // 用户卡牌管理接口
}
```

### 依赖注入架构

```go
type Entry struct {
    // 核心 DAO 依赖
    UserRepo    *userDao.Entry          // 用户信息管理
    SignRepo    *signDao.Entry          // 签到记录管理
    ActiveRepo  *activeDao.Entry        // 活动信息管理
    AccountRepo adminAccount.Repo       // 管理员账户
    
    // 卡牌业务 DAO
    CardConfigRepo card_config.Repo     // 卡牌配置
    CardSourceRepo card_source.Repo     // 卡牌来源
    CardUserRepo   card_user.Repo       // 用户卡牌
    
    // 基础设施依赖
    RedisCli *redis.RedisClient         // Redis 客户端
}
```

## 功能模块详解

### 1. 卡牌配置管理 (AdminCardConfig)

负责管理卡牌的基础配置信息，包括卡牌类型、属性、过期规则等。

#### 核心方法

```go
// 查询卡牌配置列表
func (e *Entry) CardConfigList(ctx *gin.Context) (*cardDto.AdminCardConfigListResp, error)

// 添加新的卡牌配置
func (e *Entry) CardConfigAdd(ctx *gin.Context, req *cardDto.AdminCardConfigAddReq) (*cardDto.AdminCardConfigAddResp, error)

// 更新卡牌配置
func (e *Entry) CardConfigUpdate(ctx *gin.Context, req *cardDto.AdminCardConfigUpdateReq) (*cardDto.AdminCardConfigUpdateResp, error)

// 获取卡牌配置详情
func (e *Entry) CardConfigInfo(ctx *gin.Context, req *cardDto.AdminCardConfigInfoReq) (*cardDto.AdminCardConfigInfoResp, error)

// 分页查询卡牌配置
func (e *Entry) CardConfigListInfo(ctx *gin.Context, req *cardDto.AdminCardConfigListInfoReq) (*cardDto.AdminCardConfigListInfoResp, error)
```

#### 业务特性

1. **卡牌代码唯一性验证**
   ```go
   func (e *Entry) checkCardConfigCode(ctx *gin.Context, code string) (cardConfig *card_config.Model, err error) {
       // 检查卡牌代码是否已存在
       cardConfigs, err := e.CardConfigRepo.FindByFilter(ctx, &card_config.Filter{
           CardCode: code,
       })
       if len(cardConfigs) > 0 {
           return cardConfigs[0], nil  // 代码已存在
       }
       return nil, nil  // 代码可用
   }
   ```

2. **来源依赖验证**
   ```go
   // 验证卡牌来源是否存在
   sourceInfo, err := e.CardSourceRepo.FetchByID(ctx, req.SourceID)
   if sourceInfo == nil {
       return nil, ecode.BoxCardSourceNotExistErr
   }
   ```

3. **状态管理**
   - 所有新创建的卡牌配置默认状态为启用 (`dbs.StatusEnable`)
   - 支持通过状态过滤有效配置

#### 数据转换模式

```go
// DTO 到 Model 的转换
func (e *Entry) toItemCardConfigModel(ctx *gin.Context, req *cardDto.CardConfig) *card_config.Model {
    return &card_config.Model{
        CardCode:   req.Code,
        CardType:   req.Type,
        CardDesc:   req.Desc,
        CardName:   req.Name,
        CardImg:    req.Image,
        DetailMsg:  req.DetailMsg,
        ExpireDay:  req.ExpireDay,
        CardStatus: uint32(dbs.StatusEnable),
    }
}

// Model 到 DTO 的转换
func (e *Entry) toCardConfigListInfoItem(cardConfig *card_config.Model, sourceMap map[uint64]*card_source.Model) cardDto.CardConfigListInfoItem {
    item := cardDto.CardConfigListInfoItem{
        ID: cardConfig.ID,
        CardConfig: cardDto.CardConfig{
            Code:      cardConfig.CardCode,
            Name:      cardConfig.CardName,
            Type:      cardConfig.CardType,
            // ... 其他字段映射
        },
    }
    
    // 关联来源信息
    if source, ok := sourceMap[cardConfig.Source]; ok {
        item.SourceName = source.SourceName
    }
    
    return item
}
```

### 2. 卡牌来源管理 (AdminCardSource)

管理卡牌的来源信息，用于标识卡牌的获取渠道或分类。

#### 核心方法

```go
// 添加卡牌来源
func (e *Entry) CardSourceAdd(ctx *gin.Context, req *cardDto.AdminCardSourceAddReq) (*cardDto.AdminCardSourceAddResp, error)

// 查询来源列表
func (e *Entry) CardSourceList(ctx *gin.Context, req *cardDto.AdminCardSourceListReq) (*cardDto.AdminCardSourceListResp, error)

// 更新来源信息
func (e *Entry) CardSourceUpdate(ctx *gin.Context, req *cardDto.AdminCardSourceUpdateReq) (*cardDto.AdminCardSourceUpdateResp, error)
```

#### 业务特性

1. **来源名称唯一性保证**
   ```go
   // 添加时检查重复
   cardSourceList, err := e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{
       SourceName: req.SourceName,
   })
   if len(cardSourceList) > 0 {
       return nil, ecode.CardSourceAlreadyExist
   }
   ```

2. **更新防冲突机制**
   ```go
   // 更新时避免名称冲突
   if cardSource.SourceName == req.SourceName && cardSource.ID != req.ID {
       return nil, ecode.CardSourceAlreadyExist
   }
   ```

3. **简洁的 CRUD 操作**
   - 使用 `UpdateMapByID` 进行精准字段更新
   - 直接的 Model 到 DTO 转换

### 3. 用户卡牌管理 (AdminCardUser)

处理用户卡牌的发放、查询和管理功能，是卡牌系统与用户交互的核心模块。

#### 核心方法

```go
// 卡牌发放
func (e *Entry) CardSend(ctx *gin.Context, req *cardDto.AdminCardSendReq) (*cardDto.AdminCardSendResp, error)

// 用户卡牌列表查询
func (e *Entry) UserCardList(ctx *gin.Context, req *cardDto.AdminUserCardReq) (*cardDto.AdminUserCardResp, error)

// 删除用户卡牌
func (e *Entry) UserCardDel(ctx *gin.Context, req *cardDto.AdminUserCardDelReq) (*cardDto.AdminUserCardDelResp, error)
```

#### 核心业务逻辑

1. **分布式锁保证发放原子性**
   ```go
   func (e *Entry) CardSend(ctx *gin.Context, req *cardDto.AdminCardSendReq) (*cardDto.AdminCardSendResp, error) {
       // 获取用户卡牌锁，防止并发发放
       if err := e.RedisCli.Lock(ctx, redis.GetUserItemCardLockKey(req.UserID), redis.DefaultLockTime); err != nil {
           return nil, ecode.SystemBusyErr
       }
       defer e.RedisCli.Unlock(ctx, redis.GetUserItemCardLockKey(req.UserID))
       
       // 发放逻辑...
   }
   ```

2. **过期时间计算**
   ```go
   // 基于配置的过期天数计算过期时间
   expireDay := cardConfig.ExpireDay
   tmp.ExpireTime = carbon.Now().EndOfDay().Timestamp() + int64(expireDay)*24*3600
   ```

3. **批量发放支持**
   ```go
   // 支持一次发放多张相同卡牌
   for i := uint32(0); i < req.Num; i++ {
       userItemCardInfos = append(userItemCardInfos, tmp)
   }
   
   err = e.CardUserRepo.BatchCreate(ctx, userItemCardInfos)
   ```

4. **复杂关联查询优化**
   ```go
   func (e *Entry) UserCardList(ctx *gin.Context, req *cardDto.AdminUserCardReq) (*cardDto.AdminUserCardResp, error) {
       // 1. 查询用户卡牌列表
       total, userItemCardInfos, err := e.CardUserRepo.DataPageList(ctx, filter, req.Page, req.Limit)
       
       // 2. 收集关联ID避免N+1查询
       userIDs := userItemCardInfos.GetUserIDs()
       activeIDs := userItemCardInfos.GetActiveIDs()
       accountIDs := userItemCardInfos.GetCreateBys()
       
       // 3. 批量查询关联数据
       userMap := make(map[uint64]*userDao.Model)
       activeMap := make(map[uint64]*activeDao.Model)
       accountMap := make(map[uint64]*adminAccount.Model)
       
       // 4. 并行查询用户、活动、管理员信息
       if len(userIDs) > 0 {
           userInfos, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: userIDs})
           userMap = userInfos.GetIDMap()
       }
       
       // 5. 组装响应数据
       for _, v := range userItemCardInfos {
           tmp := &cardDto.UserCardListItem{
               ID:         v.ID,
               Uid:        v.UserID,
               CardCode:   v.CardCode,
               // ... 其他字段
           }
           
           // 关联用户信息
           if u, ok := userMap[v.UserID]; ok {
               tmp.UserName = u.Nickname
           }
           
           // 关联管理员信息
           if a, ok := accountMap[v.CreateBy]; ok {
               tmp.AdminUserName = a.Name
           }
           
           // 关联使用活动信息
           if a, ok := activeMap[v.ActiveID]; ok && v.Status == card_user.UserItemCardStatusUsed {
               tmp.UseActiveInfo = fmt.Sprintf("活动ID/名称/箱子/位置:%d/%s/%d/%d",
                   a.ID, a.Title, v.BoxID, v.BoxSlot)
           }
       }
   }
   ```

## 数据模型设计

### 卡牌配置表 (card_config)

```go
type Model struct {
    ID         uint64    `gorm:"primaryKey"`
    CardCode   string    `gorm:"unique"`      // 卡牌唯一代码
    CardType   uint32                         // 卡牌类型
    CardName   string                         // 卡牌名称
    CardDesc   string                         // 卡牌描述
    CardImg    string                         // 卡牌图片
    DetailMsg  string                         // 详细说明
    ExpireDay  uint32                         // 过期天数
    Source     uint64                         // 来源ID
    CardStatus uint32                         // 卡牌状态
    CreatedAt  time.Time
    UpdatedAt  time.Time
}
```

### 卡牌来源表 (card_source)

```go
type Model struct {
    ID         uint64    `gorm:"primaryKey"`
    SourceName string    `gorm:"unique"`      // 来源名称（唯一）
    CreatedAt  time.Time
    UpdatedAt  time.Time
}
```

### 用户卡牌表 (card_user)

```go
type Model struct {
    ID         uint64    `gorm:"primaryKey"`
    UserID     uint64                         // 用户ID
    CardCode   string                         // 卡牌代码
    CardType   uint32                         // 卡牌类型
    CardName   string                         // 卡牌名称
    DetailMsg  string                         // 详细信息
    ExpireTime int64                          // 过期时间戳
    Status     uint32                         // 卡牌状态
    ActiveID   uint64                         // 使用的活动ID
    BoxID      uint64                         // 使用的箱子ID
    BoxSlot    uint32                         // 使用的位置
    SourceType uint64                         // 来源类型
    CreateBy   uint64                         // 创建者ID
    Remark     string                         // 备注
    IsDeleted  int                           // 删除标记
    CreatedAt  time.Time
    UpdatedAt  time.Time
}
```

## 业务流程

### 卡牌发放流程

```
1. 管理员发起卡牌发放请求
   ↓
2. 获取分布式锁（用户维度）
   ↓
3. 验证用户存在性
   ↓
4. 验证卡牌配置有效性
   ↓
5. 计算卡牌过期时间
   ↓
6. 批量创建用户卡牌记录
   ↓
7. 释放分布式锁
   ↓
8. 返回发放结果
```

### 卡牌配置管理流程

```
1. 卡牌配置添加/更新请求
   ↓
2. 验证卡牌代码唯一性
   ↓
3. 验证关联来源存在性
   ↓
4. 执行数据库操作
   ↓
5. 返回操作结果
```

## 错误处理规范

### 业务错误定义

```go
// 卡牌相关错误
ecode.BoxCardCodeExistErr        // 卡牌代码已存在
ecode.BoxCardNotExistErr         // 卡牌不存在
ecode.BoxCardTypeNotExistErr     // 卡牌类型不存在
ecode.BoxCardSourceNotExistErr   // 卡牌来源不存在
ecode.CardSourceAlreadyExist     // 来源已存在

// 用户相关错误
ecode.UserNotExistErr           // 用户不存在

// 系统错误
ecode.SystemErr                 // 系统错误
ecode.SystemBusyErr            // 系统繁忙（锁获取失败）
```

### 错误处理模式

```go
// 1. 参数验证错误 - 直接返回业务错误
if cardConfig == nil {
    return nil, ecode.BoxCardTypeNotExistErr
}

// 2. 数据库错误 - 记录日志并返回系统错误
if err != nil {
    log.Ctx(ctx).WithError(err).Error("CardConfigAdd Create")
    return nil, ecode.SystemErr
}

// 3. 业务逻辑错误 - 返回具体业务错误
if len(cardConfigs) > 0 {
    return nil, ecode.BoxCardCodeExistErr
}

// 4. 锁获取失败 - 返回系统繁忙
if err := e.RedisCli.Lock(ctx, lockKey, lockTime); err != nil {
    return nil, ecode.SystemBusyErr
}
```

## 性能优化策略

### 1. 批量操作优化

```go
// 批量创建用户卡牌，避免循环单个插入
err = e.CardUserRepo.BatchCreate(ctx, userItemCardInfos)
```

### 2. 查询优化

```go
// 避免 N+1 查询，使用批量查询 + Map 映射
userIDs := userItemCardInfos.GetUserIDs()
userInfos, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: userIDs})
userMap := userInfos.GetIDMap()
```

### 3. 分布式锁优化

```go
// 锁粒度控制在用户级别，避免全局锁
lockKey := redis.GetUserItemCardLockKey(req.UserID)
```

### 4. 关联查询优化

```go
// 只在需要时查询关联数据
if len(userIDs) > 0 {
    // 查询用户信息
}
if len(activeIDs) > 0 {
    // 查询活动信息
}
```

## 并发安全机制

### 分布式锁策略

```go
// 用户卡牌发放使用分布式锁保证原子性
func (e *Entry) CardSend(ctx *gin.Context, req *cardDto.AdminCardSendReq) (*cardDto.AdminCardSendResp, error) {
    // 锁定特定用户的卡牌操作
    lockKey := redis.GetUserItemCardLockKey(req.UserID)
    
    if err := e.RedisCli.Lock(ctx, lockKey, redis.DefaultLockTime); err != nil {
        return nil, ecode.SystemBusyErr
    }
    defer e.RedisCli.Unlock(ctx, lockKey)
    
    // 原子性操作...
}
```

### 并发安全考虑

1. **用户级别锁**: 防止同一用户并发获取卡牌
2. **自动解锁**: 使用 defer 确保锁释放
3. **锁超时**: 防止死锁情况
4. **锁失败处理**: 返回系统繁忙而非错误

## 代码规范总结

### 1. 接口设计原则

- **接口隔离**: 将不同职责分离到独立接口
- **依赖注入**: 统一在 Entry 结构体中管理依赖
- **单例模式**: 使用 sync.Once 确保单例初始化

### 2. 数据验证模式

```go
// 标准验证流程：检查存在性 → 验证唯一性 → 执行操作
func (e *Entry) checkCardConfigCode(ctx *gin.Context, code string) (*card_config.Model, error) {
    // 1. 查询现有记录
    // 2. 判断是否存在
    // 3. 返回验证结果
}
```

### 3. 错误处理标准

- **业务错误**: 使用 ecode 包定义的错误码
- **系统错误**: 记录详细日志，返回通用系统错误
- **参数错误**: 直接返回参数错误，不记录日志

### 4. 性能优化模式

- **批量操作**: 使用 BatchCreate 等批量方法
- **关联查询**: 收集 ID 后批量查询，使用 Map 优化查找
- **条件查询**: 仅在必要时执行查询操作

### 5. 代码组织规范

- **文件命名**: 按功能模块命名 (`admin_card_*.go`)
- **方法命名**: 清晰表达功能 (`CardConfigAdd`, `checkCardConfigCode`)
- **变量命名**: 使用有意义的名称 (`cardConfig`, `sourceInfo`)

## 开发指南

### 新增卡牌类型步骤

1. **更新 DAO 层**: 在 `card_config` 和 `card_user` 中添加新的类型常量
2. **扩展验证逻辑**: 在配置管理中添加类型特定的验证
3. **更新 DTO 结构**: 添加新类型相关的请求响应字段
4. **实现业务逻辑**: 在 Service 层添加类型特定的处理逻辑
5. **更新前端接口**: 在 Handler 层暴露新的管理接口

### 添加新的发放渠道

1. **扩展来源管理**: 在 `CardSourceAdd` 中支持新渠道
2. **更新发放逻辑**: 在 `CardSend` 中添加渠道特定处理
3. **扩展查询条件**: 在 `UserCardList` 中支持按渠道筛选

### 性能监控建议

- **发放操作**: 监控锁等待时间和批量操作性能
- **查询操作**: 监控复杂关联查询的执行时间
- **缓存命中**: 如使用缓存，监控命中率

## 重要提醒

1. **分布式锁**: 所有涉及用户卡牌状态变更的操作必须使用分布式锁
2. **批量操作**: 优先使用批量方法减少数据库交互
3. **关联查询**: 避免 N+1 问题，使用批量查询 + Map 映射
4. **错误处理**: 区分业务错误和系统错误，合理记录日志
5. **数据一致性**: 确保卡牌配置、来源、用户卡牌之间的引用完整性
6. **过期管理**: 正确计算和处理卡牌过期时间
7. **权限控制**: 管理后台操作需要适当的权限验证

这份文档提供了卡牌服务的完整架构指南，确保在盲盒项目生态系统中卡牌功能的安全、高效和可维护的运行。