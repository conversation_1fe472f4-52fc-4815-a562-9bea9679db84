package card

import (
	"blind_box/app/dao/card/card_source"
	cardDto "blind_box/app/dto/card"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"github.com/gin-gonic/gin"
)

func (e *Entry) CardSourceAdd(ctx *gin.Context, req *cardDto.AdminCardSourceAddReq) (res *cardDto.AdminCardSourceAddResp, err error) {
	res = &cardDto.AdminCardSourceAddResp{}

	var (
		cardSourceList = make([]*card_source.Model, 0)
	)

	cardSourceList, err = e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{
		SourceName: req.SourceName,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceAdd FindByFilter")
		err = ecode.SystemErr
		return
	}
	if len(cardSourceList) > 0 {
		err = ecode.CardSourceAlreadyExist
		return
	}
	cardSource := &card_source.Model{
		SourceName: req.SourceName,
	}
	_, err = e.CardSourceRepo.Create(ctx, cardSource)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceAdd Create")
		err = ecode.SystemErr
		return
	}
	res.ID = cardSource.ID

	return
}

func (e *Entry) CardSourceList(ctx *gin.Context, req *cardDto.AdminCardSourceListReq) (res *cardDto.AdminCardSourceListResp, err error) {
	res = &cardDto.AdminCardSourceListResp{}
	res.List = make([]*cardDto.AdminCardSource, 0)

	var (
		cardSourceList = make([]*card_source.Model, 0)
	)

	cardSourceList, err = e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceList FindByFilter")
		err = ecode.SystemErr
		return
	}

	for _, model := range cardSourceList {
		res.List = append(res.List, &cardDto.AdminCardSource{
			ID:         model.ID,
			SourceName: model.SourceName,
		})
	}

	return
}

func (e *Entry) CardSourceUpdate(ctx *gin.Context, req *cardDto.AdminCardSourceUpdateReq) (res *cardDto.AdminCardSourceUpdateResp, err error) {
	res = &cardDto.AdminCardSourceUpdateResp{}
	res.ID = req.ID

	var (
		cardSourceList = make([]*card_source.Model, 0)
		cardSource     = &card_source.Model{}
		updateMap      = make(map[string]interface{})
	)

	cardSourceList, err = e.CardSourceRepo.FindByFilter(ctx, &card_source.Filter{
		SourceName: req.SourceName,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceAdd FindByFilter")
		err = ecode.SystemErr
		return
	}
	if len(cardSourceList) > 0 {
		err = ecode.CardSourceAlreadyExist
		return
	}

	cardSource, err = e.CardSourceRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceUpdate FindByFilter")
		err = ecode.SystemErr
		return
	}

	if cardSource.SourceName == req.SourceName && cardSource.ID != req.ID {
		err = ecode.CardSourceAlreadyExist
		return
	}

	updateMap["source_name"] = req.SourceName

	err = e.CardSourceRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSourceUpdate UpdateMapByID")
		err = ecode.SystemErr
		return
	}

	return
}
