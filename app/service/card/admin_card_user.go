package card

import (
	activeDao "blind_box/app/dao/activity"
	adminAccount "blind_box/app/dao/admin/account"
	"blind_box/app/dao/card/card_config"
	"blind_box/app/dao/card/card_user"
	userDao "blind_box/app/dao/user"
	cardDto "blind_box/app/dto/card"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

func (e *Entry) CardSend(ctx *gin.Context, req *cardDto.AdminCardSendReq) (res *cardDto.AdminCardSendResp, err error) {
	res = &cardDto.AdminCardSendResp{}
	if err = e.RedisCli.Lock(ctx, redis.GetUserItemCardLockKey(req.UserID), redis.DefaultLockTime); err != nil {
		return res, ecode.SystemBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserItemCardLockKey(req.UserID))

	var (
		userInfo          = &userDao.Model{}
		cardConfig        = &card_config.Model{}
		userItemCardInfos = make(card_user.ModelList, 0)
	)

	if userInfo, err = e.UserRepo.FetchByID(ctx, req.UserID); err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSend FetchByID")
		return res, ecode.SystemErr
	}
	if userInfo == nil {
		log.Ctx(ctx).Error("CardSend userInfo is nil")
		return res, ecode.UserNotExistErr
	}

	if cardConfig, err = e.CardConfigRepo.FetchByCode(ctx, req.CardCode); err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSend FetchByCode")
		return res, ecode.SystemErr
	}
	if cardConfig == nil {
		log.Ctx(ctx).Error("CardSend cardConfig is nil")
		return res, ecode.BoxCardTypeNotExistErr
	}

	expireDay := cardConfig.ExpireDay

	tmp := &card_user.Model{}
	tmp.Remark = req.Remark
	tmp.UserID = req.UserID
	tmp.CardCode = cardConfig.CardCode
	tmp.CardType = cardConfig.CardType
	tmp.CardName = cardConfig.CardName
	tmp.DetailMsg = cardConfig.DetailMsg
	tmp.ExpireTime = carbon.Now().EndOfDay().Timestamp() + int64(expireDay)*24*3600
	tmp.CreateBy = req.Uid
	tmp.SourceType = cardConfig.Source

	for i := uint32(0); i < req.Num; i++ {
		userItemCardInfos = append(userItemCardInfos, tmp)
	}

	err = e.CardUserRepo.BatchCreate(ctx, userItemCardInfos)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CardSend BatchCreate")
		return res, ecode.SystemErr
	}
	return res, nil
}

func (e *Entry) UserCardList(ctx *gin.Context, req *cardDto.AdminUserCardReq) (res *cardDto.AdminUserCardResp, err error) {
	res = &cardDto.AdminUserCardResp{}
	res.List = make([]*cardDto.UserCardListItem, 0)

	var (
		userItemCardInfos card_user.ModelList
		total             int64
	)
	if total, userItemCardInfos, err = e.CardUserRepo.DataPageList(ctx, &card_user.Filter{
		UserID: req.UserID,
		//AccountID: req.,
		CardCode:  req.CardCode,
		Status:    req.Status,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Source:    req.SourceID,
	}, req.Page, req.Limit); err != nil {
		log.Ctx(ctx).WithError(err).Error("UserCardList")
		return res, ecode.SystemErr
	}
	if total <= 0 {
		return res, nil
	}
	res.Count = total

	var (
		userIDs    = userItemCardInfos.GetUserIDs()
		userMap    = make(map[uint64]*userDao.Model)
		activeIDs  = userItemCardInfos.GetActiveIDs()
		activeMap  = make(map[uint64]*activeDao.Model)
		accountIDs = userItemCardInfos.GetCreateBys()
		accountMap = make(map[uint64]*adminAccount.Model)
	)
	if t := len(userIDs); t > 0 {
		userInfos, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{
			IDS: userIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("UserCardList ListByIDs")
			return res, ecode.SystemErr
		}
		userMap = userInfos.GetIDMap()
	}
	if t := len(activeIDs); t > 0 {
		activeInfos, err := e.ActiveRepo.FindByFilter(ctx, &activeDao.Filter{
			Ids: activeIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("UserCardList ListByIDs")
			return res, ecode.SystemErr
		}
		activeMap = activeInfos.GetIDMap()
	}
	if t := len(accountIDs); t > 0 {
		accountInfos, err := e.AccountRepo.FindByFilter(ctx, &adminAccount.Filter{
			IDS: accountIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("UserCardList ListByIDs")
			return res, ecode.SystemErr
		}
		accountMap = accountInfos.GetIDMap()
	}

	for _, v := range userItemCardInfos {
		tmp := &cardDto.UserCardListItem{}
		tmp.ID = v.ID
		tmp.Uid = v.UserID
		tmp.CardCode = v.CardCode
		tmp.CardName = v.CardName
		tmp.CreateTime = uint64(v.CreatedAt.Unix())
		tmp.Status = v.Status
		tmp.ExpireTime = uint64(v.ExpireTime)
		tmp.SourceType = uint32(v.SourceType)
		tmp.Remark = v.Remark
		tmp.IsDelete = uint32(v.IsDeleted)
		if u, ok := userMap[v.UserID]; ok {
			tmp.UserName = u.Nickname
		}
		if a, ok := accountMap[v.CreateBy]; ok {
			tmp.AdminUserName = a.Name
		}
		if a, ok := activeMap[v.ActiveID]; ok && v.Status == card_user.UserItemCardStatusUsed {
			tmp.UseActiveInfo = fmt.Sprintf("活动ID/名称/箱子/位置:%d/%s/%d/%d",
				a.ID, a.Title, v.BoxID, v.BoxSlot)
		}
		res.List = append(res.List, tmp)
	}
	return res, nil
}

func (e *Entry) UserCardDel(ctx *gin.Context, req *cardDto.AdminUserCardDelReq) (res *cardDto.AdminUserCardDelResp, err error) {
	res = &cardDto.AdminUserCardDelResp{}

	if err = e.CardUserRepo.DeleteByID(ctx, req.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("UserCardDel DeleteByID")
		return res, ecode.SystemErr
	}
	return res, nil
}
