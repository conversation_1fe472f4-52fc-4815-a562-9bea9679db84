# app/service 目录代码规范

本文档记录 `/app/service` 目录下的业务逻辑层代码规范和最佳实践。

## 目录结构规范

### 模块组织方式
```
app/service/
├── [模块名]/                # 业务模块目录
│   ├── interface.go        # 接口定义和依赖注入
│   ├── [业务].go           # 具体业务实现
│   ├── admin_[业务].go     # 管理后台相关业务
│   └── [子模块]/           # 复杂模块的子目录
├── common/                  # 公共服务
└── openapi/                # 开放 API 服务
```

### 典型模块示例
- `user/`: 用户相关业务（登录、积分、签到等）
- `order/`: 订单相关业务（创建、支付、退款等）
- `box/`: 盲盒相关业务（抽盒、策略、库存等）
- `goods/`: 商品相关业务（SKU、SPU、库存管理等）
- `coupon/`: 优惠券相关业务
- `admin/`: 管理后台通用业务

## interface.go 文件规范

### 基本结构
```go
package [模块名]

import (
    // DAO 层依赖
    userDao "blind_box/app/dao/user"
    orderDao "blind_box/app/dao/order/order"
    
    // 其他 Service 依赖
    commonSrv "blind_box/app/service/common"
    
    // 工具包
    "blind_box/pkg/redis"
    "sync"
    
    jsoniter "github.com/json-iterator/go"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Server 接口定义（可选）
type Server interface {
    UserOperation
    AdminUser
}

// 业务接口定义
type UserOperation interface {
    UserLogin(ctx *gin.Context, req *dto.UserLoginReq) (*dto.UserLoginResp, error)
}

type AdminUser interface {
    AdminUserList(ctx *gin.Context, req *dto.AdminUserListReq) (*dto.AdminUserListResp, error)
}

// Entry 依赖注入结构体
type Entry struct {
    // DAO 层依赖
    UserRepo      *userDao.Entry
    OrderRepo     orderDao.Repo
    
    // Service 层依赖
    CommonSrv     commonSrv.Server
    
    // 工具依赖
    RedisCli      *redis.RedisClient
}

// 单例模式
var (
    defaultEntry         *Entry
    defaultEntryInitOnce sync.Once
)

// GetService 获取服务实例
func GetService() *Entry {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}

// newEntry 初始化依赖
func newEntry() *Entry {
    return &Entry{
        UserRepo:  userDao.GetRepo(),
        OrderRepo: orderDao.GetRepo(),
        CommonSrv: commonSrv.GetService(),
        RedisCli:  redis.GetRedisClient(),
    }
}
```

### 依赖注入规范

#### 1. DAO 层依赖
```go
// 导入时使用别名避免冲突
import (
    userDao "blind_box/app/dao/user"
    orderDao "blind_box/app/dao/order/order"
    skuDao "blind_box/app/dao/goods/sku"
)

type Entry struct {
    UserRepo  *userDao.Entry    // 使用指针类型
    OrderRepo orderDao.Repo      // 或接口类型
    SkuRepo   *skuDao.Entry
}
```

#### 2. Service 层依赖
```go
import (
    commonSrv "blind_box/app/service/common"
    couponSrv "blind_box/app/service/coupon"
)

type Entry struct {
    CommonSrv  commonSrv.Server    // 跨服务调用
    CouponSrv  *couponSrv.Entry
}
```

#### 3. 新增依赖步骤
```go
// 1. 在 import 中添加
import stockLogDao "blind_box/app/dao/goods/stock_log"

// 2. 在 Entry 结构体中添加字段
type Entry struct {
    // ... 其他字段
    StockLogRepo *stockLogDao.Entry  // 新增字段
}

// 3. 在 newEntry() 中初始化
func newEntry() *Entry {
    return &Entry{
        // ... 其他初始化
        StockLogRepo: stockLogDao.GetRepo(),  // 初始化
    }
}
```

## 业务方法规范

### 方法命名规范
```go
// 用户端方法 - 直接使用业务名称
func (e *Entry) UserLogin(ctx *gin.Context, req *dto.UserLoginReq) (*dto.UserLoginResp, error)
func (e *Entry) OrderCreate(ctx *gin.Context, req *dto.OrderCreateReq) (*dto.OrderCreateRes, error)
func (e *Entry) BoxOpen(ctx *gin.Context, req *dto.BoxOpenReq) (*dto.BoxOpenRes, error)

// 管理后台方法 - Admin 前缀
func (e *Entry) AdminUserList(ctx *gin.Context, req *dto.AdminUserListReq) (*dto.AdminUserListResp, error)
func (e *Entry) AdminOrderCancel(ctx *gin.Context, req *dto.AdminOrderCancelReq) (*dto.AdminOrderCancelRes, error)

// 内部方法 - 小写开头
func (e *Entry) getUserInfo(ctx *gin.Context, userID uint64) (*userDao.Model, error)
func (e *Entry) calculateOrderFee(ctx *gin.Context, order *orderDao.Model) (uint64, uint64, error)
func (e *Entry) validateStock(ctx *gin.Context, skuID uint64, quantity uint32) error
```

### 方法签名规范
```go
// 标准业务方法签名
func (e *Entry) MethodName(ctx *gin.Context, req *dto.RequestType) (*dto.ResponseType, error) {
    // 实现
}

// 带事务的内部方法
func (e *Entry) methodNameWithTx(ctx *gin.Context, tx *gorm.DB, params...) error {
    // 实现
}

// 辅助方法
func (e *Entry) helperMethod(ctx *gin.Context, params...) (returnType, error) {
    // 实现
}
```

## 事务处理规范

### 事务管理原则
- 事务在 Service 层管理
- 使用 Transaction 方法包装事务逻辑
- 传递事务对象到 DAO 层

### 标准事务模式
```go
func (e *Entry) CreateOrder(ctx *gin.Context, req *dto.OrderCreateReq) (*dto.OrderCreateRes, error) {
    var res *dto.OrderCreateRes
    
    // 开启事务
    err := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
        // 1. 创建订单
        orderModel := &orderDao.Model{
            // ... 字段赋值
        }
        if err := e.OrderRepo.CreateWithTx(ctx, tx, orderModel); err != nil {
            return err  // 返回错误会自动回滚
        }
        
        // 2. 扣减库存
        if err := e.SkuRepo.LockStockWithTx(ctx, tx, req.SkuID, req.Quantity); err != nil {
            return err
        }
        
        // 3. 使用优惠券
        if req.CouponID > 0 {
            if err := e.CouponSrv.UseCouponWithTx(ctx, tx, req.CouponID); err != nil {
                return err
            }
        }
        
        // 4. 扣减积分
        if req.UsePoints > 0 {
            if err := e.UserSrv.UsePointsWithTx(ctx, tx, req.UserID, req.UsePoints); err != nil {
                return err
            }
        }
        
        res = &dto.OrderCreateRes{
            OrderID: orderModel.ID,
        }
        return nil  // 返回 nil 会自动提交
    })
    
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("CreateOrder transaction failed")
        return nil, err
    }
    
    return res, nil
}
```

### 嵌套事务处理
```go
// 支持事务传递的方法
func (e *Entry) ProcessOrderWithTx(ctx *gin.Context, tx *gorm.DB, orderID uint64) error {
    // 使用传入的事务
    order, err := e.OrderRepo.FetchByIDWithTx(ctx, tx, orderID)
    if err != nil {
        return err
    }
    
    // 继续使用同一个事务
    if err := e.updateOrderStatusWithTx(ctx, tx, order); err != nil {
        return err
    }
    
    return nil
}
```

## 错误处理规范

### 错误返回原则
```go
// Service 层返回业务错误
if userModel == nil {
    return nil, ecode.UserNotExistErr
}

// 参数验证错误
if err := req.Validate(); err != nil {
    log.Ctx(ctx).WithError(err).Error("parameter validation failed")
    return nil, ecode.ParamErr
}

// 数据库错误
if err != nil {
    log.Ctx(ctx).WithError(err).Error("database operation failed")
    return nil, err  // 直接返回原始错误或包装为业务错误
}

// 业务逻辑错误
if stock < quantity {
    return nil, ecode.StockNotEnoughErr
}
```

### 错误日志记录
```go
// 记录错误上下文
log.Ctx(ctx).WithError(err).WithFields(log.Fields{
    "userID":  req.UserID,
    "orderID": req.OrderID,
}).Error("OrderCreate failed")

// 记录警告
log.Ctx(ctx).Warn("Cache update failed, but continue: %v", err)

// 记录调试信息
log.Ctx(ctx).Debug("Processing order: %+v", order)
```

### 非关键错误处理
```go
// 缓存更新失败不影响主流程
if err := e.UserRepo.RedisClearUserInfo(ctx, userID); err != nil {
    log.Ctx(ctx).WithError(err).Warn("Failed to clear user cache, userID=%d", userID)
    // 不返回错误，继续执行
}

// 日志记录失败不影响业务
if err := e.logUserAction(ctx, userID, action); err != nil {
    log.Ctx(ctx).WithError(err).Warn("Failed to log user action")
    // 继续执行
}
```

## Service 层与 DAO 层交互

### 基本交互模式
```go
func (e *Entry) GetUserInfo(ctx *gin.Context, userID uint64) (*dto.UserInfoResp, error) {
    // 1. 调用 DAO 层获取数据
    userModel, err := e.UserRepo.FetchByID(ctx, userID)
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("fetch user failed")
        return nil, err
    }
    
    // 2. 业务逻辑处理
    if userModel == nil || userModel.Status != 1 {
        return nil, ecode.UserNotExistErr
    }
    
    // 3. 数据转换
    resp := &dto.UserInfoResp{
        UserID:   userModel.ID,
        Nickname: userModel.Nickname,
        Avatar:   helper.GetImageCdnUrl(ctx, userModel.Avatar),
    }
    
    return resp, nil
}
```

### 批量查询优化
```go
func (e *Entry) GetOrderList(ctx *gin.Context, req *dto.OrderListReq) (*dto.OrderListRes, error) {
    // 1. 批量查询主数据
    orders, err := e.OrderRepo.FindByFilter(ctx, &orderDao.Filter{
        UserID: req.UserID,
        Status: req.Status,
    })
    if err != nil {
        return nil, err
    }
    
    // 2. 收集关联 ID
    userIDs := orders.GetUserIDs()
    skuIDs := orders.GetSkuIDs()
    
    // 3. 批量查询关联数据
    users, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDs: userIDs})
    if err != nil {
        return nil, err
    }
    userMap := users.GetIDMap()
    
    skus, err := e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{Ids: skuIDs})
    if err != nil {
        return nil, err
    }
    skuMap := skus.GetIDMap()
    
    // 4. 组装结果
    res := &dto.OrderListRes{
        List: make([]*dto.OrderItem, 0, len(orders)),
    }
    
    for _, order := range orders {
        item := &dto.OrderItem{
            OrderID:  order.ID,
            UserName: userMap[order.UserID].Nickname,
            SkuName:  skuMap[order.SkuID].Title,
        }
        res.List = append(res.List, item)
    }
    
    return res, nil
}
```

### 缓存使用
```go
func (e *Entry) GetUserInfoWithCache(ctx *gin.Context, userID uint64) (*dto.UserInfoResp, error) {
    // 1. 尝试从缓存读取
    userModel, err := e.UserRepo.RedisUserInfo(ctx, userID)
    if err != nil && err != redis.Nil {
        log.Ctx(ctx).WithError(err).Warn("redis get failed")
        // 缓存错误不影响业务，继续从数据库读取
    }
    
    // 2. 缓存未命中，从数据库读取
    if userModel == nil {
        userModel, err = e.UserRepo.FetchByID(ctx, userID)
        if err != nil {
            return nil, err
        }
        
        // 3. 异步更新缓存
        go func() {
            if err := e.UserRepo.RedisReloadUserInfo(context.Background(), userID); err != nil {
                log.WithError(err).Warn("reload cache failed")
            }
        }()
    }
    
    // 4. 返回结果
    return e.convertToUserInfo(userModel), nil
}
```

## 复杂业务逻辑组织

### 策略模式
```go
// 定义策略接口
type PaymentStrategy interface {
    Pay(ctx *gin.Context, order *orderDao.Model) error
    Refund(ctx *gin.Context, order *orderDao.Model) error
}

// 实现具体策略
type WechatPayStrategy struct{}
func (s *WechatPayStrategy) Pay(ctx *gin.Context, order *orderDao.Model) error {
    // 微信支付逻辑
}

type AlipayStrategy struct{}
func (s *AlipayStrategy) Pay(ctx *gin.Context, order *orderDao.Model) error {
    // 支付宝支付逻辑
}

// 使用策略
func (e *Entry) ProcessPayment(ctx *gin.Context, req *dto.PaymentReq) error {
    var strategy PaymentStrategy
    
    switch req.PayMethod {
    case PayMethodWechat:
        strategy = &WechatPayStrategy{}
    case PayMethodAlipay:
        strategy = &AlipayStrategy{}
    default:
        return ecode.PayMethodNotSupportErr
    }
    
    return strategy.Pay(ctx, req.Order)
}
```

### 业务流程拆分
```go
// 主流程
func (e *Entry) CreateOrder(ctx *gin.Context, req *dto.OrderCreateReq) (*dto.OrderCreateRes, error) {
    // 1. 参数验证
    if err := e.validateOrderRequest(ctx, req); err != nil {
        return nil, err
    }
    
    // 2. 计算价格
    priceInfo, err := e.calculateOrderPrice(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 3. 检查库存
    if err := e.checkAndLockStock(ctx, req.Items); err != nil {
        return nil, err
    }
    
    // 4. 创建订单
    order, err := e.createOrderWithTx(ctx, req, priceInfo)
    if err != nil {
        // 回滚库存
        e.rollbackStock(ctx, req.Items)
        return nil, err
    }
    
    // 5. 发送通知
    go e.sendOrderNotification(ctx, order)
    
    return &dto.OrderCreateRes{
        OrderID: order.ID,
    }, nil
}

// 子流程方法
func (e *Entry) validateOrderRequest(ctx *gin.Context, req *dto.OrderCreateReq) error {
    // 验证逻辑
}

func (e *Entry) calculateOrderPrice(ctx *gin.Context, req *dto.OrderCreateReq) (*PriceInfo, error) {
    // 价格计算逻辑
}

func (e *Entry) checkAndLockStock(ctx *gin.Context, items []*OrderItem) error {
    // 库存检查和锁定
}
```

### 辅助方法组织
```go
// 将相关的辅助方法放在一起
// order_helper.go

func (e *Entry) calculateOrderFee(ctx *gin.Context, order *orderDao.Model) (usedFee, totalFee uint64, err error) {
    // 费用计算逻辑
}

func (e *Entry) validateOrderStatus(status uint32) bool {
    // 状态验证逻辑
}

func (e *Entry) canRefundOrder(status uint32) bool {
    // 退款条件判断
}

func (e *Entry) generateOrderNo() string {
    // 订单号生成
}
```

## 最佳实践

### 1. 依赖注入管理
- 所有依赖在 `interface.go` 中统一管理
- 使用单例模式避免重复初始化
- 新增依赖时更新 Entry 结构和 newEntry 方法

### 2. 事务使用原则
- 事务范围尽可能小
- 避免在事务中进行外部调用（如 HTTP、Redis）
- 使用 WithTx 方法支持事务传递

### 3. 错误处理策略
- 关键错误立即返回
- 非关键错误记录日志后继续
- 使用 ecode 包定义业务错误

### 4. 性能优化
- 批量查询代替循环查询（避免 N+1）
- 使用缓存减少数据库访问
- 异步处理非关键操作

### 5. 代码组织
- 复杂业务逻辑拆分为多个方法
- 相关功能放在同一文件
- 使用策略模式处理多分支逻辑

### 6. 日志规范
- 错误日志包含上下文信息
- 关键操作记录 Info 日志
- 调试信息使用 Debug 级别

## 注意事项

1. **Service 层不包含 HTTP 相关内容**：参数从 DTO 获取，不直接操作 Request/Response
2. **避免循环依赖**：Service 之间通过接口依赖，避免直接引用
3. **保持方法纯净**：一个方法只做一件事
4. **事务一致性**：确保事务中的所有操作要么全部成功，要么全部失败
5. **并发安全**：使用锁或事务保证并发操作的数据一致性
6. **缓存更新**：数据变更后及时更新或清理缓存