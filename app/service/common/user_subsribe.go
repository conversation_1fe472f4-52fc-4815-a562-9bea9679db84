package common

import (
	subDao "blind_box/app/dao/user/subscribe"

	"github.com/gin-gonic/gin"
)

// JudgeUserSubscribe 适用于单个活动详情
func (e *Entry) JudgeUserSubscribe(ctx *gin.Context, uid, entityId uint64, entityType uint32) bool {
	model, err := e.SubscribeRepo.FetchByEntity(ctx, uid, entityId, subDao.EntityType(entityType))
	if err != nil {
		return false
	}
	if model.Id != 0 {
		return true
	}

	return false
}

// JudgeUserSubscribeSpu 适用于单个商品详情
func (e *Entry) JudgeUserSubscribeSpu(ctx *gin.Context, uid, spuId uint64) map[uint64]struct{} {
	ret := make(map[uint64]struct{})
	subList, err := e.SubscribeRepo.FindByFilter(ctx, &subDao.Filter{
		UserId:           uid,
		EntityRelationId: spuId,
		EntityType:       uint32(subDao.EntityTypeSkuHasStock),
	})
	if err != nil {
		return ret
	}

	for _, item := range subList {
		if item.Id != 0 {
			ret[item.EntityId] = struct{}{}
		}
	}

	return ret
}
