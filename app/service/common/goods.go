package common

import (
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	shopDao "blind_box/app/dao/resource/shop"
	goodsDto "blind_box/app/dto/goods"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	"context"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/errgroup"
)

func (e *Entry) GetCommonSkuResp(ctx *gin.Context, spuIds, skuIds []uint64) (map[uint64]goodsDto.CrSkuItem, error) {
	var (
		eg             errgroup.Group
		spuList        = spuDao.ModelList{}
		spuMap         = map[uint64]*spuDao.Model{}
		skuList        = skuDao.ModelList{}
		skuUnitList    = skuUnitDao.ModelList{}
		skuUnitListMap = map[uint64]skuUnitDao.ModelList{}
		shopMap        = map[uint64]*shopDao.Model{}
		ret            = map[uint64]goodsDto.CrSkuItem{}
	)
	if len(spuIds) == 0 || len(skuIds) == 0 {
		return ret, nil
	}

	eg.Go(func(context.Context) (err error) {
		if spuList, err = e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{Ids: spuIds}); err != nil {
			return
		}
		spuMap = spuList.GetIdMap()
		return
	})
	eg.Go(func(context.Context) (err error) {
		if shopMap, err = e.ShopRepo.RedisShopMap(ctx); err != nil {
			return
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if skuList, err = e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{Ids: skuIds}); err != nil {
			return
		}
		return
	})
	eg.Go(func(context.Context) (err error) {
		if skuUnitList, err = e.SkuUnitRepo.FindByFilter(ctx, &skuUnitDao.Filter{SkuIds: skuIds}); err != nil {
			return
		}
		skuUnitListMap = skuUnitList.GetSkuUnitListMap()
		return
	})

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("GetCommonSkuResp err")
		return nil, err
	}

	var (
		eg1       errgroup.Group
		batchList = batchDao.ModelList{}
		batchMap  = make(map[uint64]*batchDao.Model)
	)

	eg1.Go(func(context.Context) (err error) {
		if batchList, err = e.BatchRepo.FindByFilter(ctx, &batchDao.Filter{Ids: spuList.GetBatchIds()}); err != nil {
			return
		}
		batchMap = batchList.GetIdMap()
		return
	})

	if err := eg1.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminSpuList eg1 err")
		return nil, err
	}

	for _, sku := range skuList {
		spu, ok := spuMap[sku.SpuId]
		if !ok {
			continue
		}
		batch, ok := batchMap[spu.BatchId]
		if !ok {
			continue
		}
		item := goodsDto.ToSkuItemResp(ctx, sku, spu, batch, shopMap, skuUnitListMap)
		ret[sku.ID] = item
	}

	return ret, nil
}

func (e Entry) CheckSkuExist(ctx *gin.Context, skuId uint64) (model *skuDao.Model, err error) {
	model, err = e.SkuRepo.FetchByID(ctx, skuId)
	if err != nil {
		return nil, err
	}
	if model.ID == 0 {
		return nil, ecode.ParamInvalidErr
	}
	return model, nil
}
