package common

import (
	actDao "blind_box/app/dao/activity"
	areaDao "blind_box/app/dao/admin/area"
	batchDao "blind_box/app/dao/goods/batch"
	skuDao "blind_box/app/dao/goods/sku"
	skuShopDao "blind_box/app/dao/goods/sku_shop"
	skuUnitDao "blind_box/app/dao/goods/sku_unit"
	spuDao "blind_box/app/dao/goods/spu"
	spuTagDao "blind_box/app/dao/goods/spu_tag"
	tagDao "blind_box/app/dao/goods/tag"
	brandDao "blind_box/app/dao/resource/brand"
	shopDao "blind_box/app/dao/resource/shop"
	supplierDao "blind_box/app/dao/resource/supplier"
	vendorDao "blind_box/app/dao/resource/vendor"
	userDao "blind_box/app/dao/user"
	addrDao "blind_box/app/dao/user/addr"
	subDao "blind_box/app/dao/user/subscribe"
	goodsDto "blind_box/app/dto/goods"
	"sync"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	GoodsSrv
	UserSubSrv
}

type GoodsSrv interface {
	GetCommonSkuResp(ctx *gin.Context, spuIds, skuIds []uint64) (map[uint64]goodsDto.CrSkuItem, error)
	CheckSkuExist(ctx *gin.Context, skuId uint64) (model *skuDao.Model, err error)
	CheckActivityExist(ctx *gin.Context, actId uint64) (model *actDao.Model, err error)
}

type UserSubSrv interface {
	JudgeUserSubscribe(ctx *gin.Context, uid, entityId uint64, entityType uint32) bool
	JudgeUserSubscribeSpu(ctx *gin.Context, uid, spuId uint64) map[uint64]struct{}
}

type Entry struct {
	UserRepo      *userDao.Entry
	AddrRepo      *addrDao.Entry
	SpuRepo       *spuDao.Entry
	SkuShopRepo   *skuShopDao.Entry
	SkuRepo       *skuDao.Entry
	SkuUnitRepo   *skuUnitDao.Entry
	SubscribeRepo *subDao.Entry
	BatchRepo     *batchDao.Entry
	TagRepo       *tagDao.Entry
	SpuTagRepo    *spuTagDao.Entry
	BrandRepo     brandDao.Repo
	SupplierRepo  supplierDao.Repo
	VendorRepo    vendorDao.Repo
	AreaRepo      areaDao.Repo
	ShopRepo      shopDao.Repo
	ActRepo       *actDao.Entry
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:      userDao.GetRepo(),
		AddrRepo:      addrDao.GetRepo(),
		SpuRepo:       spuDao.GetRepo(),
		SkuRepo:       skuDao.GetRepo(),
		SkuShopRepo:   skuShopDao.GetRepo(),
		SkuUnitRepo:   skuUnitDao.GetRepo(),
		BatchRepo:     batchDao.GetRepo(),
		TagRepo:       tagDao.GetRepo(),
		BrandRepo:     brandDao.GetRepo(),
		SupplierRepo:  supplierDao.GetRepo(),
		VendorRepo:    vendorDao.GetRepo(),
		AreaRepo:      areaDao.GetRepo(),
		ShopRepo:      shopDao.GetRepo(),
		ActRepo:       actDao.GetRepo(),
		SubscribeRepo: subDao.GetRepo(),
	}
}
