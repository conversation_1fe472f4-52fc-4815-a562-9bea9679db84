package market

import (
	"sync"

	activityDao "blind_box/app/dao/activity"
	bannerDao "blind_box/app/dao/market/banner"
	bannerDto "blind_box/app/dto/market"
	"blind_box/pkg/redis"
	"github.com/gin-gonic/gin"
)

type Server interface {
	Banner

	AdminBanner
}

type Banner interface {
	BannerList(ctx *gin.Context, req *bannerDto.BannerListReq) (res *bannerDto.BannerListResp, err error)
}

type AdminBanner interface {
	AdminBannerCreate(ctx *gin.Context, req *bannerDto.AdminBannerCreateReq) (res *bannerDto.AdminBannerCreateResp, err error)
	AdminBannerUpdate(ctx *gin.Context, req *bannerDto.AdminBannerUpdateReq) (res *bannerDto.AdminBannerUpdateResp, err error)
	AdminBannerList(ctx *gin.Context, req *bannerDto.AdminBannerListReq) (res *bannerDto.AdminBannerListResp, err error)
	AdminBannerDetail(ctx *gin.Context, req *bannerDto.AdminBannerDetailReq) (res *bannerDto.AdminBannerDetailResp, err error)
	AdminBannerDelete(ctx *gin.Context, req *bannerDto.AdminBannerDeleteReq) (res *bannerDto.AdminBannerDeleteResp, err error)
}

// TODO替换
type Entry struct {
	RedisCli *redis.RedisClient

	BannerRepo bannerDao.Repo
	ActiveRepo *activityDao.Entry
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RedisCli:   redis.GetRedisClient(),
		BannerRepo: bannerDao.GetRepo(),
		ActiveRepo: activityDao.GetRepo(),
	}
}
