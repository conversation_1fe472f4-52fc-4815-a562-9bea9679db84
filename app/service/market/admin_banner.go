package market

import (
	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	bannerDao "blind_box/app/dao/market/banner"
	bannerDto "blind_box/app/dto/market"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
)

func (e *Entry) AdminBannerCreate(ctx *gin.Context, req *bannerDto.AdminBannerCreateReq) (res *bannerDto.AdminBannerCreateResp, err error) {
	res = &bannerDto.AdminBannerCreateResp{}

	var (
		newMoel = &bannerDao.Model{
			Title:     req.Title,
			Position:  req.Position,
			Image:     req.Image,
			JumpType:  req.JumpType,
			JumpParam: req.JumpParam,
			JumpUrl:   req.JumpUrl,
			Channel:   req.Channel,
			Status:    uint32(dbs.StatusEnable),
			Sort:      req.Sort,
		}
		activeModel = &actDao.Model{}
	)

	if req.ActiveID > 0 {
		// 校验activeID
		if activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID); err != nil {
			log.Ctx(ctx).WithError(err).Error("error")
			return nil, err
		}
		if activeModel == nil || activeModel.ID == 0 {
			return nil, ecode.ActNotExistErr
		}

		newMoel.ActiveID = req.ActiveID
	}

	id, err := e.BannerRepo.Create(ctx, newMoel)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("error")
		return nil, err
	}
	res.ID = id

	return res, nil
}

func (e *Entry) AdminBannerUpdate(ctx *gin.Context, req *bannerDto.AdminBannerUpdateReq) (res *bannerDto.AdminBannerUpdateResp, err error) {
	res = &bannerDto.AdminBannerUpdateResp{}
	var (
		bannerModel *bannerDao.Model
		activeModel *actDao.Model

		updateMap = make(map[string]interface{})
	)

	if bannerModel, err = e.BannerRepo.FetchByID(ctx, req.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("error")
		return nil, err
	}
	if bannerModel == nil || bannerModel.ID == 0 {
		return nil, ecode.MarketBannerNotExistErr
	}

	if req.ActiveID > 0 {
		// 校验activeID
		if activeModel, err = e.ActiveRepo.FetchByID(ctx, req.ActiveID); err != nil {
			log.Ctx(ctx).WithError(err).Error("error")
			return nil, err
		}
		if activeModel == nil || activeModel.ID == 0 {
			return nil, ecode.ActNotExistErr
		}
		if activeModel.ID != bannerModel.ActiveID {
			updateMap["active_id"] = req.ActiveID
		}
	}

	if bannerModel.Title != req.Title {
		updateMap["title"] = req.Title
	}
	if bannerModel.Position != req.Position {
		updateMap["position"] = req.Position
	}
	if bannerModel.Image != req.Image {
		updateMap["image"] = req.Image
	}
	if bannerModel.JumpType != req.JumpType {
		updateMap["jump_type"] = req.JumpType
	}
	if bannerModel.JumpParam != req.JumpParam {
		updateMap["jump_param"] = req.JumpParam
	}
	if bannerModel.JumpUrl != req.JumpUrl {
		updateMap["jump_url"] = req.JumpUrl
	}
	if bannerModel.Status != req.Status {
		updateMap["status"] = req.Status
	}
	if bannerModel.Sort != req.Sort {
		updateMap["sort"] = req.Sort
	}
	if bannerModel.Channel != req.Channel {
		updateMap["channel"] = req.Channel
	}
	if len(updateMap) == 0 {
		return res, nil
	}

	err = e.BannerRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("error")
		return nil, err
	}

	return res, nil
}

func (e *Entry) AdminBannerList(ctx *gin.Context, req *bannerDto.AdminBannerListReq) (res *bannerDto.AdminBannerListResp, err error) {
	res = &bannerDto.AdminBannerListResp{}
	res.List = make([]bannerDto.AdminBannerListItem, 0)

	var (
		bannerList = make(bannerDao.ModelList, 0)
		count      int64
		activeIDs  = make([]uint64, 0)
		activeMap  = make(map[uint64]*actDao.Model)
	)

	count, bannerList, err = e.BannerRepo.DataPageList(ctx, &bannerDao.Filter{
		Status:   req.Status,
		Position: req.Position,
		JumpType: req.JumpType,
		Channel:  req.Channel,
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("error")
		return nil, err
	}

	if count == 0 {
		return res, nil
	}
	if len(bannerList) == 0 {
		return res, nil
	}

	res.Count = count
	activeIDs = bannerList.GetActiveIDs()

	if len(activeIDs) > 0 {
		activeList, err := e.ActiveRepo.FindByFilter(ctx, &actDao.Filter{
			Ids: activeIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("error")
			return nil, err
		}
		activeMap = activeList.GetIDMap()
	}

	for _, v := range bannerList {
		item := bannerDto.AdminBannerListItem{
			ID:        v.ID,
			Title:     v.Title,
			Position:  v.Position,
			Image:     v.Image,
			JumpType:  v.JumpType,
			JumpParam: v.JumpParam,
			JumpUrl:   v.JumpUrl,
			Status:    v.Status,
			Sort:      v.Sort,
			ActiveID:  v.ActiveID,
			Channel:   v.Channel,
		}
		if v.ActiveID > 0 {
			if active, ok := activeMap[v.ActiveID]; ok {
				item.ActiveTitle = active.Title
			}
		}

		res.List = append(res.List, item)
	}

	return res, nil
}

func (e *Entry) AdminBannerDetail(ctx *gin.Context, req *bannerDto.AdminBannerDetailReq) (res *bannerDto.AdminBannerDetailResp, err error) {
	res = &bannerDto.AdminBannerDetailResp{}

	var (
		bannerModel *bannerDao.Model
		activeModel *actDao.Model
	)

	if bannerModel, err = e.BannerRepo.FetchByID(ctx, req.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("error")
		return nil, err
	}
	if bannerModel == nil || bannerModel.ID == 0 {
		return nil, ecode.MarketBannerNotExistErr
	}

	res.ID = bannerModel.ID
	res.Title = bannerModel.Title
	res.Position = bannerModel.Position
	res.Image = bannerModel.Image
	res.JumpType = bannerModel.JumpType
	res.JumpParam = bannerModel.JumpParam
	res.JumpUrl = bannerModel.JumpUrl
	res.Status = bannerModel.Status
	res.Sort = bannerModel.Sort
	res.ActiveID = bannerModel.ActiveID
	res.Channel = bannerModel.Channel

	if res.ActiveID > 0 {
		if activeModel, err = e.ActiveRepo.FetchByID(ctx, res.ActiveID); err != nil {
			log.Ctx(ctx).WithError(err).Error("error")
			return nil, err
		}
		if activeModel == nil || activeModel.ID == 0 {
			return nil, ecode.ActNotExistErr
		}
		res.ActiveTitle = activeModel.Title
	}

	return res, nil
}

func (e *Entry) AdminBannerDelete(ctx *gin.Context, req *bannerDto.AdminBannerDeleteReq) (res *bannerDto.AdminBannerDeleteResp, err error) {
	res = &bannerDto.AdminBannerDeleteResp{}

	err = e.BannerRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	})
	if err != nil {
		return nil, err
	}

	return res, nil
}
