package market

import (
	"blind_box/app/common/dbs"
	bannerDao "blind_box/app/dao/market/banner"
	bannerDto "blind_box/app/dto/market"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

// BannerList ...
func (e *Entry) BannerList(ctx *gin.Context, req *bannerDto.BannerListReq) (res *bannerDto.BannerListResp, err error) {
	res = &bannerDto.BannerListResp{}
	res.List = make([]bannerDto.BannerListItem, 0)

	var (
		page       = 1
		limit      = 10
		bannerList = make([]*bannerDao.Model, 0)
	)

	_, bannerList, err = e.BannerRepo.DataPageList(ctx, &bannerDao.Filter{
		Status:   uint32(dbs.StatusEnable),
		Position: req.Position,
		Sort: []clause.OrderByColumn{
			{Column: clause.Column{Name: "sort"}, Desc: true},
			{Column: clause.Column{Name: "id"}, Desc: true},
		},
	}, page, limit)
	if err != nil {
		return nil, err
	}
	if len(bannerList) == 0 {
		return res, nil
	}

	for _, v := range bannerList {
		item := bannerDto.BannerListItem{
			ID:        v.ID,
			Title:     v.Title,
			Position:  v.Position,
			Image:     v.Image,
			JumpType:  v.JumpType,
			JumpParam: v.JumpParam,
			JumpUrl:   v.JumpUrl,
			Channel:   v.Channel,
			ActiveID:  v.ActiveID,
		}
		res.List = append(res.List, item)
	}

	return res, nil
}
