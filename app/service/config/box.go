package config

import (
	"blind_box/app/common/dbs"
	configDao "blind_box/app/dao/admin/config"
	configDto "blind_box/app/dto/config"
	"encoding/json"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func (e *Entry) BoxConfig(ctx *gin.Context) (res *configDto.BoxConfigResp, err error) {
	res = &configDto.BoxConfigResp{
		YaoNum: 12,
	}

	var (
		confList = make(configDao.ModelList, 0)
	)

	// FreightFeeRule 快递费规则配置

	type FreightFeeRule struct {
		Enable      bool   `json:"enable"`      // 是否启用
		Threshold   uint64 `json:"threshold"`   // 门槛金额
		Fee         uint64 `json:"fee"`         // 单位费用
		Description string `json:"description"` // 描述
	}

	confList, err = e.ConfigRepo.FindByFilter(ctx, &configDao.Filter{
		ConfigKeys: []configDao.ConfigKey{
			configDao.KeyYaoNum,
			configDao.KeyPostFee,
			configDao.KeyPostCnt,
			configDao.KeyAnnouncement,
			configDao.KeyUnderConstruct,
			configDao.KeyUnderConstructTime,
		},
		Status: uint32(dbs.StatusEnable),
	})
	if err != nil {
		return nil, err
	}
	if len(confList) == 0 {
		return res, nil
	}

	for _, v := range confList {
		switch v.ConfigKey {
		case string(configDao.KeyYaoNum):
			// string to uint32
			yaoNum, parseErr := strconv.ParseUint(v.ConfigValue, 10, 32)
			if parseErr != nil {
				// Use 12 as default value if parsing fails
				res.YaoNum = 12
			} else {
				res.YaoNum = uint32(yaoNum)
			}
		case string(configDao.ConfigKeyFreightFeeRule):
			var rule FreightFeeRule
			parseErr := json.Unmarshal([]byte(v.ConfigValue), &rule)
			if parseErr != nil {
				res.PostFee = 0
			} else if rule.Enable && rule.Threshold > 0 && rule.Fee > 0 {
				if res.TotalFee < rule.Threshold {
					res.PostFee = rule.Fee
				}
			}
		case string(configDao.KeyPostFee):
			postFee, parseErr := strconv.ParseUint(v.ConfigValue, 10, 64)
			if parseErr != nil {
				res.PostFee = 999900
			} else {
				res.PostFee = postFee
			}
		case string(configDao.KeyPostCnt):
			postCnt, parseErr := strconv.ParseUint(v.ConfigValue, 10, 32)
			if parseErr != nil {
				res.PostCnt = 999
			} else {
				res.PostCnt = uint32(postCnt)
			}
		case string(configDao.KeyAnnouncement):
			res.Announcement = v.ConfigValue
		case string(configDao.KeyUnderConstruct):
			underConstruct, parseErr := strconv.ParseUint(v.ConfigValue, 10, 32)
			if parseErr != nil {
				res.UnderConstruct = false
			} else {
				res.UnderConstruct = (underConstruct == 1)
			}
		case string(configDao.KeyUnderConstructTime):
			// 5,6 parse to []uint32
			tmp := strings.Split(v.ConfigValue, ",")
			if len(tmp) > 0 {
				res.UnderConstructTime = make([]uint32, 0, len(tmp))
				for _, v := range tmp {
					if v == "" {
						continue
					}
					underConstructTime, parseErr := strconv.ParseUint(v, 10, 32)
					if parseErr != nil {
						continue
					}
					res.UnderConstructTime = append(res.UnderConstructTime, uint32(underConstructTime))
				}
			} else {
				res.UnderConstructTime = []uint32{}
			}
		}
	}

	return res, nil
}
