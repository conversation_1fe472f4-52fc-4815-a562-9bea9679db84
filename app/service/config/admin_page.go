package config

import (
	"blind_box/app/common/dbs"
	configDao "blind_box/app/dao/admin/config"
	configDto "blind_box/app/dto/config"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AdminPageConfig 管理后台 - 获取页面配置列表
func (e *Entry) AdminPageConfig(ctx *gin.Context, req *configDto.AdminPageConfigReq) (*configDto.AdminPageConfigResp, error) {
	resp := &configDto.AdminPageConfigResp{
		List:  make([]configDto.AdminPageConfigItem, 0),
		Count: 0,
		IsEnd: true,
	}

	// 构建查询条件
	filter := &configDao.Filter{
		ConfigType: 2,
	}

	// 添加筛选条件
	if req.Key != "" {
		filter.ConfigKey = configDao.ConfigKey(req.Key)
	}
	if req.Status != 0 {
		filter.Status = req.Status
	}

	// 分页查询
	total, list, err := e.ConfigRepo.DataPageList(ctx, filter, int32(req.Page), int32(req.Limit))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminPageConfig DataPageList error")
		return resp, ecode.SystemErr
	}

	resp.Count = total
	if total == 0 {
		return resp, nil
	}

	// 转换数据格式
	for _, item := range list {
		resp.List = append(resp.List, configDto.AdminPageConfigItem{
			ID:     uint32(item.ID),
			Key:    item.ConfigKey,
			Data:   item.ConfigValue,
			Status: item.Status,
		})
	}

	// 判断是否结束分页
	resp.IsEnd = int64(req.Page*req.Limit) >= total

	return resp, nil
}

// AdminUpdatePageConfig 管理后台 - 更新页面配置
func (e *Entry) AdminUpdatePageConfig(ctx *gin.Context, req *configDto.AdminUpdatePageConfigReq) (*configDto.AdminUpdatePageConfigResp, error) {
	// 基本参数验证
	if req.Key == "" {
		log.Ctx(ctx).Warn("Empty config key")
		return nil, ecode.ParamErr
	}

	// 构建数据模型
	model := &configDao.Model{
		ConfigType:  2, // 页面配置类型
		ConfigKey:   req.Key,
		ConfigValue: req.Data,
		Status:      req.Status,
		UpdatedAt:   time.Now(),
	}

	// 如果Status为0，默认设置为启用
	if model.Status == 0 {
		model.Status = uint32(dbs.StatusEnable)
	}

	// 创建或更新配置
	if err := e.ConfigRepo.CreateOrUpdate(ctx, model); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUpdatePageConfig CreateOrUpdate error")
		return nil, ecode.SystemErr
	}

	// 清除相关缓存
	if err := e.clearPageConfigCache(ctx, req.Key); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Clear page config cache error")
	}

	return &configDto.AdminUpdatePageConfigResp{
		AdminPageConfigItem: configDto.AdminPageConfigItem{
			ID:     uint32(model.ID),
			Key:    model.ConfigKey,
			Data:   model.ConfigValue,
			Status: model.Status,
		},
	}, nil
}

// PageConfig 微信小程序端 - 获取页面配置
func (e *Entry) PageConfig(ctx *gin.Context, req *configDto.PageConfigReq) (*configDto.PageConfigResp, error) {
	resp := &configDto.PageConfigResp{
		Config: make(map[string]string),
	}

	if req.Key != "" {
		// 解析逗号分隔的key
		keys := strings.Split(req.Key, ",")
		for _, key := range keys {
			// 去除首尾空格
			key = strings.TrimSpace(key)
			if key == "" {
				continue // 跳过空字符串
			}

			// 获取单个配置
			value, err := e.ConfigRepo.RedisGetPageConfig(ctx, key)
			if err != nil {
				log.Ctx(ctx).WithError(err).WithField("key", key).Error("PageConfig RedisGetPageConfig error")
				return resp, ecode.SystemErr
			}
			if value != "" {
				resp.Config[key] = value
			}
		}
	} else {
		// 获取所有页面配置
		configMap, err := e.ConfigRepo.RedisPageConfigMap(ctx)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("PageConfig RedisPageConfigMap error")
			return resp, ecode.SystemErr
		}
		resp.Config = configMap
	}

	return resp, nil
}

// clearPageConfigCache 清除页面配置相关缓存
func (e *Entry) clearPageConfigCache(ctx *gin.Context, key string) error {
	// 清除页面配置列表缓存
	if err := e.ConfigRepo.RedisClearPageConfigList(ctx); err != nil {
		return err
	}

	// 清除单个配置缓存
	cacheKey := redisPkg.GetPageConfigKey(key)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)

	// 清除通用配置列表缓存
	if err := e.ConfigRepo.RedisClearConfigList(ctx); err != nil {
		return err
	}

	return nil
}
