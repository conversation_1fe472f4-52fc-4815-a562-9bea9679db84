package config

import (
	configDao "blind_box/app/dao/admin/config"
	configDto "blind_box/app/dto/config"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
)

type Server interface {
	BoxConfig
	PageConfig
}

type BoxConfig interface {
	BoxConfig(ctx *gin.Context) (res *configDto.BoxConfigResp, err error)
}

type PageConfig interface {
	AdminPageConfig(ctx *gin.Context, req *configDto.AdminPageConfigReq) (*configDto.AdminPageConfigResp, error)
	AdminUpdatePageConfig(ctx *gin.Context, req *configDto.AdminUpdatePageConfigReq) (*configDto.AdminUpdatePageConfigResp, error)
	PageConfig(ctx *gin.Context, req *configDto.PageConfigReq) (*configDto.PageConfigResp, error)
}

// TODO替换
type Entry struct {
	RedisCli *redis.RedisClient

	ConfigRepo configDao.Repo
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RedisCli:   redis.GetRedisClient(),
		ConfigRepo: configDao.GetRepo(),
	}
}
