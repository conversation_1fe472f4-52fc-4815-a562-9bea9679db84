package order

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	orderDto "blind_box/app/dto/order"
	"blind_box/pkg/helper"
	"blind_box/pkg/job"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
)

// CancelTimeoutOrders 处理延迟队列中的超时订单（带分布式锁）
func (e *Entry) CancelTimeoutOrders() error {
	ctx := helper.GenGinCtx()

	// 使用分布式锁防止多实例重复执行
	return job.DefaultDistributedLock.WithLock(ctx, "order_timeout_cancel", 3*time.Minute, func() error {
		// 处理延迟队列中的过期项
		return redis.OrderTimeoutQueue.ProcessExpiredItems(ctx, func(item *redis.DelayQueueItem) error {
			return e.processTimeoutOrder(ctx, item)
		})
	})
}

// processTimeoutOrder 处理单个超时订单
func (e *Entry) processTimeoutOrder(ctx context.Context, item *redis.DelayQueueItem) error {
	// 解析订单ID
	orderIDStr := strings.TrimPrefix(item.ID, "order:")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("processTimeoutOrder parse orderID failed: %s", item.ID)
		return fmt.Errorf("invalid order ID format: %s", item.ID)
	}

	log.WithContext(ctx).Info("processTimeoutOrder start: orderID=%d, expireAt=%d", orderID, item.ExpireAt)

	// 调用后台订单取消接口，自动释放库存
	_, err = e.OrderService.AdminOrderCancel(ctx.(*gin.Context), &orderDto.AdminOrderCancelReq{
		OrderID:   orderID,
		IsReturn:  true, // 释放库存
		Reason:    "System auto cancel: payment timeout",
		AccountID: 0, // 系统账号
	})

	if err != nil {
		log.WithContext(ctx).WithError(err).Error("processTimeoutOrder cancel failed: orderID=%d", orderID)
		return fmt.Errorf("failed to cancel timeout order %d: %w", orderID, err)
	}

	log.WithContext(ctx).Info("processTimeoutOrder completed: orderID=%d", orderID)
	return nil
}
