package order

import (
	"sync"

	orderDao "blind_box/app/dao/order/order"
	orderService "blind_box/app/service/order"
)

type Entry struct {
	OrderRepo    orderDao.Repo
	OrderService *orderService.Entry
}

var (
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetJob() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		OrderRepo:    orderDao.GetRepo(),
		OrderService: orderService.GetService(),
	}
}
