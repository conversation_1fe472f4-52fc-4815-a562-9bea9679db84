package user

import (
	"blind_box/app/dao/user/points"
	"blind_box/app/service/user"
	"blind_box/pkg/log"
	"net/http/httptest"
	"sync"

	"github.com/gin-gonic/gin"
)

var (
	pointsJobInit     sync.Once
	pointsJobInstance *PointsJob
)

type PointsJob struct {
	userService *user.Entry
}

func GetPointsJob() *PointsJob {
	if pointsJobInstance == nil {
		pointsJobInit.Do(func() {
			pointsJobInstance = &PointsJob{
				userService: user.GetService(),
			}
		})
	}
	return pointsJobInstance
}

// createJobContext 创建用于后台任务的gin.Context
func createJobContext(jobName string) *gin.Context {
	// 创建一个测试用的HTTP请求
	req := httptest.NewRequest("POST", "/job/"+jobName, nil)
	w := httptest.NewRecorder()

	// 创建gin.Context
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = req
	ctx.Set("job", jobName)

	return ctx
}

// ProcessExpiredPoints 处理过期积分（优先使用Redis版本）
func (j *PointsJob) ProcessExpiredPoints() error {
	ctx := createJobContext("points_expire")

	// 每次处理最多1000条过期记录
	const batchSize = 1000

	log.Ctx(ctx).Info("PointsJob: Starting to process expired points with Redis optimization")

	// 尝试使用Redis版本处理过期积分
	redisService := user.NewPointsRedisService(points.GetRepo(), j.userService.UserRepo)
	err := redisService.ProcessExpiredPointsWithRedis(ctx, batchSize)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("PointsJob: Redis version failed, falling back to traditional method")

		// 降级到传统方式
		err = j.userService.ProcessExpiredPoints(ctx, batchSize)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("PointsJob: Failed to process expired points")
			return err
		}
	}

	log.Ctx(ctx).Info("PointsJob: Successfully processed expired points")
	return nil
}

// ProcessExpiredPointsRedisOnly 仅使用Redis版本处理过期积分（测试用）
func (j *PointsJob) ProcessExpiredPointsRedisOnly() error {
	ctx := createJobContext("points_expire_redis_only")

	const batchSize = 1000

	log.Ctx(ctx).Info("PointsJob: Processing expired points using Redis only")

	redisService := user.NewPointsRedisService(points.GetRepo(), j.userService.UserRepo)
	err := redisService.ProcessExpiredPointsWithRedis(ctx, batchSize)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PointsJob: Failed to process expired points with Redis")
		return err
	}

	log.Ctx(ctx).Info("PointsJob: Successfully processed expired points with Redis")
	return nil
}

// SyncExpireQueueFromDB 从数据库同步过期队列到Redis（修复任务）
func (j *PointsJob) SyncExpireQueueFromDB() error {
	ctx := createJobContext("points_sync_expire_queue")

	log.Ctx(ctx).Info("PointsJob: Starting to sync expire queue from database to Redis")

	redisService := user.NewPointsRedisService(points.GetRepo(), j.userService.UserRepo)
	err := redisService.SyncExpireQueueFromDB(ctx)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PointsJob: Failed to sync expire queue from DB")
		return err
	}

	log.Ctx(ctx).Info("PointsJob: Successfully synced expire queue from database to Redis")
	return nil
}

// SyncAllUserPointsBalance 同步所有用户的积分余额（维护任务，可选）
func (j *PointsJob) SyncAllUserPointsBalance() error {
	ctx := createJobContext("points_sync")

	log.Ctx(ctx).Info("PointsJob: Starting to sync all user points balance")

	// 这里可以实现批量同步逻辑，暂时先提供单用户同步接口
	// 实际使用时可以根据需要实现批量处理

	log.Ctx(ctx).Info("PointsJob: Sync all user points balance task completed")
	return nil
}
