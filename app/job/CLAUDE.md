# app/job 目录规范文档

本文档记录 `app/job` 目录下定时任务和后台作业的代码规范和最佳实践。

## 目录结构

```
app/job/
├── job.go            # 任务调度核心文件
├── activity/         # 活动相关任务
│   └── subscribe.go  # 订阅通知任务
├── card/            # 卡券相关任务
│   ├── interface.go # 接口定义
│   └── card_expire.go # 卡券过期处理
├── order/           # 订单相关任务
│   ├── interface.go # 接口定义
│   └── timeout_cancel.go # 超时订单取消
├── subscribe/       # 订阅通知任务
│   ├── interface.go # 接口定义
│   └── user_sub.go  # 用户订阅通知
└── user/            # 用户相关任务
    └── points_expire.go # 积分过期处理
```

## 核心架构

### 任务调度器 (job.go)

#### 基本结构
```go
type Job struct {
    cron    *cron.Cron              // cron 调度器
    mu      sync.Mutex              // 互斥锁
    entries map[string]cron.EntryID // 任务注册表
}

// 单例模式
var (
    jobInit     sync.Once
    jobInstance *Job
)

func GetJob() *Job {
    if jobInstance == nil {
        jobInit.Do(func() {
            jobInstance = &Job{
                cron:    cron.New(cron.WithSeconds()), // 支持秒级任务
                entries: make(map[string]cron.EntryID),
            }
        })
    }
    return jobInstance
}
```

#### Cron 表达式规范
```
秒 分 时 日 月 周
0-59 0-59 0-23 1-31 1-12 0-7

常用表达式：
"0 */10 * * * *"    # 每10分钟执行
"0 0 * * * *"       # 每小时执行
"0 0 3 * * *"       # 每天凌晨3点执行
"0 */3 * * * *"     # 每3分钟执行
"0 0 */6 * * *"     # 每6小时执行
```

### 任务管理方法

#### 添加任务
```go
func (j *Job) AddTask(ctx context.Context, name string, schedule string, task func()) error {
    j.mu.Lock()
    defer j.mu.Unlock()
    
    // 防止重复添加
    if _, exists := j.entries[name]; exists {
        return nil
    }
    
    log.WithContext(ctx).WithField("name", name).WithField("schedule", schedule).Info("Job AddTask")
    
    id, err := j.cron.AddFunc(schedule, task)
    if err != nil {
        log.WithContext(ctx).WithError(err).Warn("AddTask err")
        return err
    }
    
    j.entries[name] = id
    return nil
}
```

#### 删除任务
```go
func (j *Job) RemoveTask(ctx context.Context, name string) {
    j.mu.Lock()
    defer j.mu.Unlock()
    
    id, exists := j.entries[name]
    if !exists {
        log.WithContext(ctx).WithField("name", name).Info("Task not exists")
        return
    }
    
    j.cron.Remove(id)
    delete(j.entries, name)
}
```

## 任务实现规范

### interface.go 结构
```go
package [module]

import (
    // DAO 层依赖
    userDao "blind_box/app/dao/user"
    orderDao "blind_box/app/dao/order/order"
    
    // Service 层依赖
    orderService "blind_box/app/service/order"
    
    "sync"
)

// Entry 任务结构体
type Entry struct {
    // 依赖注入
    UserRepo     *userDao.Entry
    OrderRepo    orderDao.Repo
    OrderService *orderService.Entry
}

// 单例模式
var (
    defaultEntry         *Entry
    defaultEntryInitOnce sync.Once
)

// GetJob 获取任务实例
func GetJob() *Entry {
    if defaultEntry == nil {
        defaultEntryInitOnce.Do(func() {
            defaultEntry = newEntry()
        })
    }
    return defaultEntry
}

// newEntry 初始化依赖
func newEntry() *Entry {
    return &Entry{
        UserRepo:     userDao.GetRepo(),
        OrderRepo:    orderDao.GetRepo(),
        OrderService: orderService.GetService(),
    }
}
```

### 任务方法实现
```go
// 任务方法命名规范：功能描述 + 动作
func (e *Entry) UserCardExpire() error {
    ctx := helper.GenGinCtx()  // 生成上下文
    
    // 任务逻辑
    _, err := e.UserCardRepo.UpdateUserCardExpire(ctx)
    if err != nil {
        log.Ctx(ctx).WithError(err).Error("UserCardExpire failed")
        return err
    }
    
    log.Ctx(ctx).Info("UserCardExpire completed")
    return nil
}
```

## 任务注册规范

### InitTasks 方法
```go
func (j *Job) InitTasks(ctx context.Context) {
    // 1. 初始化基础设施
    redis.InitDelayQueues()         // 初始化延迟队列
    job.InitDistributedLock()       // 初始化分布式锁
    job.InitMetrics()               // 初始化监控指标
    
    log.WithContext(ctx).Info("Job system initialized")
    
    // 2. 注册定时任务
    // 卡券过期处理 - 每10分钟执行
    j.AddTask(ctx, "UserCardExpire", "0 */10 * * * *", 
        job.TaskWrapper("UserCardExpire", func() error {
            return card.GetJob().UserCardExpire()
        }))
    
    // 积分过期处理 - 每小时执行
    j.AddTask(ctx, "PointsExpire", "0 0 * * * *",
        job.TaskWrapper("PointsExpire", func() error {
            return userJob.GetPointsJob().ProcessExpiredPoints()
        }))
    
    // 订阅通知 - 每3分钟执行
    j.AddTask(ctx, "UserSubscribeNotify", "0 */3 * * * *",
        job.TaskWrapper("UserSubscribeNotify", func() error {
            return subscribe.GetJob().UserSubscribeNotify()
        }))
    
    // 超时订单取消 - 每5分钟执行（兜底机制）
    j.AddTask(ctx, "OrderTimeoutCancel", "0 */5 * * * *",
        job.TaskWrapper("OrderTimeoutCancel", func() error {
            return orderJob.GetJob().CancelTimeoutOrders()
        }))
    
    // 维护任务 - 每6小时执行
    j.AddTask(ctx, "DelayQueueCleanup", "0 0 */6 * * *",
        job.TaskWrapper("DelayQueueCleanup", func() error {
            return redis.OrderTimeoutQueue.CleanExpiredItems(ctx, time.Now().Add(-24*time.Hour))
        }))
    
    // 监控指标收集 - 每5分钟执行
    j.AddTask(ctx, "MetricsCollector", "0 */5 * * * *",
        job.TaskWrapper("MetricsCollector", func() error {
            return j.collectMetrics(ctx)
        }))
}
```

## 高级特性

### 1. 分布式锁

防止多实例重复执行任务：

```go
func (e *Entry) CancelTimeoutOrders() error {
    ctx := helper.GenGinCtx()
    
    // 使用分布式锁，3分钟超时
    return job.DefaultDistributedLock.WithLock(ctx, "order_timeout_cancel", 3*time.Minute, func() error {
        // 处理延迟队列中的过期项
        return redis.OrderTimeoutQueue.ProcessExpiredItems(ctx, func(item *redis.DelayQueueItem) error {
            return e.processTimeoutOrder(ctx, item)
        })
    })
}
```

### 2. 延迟队列

使用 Redis 实现的延迟队列处理超时任务：

```go
// 添加到延迟队列
redis.OrderTimeoutQueue.Add(ctx, &redis.DelayQueueItem{
    ID:       fmt.Sprintf("order:%d", orderID),
    ExpireAt: time.Now().Add(30 * time.Minute).Unix(),
    Data:     orderData,
})

// 处理过期项
redis.OrderTimeoutQueue.ProcessExpiredItems(ctx, func(item *redis.DelayQueueItem) error {
    // 处理逻辑
    return nil
})
```

### 3. 任务包装器

使用 TaskWrapper 自动记录任务执行指标：

```go
job.TaskWrapper("TaskName", func() error {
    // 自动记录：
    // - 任务开始时间
    // - 任务执行时长
    // - 任务成功/失败状态
    // - 任务执行次数
    return taskFunc()
})
```

### 4. 游标管理

用于增量处理大量数据：

```go
cursorManager := job.NewCursorManager()

// 获取游标
cursor, err := cursorManager.GetCursor(ctx, "PointsExpire")
if err != nil {
    // 首次执行，从头开始
    cursor = &job.JobCursor{
        JobName:        "PointsExpire",
        LastID:         0,
        LastUpdateTime: time.Now().Add(-24 * time.Hour),
    }
}

// 从游标位置继续处理
records, err := repo.FindAfterID(ctx, cursor.LastID, batchSize)

// 更新游标
cursor.LastID = lastProcessedID
cursor.LastUpdateTime = time.Now()
cursorManager.UpdateCursor(ctx, cursor)
```

### 5. 监控指标

收集任务执行的监控数据：

```go
func (j *Job) collectMetrics(ctx context.Context) error {
    if job.Metrics == nil {
        return nil
    }
    
    // 收集延迟队列指标
    if stats, err := redis.OrderTimeoutQueue.GetQueueStats(ctx); err == nil {
        total := int64(stats["total_size"].(int64))
        expired := int64(stats["expired_size"].(int64))
        pending := int64(stats["pending_size"].(int64))
        job.Metrics.UpdateDelayQueueSize("order_timeout", total, expired, pending)
    }
    
    // 收集游标延迟指标
    cursorManager := job.NewCursorManager()
    jobNames := []string{"PointsExpire", "UserCardExpire", "UserSubscribeNotify"}
    for _, jobName := range jobNames {
        if cursor, err := cursorManager.GetCursor(ctx, jobName); err == nil {
            lagMinutes := time.Since(cursor.LastUpdateTime).Minutes()
            job.Metrics.UpdateCursorLag(jobName, lagMinutes)
        }
    }
    
    return nil
}
```

## 任务开发规范

### 1. 上下文生成

任务执行需要生成 gin.Context：

```go
// 方式一：使用 helper
ctx := helper.GenGinCtx()

// 方式二：创建测试上下文
func createJobContext(jobName string) *gin.Context {
    req := httptest.NewRequest("POST", "/job/"+jobName, nil)
    w := httptest.NewRecorder()
    
    ctx, _ := gin.CreateTestContext(w)
    ctx.Request = req
    ctx.Set("job", jobName)
    
    return ctx
}
```

### 2. 错误处理

```go
func (e *Entry) ProcessTask() error {
    ctx := helper.GenGinCtx()
    
    // 记录开始
    log.Ctx(ctx).Info("ProcessTask started")
    
    // 执行任务
    result, err := e.doProcess(ctx)
    if err != nil {
        // 记录错误但不中断后续任务
        log.Ctx(ctx).WithError(err).Error("ProcessTask failed")
        return err
    }
    
    // 记录完成
    log.Ctx(ctx).WithField("result", result).Info("ProcessTask completed")
    return nil
}
```

### 3. 批量处理

处理大量数据时使用批次：

```go
func (e *Entry) BatchProcess() error {
    const batchSize = 1000
    offset := 0
    
    for {
        // 分批查询
        records, err := e.Repo.FindByPage(ctx, offset, batchSize)
        if err != nil {
            return err
        }
        
        if len(records) == 0 {
            break  // 处理完成
        }
        
        // 处理当前批次
        for _, record := range records {
            if err := e.processRecord(ctx, record); err != nil {
                log.Ctx(ctx).WithError(err).Warn("Process record failed: %d", record.ID)
                // 记录错误但继续处理
            }
        }
        
        offset += batchSize
        
        // 进度日志
        log.Ctx(ctx).Info("Processed %d records", offset)
    }
    
    return nil
}
```

### 4. 并发处理

使用 errgroup 管理并发任务：

```go
func (e *Entry) ConcurrentProcess() error {
    var eg errgroup.Group
    
    // 并发执行多个任务
    eg.Go(func() error {
        return e.processType1()
    })
    
    eg.Go(func() error {
        return e.processType2()
    })
    
    eg.Go(func() error {
        return e.processType3()
    })
    
    // 等待所有任务完成
    if err := eg.Wait(); err != nil {
        log.Ctx(ctx).WithError(err).Error("Concurrent process failed")
        return err
    }
    
    return nil
}
```

### 5. 降级处理

优先使用高性能方案，失败时降级：

```go
func (j *PointsJob) ProcessExpiredPoints() error {
    ctx := createJobContext("points_expire")
    const batchSize = 1000
    
    log.Ctx(ctx).Info("Starting with Redis optimization")
    
    // 尝试使用 Redis 版本
    redisService := user.NewPointsRedisService(points.GetRepo(), j.userService.UserRepo)
    err := redisService.ProcessExpiredPointsWithRedis(ctx, batchSize)
    if err != nil {
        log.Ctx(ctx).WithError(err).Warn("Redis version failed, falling back")
        
        // 降级到传统方式
        err = j.userService.ProcessExpiredPoints(ctx, batchSize)
        if err != nil {
            log.Ctx(ctx).WithError(err).Error("Failed to process expired points")
            return err
        }
    }
    
    log.Ctx(ctx).Info("Successfully processed expired points")
    return nil
}
```

## 最佳实践

### 1. 任务频率设计

根据业务特点设置合理的执行频率：

- **高频任务** (1-5分钟): 订单超时、库存释放
- **中频任务** (10-30分钟): 卡券过期、订阅通知
- **低频任务** (1-24小时): 数据清理、统计汇总
- **维护任务** (每天/每周): 日志清理、数据归档

### 2. 幂等性设计

任务应支持重复执行而不产生副作用：

```go
// 检查是否已处理
if isProcessed(record) {
    log.Debug("Already processed: %d", record.ID)
    return nil
}

// 使用事务确保原子性
tx.Transaction(func(tx *gorm.DB) error {
    // 检查并标记
    if err := markAsProcessing(tx, record); err != nil {
        return err
    }
    
    // 执行处理
    if err := process(tx, record); err != nil {
        return err
    }
    
    // 标记完成
    return markAsCompleted(tx, record)
})
```

### 3. 容错设计

```go
// 重试机制
maxRetries := 3
for i := 0; i < maxRetries; i++ {
    if err := processTask(); err != nil {
        log.Warn("Attempt %d failed: %v", i+1, err)
        time.Sleep(time.Second * time.Duration(i+1))
        continue
    }
    break
}

// 部分失败处理
successCount := 0
failCount := 0
for _, item := range items {
    if err := processItem(item); err != nil {
        failCount++
        log.Error("Process item failed: %v", err)
        continue
    }
    successCount++
}
log.Info("Processed: success=%d, failed=%d", successCount, failCount)
```

### 4. 资源控制

```go
// 控制并发数
sem := make(chan struct{}, 10)  // 最多10个并发

for _, item := range items {
    sem <- struct{}{}
    go func(item Item) {
        defer func() { <-sem }()
        processItem(item)
    }(item)
}

// 设置超时
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
defer cancel()

if err := longRunningTask(ctx); err != nil {
    if errors.Is(err, context.DeadlineExceeded) {
        log.Error("Task timeout")
    }
}
```

### 5. 监控告警

```go
// 任务执行前后发送通知
func (e *Entry) MonitoredTask() error {
    startTime := time.Now()
    
    // 发送开始通知
    notifyTaskStart("TaskName")
    
    // 执行任务
    err := e.executeTask()
    
    // 记录执行时间
    duration := time.Since(startTime)
    
    if err != nil {
        // 发送失败告警
        alertTaskFailed("TaskName", err, duration)
        return err
    }
    
    // 发送完成通知
    notifyTaskComplete("TaskName", duration)
    return nil
}
```

## 性能优化

### 1. 批量操作
- 使用批量查询代替循环查询
- 批量更新减少数据库交互
- 合理设置批次大小（通常1000-5000）

### 2. 缓存利用
- 热点数据预加载到缓存
- 使用 Redis Pipeline 批量操作
- 合理设置缓存过期时间

### 3. 异步处理
- 非关键操作使用 goroutine
- 使用消息队列解耦
- 避免阻塞主流程

### 4. 数据库优化
- 使用索引优化查询
- 避免全表扫描
- 使用读写分离

## 故障处理

### 1. 任务积压
- 检查任务执行时长
- 增加任务并发数
- 优化处理逻辑

### 2. 重复执行
- 检查分布式锁是否生效
- 确认任务幂等性
- 检查多实例部署

### 3. 任务失败
- 查看错误日志
- 检查依赖服务
- 手动触发重试

## 扩展指南

### 添加新任务模块

1. **创建目录结构**
```bash
mkdir app/job/newmodule
touch app/job/newmodule/interface.go
touch app/job/newmodule/task.go
```

2. **实现接口文件**
```go
// interface.go
package newmodule

type Entry struct {
    // 依赖注入
}

func GetJob() *Entry {
    // 单例实现
}
```

3. **实现任务逻辑**
```go
// task.go
func (e *Entry) ProcessTask() error {
    // 任务实现
}
```

4. **注册任务**
```go
// 在 job.go 的 InitTasks 中添加
j.AddTask(ctx, "NewTask", "0 */10 * * * *",
    job.TaskWrapper("NewTask", func() error {
        return newmodule.GetJob().ProcessTask()
    }))
```

## 常见问题

### Q: 任务执行时间过长怎么办？
A: 
1. 优化查询和处理逻辑
2. 增加批处理大小
3. 使用并发处理
4. 考虑拆分为多个小任务

### Q: 如何避免任务重复执行？
A: 
1. 使用分布式锁
2. 实现幂等性
3. 记录处理状态
4. 使用唯一标识

### Q: 如何处理任务失败？
A: 
1. 实现重试机制
2. 记录失败日志
3. 发送告警通知
4. 提供手动补偿机制

## 注意事项

1. **分布式环境**: 考虑多实例部署情况
2. **资源消耗**: 避免占用过多系统资源
3. **错误处理**: 不要忽略错误，合理记录和处理
4. **监控告警**: 关键任务需要监控和告警
5. **数据一致性**: 使用事务保证数据一致性
6. **性能影响**: 避免在业务高峰期执行重任务