package card

import (
	"sync"

	"blind_box/app/dao/card/card_user"
)

type Job interface {
}

type Entry struct {
	UserCardRepo card_user.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetJob() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserCardRepo: card_user.GetRepo(),
	}
}
