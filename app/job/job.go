package job

import (
	"blind_box/app/job/card"
	orderJob "blind_box/app/job/order"
	"blind_box/app/job/subscribe"
	userJob "blind_box/app/job/user"
	"blind_box/pkg/job"
	"blind_box/pkg/log"
	"blind_box/pkg/redis"
	"context"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

var (
	jobInit     sync.Once
	jobInstance *Job
)

type Job struct {
	cron    *cron.Cron
	mu      sync.Mutex
	entries map[string]cron.EntryID
}

func GetJob() *Job {
	if jobInstance == nil {
		jobInit.Do(func() {
			jobInstance = &Job{
				cron:    cron.New(cron.WithSeconds()), // 支持秒级任务
				entries: make(map[string]cron.EntryID),
			}
		})
	}
	return jobInstance
}

// AddTask 添加任务
func (j *Job) AddTask(ctx context.Context, name string, schedule string, task func()) error {
	j.mu.<PERSON>()
	defer j.mu.Unlock()

	if _, exists := j.entries[name]; exists {
		return nil
	}
	log.WithContext(ctx).WithField("name", name).WithField("schedule", schedule).Info("Job AddTask")

	id, err := j.cron.AddFunc(schedule, task)
	if err != nil {
		log.WithContext(ctx).WithError(err).Warn("AddTask err")
		return err
	}

	j.entries[name] = id
	return nil
}

// RemoveTask 删除任务
func (j *Job) RemoveTask(ctx context.Context, name string) {
	j.mu.Lock()
	defer j.mu.Unlock()

	id, exists := j.entries[name]
	if !exists {
		log.WithContext(ctx).WithField("name", name).Info("Task not exists")
		return
	}

	j.cron.Remove(id)
	delete(j.entries, name)
}

// Start 启动定时任务
func (j *Job) Start(ctx context.Context) error {
	j.InitTasks(ctx)
	j.cron.Start()
	return nil
}

// Close 停止定时任务
func (j *Job) Close(ctx context.Context) error {
	j.cron.Stop()
	return nil
}

func (j *Job) Name() string {
	return "Job"
}

// collectMetrics 收集各种监控指标
func (j *Job) collectMetrics(ctx context.Context) error {
	if job.Metrics == nil {
		return nil
	}

	// 收集延迟队列指标
	if stats, err := redis.OrderTimeoutQueue.GetQueueStats(ctx); err == nil {
		total := int64(stats["total_size"].(int64))
		expired := int64(stats["expired_size"].(int64))
		pending := int64(stats["pending_size"].(int64))
		job.Metrics.UpdateDelayQueueSize("order_timeout", total, expired, pending)
	}

	// 收集游标延迟指标
	cursorManager := job.NewCursorManager()
	jobNames := []string{"PointsExpire", "UserCardExpire", "UserSubscribeNotify"}
	for _, jobName := range jobNames {
		if cursor, err := cursorManager.GetCursor(ctx, jobName); err == nil {
			lagMinutes := time.Since(cursor.LastUpdateTime).Minutes()
			job.Metrics.UpdateCursorLag(jobName, lagMinutes)
		}
	}

	return nil
}

func (j *Job) InitTasks(ctx context.Context) {
	// 初始化延迟队列和分布式锁
	redis.InitDelayQueues()
	job.InitDistributedLock()
	job.InitMetrics()

	log.WithContext(ctx).Info("Job system initialized: delay queues, distributed locks and metrics ready")

	// 每10分钟处理一次卡券过期（使用增量处理）
	j.AddTask(ctx, "UserCardExpire", "0 */10 * * * *", job.TaskWrapper("UserCardExpire", func() error {
		return card.GetJob().UserCardExpire()
	}))

	// 添加积分过期处理任务 - 每小时执行一次
	j.AddTask(ctx, "PointsExpire", "0 0 * * * *", job.TaskWrapper("PointsExpire", func() error {
		return userJob.GetPointsJob().ProcessExpiredPoints()
	}))

	// 添加积分余额同步任务 - 每天凌晨3点执行一次（可选的维护任务）
	j.AddTask(ctx, "PointsSync", "0 0 3 * * *", job.TaskWrapper("PointsSync", func() error {
		return userJob.GetPointsJob().SyncAllUserPointsBalance()
	}))

	// 每3分钟执行一次订阅通知（降低频率，减少数据库压力）
	j.AddTask(ctx, "UserSubscribeNotify", "0 */3 * * * *", job.TaskWrapper("UserSubscribeNotify", func() error {
		return subscribe.GetJob().UserSubscribeNotify()
	}))

	// 每5分钟检查超时未支付订单并自动取消释放库存（延迟队列已处理大部分，这里作为兜底）
	j.AddTask(ctx, "OrderTimeoutCancel", "0 */5 * * * *", job.TaskWrapper("OrderTimeoutCancel", func() error {
		return orderJob.GetJob().CancelTimeoutOrders()
	}))

	// 每6小时清理过期的延迟队列项（维护任务，降低频率）
	j.AddTask(ctx, "DelayQueueCleanup", "0 0 */6 * * *", job.TaskWrapper("DelayQueueCleanup", func() error {
		return redis.OrderTimeoutQueue.CleanExpiredItems(ctx, time.Now().Add(-24*time.Hour))
	}))

	// 每5分钟更新监控指标
	j.AddTask(ctx, "MetricsCollector", "0 */5 * * * *", job.TaskWrapper("MetricsCollector", func() error {
		return j.collectMetrics(ctx)
	}))
}
