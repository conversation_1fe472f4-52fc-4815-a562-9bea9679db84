package activity

import (
	"blind_box/app/api/wechat/miniprogram"
	actDao "blind_box/app/dao/activity"
	actSubDao "blind_box/app/dao/activity/subscribe"
	userDao "blind_box/app/dao/user"
	"blind_box/config"
	"blind_box/pkg/log"
	"context"
	"errors"
	"fmt"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/silenceper/wechat/v2/miniprogram/subscribe"
	"golang.org/x/sync/errgroup"
)

const (
	ActNotifyTemp = "dWaqG_DkMTm5r8tyf0s7wYLCrexTTMH6TyLksPOe-eg"
	ActNotifyPath = "/pages/detail/index?id=%v"
)

// ActSubscribeNotify 活动订阅通知  TODO discard
func ActSubscribeNotify(ctx *gin.Context) {
	// cli := redisPkg.GetRedisClient()
	// if err := cli.Lock(redisPkg.BoxLock_CronActSubscribeNotify, redisPkg.LockTimeTwoMinute); err != nil {
	// 	log.Ctx(ctx).WithError(err).Error("redisPkg cli.Lock err:%v, lockKey:%v", err.Error(), redisPkg.BoxLock_CronActSubscribeNotify)
	// 	return
	// }
	log.Ctx(ctx).Debug("ActSubscribeNotify cron start")
	subList, err := actSubDao.GetRepo().FindUserSubByFilter(ctx, &actSubDao.UserSubFilter{ByRemind: true})
	actIDS := subList.GetActIDS()
	if err != nil || len(actIDS) == 0 {
		log.Ctx(ctx).Error("ActSubscribeNotify scDao.FindByFilter return err:%v, subList:%+v", err, subList)
		return
	}
	toRemindAct, err := actDao.GetRepo().FindByFilter(ctx, &actDao.Filter{Ids: actIDS, StartTime: carbon.Now().Timestamp()})
	if err != nil || len(toRemindAct) == 0 {
		log.Ctx(ctx).Error("ActSubscribeNotify actDao.FindByFilter return err:%v, scList:%+v, ", err, toRemindAct)
		return
	}

	var (
		eg, _ = errgroup.WithContext(context.Background())
		sub   = miniprogram.GetApi().InitWechatMiniProgram(ctx).GetSubscribe()
	)
	for _, temp := range toRemindAct {
		actInfo := temp
		if utf8.RuneCountInString(actInfo.Title) > 20 {
			actInfo.Title = string([]rune(actInfo.Title)[:17]) + "..." // 活动名称超过20个字，截取20个字
		}
		eg.Go(func() (err error) {
			actSubList, err := actSubDao.GetRepo().FindUserSubByFilter(ctx, &actSubDao.UserSubFilter{ActID: actInfo.ID, ByRemind: true})
			if err != nil {
				log.Ctx(ctx).Error("ActSubscribeNotify FindUserSubByFilter err:%v", err.Error())
				return
			}
			for _, val := range actSubList {
				log.Ctx(ctx).Debug("actSubList:%+v", val)
			}
			uids := actSubList.GetUIDS()
			if len(uids) == 0 {
				log.Ctx(ctx).Info("ActSubscribeNotify uids empty")
				return
			}

			userAuthList, err := userDao.GetRepo().FindAuthByFilter(ctx, &userDao.AuthFilter{
				UserIDs:  uids,
				AuthType: uint32(userDao.AtWxApplet),
			})
			if err != nil {
				log.Ctx(ctx).Error("ActSubscribeNotify userDao FindAuthByFilter err:%v", err.Error())
				return
			}

			errGroup := []error{}
			for _, userAuth := range userAuthList {
				msg := &subscribe.Message{
					ToUser:     userAuth.AuthUid,
					TemplateID: ActNotifyTemp,
					Page:       fmt.Sprintf(ActNotifyPath, actInfo.ID),
					Data: map[string]*subscribe.DataItem{
						"thing1": {
							Value: actInfo.Title,
						},
						"time2": {
							Value: actInfo.StartTime,
						},
						"thing3": {
							Value: "快来看看吧",
						},
					},
					Lang: "zh_CN",
				}
				if config.AppCfg.Env == config.ENT_TEST {
					msg.MiniprogramState = "trial"
				}
				log.Ctx(ctx).Debug("sub.Send msg:%+v", msg)
				if err := sub.Send(msg); err != nil {
					log.Ctx(ctx).Error("ActSubscribeNotify sub.Send err:%v, msg:%+v", err.Error(), msg)
					errGroup = append(errGroup, err)
				}
			}
			if len(errGroup) != 0 {
				log.Ctx(ctx).Error("ActSubscribeNotify wechat notify err:%+v", errGroup)
				return errors.New("ActSubscribeNotify wechat notify err")
			}
			// TODO 根据成功的uid进行更新
			return actSubDao.GetRepo().UpdateSubRemind(ctx, actInfo.ID)
		})
	}
	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).Error("ActSubscribeNotify eg.Wait() err:%v", err.Error())
		return
	}
	log.Ctx(ctx).Debug("ActSubscribeNotify cron end")
}
