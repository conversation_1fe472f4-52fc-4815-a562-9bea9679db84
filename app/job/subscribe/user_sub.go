package subscribe

import (
	"blind_box/app/api/wechat/applet"
	actDao "blind_box/app/dao/activity"
	skuDao "blind_box/app/dao/goods/sku"
	spuDao "blind_box/app/dao/goods/spu"
	userDao "blind_box/app/dao/user"
	subDao "blind_box/app/dao/user/subscribe"
	"blind_box/pkg/helper"
	"blind_box/pkg/log"
	"blind_box/pkg/util/decimalUtil"
	"fmt"
	"unicode/utf8"

	"github.com/golang-module/carbon/v2"
	"github.com/silenceper/wechat/v2/miniprogram/subscribe"
	"golang.org/x/sync/errgroup"
)

// UserSubscribeNotify 用户订阅通知 TODO 保证只有一个节点执行
// 若执行时间超过1分钟，1分钟后再次执行时是否会重复通知？
func (e *Entry) UserSubscribeNotify() error {
	var (
		eg          errgroup.Group
		ctx         = helper.GenGinCtx()
		filter      = &subDao.Filter{Status: uint32(subDao.SubStatusWaitRemind)}
		subTypeList = []subDao.EntityType{
			subDao.EntityTypeBoxSaleStart,
			subDao.EntityTypeSkuHasStock,
			subDao.EntityTypeActStart,
		}
	)
	log.Ctx(ctx).Debug("UserSubscribeNotify cron start")

	for _, subType := range subTypeList {
		eg.Go(func() (err error) {
			filter.EntityType = uint32(subType)
			subList, err := e.SubRepo.FindByFilter(ctx, filter)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify e.SubRepo.FindByFilter err")
				return
			}
			if len(subList) == 0 {
				log.Ctx(ctx).WithField("subType", subType).Info("UserSubscribeNotify subList empty")
				return
			}
			userIds, entityIds, spuIds := subList.GetUserEntityRelationIds()

			var (
				eg1      errgroup.Group
				authList = userDao.UserAuthList{}
				authMap  = map[uint64]*userDao.UserAuth{}
				actList  = actDao.ModelList{}
				actMap   = map[uint64]*actDao.Model{}
				skuList  = skuDao.ModelList{}
				skuMap   = map[uint64]*skuDao.Model{}
				spuList  = spuDao.ModelList{}
				spuMap   = map[uint64]*spuDao.Model{}
				nowTime  = carbon.Now().Timestamp() - 60*5 // 提前5分钟
			)
			eg1.Go(func() (err error) {
				if authList, err = e.UserRepo.FindAuthByFilter(ctx, &userDao.AuthFilter{UserIDs: userIds, AuthType: uint32(userDao.AtWxApplet)}); err != nil {
					return
				}
				authMap = authList.GetIdMap()
				return
			})
			switch subType {
			case subDao.EntityTypeBoxSaleStart, subDao.EntityTypeActStart:
				eg1.Go(func() (err error) {
					if actList, err = e.ActRepo.FindByFilter(ctx, &actDao.Filter{Ids: entityIds, StartTime: nowTime}); err != nil {
						return
					}
					actMap = actList.GetIDMap()
					return
				})
			case subDao.EntityTypeSkuHasStock:
				eg1.Go(func() (err error) {
					if skuList, err = e.SkuRepo.FindByFilter(ctx, &skuDao.Filter{Ids: entityIds}); err != nil {
						return
					}
					skuMap = skuList.GetIDMap()
					return
				})
				eg1.Go(func() (err error) {
					if spuList, err = e.SpuRepo.FindByFilter(ctx, &spuDao.Filter{Ids: spuIds}); err != nil {
						return
					}
					spuMap = spuList.GetIdMap()
					return
				})
			}
			if err := eg1.Wait(); err != nil {
				log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify eg1.Wait() err")
				return err
			}

			var (
				invalidIds, remindIds = []uint64{}, []uint64{}
			)
			for _, sub := range subList {
				userAuth, ok := authMap[sub.UserId]
				if !ok {
					invalidIds = append(invalidIds, sub.Id)
					continue
				}

				subTemplate, err := applet.GetApi().GetSubTemplate(ctx, sub.EntityType, userAuth.AuthUid)
				if err != nil {
					log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify applet.GetApi().GetSubTemplate err")
					invalidIds = append(invalidIds, sub.Id)
					continue
				}

				switch sub.EntityType {
				case uint32(subDao.EntityTypeBoxSaleStart), uint32(subDao.EntityTypeActStart):
					actInfo, ok := actMap[sub.EntityId]

					if !ok {
						invalidIds = append(invalidIds, sub.Id)
						continue
					}
					if actInfo.EndTime != 0 && actInfo.EndTime < carbon.Now().Timestamp() {
						invalidIds = append(invalidIds, sub.Id)
						continue
					}
					if utf8.RuneCountInString(actInfo.Title) > 20 {
						actInfo.Title = string([]rune(actInfo.Title)[:17]) + "..." // 活动名称超过20个字，截取20个字
					}
					subTemplate.Page = fmt.Sprintf(subTemplate.Page, actInfo.ID)
					subTemplate.Data = map[string]*subscribe.DataItem{
						"thing1": {
							Value: actInfo.Title,
						},
						"time2": {
							Value: actInfo.GetStartTime(),
						},
						"thing3": {
							Value: "您订阅的活动即将开始，快来看看吧~~",
						},
					}
					if err := applet.GetApi().SendMsg(ctx, subTemplate); err != nil {
						continue
					}
					remindIds = append(remindIds, sub.Id)
				case uint32(subDao.EntityTypeSkuHasStock):
					sku, ok := skuMap[sub.EntityId]
					spu, okk := spuMap[sub.EntityRelationId]
					if !ok || !okk {
						invalidIds = append(invalidIds, sub.Id)
						continue
					}
					if sku.GetUsableNum() > 0 {
						if utf8.RuneCountInString(spu.Title) > 20 {
							spu.Title = string([]rune(spu.Title)[:17]) + "..." // 商品名称超过20个字，截取20个字
						}

						subTemplate.Page = fmt.Sprintf(subTemplate.Page, sku.SpuId, sku.ID)
						subTemplate.Data = map[string]*subscribe.DataItem{
							"thing1": {
								Value: spu.Title,
							},
							"amount2": {
								Value: fmt.Sprintf("%v元", decimalUtil.AmountToFloat(sku.SellPrice)),
							},
							"thing3": {
								Value: "您预约的商品已到货，快来看看吧~~",
							},
						}
						if err := applet.GetApi().SendMsg(ctx, subTemplate); err != nil {
							continue
						}
						remindIds = append(remindIds, sub.Id)
					}
				}
			}
			if len(remindIds) != 0 {
				if err := e.SubRepo.UpdateMapByIds(ctx, remindIds, map[string]interface{}{"status": subDao.SubStatusReminded}); err != nil {
					log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify e.SubRepo.UpdateSubRemind err")
					return err
				}
			}
			if len(invalidIds) != 0 {
				if err := e.SubRepo.UpdateMapByIds(ctx, invalidIds, map[string]interface{}{"status": subDao.SubStatusInvalid}); err != nil {
					log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify e.SubRepo.UpdateSubStatus err")
					return err
				}
			}

			return
		})
	}

	if err := eg.Wait(); err != nil {
		log.Ctx(ctx).WithError(err).Error("UserSubscribeNotify eg.Wait() err")
		return err
	}

	log.Ctx(ctx).Debug("UserSubscribeNotify cron end")
	return nil
}
