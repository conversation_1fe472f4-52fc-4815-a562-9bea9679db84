package subscribe

import (
	actDao "blind_box/app/dao/activity"
	skuDao "blind_box/app/dao/goods/sku"
	spuDao "blind_box/app/dao/goods/spu"
	userDao "blind_box/app/dao/user"
	subDao "blind_box/app/dao/user/subscribe"
	"sync"
)

type Job interface {
}

type Entry struct {
	SubRepo  *subDao.Entry
	UserRepo *userDao.Entry
	SkuRepo  *skuDao.Entry
	SpuRepo  *spuDao.Entry
	ActRepo  *actDao.Entry
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetJob() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		SubRepo:  subDao.GetRepo(),
		UserRepo: userDao.GetRepo(),
		SkuRepo:  skuDao.GetRepo(),
		SpuRepo:  spuDao.GetRepo(),
		ActRepo:  actDao.GetRepo(),
	}
}
