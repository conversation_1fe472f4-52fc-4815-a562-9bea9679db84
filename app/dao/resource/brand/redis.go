package brand

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableBrandList .
func (e *Entry) RedisEnableBrandList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisBrandList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisBrandList .
func (e *Entry) RedisBrandList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.BrandListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadBrandList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisBrandMap .
func (e *Entry) RedisBrandMap(ctx *gin.Context) (map[uint64]*Model, error) {
	companyList, err := e.RedisBrandList(ctx)
	if err != nil {
		return nil, err
	}
	return companyList.GetIDMap(), nil
}

// RedisReloadBrandList redis重载数据列表
func (e *Entry) RedisReloadBrandList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.BrandListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearBrandList .
func (e *Entry) RedisClearBrandList(ctx *gin.Context) error {
	cacheKey := redisPkg.BrandListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
