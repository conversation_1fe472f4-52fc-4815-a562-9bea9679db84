package brand

import (
	"sync"

	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	BrandRepo
	BrandRedis
}

type BrandRepo interface {
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	BatchCreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Model) error
	FetchByID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type BrandRedis interface {
	RedisEnableBrandList(*gin.Context) (ModelList, error)
	RedisBrandList(*gin.Context) (ModelList, error)
	RedisBrandMap(*gin.Context) (map[uint64]*Model, error)
	RedisReloadBrandList(*gin.Context) (ModelList, error)
	RedisClearBrandList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
