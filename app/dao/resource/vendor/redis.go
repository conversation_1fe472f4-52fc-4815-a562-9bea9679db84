package vendor

import (
	"encoding/json"

	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableVendorList .
func (e *Entry) RedisEnableVendorList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisVendorList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisVendorList .
func (e *Entry) RedisVendorList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.VendorListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadVendorList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisVendorMap .
func (e *Entry) RedisVendorMap(ctx *gin.Context) (map[uint64]*Model, error) {
	companyList, err := e.RedisVendorList(ctx)
	if err != nil {
		return nil, err
	}
	return companyList.GetIDMap(), nil
}

// RedisReloadVendorList redis重载数据列表
func (e *Entry) RedisReloadVendorList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.VendorListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearVendorList .
func (e *Entry) RedisClearVendorList(ctx *gin.Context) error {
	cacheKey := redisPkg.VendorListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
