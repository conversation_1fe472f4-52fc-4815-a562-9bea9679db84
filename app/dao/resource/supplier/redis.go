package supplier

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableSupplierList .
func (e *Entry) RedisEnableSupplierList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisSupplierList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisSupplierList .
func (e *Entry) RedisSupplierList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.SupplierListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadSupplierList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisSupplierMap .
func (e *Entry) RedisSupplierMap(ctx *gin.Context) (map[uint64]*Model, error) {
	companyList, err := e.RedisSupplierList(ctx)
	if err != nil {
		return nil, err
	}
	return companyList.GetIDMap(), nil
}

// RedisReloadSupplierList redis重载数据列表
func (e *Entry) RedisReloadSupplierList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.SupplierListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearSupplierList .
func (e *Entry) RedisClearSupplierList(ctx *gin.Context) error {
	cacheKey := redisPkg.SupplierListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
