package supplier

import (
	"time"

	"blind_box/app/common/dbs"
)

type Model struct {
	ID        uint64     `gorm:"primarykey" json:"id"`
	Name      string     `json:"name,omitempty"`
	Cover     dbs.CdnImg `json:"cover,omitempty"`
	Sort      uint32     `json:"sort,omitempty"`
	Status    uint32     `json:"status,omitempty"`
	IsDeleted uint32     `json:"is_deleted,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "resource_supplier"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID       uint64
	NotID    uint64
	IDS      []uint64
	Name     string
	NameList []string
	Status   uint32
	Sort     dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetNameMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Name]; !ok {
			retMap[val.Name] = val
		}
	}
	return retMap
}
