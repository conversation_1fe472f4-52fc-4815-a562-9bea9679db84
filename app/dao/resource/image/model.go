package image

import (
	"blind_box/app/common/dbs"

	"gorm.io/gorm/clause"
)

const (
	MappingActiveDetailImages = 1
	MappingSpuImages          = 2
)

type Model struct {
	dbs.ModelWithDel

	URL dbs.CdnImg `json:"url,omitempty"` // 图片地址

	MappingType uint64 `json:"mapping_type,omitempty"` // 图片类型

	MappingID uint64 `json:"mapping_id,omitempty"` // 关联ID
}

func (Model) TableName() string {
	return "resource_image"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	MappingType uint32 // 图片类型

	MappingID  uint64 // 关联ID
	MappingIDs []uint64

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetMapppingIDKeyMap() map[uint64]ModelList {
	ret := make(map[uint64]ModelList, 0)
	for _, val := range ml {
		if _, ok := ret[val.MappingID]; !ok {
			ret[val.MappingID] = make(ModelList, 0)
		}
		ret[val.MappingID] = append(ret[val.MappingID], val)
	}
	return ret
}
