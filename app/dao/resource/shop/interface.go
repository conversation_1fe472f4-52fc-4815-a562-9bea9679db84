package shop

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	ShopRepo
	ShopRedis
}

type ShopRepo interface {
	BatchCreate(ctx *gin.Context, m ModelList) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type ShopRedis interface {
	RedisEnableShopList(*gin.Context) (ModelList, error)
	RedisShopList(*gin.Context) (ModelList, error)
	RedisShopMap(*gin.Context) (map[uint64]*Model, error)
	RedisShopNameMap(*gin.Context) (map[string]*Model, error)
	RedisReloadShopList(*gin.Context) (ModelList, error)
	RedisClearShopList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
