package shop

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableShopList .
func (e *Entry) RedisEnableShopList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisShopList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisShopList .
func (e *Entry) RedisShopList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.ShopListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadShopList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisShopMap .
func (e *Entry) RedisShopMap(ctx *gin.Context) (map[uint64]*Model, error) {
	ShopList, err := e.RedisShopList(ctx)
	if err != nil {
		return nil, err
	}
	return ShopList.GetIDMap(), nil
}

// RedisShopNameMap .
func (e *Entry) RedisShopNameMap(ctx *gin.Context) (map[string]*Model, error) {
	ShopList, err := e.RedisShopList(ctx)
	if err != nil {
		return nil, err
	}
	return ShopList.GetNameMap(), nil
}

// RedisReloadShopList redis重载数据列表
func (e *Entry) RedisReloadShopList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.ShopListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearShopList .
func (e *Entry) RedisClearShopList(ctx *gin.Context) error {
	cacheKey := redisPkg.ShopListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
