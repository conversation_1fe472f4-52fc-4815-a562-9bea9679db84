package merchant

import (
	"encoding/json"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Model 商户信息模型
type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	IsDeleted uint32    `gorm:"type:tinyint(1);default:0;comment:软删除" json:"is_deleted"` // 软删除

	AppID             string `gorm:"type:varchar(64);uniqueIndex;not null;comment:应用ID"`  // 应用ID
	AppSecret         string `gorm:"type:varchar(128);not null;comment:应用密钥"`             // 应用密钥
	MerchantName      string `gorm:"type:varchar(128);not null;comment:商户名称"`             // 商户名称
	MerchantCode      string `gorm:"type:varchar(64);uniqueIndex;not null;comment:商户编码"`  // 商户编码
	ContactName       string `gorm:"type:varchar(64);comment:联系人姓名"`                      // 联系人姓名
	ContactMobile     string `gorm:"type:varchar(20);comment:联系人手机"`                      // 联系人手机
	ContactEmail      string `gorm:"type:varchar(128);comment:联系人邮箱"`                     // 联系人邮箱
	Status            uint32 `gorm:"type:tinyint(1);default:1;comment:状态：1-启用，2-禁用"`      // 状态
	CallbackUrl       string `gorm:"type:varchar(512);comment:默认回调地址"`                    // 默认回调地址
	IpWhitelist       string `gorm:"type:text;comment:IP白名单，多个用逗号分隔"`                     // IP白名单
	DailyLimit        uint64 `gorm:"type:bigint(20);default:100000;comment:每日调用限制"`       // 每日调用限制
	QpsLimit          uint32 `gorm:"type:int(11);default:100;comment:QPS限制"`              // QPS限制
	SingleAmountLimit uint64 `gorm:"type:bigint(20);default:10000000;comment:单笔金额限制（分）"`  // 单笔金额限制
	DailyAmountLimit  uint64 `gorm:"type:bigint(20);default:100000000;comment:每日金额限制（分）"` // 每日金额限制
	AllowedSkus       string `gorm:"type:text;comment:允许的SKU列表，JSON格式"`                   // 允许的SKU列表
	Remark            string `gorm:"type:varchar(512);comment:备注"`                        // 备注
}

// TableName 表名
func (Model) TableName() string {
	return "openapi_merchant"
}

// Filter 查询条件
type Filter struct {
	ID           uint64
	AppID        string
	MerchantCode string
	Status       uint32
	MerchantName string // 模糊查询
}

// ModelList 商户列表
type ModelList []*Model

// MerchantStatus 商户状态
const (
	MerchantStatusEnable  uint32 = 1 // 启用
	MerchantStatusDisable uint32 = 2 // 禁用
)

// BeforeCreate 创建前钩子
func (m *Model) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里生成默认的AppID和AppSecret
	return nil
}

// IsEnabled 是否启用
func (m *Model) IsEnabled() bool {
	return m.Status == MerchantStatusEnable
}

// GetIPWhitelist 获取IP白名单列表
func (m *Model) GetIPWhitelist() []string {
	if m.IpWhitelist == "" {
		return []string{}
	}

	// 解析逗号分隔的IP列表
	ips := strings.Split(m.IpWhitelist, ",")
	result := make([]string, 0, len(ips))
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			result = append(result, ip)
		}
	}

	return result
}

// GetAllowedSkus 获取允许的SKU列表
func (m *Model) GetAllowedSkus() []uint64 {
	if m.AllowedSkus == "" {
		return []uint64{}
	}

	// 解析JSON格式的SKU列表
	var skuIds []uint64
	if err := json.Unmarshal([]byte(m.AllowedSkus), &skuIds); err != nil {
		// 如果解析失败，返回空列表
		return []uint64{}
	}

	return skuIds
}

// SetAllowedSkus 设置SKU白名单
func (m *Model) SetAllowedSkus(skuIds []uint64) error {
	if len(skuIds) == 0 {
		m.AllowedSkus = ""
		return nil
	}

	data, err := json.Marshal(skuIds)
	if err != nil {
		return err
	}

	m.AllowedSkus = string(data)
	return nil
}

// IsSkuAllowed 检查SKU是否在白名单中
func (m *Model) IsSkuAllowed(skuId uint64) bool {
	// 如果AllowedSkus为空，表示允许所有SKU
	if m.AllowedSkus == "" {
		return true
	}

	allowedSkus := m.GetAllowedSkus()
	for _, id := range allowedSkus {
		if id == skuId {
			return true
		}
	}

	return false
}
