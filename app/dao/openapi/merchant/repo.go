package merchant

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// FetchByID 根据ID查询
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, false).WithContext(ctx).Where("id = ? AND is_deleted = ?", id, 0).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FetchByAppID 根据AppID查询
func (e *Entry) FetchByAppID(ctx *gin.Context, appID string) (*Model, error) {
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, false).WithContext(ctx).Where("app_id = ? AND is_deleted = ?", appID, 0).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FetchByMerchantCode 根据商户编码查询
func (e *Entry) FetchByMerchantCode(ctx *gin.Context, code string) (*Model, error) {
	var model Model
	err := e.MysqlEngine.UseWithGinCtx(ctx, false).WithContext(ctx).Where("merchant_code = ? AND is_deleted = ?", code, 0).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByFilter 根据条件查询列表
func (e *Entry) FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).WithContext(ctx).Where("is_deleted = ?", 0)

	if filter.ID > 0 {
		query = query.Where("id = ?", filter.ID)
	}
	if filter.AppID != "" {
		query = query.Where("app_id = ?", filter.AppID)
	}
	if filter.MerchantCode != "" {
		query = query.Where("merchant_code = ?", filter.MerchantCode)
	}
	if filter.Status > 0 {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.MerchantName != "" {
		query = query.Where("merchant_name LIKE ?", fmt.Sprintf("%%%s%%", filter.MerchantName))
	}

	var list ModelList
	err := query.Order("id DESC").Find(&list).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

// Create 创建商户
func (e *Entry) Create(ctx *gin.Context, model *Model) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).WithContext(ctx).Create(model).Error
}

// CreateWithTx 在事务中创建
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error {
	return tx.WithContext(ctx).Create(model).Error
}

// Update 更新商户信息
func (e *Entry) Update(ctx *gin.Context, model *Model) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).WithContext(ctx).Where("id = ?", model.ID).Updates(model).Error
}

// UpdateWithTx 在事务中更新
func (e *Entry) UpdateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error {
	return tx.WithContext(ctx).Where("id = ?", model.ID).Updates(model).Error
}

// UpdateStatus 更新状态
func (e *Entry) UpdateStatus(ctx *gin.Context, id uint64, status uint32) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).WithContext(ctx).Model(&Model{}).Where("id = ?", id).Update("status", status).Error
}

// Delete 软删除
func (e *Entry) Delete(ctx *gin.Context, id uint64) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).WithContext(ctx).Model(&Model{}).Where("id = ?", id).Update("is_deleted", 1).Error
}

// CountByFilter 根据条件统计数量
func (e *Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).WithContext(ctx).Model(&Model{}).Where("is_deleted = ?", 0)

	if filter.Status > 0 {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.MerchantName != "" {
		query = query.Where("merchant_name LIKE ?", fmt.Sprintf("%%%s%%", filter.MerchantName))
	}

	var count int64
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}

// CheckIPWhitelist 检查IP是否在白名单中
func (e *Entry) CheckIPWhitelist(ctx *gin.Context, appID, ip string) (bool, error) {
	merchant, err := e.FetchByAppID(ctx, appID)
	if err != nil {
		return false, err
	}
	if merchant == nil {
		return false, fmt.Errorf("merchant not found")
	}

	// 如果没有设置白名单，默认允许所有IP
	if merchant.IpWhitelist == "" {
		return true, nil
	}

	// TODO: 实现IP白名单检查逻辑
	// 这里需要解析IP白名单并进行匹配

	return true, nil
}
