package merchant

import (
	"sync"

	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	Merchant
}

type Merchant interface {
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)

	FetchByAppID(ctx *gin.Context, appID string) (*Model, error)

	FetchByMerchantCode(ctx *gin.Context, code string) (*Model, error)

	FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)

	Create(ctx *gin.Context, model *Model) error

	CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error

	Update(ctx *gin.Context, model *Model) error

	UpdateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error

	UpdateStatus(ctx *gin.Context, id uint64, status uint32) error

	Delete(ctx *gin.Context, id uint64) error

	CountByFilter(ctx *gin.Context, filter *Filter) (int64, error)

	CheckIPWhitelist(ctx *gin.Context, appID, ip string) (bool, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
