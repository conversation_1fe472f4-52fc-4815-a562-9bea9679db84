package order_detail

import (
	"fmt"

	"blind_box/app/common/dbs"
	actDao "blind_box/app/dao/activity"
	"blind_box/app/dao/box/box_goods"
	"blind_box/app/dao/box/box_record"
	orderDao "blind_box/app/dao/order/order"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

var _model = &Model{}

const (
	TradeStatus_1 = 1
	TradeStatus_2 = 2
	TradeStatus_3 = 3
)

const (
	DeliveryStatus_1 = 1
	DeliveryStatus_2 = 2
	DeliveryStatus_3 = 3
	DeliveryStatus_4 = 4
	DeliveryStatus_5 = 5
)

type Model struct {
	//model.BoxTradeDetailInfo

	dbs.ModelWithDel

	OrderID        uint64 // 订单ID
	ActiveID       uint64 // 活动ID
	BoxID          uint64 // 盒子ID
	BoxNo          string // 盒子编号
	BoxSlot        string
	GoodsID        uint64 // 赏品ID
	GoodsLevel     string // 赏品级别
	UserID         uint64 // 购买者ID
	SpuID          uint64
	SkuID          uint64
	BatchID        uint64 // 批次ID
	GoodsName      string // 赏品名称
	ActiveTitle    string // 活动标题
	TradeStatus    uint32 // 交易状态 1-已支付
	DeliveryStatus uint32 // 发货状态 0未发货  1申请中  2出库中 3已发货 4取消发货
	Remark         string // 备注
	Quantity       uint32 // 购买数量，盲盒模式为1，直接购买模式为实际数量

	ActiveType uint32
}

func (Model) TableName() string {
	return "box_trade_detail_info"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	UserID   uint64
	ActiveID uint64

	DeliveryType uint32
	OnlyDelivery uint32

	OrderID  uint64
	OrderIDs []uint64

	Status uint32

	Sort []clause.OrderByColumn

	Limit int
}

func (ml ModelList) GetIDs() []uint64 {
	ids := make([]uint64, 0, len(ml))
	for _, m := range ml {
		ids = append(ids, m.ID)
	}
	ids = lo.Uniq(ids)
	return ids
}

func (ml ModelList) GetOrderMap() map[uint64]ModelList {
	idMap := make(map[uint64]ModelList)
	for _, m := range ml {
		if _, ok := idMap[m.OrderID]; !ok {
			idMap[m.OrderID] = make(ModelList, 0)
		}
		idMap[m.OrderID] = append(idMap[m.OrderID], m)
	}
	return idMap
}

func (ml ModelList) GetGoodsIDs() []uint64 {
	goodsIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		goodsIDs = append(goodsIDs, m.GoodsID)
	}
	goodsIDs = lo.Uniq(goodsIDs)
	return goodsIDs
}

func (ml ModelList) GetSkuIDs() []uint64 {
	skuIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.SkuID > 0 {
			skuIDs = append(skuIDs, m.SkuID)
		}
	}
	skuIDs = lo.Uniq(skuIDs)
	return skuIDs
}

func (ml ModelList) GetSpuIDs() []uint64 {
	spuIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.SpuID > 0 {
			spuIDs = append(spuIDs, m.SpuID)
		}
	}
	spuIDs = lo.Uniq(spuIDs)
	return spuIDs
}

func (ml ModelList) GetBatchIDs() []uint64 {
	batchIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.BatchID > 0 {
			batchIDs = append(batchIDs, m.BatchID)
		}
	}
	batchIDs = lo.Uniq(batchIDs)
	return batchIDs
}

func (ml ModelList) GetActiveIDs() []uint64 {
	activeIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		activeIDs = append(activeIDs, m.ActiveID)
	}
	activeIDs = lo.Uniq(activeIDs)
	return activeIDs
}

func NewBoxTradeDetailInfo(
	orderInfo *orderDao.Model,
	box *box_record.Model,
	goodsInfo *box_goods.Model,
	active *actDao.Model,
	remark string,
	slot uint32,
) *Model {
	return &Model{
		OrderID:        orderInfo.ID,
		ActiveID:       active.ID,
		BoxID:          box.ID,
		BoxNo:          box.BoxNo,
		GoodsID:        goodsInfo.ID,
		GoodsLevel:     goodsInfo.LevelName,
		SpuID:          goodsInfo.SpuID,
		SkuID:          goodsInfo.SkuID,
		UserID:         orderInfo.UserID,
		GoodsName:      goodsInfo.GoodsName,
		ActiveTitle:    active.Title,
		TradeStatus:    TradeStatus_1, // TODO: status
		DeliveryStatus: DeliveryStatus_1,
		Remark:         remark,
		ActiveType:     active.ActType,
		BoxSlot:        fmt.Sprintf("%d", slot),
		Quantity:       1, // 盲盒模式默认数量为1
	}
}
