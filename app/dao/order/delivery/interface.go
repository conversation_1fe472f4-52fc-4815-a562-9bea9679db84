package delivery

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Interface interface {
	Create(ctx *gin.Context, model *Model) error
	CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error
	Update(ctx *gin.Context, model *Model) error
	UpdateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error
	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error
	Delete(ctx *gin.Context, id uint64) error
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	FetchByOrderID(ctx *gin.Context, orderID uint64) (*Model, error)
	DataPageList(ctx *gin.Context, filter *Filter, page, limit int) (total int64, list ModelList, err error)
	FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)
	CountByFilter(ctx *gin.Context, filter *Filter) (int64, error)
}
