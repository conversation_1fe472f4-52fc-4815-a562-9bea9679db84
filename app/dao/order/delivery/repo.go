package delivery

import (
	"fmt"
	"sync"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	Interface
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}

// Create 创建发货记录
func (e *Entry) Create(ctx *gin.Context, model *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

// CreateWithTx 使用事务创建发货记录
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(model).Error; err != nil {
		return err
	}
	return nil
}

// Update 更新发货记录
func (e *Entry) Update(ctx *gin.Context, model *Model) error {
	return e.UpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

// UpdateWithTx 使用事务更新发货记录
func (e *Entry) UpdateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error {
	if err := tx.Model(model).Where("id = ?", model.ID).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID 根据ID更新字段
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx 使用事务根据ID更新字段
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// Delete 软删除发货记录
func (e *Entry) Delete(ctx *gin.Context, id uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).
		Where("id = ?", id).Update(string(dbs.SoftDelField), 1).Error; err != nil {
		return err
	}
	return nil
}

// FetchByID 根据ID获取发货记录
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ? AND is_deleted = 0", id).First(ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}

// FetchByOrderID 根据订单ID获取发货记录
func (e *Entry) FetchByOrderID(ctx *gin.Context, orderID uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("order_id = ? AND is_deleted = 0", orderID).First(ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}

// buildQuery 构建查询条件
func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		f._eq_id(),
		f._in_ids(),
		f._eq_order_id(),
		f._in_order_ids(),
		f._eq_delivery_status(),
		f._like_tracking_number(),
		f._eq_express_code(),
		f._between_shipped_at(),
	)

	return query
}

// 过滤条件方法
func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID > 0 {
			return db.Where("id = ?", f.ID)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id IN ?", f.IDs)
		}
		return db
	}
}

func (f Filter) _eq_order_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.OrderID > 0 {
			return db.Where("order_id = ?", f.OrderID)
		}
		return db
	}
}

func (f Filter) _in_order_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.OrderIDs) > 0 {
			return db.Where("order_id IN ?", f.OrderIDs)
		}
		return db
	}
}

func (f Filter) _eq_delivery_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.DeliveryStatus > 0 {
			return db.Where("delivery_status = ?", f.DeliveryStatus)
		}
		return db
	}
}

func (f Filter) _like_tracking_number() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.TrackingNumber != "" {
			return db.Where("tracking_number LIKE ?", "%"+f.TrackingNumber+"%")
		}
		return db
	}
}

func (f Filter) _eq_express_code() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ExpressCode != "" {
			return db.Where("express_code = ?", f.ExpressCode)
		}
		return db
	}
}

func (f Filter) _between_shipped_at() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShippedAtStart > 0 && f.ShippedAtEnd > 0 {
			return db.Where("shipped_at BETWEEN ? AND ?", f.ShippedAtStart, f.ShippedAtEnd)
		} else if f.ShippedAtStart > 0 {
			return db.Where("shipped_at >= ?", f.ShippedAtStart)
		} else if f.ShippedAtEnd > 0 {
			return db.Where("shipped_at <= ?", f.ShippedAtEnd)
		}
		return db
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				continue
			}
			db = db.Order(val)
		}
		return db
	}
}

// DataPageList 分页查询发货记录
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").
		Scopes(f._sort()).
		Offset((page - 1) * limit).Limit(limit).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter 根据过滤条件查询发货记录列表
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true), f)

	ret := ModelList{}
	if err := query.Scopes(f._sort()).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter 根据过滤条件统计发货记录数量
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (int64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
