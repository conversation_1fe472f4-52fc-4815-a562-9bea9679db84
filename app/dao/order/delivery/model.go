package delivery

import (
	"blind_box/app/common/dbs"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

type (
	DeliveryStatus uint32
)

const (
	DeliveryStatusPending   DeliveryStatus = 1 // 待发货
	DeliveryStatusShipped   DeliveryStatus = 2 // 已发货
	DeliveryStatusDelivered DeliveryStatus = 3 // 已送达
	DeliveryStatusReturned  DeliveryStatus = 4 // 已退回
)

// Model 订单发货表
type Model struct {
	dbs.ModelWithDel

	OrderID        uint64 `json:"order_id" gorm:"uniqueIndex;not null;comment:订单ID"`                     // 订单ID，与box_order_info.id关联
	ExpressCompany string `json:"express_company" gorm:"size:50;comment:快递公司"`                           // 快递公司名称
	ExpressCode    string `json:"express_code" gorm:"size:20;comment:快递公司编码"`                            // 快递公司编码（如：SF=顺丰）
	TrackingNumber string `json:"tracking_number" gorm:"size:100;not null;comment:快递单号"`                 // 快递单号
	DeliveryStatus uint32 `json:"delivery_status" gorm:"default:1;comment:发货状态,1-待发货,2-已发货,3-已送达,4-已退回"` // 发货状态
	ShippedAt      int64  `json:"shipped_at" gorm:"comment:发货时间戳"`                                       // 发货时间
	DeliveredAt    int64  `json:"delivered_at" gorm:"comment:送达时间戳"`                                     // 送达时间
	Remark         string `json:"remark" gorm:"size:500;comment:备注"`                                     // 备注信息
	AdminID        uint64 `json:"admin_id" gorm:"comment:操作管理员ID"`                                       // 操作的管理员ID（实际存储AccountID）
}

func (Model) TableName() string {
	return "box_order_delivery"
}

type ModelList []*Model

func (ml ModelList) GetIDs() []uint64 {
	ids := make([]uint64, 0, len(ml))
	for _, m := range ml {
		ids = append(ids, m.ID)
	}
	return lo.Uniq(ids)
}

func (ml ModelList) GetOrderIDs() []uint64 {
	orderIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		orderIDs = append(orderIDs, m.OrderID)
	}
	return lo.Uniq(orderIDs)
}

func (ml ModelList) GetAdminIDs() []uint64 {
	adminIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		adminIDs = append(adminIDs, m.AdminID)
	}
	return lo.Uniq(adminIDs)
}

// Filter 查询过滤器
type Filter struct {
	ID  uint64
	IDs []uint64

	OrderID  uint64
	OrderIDs []uint64

	DeliveryStatus uint32
	TrackingNumber string
	ExpressCode    string

	// 时间范围过滤
	ShippedAtStart int64
	ShippedAtEnd   int64

	Sort []clause.OrderByColumn
}
