package order

import (
	"fmt"
	"time"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"out_trade_no",
				"transaction_id",
				"fee_type",
				"total_fee",
				"fee",
				"active_id",
				"user_id",
				"box_id",
				"box_no",
				"box_slot",
				"lottery_total",
				"lottery_left",
				"order_status",
				"source_platform",
				"pay_method",
				"consume_type",
				"consume_msg",
				"order_type",
				"remark",
				"pay_time",
				"used_fee",
				"cash_fee",
				"coupon_fee",
				"coupon_id",
				"points_fee",
				"points_use",
				"purchase_type",
				"delivery_id",
				"shop_id",
				"address_id",
				"pay_expire_time",
			}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		f._eq_id(),
		f._eq_status(),
		f._in_ids(),
		f._eq_user_id(),
		f._eq_order_type(),
		f._in_order_status(),
		f._like_out_trade_no(),
		f._time_range(),
	)

	return query
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.ID; t > 0 {
			return db.Where("id = ?", t)
		}
		return db
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.Status; t > 0 {
			return db.Where("order_status = ?", t)
		}
		return db
	}
}

func (f Filter) _eq_user_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.UserID; t > 0 {
			return db.Where("user_id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id in ?", f.IDs)
		}
		return db
	}
}

func (f Filter) _eq_order_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.OrderType; t > 0 {
			return db.Where("order_type = ?", t)
		}
		return db
	}
}

func (f Filter) _in_order_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.OrderStatusList) > 0 {
			if len(f.OrderStatusList) == 1 {
				// 单个状态使用等于查询
				return db.Where("order_status = ?", f.OrderStatusList[0])
			} else {
				// 多个状态使用 IN 查询
				return db.Where("order_status IN ?", f.OrderStatusList)
			}
		}
		return db
	}
}

func (f Filter) _like_out_trade_no() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.OutTradeNo != "" {
			return db.Where("out_trade_no LIKE ?", "%"+f.OutTradeNo+"%")
		}
		return db
	}
}

func (f Filter) _time_range() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.CreatedAtStart > 0 {
			db = db.Where("created_at >= ?", time.Unix(f.CreatedAtStart, 0))
		}
		if f.CreatedAtEnd > 0 {
			db = db.Where("created_at <= ?", time.Unix(f.CreatedAtEnd, 0))
		}
		return db
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").
		Scopes(f._sort()).
		Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter 避免主从未同步先读取情况, 此处读取主库
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true), f)

	ret := ModelList{}
	if err := query.Scopes(f._sort()).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e *Entry) FetchByOutTradeNo(ctx *gin.Context, outTradeNo string) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("out_trade_no = ?", outTradeNo).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
