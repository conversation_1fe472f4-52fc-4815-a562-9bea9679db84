package order

import (
	"blind_box/app/common/dbs"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

type (
	OrderStatus uint32
	ConsumeType uint32

	OrderType uint32
)

var (
	SourcePlatformWx = 1 //微信平台

	OrderTypeBox    OrderType = 1
	OrderTypeDirect OrderType = 2 //直接购买商品

	OrderStatusCreated  OrderStatus = 1
	OrderStatusPayIng   OrderStatus = 2
	OrderStatusPayOk    OrderStatus = 3
	OrderStatusPayFail  OrderStatus = 4
	OrderStatusRefund   OrderStatus = 5 //支付状态,1-订单生成,2-支付中,3-支付成功,4-支付失败,5-已退款,6-订单取消/关闭 7-订单完成
	OrderStatusCancel   OrderStatus = 6 // 订单取消/关闭
	OrderStatusDone     OrderStatus = 7 // 订单完成
	OrderStatusDelivery OrderStatus = 8 // 订单发货

	OrderInfoPayMethodMethodWechat  = 1 // 微信支付
	OrderInfoPayMethodMethodBalance = 2 // 余额
	OrderInfoPayMethodMethodAli     = 5 // 支付宝

	ConsumeTypeBox    ConsumeType = 1
	ConsumeTypeDirect ConsumeType = 3 //直接购买商品

	DeliveryTypeExpress uint32 = 1 // 快递
	DeliveryTypeStore   uint32 = 2 // 到店取货
)

type Model struct {
	dbs.ModelWithDel

	OutTradeNo     string // '订单号',
	TransactionId  string // '支付订单号',
	FeeType        string // '货币类型,CNY',
	TotalFee       uint64 // '总金额',
	Fee            uint64 // '单价',
	ActiveID       uint64 // '活动ID',
	UserID         uint64
	BoxID          uint64 // '箱子ID',
	BoxNo          string // '箱子编号',
	BoxSlot        string
	LotteryTotal   uint32 // '总可抽奖次数',
	LotteryLeft    uint32 // '剩余可抽奖次数',
	OrderStatus    uint32 // COMMENT '支付状态,1-订单生成,2-支付中,3-支付成功,4-支付失败,5-已退款 6-订单取消/关闭 7-订单完成',
	SourcePlatform uint32 // '来源平台(用户UID的注册平台),1:微信',
	PayMethod      uint32 // '支付方式,1:微信 2:余额,3:微信+魔币,4:余额+魔币 5-支付宝 6-魔魂',
	ConsumeType    uint32 // '消费类型,1:活动;2:快递;3:退款;4-试玩 5-交易 6-余额提现',
	ConsumeMsg     string // '消费信息',
	OrderType      uint32 // ` tinyint(1) DEFAULT NULL,
	Remark         string //` varchar(100) DEFAULT NULL,
	PayTime        uint64 // 支付时间，秒级时间戳
	UsedFee        uint64 //` bigint(10) DEFAULT '0',	余额
	CashFee        uint64 //` bigint(20) DEFAULT '0' COMMENT '实际支付金额',
	CouponFee      uint64 // 优惠券抵扣金额
	CouponID       uint64 // 优惠券id
	PointsFee      uint64 // 积分抵扣金额
	PointsUse      uint64 // 消耗的积分数量
	PurchaseType   uint32 // 购买类型: 1-盲盒随机购买, 3-直接购买
	FreightFee     uint64 // 运费

	DeliveryID    uint32 `json:"delivery_id" form:"delivery_id"`         // 配送方式ID，1-快递,2-到店取
	ShopID        uint64 `json:"shop_id" form:"shop_id"`                 // 店铺ID，可选
	AddressID     uint64 `json:"address_id" form:"address_id"`           // 收货地址ID，可选
	PayExpireTime int64  `json:"pay_expire_time" form:"pay_expire_time"` // 最后支付时间，秒级时间戳
	MerchantID    uint64 `json:"merchant_id" form:"merchant_id"`         // OpenAPI商户ID
	PosOrderNo    string `json:"pos_order_no" form:"pos_order_no"`       // POS机订单号
}

func (Model) TableName() string {
	return "box_order_info"
}

type ModelList []*Model

func (ml ModelList) GetIDs() []uint64 {
	ids := make([]uint64, 0, len(ml))
	for _, m := range ml {
		ids = append(ids, m.ID)
	}
	ids = lo.Uniq(ids)
	return ids
}

func (ml ModelList) GetActiveIDs() []uint64 {
	activeIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		activeIDs = append(activeIDs, m.ActiveID)
	}
	activeIDs = lo.Uniq(activeIDs)
	return activeIDs
}

func (ml ModelList) GetAddressIDs() []uint64 {
	addressIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.AddressID > 0 {
			addressIDs = append(addressIDs, m.AddressID)
		}
	}
	addressIDs = lo.Uniq(addressIDs)
	return addressIDs
}

func (ml ModelList) GetShopIDs() []uint64 {
	shopIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.ShopID > 0 {
			shopIDs = append(shopIDs, m.ShopID)
		}
	}
	shopIDs = lo.Uniq(shopIDs)
	return shopIDs
}

func (ml ModelList) GetUserIDs() []uint64 {
	userIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		if m.UserID > 0 {
			userIDs = append(userIDs, m.UserID)
		}
	}
	userIDs = lo.Uniq(userIDs)
	return userIDs
}

type Filter struct {
	ID  uint64
	IDs []uint64

	Status uint32

	UserID uint64

	// 新增字段用于 OrderList 查询
	OrderType       uint32   // 订单类型过滤
	OrderStatusList []uint32 // 订单状态列表过滤，支持单个或多个状态
	OutTradeNo      string   // 订单号过滤

	// 时间范围过滤（使用秒级时间戳）
	CreatedAtStart int64
	CreatedAtEnd   int64

	Sort []clause.OrderByColumn
}
