package demo

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type Model struct {
	ID           uint64    `json:"id,omitempty"`
	UID          uint64    `json:"user_id,omitempty"`
	Title        string    `json:"title,omitempty"`
	Sort         uint32    `json:"sort,omitempty"`
	Status       uint32    `json:"status,omitempty"`
	IsDeleted    uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedTime  time.Time `json:"created_at,omitempty"` // 创建时间
	ModifiedTime time.Time `json:"updated_at,omitempty"` // 修改时间
}

func (m *Model) TableName() string {
	return "demo"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedTime.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	UID    uint64
	Uids   []uint64
	Title  string
	Status uint32
	Sort   dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetUids() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.UID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}
