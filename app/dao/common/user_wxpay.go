package common

// orderDao "blind_box/app/dao/order"
// "blind_box/app/dto/common"

type WxpayOrderInfo struct {
	// OrderList         orderDao.ModelList                        `json:"order_list"`
	// OrderGoodsListMap map[uint64]orderDao.OrderGoodsList        `json:"order_goods_list"`
	PayEntityIdsMap map[uint64][]string `json:"pay_entity_ids_map"`
	// Req               common.CommonOrderGoodsCollectionExtraReq `json:"req"`
	CreateBy    uint64 `json:"create_by"`
	OperateBy   uint64 `json:"operate_by"`
	OperateType uint64 `json:"operate_type"`
	TimeExpire  string // 最后支付时间 rfc3339格式
}

type WxrefundOrderInfo struct {
	PayNo        string `json:"pay_no"`
	TotalAmount  uint64 `json:"total_amount"`
	RefundAmount uint64 `json:"refund_amount"`
}
