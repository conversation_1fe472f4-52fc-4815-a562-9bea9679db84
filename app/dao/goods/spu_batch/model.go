package spuBatch

type Model struct {
	SpuId     uint64 `json:"spu_id,omitempty"`
	BatchId   uint64 `json:"batch_id,omitempty"`
	IsDefault uint32 `json:"is_default,omitempty"`
}

func (m *Model) TableName() string {
	return "goods_spu_batch"
}

type Filter struct {
	SpuId     uint64
	BatchId   uint64
	IsDefault uint32
}

type ModelList []*Model

func (ml ModelList) GetSpuIds() []uint64 {
	spuIds := []uint64{}
	for _, val := range ml {
		spuIds = append(spuIds, val.SpuId)
	}
	return spuIds
}

func (ml ModelList) GetBatchIds() []uint64 {
	batchIds := []uint64{}
	for _, val := range ml {
		batchIds = append(batchIds, val.BatchId)
	}
	return batchIds
}

func (ml ModelList) GetSpuBatchIds() ([]uint64, []uint64) {
	spuIds := []uint64{}
	spuMap := make(map[uint64]struct{}, 0)

	batchIds := []uint64{}
	batchMap := make(map[uint64]struct{}, 0)

	for _, val := range ml {
		if _, ok := spuMap[val.SpuId]; !ok {
			spuIds = append(spuIds, val.SpuId)
			spuMap[val.SpuId] = struct{}{}
		}
		if _, ok := batchMap[val.BatchId]; !ok {
			batchIds = append(batchIds, val.BatchId)
			batchMap[val.BatchId] = struct{}{}
		}
	}
	return spuIds, batchIds
}
