package sku

import (
	"blind_box/app/common/dbs"
	"time"
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	SpuId     uint64    `json:"spu_id,omitempty"`
	SkuId     uint64    `json:"sku_id,omitempty"`
	Title     string    `json:"title,omitempty"`
	UnitRatio uint32    `json:"unit_ratio,omitempty"`
	IsPrimary uint32    `json:"is_primary,omitempty"` // 是否主规格 1是 0否
	Status    uint32    `json:"status,omitempty"`     // 状态 1上架 2下架
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_sku_unit"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) JudgeCanOrdered() bool {
	return m.Status == uint32(dbs.StatusEnable)
}

type Filter struct {
	ID        uint64
	NotId     uint64
	NotIds    []uint64
	Ids       []uint64
	Title     string
	TitleLike string
	Status    uint32
	SpuId     uint64
	SpuIds    []uint64
	SkuId     uint64
	SkuIds    []uint64
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIdMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIdEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetIds() []uint64 {
	idList := make([]uint64, 0, len(ml))
	for _, v := range ml {
		idList = append(idList, v.ID)
	}
	return idList
}

func (ml ModelList) GetSkuUnitListMap() map[uint64]ModelList {
	retMap := make(map[uint64]ModelList, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.SkuId]; !ok {
			retMap[val.SkuId] = ModelList{}
		}
		retMap[val.SkuId] = append(retMap[val.SkuId], val)
	}
	return retMap
}
