package spu

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/ecode"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"batch_id", "title", "sub_title", "code", "cover", "brand_id", "vendor_id", "supplier_id",
				"status", "pre_start_time", "pre_end_time", "lowest_price", "highest_price", "sort", "detail",
			}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, groupId uint32, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, groupId, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, groupId uint32, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByFilter .
func (e *Entry) UpdateMapByFilter(ctx *gin.Context, f *Filter, data map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), f, data)
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, f *Filter, data map[string]interface{}) error {
	query := tx.Model(&Model{})
	if f.ID == 0 && len(f.Ids) == 0 {
		return ecode.SqlParamErr
	}
	if f.ID > 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// DeleteByID .
func (e *Entry) DeleteByID(ctx *gin.Context, id uint64, groupId uint32) error {
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
		"status":                  dbs.StatusDisable,
	}).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Table(_Model.TableName() + " spu").Where(fmt.Sprintf("spu.%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("spu.id = ?", f.ID)
	}
	if f.NotID != 0 {
		query.Where("spu.id <> ?", f.NotID)
	}
	if len(f.Ids) > 0 {
		query.Where("spu.id in ?", f.Ids)
	}
	if f.Title != "" {
		query.Where("spu.title = ?", f.Title)
	}
	if f.TitleLike != "" {
		query.Where("spu.title like ?", "%"+f.TitleLike+"%")
	}
	if f.Code != "" {
		query.Where("spu.code = ?", f.Code)
	}
	if len(f.CodeList) > 0 {
		query.Where("spu.code in ?", f.CodeList)
	}
	if f.CodeLike != "" {
		query.Where("spu.code like ?", "%"+f.CodeLike+"%")
	}
	if f.CategoryId != 0 {
		query.Where("spu.category_id = ?", f.CategoryId)
	}
	if f.BrandId != 0 {
		query.Where("spu.brand_id = ?", f.BrandId)
	}
	if f.VendorId != 0 {
		query.Where("spu.vendor_id = ?", f.VendorId)
	}
	if f.SupplierId != 0 {
		query.Where("spu.supplier_id = ?", f.SupplierId)
	}
	if f.Status != 0 {
		query.Where("spu.status = ?", f.Status)
	}
	if f.SellType != 0 {
		query.Joins("LEFT JOIN "+_BatchModel.TableName()+" b ON spu.batch_id = b.id").
			Where("b.is_deleted = 0").Where("b.sell_type = ?", f.SellType)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("spu.id").Count(&total)
	if err = query.Select("spu.*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSortAlias(f.Sort, "spu")).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSortAlias(f.Sort, "spu")).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(fmt.Sprintf("spu.%s", field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Select("spu.id").Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
