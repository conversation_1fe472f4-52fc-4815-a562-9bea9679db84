package spu

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/golang-module/carbon/v2"
	"github.com/samber/lo"
)

const (
	SellType_1 = 1 // 现货
)

// 商品主表
type Model struct {
	ID           uint64     `gorm:"primarykey" json:"id"`
	BatchId      uint64     `json:"batch_id,omitempty"`
	Title        string     `json:"title,omitempty"`
	SubTitle     string     `json:"sub_title,omitempty"`
	Code         string     `json:"code,omitempty"`
	Cover        dbs.CdnImg `json:"cover,omitempty"`
	BrandId      uint64     `json:"brand_id,omitempty"`
	VendorId     uint64     `json:"vendor_id,omitempty"`
	SupplierId   uint64     `json:"supplier_id,omitempty"`
	Status       uint32     `json:"status,omitempty"`         // 状态 1上架 2下架
	PreStartTime int64      `json:"pre_start_time,omitempty"` // 预售开始时间
	PreEndTime   int64      `json:"pre_end_time,omitempty"`   // 预售结束时间
	LowestPrice  uint64     `json:"lowest_price,omitempty"`   // 最低价格
	HighestPrice uint64     `json:"highest_price,omitempty"`
	IsRedeem     uint32     `json:"is_redeem,omitempty"` // 是否可兑换 1是 0否
	Sort         uint32     `json:"sort,omitempty"`
	Detail       string     `json:"detail,omitempty"`
	IsDeleted    uint32     `json:"is_deleted,omitempty"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_spu"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetPreStartTime() string {
	if m.PreStartTime == 0 {
		return ""
	}
	return carbon.CreateFromTimestamp(m.PreStartTime).ToDateTimeString()
}

func (m *Model) GetPreEndTime() string {
	if m.PreEndTime == 0 {
		return ""
	}
	return carbon.CreateFromTimestamp(m.PreEndTime).ToDateTimeString()
}

func (m *Model) JudgeCanOrdered() bool {
	return m.Status == uint32(dbs.StatusEnable)
}

type Filter struct {
	ID         uint64
	NotID      uint64
	Ids        []uint64
	Title      string
	TitleLike  string
	Code       string
	CodeList   []string
	CodeLike   string
	CategoryId uint64
	BrandId    uint64
	VendorId   uint64
	SupplierId uint64
	SellType   uint32
	Status     uint32
	Sort       dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIdMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIdEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetBatchIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.BatchId
	})
	ret = lo.Uniq(ret)
	return ret
}
