package spuTag

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/goods/tag"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == 0 {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// DeleteBySpuID .
func (e *Entry) DeleteBySpuID(ctx *gin.Context, spuId uint64, groupId tag.TagGroup) error {
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("spu_id = ? and group_id = ?", spuId, groupId)
	if err := query.Delete(&Model{}).Error; err != nil {
		return err
	}
	return nil
}

// // CreateOrUpdate .
// func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
// 	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
// }

// // CreateOrUpdateWithTx .
// func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
// 	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
// 		Clauses(clause.OnConflict{
// 			Columns:   []clause.Column{{Name: "id"}},
// 			DoUpdates: clause.AssignmentColumns([]string{"name", "cover"}),
// 		}).Create(&m).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// // UpdateMapByID .
// func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
// 	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
// }

// // UpdateMapByIDWithTx .
// func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
// 	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{})
	if f.SpuId != 0 {
		query.Where("spu_id = ?", f.SpuId)
	}
	if f.GroupId != 0 {
		query.Where("group_id = ?", f.GroupId)
	}
	if f.TagId != 0 {
		query.Where("tag_id = ?", f.TagId)
	}
	if len(f.SpuIds) > 0 {
		query.Where("spu_id in ?", f.SpuIds)
	}
	if len(f.GroupIds) > 0 {
		query.Where("group_id in ?", f.GroupIds)
	}
	if len(f.TagIds) > 0 {
		query.Where("tag_id in ?", f.TagIds)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
