package spuTag

import (
	"github.com/samber/lo"
)

type Model struct {
	SpuId   uint64 `json:"spu_id,omitempty"`
	GroupId uint32 `json:"group_id,omitempty"`
	TagId   uint64 `json:"tag_id,omitempty"`
}

func (m *Model) TableName() string {
	return "goods_spu_tag"
}

type Filter struct {
	SpuId    uint64
	TagId    uint64
	GroupId  uint32
	TagIds   []uint64
	SpuIds   []uint64
	GroupIds []uint32
}

type ModelList []*Model

func (ml ModelList) GetTagIDs() []uint64 {
	ret := make([]uint64, 0, len(ml))
	for _, val := range ml {
		ret = append(ret, val.TagId)
	}
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetSpuIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.SpuId]; !ok {
			retMap[val.SpuId] = val
		}
	}
	return retMap
}

func (ml ModelList) GetSpuIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.SpuId
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetSpuIDOnlyMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.SpuId]; !ok {
			retMap[val.SpuId] = val
		}
	}
	return retMap
}
