package tag

import (
	"encoding/json"

	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableGroupTagList .
func (e *Entry) RedisEnableGroupTagList(ctx *gin.Context, groupId uint32) (ModelList, error) {
	list, err := e.RedisGroupTagList(ctx, groupId)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisGroupTagList .
func (e *Entry) RedisGroupTagList(ctx *gin.Context, groupId uint32) (ModelList, error) {
	cacheKey := redisPkg.GetSpuGroupTagListKey(groupId)
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadGroupTagList(ctx, groupId)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisGroupTagMap .
func (e *Entry) RedisGroupTagMap(ctx *gin.Context, groupId uint32) (map[uint64]*Model, error) {
	companyList, err := e.RedisGroupTagList(ctx, groupId)
	if err != nil {
		return nil, err
	}
	return companyList.GetIDMap(), nil
}

// RedisReloadGroupTagList redis重载数据列表
func (e *Entry) RedisReloadGroupTagList(ctx *gin.Context, groupId uint32) (ModelList, error) {
	var (
		cacheKey   = redisPkg.GetSpuGroupTagListKey(groupId)
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{GroupID: groupId})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearGroupTagList .
func (e *Entry) RedisClearGroupTagList(ctx *gin.Context, groupId uint32) error {
	cacheKey := redisPkg.GetSpuGroupTagListKey(groupId)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
