package tag

import (
	"time"

	"blind_box/app/common/dbs"
)

type TagGroup uint32
type TagGroupName string

const (
	GroupCateID TagGroup     = 1
	GroupCate   TagGroupName = "category"
)

var (
	_Model          = &Model{}
	GroupIDEmptyMap = map[TagGroup]struct{}{
		GroupCateID: {},
	}

	GroupMap = map[TagGroup]TagGroupName{
		GroupCateID: GroupCate,
	}

	GroupMapReverse = map[TagGroupName]TagGroup{
		GroupCate: GroupCateID,
	}
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	Name      string    `json:"name,omitempty"`
	GroupID   uint32    `json:"group_id,omitempty"`
	Status    uint32    `json:"status,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_tag"
}

type Filter struct {
	ID       uint64
	NotID    uint64
	Ids      []uint64
	Status   uint32
	GroupID  uint32
	Name     string
	NameList []string
	NameLike string

	Sort dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetNameMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Name]; !ok {
			retMap[val.Name] = val
		}
	}
	return retMap
}

type TagGroupModel struct {
	GroupID   uint64 `json:"group_id"`
	GroupName string `json:"group_name"`
}

var TagGroupList = []*TagGroupModel{
	{
		GroupID:   uint64(GroupCateID),
		GroupName: "商品分类",
	},
}
