package tag

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"name"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.RedisClearGroupTagList(ctx, m.GroupID)
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, groupId uint32, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, groupId, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, groupId uint32, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	e.RedisClearGroupTagList(ctx, groupId)
	return nil
}

// DeleteByID .
func (e *Entry) DeleteByID(ctx *gin.Context, id uint64, groupId uint32) error {
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(map[string]interface{}{dbs.SoftDelField.String(): dbs.True}).Error; err != nil {
		return err
	}
	e.RedisClearGroupTagList(ctx, groupId)
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.NotID != 0 {
		query.Where("id <> ?", f.NotID)
	}
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if f.GroupID != 0 {
		query.Where("group_id = ?", f.GroupID)
	}
	if f.Name != "" {
		query.Where("name = ?", f.Name)
	}
	if f.NameLike != "" {
		query.Where("name like ?", "%"+f.NameLike+"%")
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if len(f.NameList) > 0 {
		query.Where("name in (?)", f.NameList)
	}
	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
