package skuShop

type Model struct {
	SpuId  uint64 `json:"spu_id,omitempty"`
	SkuId  uint64 `json:"sku_id,omitempty"`
	ShopId uint64 `json:"shop_id,omitempty"`
}

func (m *Model) TableName() string {
	return "goods_sku_shop"
}

type Filter struct {
	SpuId   uint64
	SpuIds  []uint64
	SkuId   uint64
	SkuIds  []uint64
	ShopId  uint64
	ShopIds []uint64
}

type ModelList []*Model

func (ml ModelList) GetSpuSkuIds() ([]uint64, []uint64) {
	spuIds := []uint64{}
	spuMap := make(map[uint64]struct{}, 0)

	skuIds := []uint64{}
	skuMap := make(map[uint64]struct{}, 0)

	for _, val := range ml {
		if _, ok := spuMap[val.SpuId]; !ok {
			spuIds = append(spuIds, val.SpuId)
			spuMap[val.SpuId] = struct{}{}
		}
		if _, ok := skuMap[val.SkuId]; !ok {
			skuIds = append(skuIds, val.SkuId)
			skuMap[val.SkuId] = struct{}{}
		}
	}
	return spuIds, skuIds
}

type SkuShop struct {
	SpuId    uint64 `json:"spu_id,omitempty"`
	SkuId    uint64 `json:"sku_id,omitempty"`
	ShopId   uint64 `json:"shop_id,omitempty"`
	ShopName string `json:"shop_name,omitempty"`
}

type SkuShopList []*SkuShop
