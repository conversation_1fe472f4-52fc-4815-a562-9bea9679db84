package skuShop

import (
	"blind_box/app/common/dbs"
	shopDao "blind_box/app/dao/resource/shop"
	"blind_box/pkg/redis"
	"sync"
)

var (
	_Model     = &Model{}
	_ShopModel = &shopDao.Model{}
)

type Repo interface {
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo         Repo
	defaultRepo *Entry

	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
