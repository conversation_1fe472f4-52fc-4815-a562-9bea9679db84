package sku

import (
	"time"

	"blind_box/app/common/dbs"
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	Title     string    `json:"title,omitempty"`
	SellType  uint32    `json:"sell_type,omitempty"`
	Status    uint32    `json:"status,omitempty"` // 状态 1启用 2禁用
	Desc      string    `json:"desc,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_batch"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID        uint64
	NotID     uint64
	Ids       []uint64
	SellType  uint32
	Title     string
	TitleLike string
	Status    uint32
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIdMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIdEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetIdList() []uint64 {
	idList := make([]uint64, 0, len(ml))
	for _, v := range ml {
		idList = append(idList, v.ID)
	}
	return idList
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
