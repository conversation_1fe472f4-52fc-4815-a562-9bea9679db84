package stockLog

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type (
	LogType  uint32
	LogParam struct {
		UserId     uint64
		SpuId      uint64
		SkuId      uint64
		EntityId   uint64
		LogType    LogType
		BeforeVal  uint32 // 修改前的值
		CurrentVal uint32 // 当前值
		CreateBy   uint64
	}
	LogParamList []*LogParam
)

const (
	LtSysSet LogType = 100 + iota // 系统设置
)
const (
	LtBoxCreate LogType = 200 + iota // 盲盒创建
	LtBoxUpdate                      // 盲盒更新
)

const (
	LtOrderCreate LogType = 300 + iota // 订单创建
	LtOrderPay                         // 订单支付
	LtOrderRefund                      // 订单退款
	LtOrderCancel                      // 订单取消
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	SpuId     uint64    `json:"spu_id,omitempty"`
	SkuId     uint64    `json:"sku_id,omitempty"`
	UserId    uint64    `json:"user_id,omitempty"`
	LogType   uint32    `json:"log_type,omitempty"`
	SubType   uint32    `json:"sub_type,omitempty"`
	EntityId  uint64    `json:"entity_id,omitempty"` // 关联实体ID，比如盲盒ID、订单ID等
	Value     uint32    `json:"value,omitempty"`
	BeforeVal uint32    `json:"before_val,omitempty"`
	AfterVal  uint32    `json:"after_val,omitempty"`
	CreateBy  uint64    `json:"create_by,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_sku_stock_log"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	SkuId   uint64
	SkuIds  []uint64
	SpuId   uint64
	SpuIds  []uint64
	LogType uint32
	Sort    dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetSpuSkuIds() ([]uint64, []uint64) {
	spuIds := []uint64{}
	spuMap := make(map[uint64]struct{}, 0)

	skuIds := []uint64{}
	skuMap := make(map[uint64]struct{}, 0)

	for _, val := range ml {
		if _, ok := spuMap[val.SpuId]; !ok {
			spuIds = append(spuIds, val.SpuId)
			spuMap[val.SpuId] = struct{}{}
		}
		if _, ok := skuMap[val.SkuId]; !ok {
			skuIds = append(skuIds, val.SkuId)
			skuMap[val.SkuId] = struct{}{}
		}
	}
	return spuIds, skuIds
}

func (ml LogParamList) GetSkuIds() []uint64 {
	ret := lo.Map(ml, func(item *LogParam, idx int) uint64 {
		return item.SkuId
	})
	ret = lo.Uniq(ret)
	return ret
}
