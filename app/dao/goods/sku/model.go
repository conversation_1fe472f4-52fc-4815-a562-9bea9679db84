package sku

import (
	"blind_box/app/common/dbs"
	"time"
)

type DeliveryType uint32

const (
	DeliveryTypeNormal DeliveryType = iota + 1 // 送到家
	DeliveryTypeShop                           // 到店取
	DeliveryTypeBox                            // 抽盲盒
)

type Model struct {
	ID            uint64     `gorm:"primarykey" json:"id"`
	SpuId         uint64     `json:"spu_id,omitempty"`
	BatchId       uint64     `json:"batch_id,omitempty"`
	Title         string     `json:"title,omitempty"`
	Code          string     `json:"code,omitempty"`
	Cover         dbs.CdnImg `json:"cover,omitempty"`
	Total         uint32     `json:"total,omitempty"`
	LockNum       uint32     `json:"lock_num,omitempty"`
	UsedNum       uint32     `json:"used_num,omitempty"`
	RefundNum     uint32     `json:"refund_num,omitempty"`
	OriginalPrice uint64     `json:"original_price,omitempty"`
	SellPrice     uint64     `json:"sell_price,omitempty"`
	RefPrice      uint64     `json:"ref_price,omitempty"`
	BarCode       string     `json:"bar_code,omitempty"`
	DeliveryList  string     `json:"delivery_list,omitempty"`
	Status        uint32     `json:"status,omitempty"` // 状态 1上架 2下架
	IsDeleted     uint32     `json:"is_deleted,omitempty"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "goods_sku"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetUsableNum() uint32 {
	if m.Total+m.RefundNum >= m.LockNum+m.UsedNum {
		return m.Total + m.RefundNum - m.LockNum - m.UsedNum
	}
	return 0
}
func (m *Model) IsSoldOut() uint32 {
	if m.GetUsableNum() == 0 {
		return uint32(dbs.StatusEnable)
	}
	return uint32(dbs.StatusDisable)
}

func (m *Model) JudgeCanOrdered() bool {
	return m.Status == uint32(dbs.StatusEnable)
}

type Filter struct {
	ID          uint64
	NotId       uint64
	NotIds      []uint64
	Ids         []uint64
	Title       string
	TitleLike   string
	Status      uint32
	SpuID       uint64
	NotSpuID    uint64
	SpuIds      []uint64
	Code        string
	CodeLike    string
	CodeList    []string
	BarCode     string
	BarCodeList []string
	Sort        dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetCodeMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Code]; !ok {
			retMap[val.Code] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetIds() []uint64 {
	idList := make([]uint64, 0, len(ml))
	for _, v := range ml {
		idList = append(idList, v.ID)
	}
	return idList
}

func (ml ModelList) GetSpuBatchIds() ([]uint64, []uint64) {
	spuIds := []uint64{}
	spuMap := make(map[uint64]struct{}, 0)

	batchIds := []uint64{}
	batchMap := make(map[uint64]struct{}, 0)

	for _, val := range ml {
		if _, ok := spuMap[val.SpuId]; !ok {
			spuIds = append(spuIds, val.SpuId)
			spuMap[val.SpuId] = struct{}{}
		}
		if _, ok := batchMap[val.BatchId]; !ok {
			batchIds = append(batchIds, val.BatchId)
			batchMap[val.BatchId] = struct{}{}
		}
	}
	return spuIds, batchIds
}
