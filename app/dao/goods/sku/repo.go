package sku

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/ecode"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"title", "code", "cover", "original_price", "sell_price", "ref_price", "bar_code", "status"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Model) error {
	if len(models) == 0 {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreateOrUpdate .
func (e *Entry) BatchCreateOrUpdate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateOrUpdateWithTx .
func (e *Entry) BatchCreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"title", "code", "cover", "original_price", "sell_price", "ref_price", "bar_code", "delivery_list", "status"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByFilter .
func (e *Entry) UpdateMapByFilter(ctx *gin.Context, f *Filter, data map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), f, data)
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, f *Filter, data map[string]interface{}) error {
	query := tx.Model(&Model{})
	if f.ID == 0 && len(f.Ids) == 0 {
		return ecode.SqlParamErr
	}
	if f.ID > 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.NotId != 0 {
		query.Where("id <> ?", f.NotId)
	}
	if len(f.NotIds) > 0 {
		query.Where("id not in ?", f.NotIds)
	}
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if f.SpuID != 0 {
		query.Where("spu_id = ?", f.SpuID)
	}
	if f.NotSpuID != 0 {
		query.Where("spu_id <> ?", f.NotSpuID)
	}
	if len(f.SpuIds) > 0 {
		query.Where("spu_id in ?", f.SpuIds)
	}
	if f.Code != "" {
		query.Where("code = ?", f.Code)
	}
	if len(f.CodeList) > 0 {
		query.Where("code in ?", f.CodeList)
	}
	if f.CodeLike != "" {
		query.Where("code like ?", "%"+f.CodeLike+"%")
	}
	if f.Title != "" {
		query.Where("title = ?", f.Title)
	}
	if f.TitleLike != "" {
		query.Where("title like ?", "%"+f.TitleLike+"%")
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.BarCode != "" {
		query.Where("bar_code = ?", f.BarCode)
	}
	if len(f.BarCodeList) > 0 {
		query.Where("bar_code in ?", f.BarCodeList)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// LockStock 锁定库存
func (e *Entry) LockStock(ctx *gin.Context, skuID uint64, quantity uint32) error {
	return e.LockStockWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), skuID, quantity)
}

// LockStockWithTx 带事务锁定库存
func (e *Entry) LockStockWithTx(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32) error {
	// 使用悲观锁确保并发安全
	var sku Model
	if err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("id = ? AND is_deleted = 0", skuID).
		First(&sku).Error; err != nil {
		return fmt.Errorf("sku not found: %w", err)
	}

	// 检查可用库存
	availableStock := sku.GetUsableNum()
	if availableStock < quantity {
		return fmt.Errorf("insufficient stock: available %d, required %d", availableStock, quantity)
	}

	// 更新锁定数量
	updates := map[string]interface{}{
		"lock_num": gorm.Expr("lock_num + ?", quantity),
	}
	
	if err := tx.Model(&Model{}).Where("id = ?", skuID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to lock stock: %w", err)
	}

	return nil
}

// ReleaseStock 释放库存
func (e *Entry) ReleaseStock(ctx *gin.Context, skuID uint64, quantity uint32) error {
	return e.ReleaseStockWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), skuID, quantity)
}

// ReleaseStockWithTx 带事务释放库存
func (e *Entry) ReleaseStockWithTx(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32) error {
	// 更新锁定数量
	updates := map[string]interface{}{
		"lock_num": gorm.Expr("GREATEST(lock_num - ?, 0)", quantity), // 防止负数
	}
	
	if err := tx.Model(&Model{}).Where("id = ?", skuID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to release stock: %w", err)
	}

	return nil
}

// ConfirmStock 确认库存（支付成功后）
func (e *Entry) ConfirmStock(ctx *gin.Context, skuID uint64, quantity uint32) error {
	return e.ConfirmStockWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), skuID, quantity)
}

// ConfirmStockWithTx 带事务确认库存
func (e *Entry) ConfirmStockWithTx(ctx *gin.Context, tx *gorm.DB, skuID uint64, quantity uint32) error {
	// 减少锁定数量，增加使用数量
	updates := map[string]interface{}{
		"lock_num": gorm.Expr("GREATEST(lock_num - ?, 0)", quantity),
		"used_num": gorm.Expr("used_num + ?", quantity),
	}
	
	if err := tx.Model(&Model{}).Where("id = ?", skuID).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to confirm stock: %w", err)
	}

	return nil
}
