package user

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	UserRepo
	UAddrRepo
}

type UserRepo interface {
}

type UAddrRepo interface {
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

// TODO替换
var (
	// defaultRepo Repo
	defaultRepo         *Entry
	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
