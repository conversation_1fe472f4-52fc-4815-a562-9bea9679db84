package collect

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type EntityType uint32

var (
	_Model = &Model{}

	EtSpu EntityType = 1
)

type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	UserID     uint64    `json:"user_id,omitempty"`
	EntityID   uint64    `json:"entity_id,omitempty"`
	EntityType uint64    `json:"entity_type,omitempty"`
	IsDeleted  uint32    `json:"is_deleted,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_collect"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	UserID       uint64
	EntityType   uint32
	EntityID     uint64
	SpuTitleLike string
	Sort         dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetEntityIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.EntityID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
