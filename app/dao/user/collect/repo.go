package collect

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

const (
	PluckEntityID dbs.PluckField = "entity_id"
)

// BatchCreateIgnore .
func (e *Entry) BatchCreateIgnore(ctx *gin.Context, UID uint64, et EntityType, m []*Model) error {
	if len(m) == dbs.False {
		return nil
	}
	valStrings := []string{}
	valArgs := []interface{}{}
	for _, item := range m {
		valStrings = append(valStrings, "(?,?,?)")
		valArgs = append(valArgs, item.UserID, item.EntityType, item.EntityID)
	}

	sql := fmt.Sprintf("INSERT IGNORE INTO %s (user_id,  entity_type, entity_id) VALUES %s",
		_Model.TableName(), strings.Join(valStrings, ","))

	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Exec(sql, valArgs...).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("BatchCreateIgnore err")
		return err
	}
	e.RedisClearUserCollect(ctx, UID, et)
	return nil
}

// DelByIds .
func (e *Entry) DelByIds(ctx *gin.Context, UID uint64, et EntityType, entityIds []uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).
		Where("user_id = ?", UID).
		Where("entity_type = ?", et).
		Where("entity_id in ?", entityIds).
		Updates(map[string]interface{}{
			dbs.SoftDelField.String(): dbs.True,
		}).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DelByIds err")
		return err
	}
	e.RedisClearUserCollect(ctx, UID, et)
	return nil
}

// UpdateMapByFilter .
func (e *Entry) UpdateMapByFilter(ctx *gin.Context, f *Filter, data map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), f, data)
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(tx *gorm.DB, f *Filter, data map[string]interface{}) error {
	if f.UserID == 0 {
		return nil
	}
	query := tx.Model(&Model{}).Where("user_id = ?", f.UserID)
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Table(_Model.TableName() + " c").Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	switch f.EntityType {
	case uint32(EtSpu):
		query.Joins("LEFT JOIN " + spuTabler.TableName() + " p ON c.entity_id = p.id").Where(fmt.Sprintf("p.%s = 0", dbs.SoftDelField))
		if f.SpuTitleLike != "" {
			query.Where("p.title like ?", "%"+f.SpuTitleLike+"%")
		}
	}
	if f.UserID != 0 {
		query.Where("c.user_id = ?", f.UserID)
	}
	if f.EntityType != 0 {
		query.Where("c.entity_type = ?", f.EntityType)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	orderStr := "c.id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	query.Select("c.id").Count(&total)
	if err = query.Select("c.*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(orderStr).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "c.id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where("id = ?", id).
		Order("id desc").Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Select("c.id").Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
