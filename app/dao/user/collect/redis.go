package collect

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisJudgeUserCollected .
func (e *Entry) RedisJudgeUserCollected(ctx *gin.Context, uid, entityID uint64, et EntityType) bool {
	entityMap, err := e.RedisUserCollectMap(ctx, uid, et)
	if err != nil {
		return false
	}
	_, ok := entityMap[entityID]
	return ok
}

// RedisUserCollectMap .
func (e *Entry) RedisUserCollectMap(ctx *gin.Context, uid uint64, et EntityType) (map[uint64]struct{}, error) {
	entityIds, err := e.RedisUserCollect(ctx, uid, et)
	if err != nil {
		return nil, err
	}

	retMap := map[uint64]struct{}{}
	for _, entityId := range entityIds {
		if _, ok := retMap[entityId]; !ok {
			retMap[entityId] = struct{}{}
		}
	}

	return retMap, nil
}

// RedisUserCollect .
func (e *Entry) RedisUserCollect(ctx *gin.Context, uid uint64, et EntityType) ([]uint64, error) {
	var (
		cacheKey = redisPkg.GetUserCollectKey(uid, uint32(et))
		ret      = []uint64{}
	)
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadUserCollect(ctx, uid, et)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadUserCollect redis重载数据列表
func (e *Entry) RedisReloadUserCollect(ctx *gin.Context, uid uint64, et EntityType) ([]uint64, error) {
	var (
		cacheKey   = redisPkg.GetUserCollectKey(uid, uint32(et))
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		cacheData  []byte
		err        error
	)
	spuIds, err := e.FindXidsByFilter(ctx, &Filter{UserID: uid, EntityType: uint32(et)}, PluckEntityID)
	if err != nil {
		return nil, err
	}
	if cacheData, err = json.Marshal(spuIds); err != nil {
		return nil, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return nil, err
	}
	return spuIds, nil
}

// RedisClearUserCollect .
func (e *Entry) RedisClearUserCollect(ctx *gin.Context, uid uint64, et EntityType) error {
	cacheKey := redisPkg.GetUserCollectKey(uid, uint32(et))
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
