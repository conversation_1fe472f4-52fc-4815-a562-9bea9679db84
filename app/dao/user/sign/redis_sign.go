package sign

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/golang-module/carbon/v2"
)

type UserSignCache struct {
	ContinueSignDays uint32 `json:"continue_sign_days,omitempty"`
	LastSignDate     string `json:"last_sign_date,omitempty"`
}

// RedisGetSignConfigList redis获取签到配置列表
func (e *Entry) RedisGetSignConfigList(ctx *gin.Context) ([]*SignConfig, error) {
	cacheKey := redisPkg.SignConfigListKey
	ret := []*SignConfig{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadSignConfigList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Unmarshal err cacheKey:%v", cacheKey)
		return nil, err
	}
	return ret, nil
}

// RedisReloadSignConfigList redis重载签到配置列表
func (e *Entry) RedisReloadSignConfigList(ctx *gin.Context) ([]*SignConfig, error) {
	var (
		cacheKey   = redisPkg.SignConfigListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = []*SignConfig{}
		cacheData  []byte
		err        error
	)
	list, err := e.GetSignConfigList(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Marshal err")
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadSignConfigList err")
		return tmp, err
	}
	return list, nil
}

// getUserSignExpireTime 获取签到过期时间(第一、二天截止时间-当前时间戳)
func getUserSignExpireTime(days uint32) time.Duration {
	var expireTime int64
	if days >= LimitDays {
		expireTime = carbon.Now().EndOfDay().Timestamp()
	} else {
		expireTime = carbon.Tomorrow().EndOfDay().Timestamp()
	}
	return time.Duration(expireTime - time.Now().Unix())
}

// RedisUpdateUserSign 更新用户签到缓存
func (e *Entry) RedisUpdateUserSign(ctx *gin.Context, uid uint64, data *UserSignCache) error {
	var (
		cacheKey   = redisPkg.GetUserSignInfoKey(uid)
		expireTime = time.Second * getUserSignExpireTime(data.ContinueSignDays)
		cacheData  []byte
		err        error
	)

	if cacheData, err = json.Marshal(data); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Marshal err")
		return err
	}
	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, cacheData, expireTime).Err(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisUpdateUserSign err")

		return err
	}
	return nil
}

// RedisGetUserSignInfo 获取用户签到缓存信息
func (e *Entry) RedisGetUserSignInfo(ctx *gin.Context, uid uint64) (*UserSignCache, error) {
	var (
		cacheKey  = redisPkg.GetUserSignInfoKey(uid)
		cacheData string
		ret       = &UserSignCache{}
		err       error
	)

	if cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result(); err != nil {
		if err == redis.Nil {
			return e.RedisReloadUserSignInfo(ctx, uid), nil
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Unmarshal err cacheKey:%v", cacheKey)
		return nil, err
	}

	return ret, nil
}

// RedisReloadUserSignInfo 从数据库中重新load签到缓存
func (e *Entry) RedisReloadUserSignInfo(ctx *gin.Context, uid uint64) *UserSignCache {
	today, yesterday := carbon.Now().ToDateString(), carbon.Yesterday().ToDateString()
	userSignCache := &UserSignCache{}
	signList, _ := e.UserLastSignList(ctx, uid, []string{today, yesterday})
	if len(signList) > 0 {
		realDate := today
		if len(signList) == 1 {
			realDate = signList[0].SignDate
		}
		for _, info := range signList {
			if info.SignDate == realDate {
				userSignCache.ContinueSignDays = info.ContinueSignDays
				userSignCache.LastSignDate = realDate
			}
		}
	}
	e.RedisUpdateUserSign(ctx, uid, userSignCache)
	return userSignCache
}
