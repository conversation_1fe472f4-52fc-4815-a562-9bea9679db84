package sign

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
)

// GetSignConfigList 获取签到配置列表
func (e *Entry) GetSignConfigList(ctx *gin.Context) (list []*SignConfig, err error) {
	if err = e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&SignConfig{}).
		Select("id", "days", "reward_type", "reward_val").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Order("sort asc, id asc").Limit(LimitDays).Find(&list).Error; err != nil {
		return
	}
	return
}
