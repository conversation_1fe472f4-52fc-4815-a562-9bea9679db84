package sign

import "time"

const (
	LimitDays = 7 // 展示的配置数(最大签到天数)
)

// 签到配置表
type SignConfig struct {
	Id         uint64    `json:"id,omitempty"`
	Days       uint32    `json:"days,omitempty"`        // 连续签到天数(1-7)
	RewardType uint32    `json:"reward_type,omitempty"` // 奖励类型: 1 潮气值
	RewardVal  uint32    `json:"reward_val,omitempty"`  // 奖励值: 潮气值
	Sort       uint32    `json:"sort,omitempty"`        // 排序
	IsDeleted  uint32    `json:"is_deleted,omitempty"`  // 软删
	CreatedAt  time.Time `json:"created_at"`            // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`            // 修改时间
}

func (m *SignConfig) TableName() string {
	return "user_sign_config"
}
