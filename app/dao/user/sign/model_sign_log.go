package sign

import "time"

type SignLog struct {
	Id               uint64    `json:"id,omitempty"`
	UserId           uint64    `json:"user_id,omitempty"`            // 用户id
	SignDate         string    `json:"sign_date,omitempty"`          // 签到日期
	ContinueSignDays uint32    `json:"continue_sign_days,omitempty"` // 连续签到天数
	IsDeleted        uint32    `json:"is_deleted,omitempty"`         // 软删
	CreatedAt        time.Time `json:"created_at"`                   // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`                   // 修改时间
}

func (m *SignLog) TableName() string {
	return "user_sign_log"
}
