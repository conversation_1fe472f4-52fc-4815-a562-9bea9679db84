package sign

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateSignLogWithTx 记录签到日志
func (e *Entry) CreateSignLogWithTx(tx *gorm.DB, m *SignLog) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UserLastSignList 用户最近签到数据
func (e *Entry) UserLastSignList(ctx *gin.Context, uid uint64, date []string) ([]*SignLog, error) {
	ret := []*SignLog{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&SignLog{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", uid).
		Where("sign_date in (?)", date).
		Where("continue_sign_days <> ?", LimitDays).
		Find(&ret).Error; err != nil && err != gorm.ErrRecordNotFound {
		return ret, err
	}
	return ret, nil
}
