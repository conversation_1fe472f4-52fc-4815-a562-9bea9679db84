package sign

import (
	"sync"

	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"

	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	// SignRepo
	// TideRepo
	// CardRepo
	// RedisSignRepo
	// RedisTideRepo
	// RedisCardRepo
}

type SignRepo interface {
	// GetSignConfigList 获取签到配置列表
	GetSignConfigList() ([]*SignConfig, error)
	// UserSign 用户签到
	UserSign(uid uint64, tideVal, currentTideVal, days uint32, date string) error
	// CreateSignLogWithTx 记录签到日志
	CreateSignLogWithTx(*gorm.DB, *SignLog) (uint64, error)
	// UserLastSignList 用户最近签到数据
	UserLastSignList(uid uint64, date []string) ([]*SignLog, error)
}

type RedisSignRepo interface {
	// RedisGetSignConfigList redis获取签到配置列表
	RedisGetSignConfigList() ([]*SignConfig, error)
	// RedisReloadSignConfigList redis重载签到配置列表
	RedisReloadSignConfigList() ([]*SignConfig, error)
	// RedisUpdateUserSign 更新用户签到缓存
	RedisUpdateUserSign(uid uint64, data *UserSignCache) error
	// RedisGetUserSignInfo 获取用户签到缓存信息
	RedisGetUserSignInfo(uid uint64) (*UserSignCache, error)
	// RedisReloadUserSignInfo 从数据库中重载签到缓存
	RedisReloadUserSignInfo(uid uint64) *UserSignCache
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultEntry         Repo
	defaultEntry *Entry

	defaultEntryInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {

	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
