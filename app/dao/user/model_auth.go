package user

import "time"

type AuthType uint32

const (
	AtWxApplet AuthType = 1 // 微信小程序
)

type Auth struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	UserID    uint64    `json:"user_id,omitempty"`
	AuthType  uint32    `json:"auth_type,omitempty"`
	AuthUid   string    `json:"auth_uid,omitempty"`
	AuthPid   string    `json:"auth_pid,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type UserAuth struct {
	*Model
	AuthID   uint64 `json:"auth_id,omitempty"`
	AuthType uint32 `json:"auth_type,omitempty"`
	AuthUid  string `json:"auth_uid,omitempty"`
	AuthPid  string `json:"auth_pid,omitempty"`
}

func (m *Auth) TableName() string {
	return "user_auth"
}

type AuthFilter struct {
	UserID   uint64
	UserIDs  []uint64
	AuthType uint32
	AuthUid  string
	Status   uint32
}

type UserAuthList []*UserAuth

func (ml UserAuthList) GetIdMap() map[uint64]*UserAuth {
	retMap := make(map[uint64]*UserAuth, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
