package wxpay

import (
	"blind_box/app/common/dbs"
	"fmt"
	"time"
)

type (
	WxType uint32
)

const (
	WxApplet WxType = 1 // 微信小程序
)

type Model struct {
	ID           uint64    `gorm:"primarykey" json:"id"`
	BillNo       string    `json:"bill_no,omitempty"`
	UserID       uint64    `json:"user_id,omitempty"`        // 用户id
	Action       uint32    `json:"action,omitempty"`         // 1支付 2退款
	PayType      uint32    `json:"pay_type,omitempty"`       // 1全款 2定金 3尾款
	WxType       uint32    `json:"wx_type,omitempty"`        // 1微信小程序
	OrderID      uint64    `json:"order_id,omitempty"`       // 订单id
	OrderGoodsID uint64    `json:"order_goods_id,omitempty"` // 订单商品id
	Amount       uint64    `json:"amount,omitempty"`         // 总金额(分)
	ValidAmount  uint64    `json:"valid_amount,omitempty"`   // 有效额度(分)
	Status       uint32    `json:"status,omitempty"`         // 1成功 2失败
	CreateBy     uint64    `json:"create_by,omitempty"`      // 创建人-用户自己创建时为0
	Remark       string    `json:"remark,omitempty"`         // 备注
	IsDeleted    uint32    `json:"is_deleted,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_wxpay_bill"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	Ids           []uint64
	UserID        uint64
	Action        uint32
	PayType       uint32
	WxType        uint32
	Status        uint32
	OrderID       uint64
	OrderGoodsID  uint64
	OrderGoodsIds []uint64
	Valid         bool
	Invalid       bool
	Sort          dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetOrderGoodsIdMap() map[uint64]ModelList {
	retMap := map[uint64]ModelList{}
	for _, val := range ml {
		retMap[val.OrderGoodsID] = append(retMap[val.OrderGoodsID], val)
	}
	return retMap
}

func (ml ModelList) GetOgidEmptyMap() map[uint64]struct{} {
	retMap := map[uint64]struct{}{}
	for _, val := range ml {
		if _, ok := retMap[val.OrderGoodsID]; !ok {
			retMap[val.OrderGoodsID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetOgidMapStrBillId() map[uint64][]string {
	retMap := map[uint64][]string{}
	for _, val := range ml {
		retMap[val.OrderGoodsID] = append(retMap[val.OrderGoodsID], fmt.Sprintf("%d", val.ID))
	}
	return retMap
}
