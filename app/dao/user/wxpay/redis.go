package wxpay

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/common"
	payLog "blind_box/app/dao/wechat/pay_log"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisSetWxpayOrderInfo .
func (e *Entry) RedisSetWxpayOrderInfo(ctx *gin.Context, billNo string, payData common.WxpayOrderInfo) error {
	var (
		cacheKey   = redisPkg.GetWxpayOrderInfoKey(billNo)
		expireTime = dbs.GetRedisExpireTime(dbs.RedisExpireTimeTwoHour)
		cacheData  []byte
		err        error
	)

	if cacheData, err = json.Marshal(payData); err != nil {
		return err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return err
	}
	return nil
}

// RedisGetWxpayOrderInfo .
func (e *Entry) RedisGetWxpayOrderInfo(ctx *gin.Context, billNo string) (common.WxpayOrderInfo, error) {
	var (
		cacheKey  = redisPkg.GetWxpayOrderInfoKey(billNo)
		ret       = common.WxpayOrderInfo{}
		wxReq     = &payLog.Model{}
		cacheData string
		err       error
	)

	if cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result(); err != nil {
		if err == redis.Nil {
			if wxReq, err = e.WxPayRepo.FetchByUdx(ctx, billNo); err != nil {
				return ret, err
			}
			cacheData = wxReq.OutEntityInfo
		} else {
			return ret, err
		}
	}

	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RedisGetWxpayOrderInfo json error")
		return ret, ecode.JsonMarshalErr
	}
	return ret, nil
}

// RedisDelWxpayOrderInfo .
func (e *Entry) RedisDelWxpayOrderInfo(ctx *gin.Context, billNo string) error {
	cacheKey := redisPkg.GetWxpayOrderInfoKey(billNo)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
