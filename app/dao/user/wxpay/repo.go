package wxpay

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m []*Model) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(tx *gorm.DB, id uint64, data map[string]interface{}) error {
	query := tx.Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(tx *gorm.DB, f *Filter, data map[string]interface{}) error {
	query := tx.Model(&Model{})
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if f.Action != 0 {
		query.Where("action = ?", f.Action)
	}
	if f.OrderGoodsID != 0 {
		query.Where("order_goods_id = ?", f.OrderGoodsID)
	}
	if len(f.OrderGoodsIds) != 0 {
		query.Where("order_goods_id in ?", f.OrderGoodsIds)
	}
	if f.PayType != 0 {
		query.Where("pay_type = ?", f.PayType)
	}
	if f.WxType != 0 {
		query.Where("wx_type = ?", f.WxType)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.Valid {
		query.Where("valid_amount > 0")
	}
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildBillQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}
	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if f.Action != 0 {
		query.Where("action = ?", f.Action)
	}
	if f.PayType != 0 {
		query.Where("pay_type = ?", f.PayType)
	}
	if f.WxType != 0 {
		query.Where("wx_type = ?", f.WxType)
	}
	if f.OrderID != 0 {
		query.Where("order_id = ?", f.OrderID)
	}
	if f.OrderGoodsID != 0 {
		query.Where("order_goods_id = ?", f.OrderGoodsID)
	}
	if len(f.OrderGoodsIds) != 0 {
		query.Where("order_goods_id in ?", f.OrderGoodsIds)
	}

	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.Valid {
		query.Where("valid_amount > 0")
	}
	if f.Invalid {
		query.Where("valid_amount = 0")
	}
	return query
}

// BillPageList .
func (e *Entry) BillPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildBillQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindBillByFilter .
func (e *Entry) FindBillByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildBillQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
