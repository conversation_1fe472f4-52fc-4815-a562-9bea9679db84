package wxpay

import (
	"blind_box/app/common/dbs"
	payLog "blind_box/app/dao/wechat/pay_log"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	ModelRepo
}

type ModelRepo interface {
	Create(ctx *gin.Context, m *Model) error
	CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error
	BatchCreate(ctx *gin.Context, m []*Model) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error
	UpdateMapByIDWithTx(tx *gorm.DB, id uint64, data map[string]interface{}) error
	UpdateMapByFilterWithTx(tx *gorm.DB, f *Filter, data map[string]interface{}) error
	BillPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error)
	FindBillByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
	WxPayRepo   payLog.Repo
}

var (
	// defaultRepo         Repo
	defaultRepo         *Entry
	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
		WxPayRepo:   payLog.GetRepo(),
	}
}
