package tide

import "time"

// 可兑换道具配置表
type CardConfig struct {
	Id              uint64    `json:"id,omitempty"`
	CardCode        string    `json:"card_code,omitempty"`         // 道具卡code
	CardType        uint32    `json:"card_type,omitempty"`         // 道具卡类型：1 透视卡 2提示卡
	CardNum         uint32    `json:"card_num,omitempty"`          // 道具卡数量
	OriginalTideVal uint32    `json:"original_tide_val,omitempty"` // 原来所需潮气值
	TideVal         uint32    `json:"tide_val,omitempty"`          // 所需潮气值
	ExpireHour      uint32    `json:"expire_hour,omitempty"`       // 兑换后过期时间(小时为单位)
	DayLimit        uint32    `json:"day_limit,omitempty"`         // 每日兑换次数
	Sort            uint32    `json:"sort,omitempty"`              // 排序
	IsDeleted       uint32    `json:"is_deleted,omitempty"`        // 软删
	CreatedAt       time.Time `json:"created_at"`                  // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`                  // 修改时间
}

func (m *CardConfig) TableName() string {
	return "user_tide_card_config"
}
