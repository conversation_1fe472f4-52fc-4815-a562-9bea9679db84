package tide

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type TideValType uint32
type TideSourceType uint32

const (
	TideValTypeAdd         TideValType = 1  // 增加
	TideValTypeSubtract    TideValType = 2  // 减少
	UserVisibleTideLogDays             = 7  // 用户可见潮气值明细天数
	EveryDayDegradeTideVal             = 10 // 每日0点消退潮气值

	TideSourceTypeReg      TideSourceType = 1 // 注册奖励
	TideSourceTypeSign     TideSourceType = 2 // 每日签到
	TideSourceTypeTask     TideSourceType = 3 // 每日任务
	TideSourceTypeDegrade  TideSourceType = 4 // 每日消退
	TideSourceTypeExchange TideSourceType = 5 // 道具兑换
	TideSourceTypeSystem   TideSourceType = 6 // 系统发放or扣除
)

type TideLog struct {
	Id          uint64    `json:"id,omitempty"`
	UserId      uint64    `json:"user_id,omitempty"`       // 用户id
	TideValType uint32    `json:"tide_val_type,omitempty"` // 潮气值类型: 1增加 2减少
	TideVal     uint32    `json:"tide_val,omitempty"`      // 潮气值
	Source      uint32    `json:"source,omitempty"`        // 潮气值来源: 1注册奖励 2每日签到 3每日任务 4每日消退 5道具兑换 6系统发放or扣除
	SourceId    uint64    `json:"source_id,omitempty"`     // 来源id
	Remark      string    `json:"remark,omitempty"`        // 备注
	IsDeleted   uint32    `json:"is_deleted,omitempty"`    // 软删
	CreatedAt   time.Time `json:"created_at"`              // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`              // 修改时间
}

func (m *TideLog) TableName() string {
	return "user_tide_log"
}

func (m *TideLog) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type LogFilter struct {
	UserId    uint64
	Source    uint32
	SourceId  uint64
	StartTime int64
	EndTime   int64
	Date      string
	Sort      dbs.CommonSort
}

type TideLogList []*TideLog

func (ml TideLogList) GetUids() []uint64 {
	ret := lo.Map(ml, func(item *TideLog, idx int) uint64 {
		return item.UserId
	})
	ret = lo.Uniq(ret)
	return ret
}

var (
	TideSourceSlice = []TideSourceType{
		TideSourceTypeReg,
		TideSourceTypeSign,
		TideSourceTypeTask,
		TideSourceTypeDegrade,
		TideSourceTypeExchange,
		TideSourceTypeSystem,
	}
	TideSourceMap = map[uint32]string{
		uint32(TideSourceTypeReg):      "注册奖励",
		uint32(TideSourceTypeSign):     "每日签到",
		uint32(TideSourceTypeTask):     "每日任务",
		uint32(TideSourceTypeDegrade):  "每日消退",
		uint32(TideSourceTypeExchange): "道具兑换",
		uint32(TideSourceTypeSystem):   "系统", // 系统发放or扣除
	}
)

type TideSource struct {
	Id   uint32 `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// AdminTideSourceList 获取潮气值来源列表
func (e *Entry) AdminTideSourceList() (list []*TideSource) {
	for _, val := range TideSourceSlice {
		list = append(list, &TideSource{
			Id:   uint32(val),
			Name: TideSourceMap[uint32(val)],
		})
	}
	return
}
