package tide

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/golang-module/carbon/v2"
)

// RedisGetCardConfigInfo redis获取单个配置项信息
func (e *Entry) RedisGetCardConfigInfo(ctx *gin.Context, tcConfigId uint64) (*CardConfig, error) {
	list, err := e.RedisGetCardConfigList(ctx)
	if err != nil {
		return e.GetCardConfigById(ctx, tcConfigId)
	}
	for _, info := range list {
		if info.Id == tcConfigId {
			return info, nil
		}
	}
	return e.GetCardConfigById(ctx, tcConfigId)
}

// RedisGetCardConfigList redis获取可兑换道具卡配置
func (e *Entry) RedisGetCardConfigList(ctx *gin.Context) ([]*CardConfig, error) {
	cacheKey := redisPkg.TideCardConfigListKey
	ret := []*CardConfig{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadCardConfigList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Unmarshal err cacheKey:%v", cacheKey)
		return nil, err
	}
	return ret, nil
}

// RedisReloadCardConfigList redis重载可兑换道具卡配置
func (e *Entry) RedisReloadCardConfigList(ctx *gin.Context) ([]*CardConfig, error) {
	var (
		cacheKey   = redisPkg.TideCardConfigListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = []*CardConfig{}
		cacheData  []byte
		err        error
	)
	list, err := e.GetCardConfigList(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Marshal err")
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadCardConfigList err")
		return tmp, err
	}
	return list, nil
}

// RedisGetCountUserExchangeCard
func (e *Entry) RedisGetCountUserExchangeCard(ctx *gin.Context, uid, tcConfigId uint64) (uint64, error) {
	cacheKey := redisPkg.GetUserTideExchangeCardKey(uid)
	ret, err := e.RedisCli.HGet(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", tcConfigId)).Int64()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadCountUserExchangeCard(ctx, uid, tcConfigId)
		}
		log.Ctx(ctx).WithError(err).Error("IsGetUserRegTide err")
		return 0, err
	}
	return uint64(ret), nil
}

// RedisReloadCountUserExchangeCard
func (e *Entry) RedisReloadCountUserExchangeCard(ctx *gin.Context, uid, tcConfigId uint64) (uint64, error) {
	cacheKey := redisPkg.GetUserTideExchangeCardKey(uid)
	ret, err := e.CountExchangeLog(ctx, &ExchangeLogFilter{
		UserId:     uid,
		TcConfigId: tcConfigId,
		Date:       carbon.Now().ToDateString(),
	})
	if err != nil {
		return 0, err
	}
	pipe := e.RedisCli.Pipeline()
	pipe.HSet(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", tcConfigId), ret)
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.GetRedisEndOfDayExpireTime())
	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadCountUserExchangeCard err")
		return 0, err
	}
	return uint64(ret), nil
}

// RedisClearCountUserExchangeCard
func (e *Entry) RedisClearCountUserExchangeCard(ctx *gin.Context, uid, tcConfigId uint64) error {
	cacheKey := redisPkg.GetUserTideExchangeCardKey(uid)
	e.RedisCli.HDel(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", tcConfigId))
	return nil
}
