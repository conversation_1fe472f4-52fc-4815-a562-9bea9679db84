package tide

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

// CreateTideLogWithTx 记录潮气值日志
func (e *Entry) CreateTideLogWithTx(tx *gorm.DB, m *TideLog) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *LogFilter) *gorm.DB {
	query := tx.Model(&TideLog{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.UserId > 0 {
		query.Where("user_id = ?", f.UserId)
	}
	if f.Source > 0 {
		query.Where("source = ?", f.Source)
	}
	if f.SourceId > 0 {
		query.Where("source_id = ?", f.SourceId)
	}
	if f.StartTime > 0 {
		query.Where("created_at >= ?", carbon.CreateFromTimestamp(f.StartTime))
	}
	if f.EndTime > 0 {
		query.Where("created_at <= ?", carbon.CreateFromTimestamp(f.EndTime))
	}
	if f.Date != "" {
		query.Where("DATE_FORMAT(created_at, '%Y-%m-%d') = ?", f.Date)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *LogFilter, page, limit int) (total int64, list TideLogList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList error")
		return 0, nil, err
	}
	return total, list, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *LogFilter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}
