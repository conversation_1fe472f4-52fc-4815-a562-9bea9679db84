package tide

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateExchangeLogWithTx 记录兑换日志
func (e *Entry) CreateExchangeLogWithTx(ctx *gin.Context, tx *gorm.DB, m *CardExchangeLog) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// CountExchangeLog
func (e *Entry) CountExchangeLog(ctx *gin.Context, filter *ExchangeLogFilter) (total int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&CardExchangeLog{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if filter.UserId > 0 {
		query.Where("user_id = ?", filter.UserId)
	}
	if filter.TcConfigId > 0 {
		query.Where("tc_config_id = ?", filter.TcConfigId)
	}
	if filter.Date != "" {
		query.Where("exchange_date = ?", filter.Date)
	}
	query.Count(&total)
	return
}
