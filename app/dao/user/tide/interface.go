package tide

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

const (
	UserTideValMax = 8000
	UserTideValMin = 0
)

type Repo interface {
}
type TideRepo interface {
	// // GetTideTaskConfigList 获取每日任务配置列表
	// GetTideTaskConfigList() ([]*TideTaskConfig, error)
	// // GetTideTaskConfigById 根据任务id获取任务信息
	// GetTideTaskConfigById(taskId uint64) (data *TideTaskConfig, err error)

	// // CreateTideLogWithTx 记录潮气值日志
	// CreateTideLogWithTx(*gorm.DB, *TideLog) error
	// // CountTideLog 统计潮气值日志
	// CountTideLog(filter *LogFilter) (int64, error)

	// // GetRealTideVal 获取真实潮气值(潮气值超过100,任务完成,tide log 不记录, 未超过100,按100补差值)
	// GetRealTideVal(tideVal, currentTideVal int32, tideValType TideValType) uint32
}

type RedisTideRepo interface {
	// // RedisGetTideTaskConfigInfo redis获取潮气值每日任务信息
	// RedisGetTideTaskConfigInfo(taskId uint64) (*TideTaskConfig, error)
	// // RedisGetTideTaskConfigList redis获取潮气值每日任务列表
	// RedisGetTideTaskConfigList() ([]*TideTaskConfig, error)
	// // RedisReloadTideTaskConfigList redis重载潮气值每日任务列表
	// RedisReloadTideTaskConfigList() ([]*TideTaskConfig, error)

	// // IsGetUserRegTide 是否已领取过用户注册奖励
	// IsGetUserRegTide(uid uint64) (bool, error)
	// // RedisReloadUserGetRegTide
	// RedisReloadUserGetRegTide(uid uint64) (bool, error)
	// // RedisClearUserGetRegTide
	// RedisClearUserGetRegTide(uid uint64) error

	// // IsGetUserTaskTide 是否已领取过完成任务奖励
	// IsGetUserTaskTide(uid, taskId uint64) (bool, error)
	// // RedisReloadUserGetTaskTide
	// RedisReloadUserGetTaskTide(uid, taskId uint64) (bool, error)
	// // RedisClearUserGetTaskTide
	// RedisClearUserGetTaskTide(uid, taskId uint64) error

	// // RedisSetUserTideTaskStatus 【摇盒-抽盒】存入缓存
	// RedisSetUserTideTaskStatus(uid uint64, taskType TideTaskType, incr int) error
	// // RedisGetUserTideTaskStatus 获取【摇盒-抽盒】缓存信息
	// RedisGetUserTideTaskStatus(uid uint64, taskType TideTaskType) (uint32, error)
}

type CardRepo interface {
	// // GetCardConfigList 获取道具卡配置列表
	// GetCardConfigList() ([]*CardConfig, error)
	// // GetCardConfigById 根据卡片id获取卡片信息
	// GetCardConfigById(tcConfigId uint64) (data *CardConfig, err error)

	// // UserTideExchangeCard 用户使用潮气值兑换道具卡
	// UserTideExchangeCard(uid, tcConfigId uint64, tideVal, currentTideVal uint32) (err error)
	// // CreateExchangeLogWithTx 记录兑换日志
	// CreateExchangeLogWithTx(*gorm.DB, *CardExchangeLog) (uint64, error)
	// // CountExchangeLog 统计道具卡兑换日志
	// CountExchangeLog(filter *ExchangeLogFilter) (int64, error)
}

type RedisCardRepo interface {
	// // RedisGetCardConfigInfo redis获取单个配置项信息
	// RedisGetCardConfigInfo(tcConfigId uint64) (*CardConfig, error)
	// // RedisGetCardConfigList redis获取可兑换道具卡配置
	// RedisGetCardConfigList() ([]*CardConfig, error)
	// // RedisReloadCardConfigList redis重载可兑换道具卡配置
	// RedisReloadCardConfigList() ([]*CardConfig, error)

	// // RedisGetCountUserExchangeCard
	// RedisGetCountUserExchangeCard(uid, tcConfigId uint64) (uint64, error)
	// // RedisReloadCountUserExchangeCard
	// RedisReloadCountUserExchangeCard(uid, tcConfigId uint64) (uint64, error)
	// // RedisClearCountUserExchangeCard
	// RedisClearCountUserExchangeCard(uid, tcConfigId uint64) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultEntry         Repo
	defaultEntry *Entry

	defaultEntryInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}

// GetRealTideVal 获取真实潮气值(潮气值超过100,任务完成,tide log 不记录, 未超过100,按100补差值)
func (e *Entry) GetRealTideVal(tideVal, currentTideVal int32, tideValType TideValType) uint32 {
	var realTideVal int32
	if currentTideVal > UserTideValMax || currentTideVal < UserTideValMin {
		return uint32(realTideVal)
	}
	realTideVal = tideVal
	if tideValType == TideValTypeAdd {
		if tideVal+currentTideVal > UserTideValMax {
			realTideVal = UserTideValMax - currentTideVal
		}
	} else {
		if currentTideVal-tideVal < UserTideValMin {
			realTideVal = currentTideVal - UserTideValMin
		}
	}
	return uint32(realTideVal)
}
