package tide

import (
	"blind_box/app/common/dbs"
	"time"
)

type TideTaskType uint32

const (
	TideTaskTypeShake TideTaskType = 1 // 摇一摇
	TideTaskTypeDraw  TideTaskType = 2 // 抽盒
)

// 每日任务配置表
type TideTaskConfig struct {
	Id            uint64    `json:"id,omitempty"`
	Title         string    `json:"title,omitempty"`          // 标题
	Cover         string    `json:"cover,omitempty"`          // 封面图
	TaskType      uint32    `json:"task_type,omitempty"`      // 任务类型: 1摇一摇 2抽盒
	AccomplishNum uint32    `json:"accomplish_num,omitempty"` // 完成次数
	TideVal       uint32    `json:"tide_val,omitempty"`       // 奖励的潮气值
	Sort          uint32    `json:"sort,omitempty"`           // 排序
	IsDeleted     uint32    `json:"is_deleted,omitempty"`     // 软删
	CreatedAt     time.Time `json:"created_at"`               // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`               // 修改时间
}

func (m *TideTaskConfig) TableName() string {
	return "user_tide_task_config"
}

type TaskConfigFilter struct {
	ID   uint64
	Sort dbs.CommonSort
}
