package tide

import "time"

type CardExchangeLog struct {
	Id           uint64    `json:"id,omitempty"`
	UserId       uint64    `json:"user_id,omitempty"`       // 用户id
	TcConfigId   uint64    `json:"tc_config_id,omitempty"`  // 道具卡配置表id
	ExchangeDate string    `json:"exchange_date,omitempty"` // 兑换日期
	Remark       string    `json:"remark,omitempty"`        // 备注(道具卡标识json)
	IsDeleted    uint32    `json:"is_deleted,omitempty"`    // 软删
	CreatedAt    time.Time `json:"created_at"`              // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`              // 修改时间
}

func (m *CardExchangeLog) TableName() string {
	return "user_tide_card_exchange_log"
}

type ExchangeLogFilter struct {
	UserId     uint64
	TcConfigId uint64
	Date       string
}
