package tide

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetCardConfigList 获取道具卡配置列表
func (e *Entry) GetCardConfigList(ctx *gin.Context) (list []*CardConfig, err error) {
	if err = e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&CardConfig{}).
		Select("id", "card_code", "card_type", "card_num", "original_tide_val", "tide_val", "expire_hour", "day_limit").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Order("sort asc, id asc").Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetCardConfigList err")
		return
	}
	return
}

// GetCardConfigById 根据卡片id获取卡片信息
func (e *Entry) GetCardConfigById(ctx *gin.Context, id uint64) (*CardConfig, error) {
	ret := &CardConfig{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&CardConfig{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).First(&ret).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return ret, err
		}
		log.Ctx(ctx).WithError(err).Error("GetCardConfigById err")
		return ret, err
	}
	return ret, nil
}
