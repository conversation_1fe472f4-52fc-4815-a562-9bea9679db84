package tide

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func (e *Entry) buildTaskConfigQuery(tx *gorm.DB, f *TaskConfigFilter) *gorm.DB {
	query := tx.Model(&TideTaskConfig{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID > 0 {
		query.Where("id = ?", f.ID)
	}
	return query
}

// FindTaskConfigByFilter .
func (e *Entry) FindTaskConfigByFilter(ctx *gin.Context, f *TaskConfigFilter) ([]*TideTaskConfig, error) {
	query := e.buildTaskConfigQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []*TideTaskConfig{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindTaskConfigByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchTaskConfigByID .
func (e *Entry) FetchTaskConfigByID(ctx *gin.Context, taskId uint64) (*TideTaskConfig, error) {
	query := e.buildTaskConfigQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &TaskConfigFilter{ID: taskId})
	ret := &TideTaskConfig{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchTaskConfigByID error")
		return ret, err
	}
	return ret, nil
}
