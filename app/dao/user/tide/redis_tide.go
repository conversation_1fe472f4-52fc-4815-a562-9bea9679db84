package tide

import (
	"fmt"

	"blind_box/app/common/dbs"
	"blind_box/pkg/ecode"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/golang-module/carbon/v2"
)

// RedisGetTideTaskConfigInfo redis获取潮气值每日任务信息
func (e *Entry) RedisGetTideTaskConfigInfo(ctx *gin.Context, taskId uint64) (*TideTaskConfig, error) {
	list, err := e.RedisGetTideTaskConfigList(ctx)
	if err != nil {
		return e.FetchTaskConfigByID(ctx, taskId)
	}
	for _, info := range list {
		if info.Id == taskId {
			return info, nil
		}
	}

	return nil, ecode.ParamErr
}

// RedisGetTideTaskConfigList redis获取潮气值每日任务列表
func (e *Entry) RedisGetTideTaskConfigList(ctx *gin.Context) ([]*TideTaskConfig, error) {
	cacheKey := redisPkg.TideTaskConfigListKey
	ret := []*TideTaskConfig{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadTideTaskConfigList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.MaUnmarshalshal err cacheKey:%v", cacheKey)
		return nil, err
	}
	return ret, nil
}

// RedisReloadTideTaskConfigList redis重载潮气值每日任务列表
func (e *Entry) RedisReloadTideTaskConfigList(ctx *gin.Context) ([]*TideTaskConfig, error) {
	var (
		cacheKey   = redisPkg.TideTaskConfigListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = []*TideTaskConfig{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindTaskConfigByFilter(ctx, &TaskConfigFilter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		log.Ctx(ctx).WithError(err).Error("json.Marshal err cacheKey:%v", cacheKey)
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadTideTaskConfigListErr cacheKey:%v", cacheKey)
		return tmp, err
	}
	return list, nil
}

// IsGetUserRegTide 是否已领取过用户注册奖励
func (e *Entry) IsGetUserRegTide(ctx *gin.Context, uid uint64) (bool, error) {
	cacheKey := redisPkg.GetUserGetRegTideKey(uid)
	ret, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Int64()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadUserGetRegTide(ctx, uid)
		}
		log.Ctx(ctx).WithError(err).Error("IsGetUserRegTideErr cacheKey:%v", cacheKey)
		return false, err
	}
	return ret > 0, nil
}

// RedisReloadUserGetRegTide
func (e *Entry) RedisReloadUserGetRegTide(ctx *gin.Context, uid uint64) (bool, error) {
	cacheKey := redisPkg.GetUserGetRegTideKey(uid)
	ret, err := e.CountByFilter(ctx, &LogFilter{
		UserId: uid,
		Source: uint32(TideSourceTypeReg),
	})
	if err != nil {
		return false, err
	}
	cacheData := dbs.False
	if ret > 0 {
		cacheData = dbs.True
	}
	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, cacheData, dbs.GetRedisEndOfDayExpireTime()).Err(); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadUserGetRegTideErr cacheKey:%v", cacheKey)
		return false, err
	}
	return ret > 0, nil
}

// RedisClearUserGetRegTide
func (e *Entry) RedisClearUserGetRegTide(ctx *gin.Context, uid uint64) error {
	cacheKey := redisPkg.GetUserGetRegTideKey(uid)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

// IsGetUserTaskTide 是否已领取过完成奖励
func (e *Entry) IsGetUserTaskTide(ctx *gin.Context, uid, taskId uint64) (bool, error) {
	cacheKey := redisPkg.GetUserGetTaskTideKey(uid)
	ret, err := e.RedisCli.HGet(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", taskId)).Int64()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadUserGetTaskTide(ctx, uid, taskId)
		}
		log.Ctx(ctx).WithError(err).Error("IsGetUserTaskTideErr cacheKey:%v", cacheKey)
		return false, err
	}
	return ret > 0, nil
}

// RedisReloadUserGetTaskTide
func (e *Entry) RedisReloadUserGetTaskTide(ctx *gin.Context, uid, taskId uint64) (bool, error) {
	cacheKey := redisPkg.GetUserGetTaskTideKey(uid)
	ret, err := e.CountByFilter(ctx, &LogFilter{
		UserId:   uid,
		Source:   uint32(TideSourceTypeTask),
		SourceId: taskId,
		Date:     carbon.Now().ToDateString(),
	})
	if err != nil {
		return false, err
	}
	pipe := e.RedisCli.Pipeline()
	pipe.HSet(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", taskId), ret)
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.GetRedisEndOfDayExpireTime())
	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisReloadUserGetTaskTideErr cacheKey:%v", cacheKey)
		return false, err
	}
	return ret > 0, nil
}

// RedisClearUserGetTaskTide
func (e *Entry) RedisClearUserGetTaskTide(ctx *gin.Context, uid, taskId uint64) error {
	cacheKey := redisPkg.GetUserGetTaskTideKey(uid)
	e.RedisCli.HDel(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", taskId))
	return nil
}

// RedisSetUserTideTaskStatus
func (e *Entry) RedisSetUserTideTaskStatus(ctx *gin.Context, uid uint64, taskType TideTaskType, incr uint32) error {
	cacheKey := redisPkg.GetUserTideTaskStatusKey(uid)
	pipe := e.RedisCli.Pipeline()
	pipe.HIncrBy(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", taskType), int64(incr))
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.GetRedisEndOfDayExpireTime())
	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Error("RedisSetUserTideTaskStatusErr cacheKey:%v", cacheKey)
		return err
	}
	return nil
}

// RedisGetUserTideTaskStatus
func (e *Entry) RedisGetUserTideTaskStatus(ctx *gin.Context, uid uint64, taskType TideTaskType) (uint32, error) {
	cacheKey := redisPkg.GetUserTideTaskStatusKey(uid)
	ret, err := e.RedisCli.HGet(ctx.Request.Context(), cacheKey, fmt.Sprintf("%v", taskType)).Uint64()
	if err != nil && err != redis.Nil {
		log.Ctx(ctx).WithError(err).Error("RedisGetUserTideTaskStatus err cacheKey:%v", cacheKey)
		return 0, err
	}
	return uint32(ret), nil
}
