package user

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateWithTx .
func (e *Entry) CreateAuthWithTx(ctx *gin.Context, tx *gorm.DB, m *Auth) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateAuthMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Auth{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildAuthQuery(tx *gorm.DB, f *Auth<PERSON>ilt<PERSON>, sel bool) *gorm.DB {
	query := tx.Table(_Auth.TableName() + " a").
		Joins("LEFT JOIN " + _Model.TableName() + " u ON a.user_id = u.id").Where("u.is_deleted = 0")

	if f.UserID > 0 {
		query.Where("a.user_id = ?", f.UserID)
	}
	if t := f.UserIDs; len(t) > 0 {
		query.Where("a.user_id IN ?", t)
	}
	if f.AuthType > 0 {
		query.Where("a.auth_type = ?", f.AuthType)
	}
	if f.AuthUid != "" {
		query.Where("a.auth_uid = ?", f.AuthUid)
	}
	if f.Status != 0 {
		query.Where("u.status = ? ", f.Status)
	}

	if sel {
		return query.Select("u.*, a.id as auth_id, a.auth_type, a.auth_uid")
	}
	return query
}

// AuthPageList .
func (e *Entry) AuthPageList(ctx *gin.Context, f *AuthFilter, page int, limit int) (total int64, list UserAuthList, err error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false)
	query.Select("a.id").Count(&total)
	if err = query.Select("u.*, a.id as auth_id, a.auth_type, a.auth_uid").Offset((page - 1) * limit).Limit(int(limit)).
		Order("a.id desc").Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindAuthByFilter .
func (e *Entry) FindAuthByFilter(ctx *gin.Context, f *AuthFilter) (UserAuthList, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, true)
	ret := UserAuthList{}
	if err := query.Order("a.id desc").Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchAuthByUdx .
func (e *Entry) FetchAuthByUdx(ctx *gin.Context, authType AuthType, authUid string) (*UserAuth, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false),
		&AuthFilter{AuthType: uint32(authType), AuthUid: authUid}, true)
	ret := &UserAuth{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchAuthByUdx error")
		return ret, err
	}
	return ret, nil
}

// FetchAuthByUdx .
func (e *Entry) FetchAuthByUid(ctx *gin.Context, uid uint64) (*UserAuth, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &AuthFilter{UserID: uid}, true)
	ret := &UserAuth{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchAuthByUdx error")
		return ret, err
	}
	return ret, nil
}

// CountAuthByFilter .
func (e *Entry) CountAuthByFilter(ctx *gin.Context, f *AuthFilter) (num int64, err error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false).Select("a.id")
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
