package user

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisUserInfo .
func (e *Entry) RedisUserInfo(ctx *gin.Context, uid uint64) (*Model, error) {
	var (
		cacheKey  = redisPkg.GetUserInfoKey(uid)
		ret       = &Model{}
		cacheData string
		err       error
	)
	if cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result(); err != nil {
		if err == redis.Nil {
			return e.RedisReloadUserInfo(ctx, uid)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadUserInfo redis重载数据列表
func (e *Entry) RedisReloadUserInfo(ctx *gin.Context, uid uint64) (*Model, error) {
	var (
		cacheKey   = redisPkg.GetUserInfoKey(uid)
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		cacheData  []byte
		uInfo      = &Model{}
		err        error
	)
	if uInfo, err = e.FetchByID(ctx, uid); err != nil {
		return nil, err
	}
	if cacheData, err = json.Marshal(uInfo); err != nil {
		return nil, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return nil, err
	}
	return uInfo, nil
}

// RedisClearUserInfo .
func (e *Entry) RedisClearUserInfo(ctx *gin.Context, uid uint64) error {
	cacheKey := redisPkg.GetUserInfoKey(uid)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

// RedisGetUserToken .
func (e *Entry) RedisGetUserToken(ctx *gin.Context, uid uint64) (string, error) {
	var (
		cacheKey       = redisPkg.GetUserTokenKey(uid)
		cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	)
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return "", nil
		}
		return "", err
	}

	return cacheData, nil
}

// RedisSetUserToken .
func (e *Entry) RedisSetUserToken(ctx *gin.Context, uid uint64, token string) error {
	var (
		cacheKey   = redisPkg.GetUserTokenKey(uid)
		expireTime = dbs.GetRedisExpireTime(dbs.RedisExpireTimeMonth)
	)

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, token, expireTime).Err(); err != nil {
		return err
	}
	return nil
}

// RedisClearUserToken .
func (e *Entry) RedisClearUserToken(ctx *gin.Context, uid uint64) error {
	cacheKey := redisPkg.GetUserTokenKey(uid)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
