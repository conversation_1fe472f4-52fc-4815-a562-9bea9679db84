package user

import (
	"blind_box/app/common/dbs"
	userDto "blind_box/app/dto/user"
	"blind_box/pkg/helper"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"github.com/samber/lo"
)

var (
	_Model = &Model{}
	_Auth  = &Auth{}
)

type Model struct {
	ID            uint64     `gorm:"primarykey" json:"id"`
	Nickname      string     `json:"nickname,omitempty"`
	Mobile        string     `json:"mobile,omitempty"`
	CountryCode   string     `json:"country_code,omitempty"`
	Email         string     `json:"email,omitempty"`
	Avatar        dbs.CdnImg `json:"avatar,omitempty"`
	Status        uint32     `json:"status,omitempty"`
	TideVal       uint32     `json:"tide_val,omitempty"` // 潮玩值
	Points        uint64     `json:"points"`             // 积分余额
	LastLoginTime int64      `json:"last_login_time,omitempty"`
	IsDeleted     uint32     `json:"is_deleted,omitempty"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetLastLoginTime() string {
	if m.LastLoginTime == 0 {
		return ""
	}
	return carbon.CreateFromTimestamp(m.LastLoginTime).ToDateTimeString()
}

func (m *Model) ToUserInfoResp(ctx *gin.Context) *userDto.AdminUserListItem {
	ret := &userDto.AdminUserListItem{
		ID:            m.ID,
		Nickname:      m.Nickname,
		Avatar:        helper.GetImageCdnUrl(ctx, m.Avatar),
		Mobile:        m.Mobile,
		Email:         m.Email,
		Status:        m.Status,
		TideVal:       m.TideVal,
		Points:        m.Points, // 添加积分字段
		LastLoginTime: m.GetLastLoginTime(),
		LevelInfo:     &userDto.AdminLevelListItem{},
	}

	return ret
}

type Filter struct {
	ID       uint64
	NotID    uint64
	IDS      []uint64
	UID      uint64
	Nickname string
	Mobile   string
	Email    string
	Status   uint32
	Sort     dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
