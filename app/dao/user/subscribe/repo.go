package subscribe

import (
	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}, {Name: "entity_id"}, {Name: "entity_type"}},
			DoUpdates: clause.AssignmentColumns([]string{"status"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// DelByEntity .
func (e *Entry) DelByEntity(ctx *gin.Context, uid, entityId uint64, entityType EntityType) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).
		Where("user_id = ? and entity_id = ? and entity_type = ?", uid, entityId, entityType).
		Delete(&Model{}).Error; err != nil {
		return err
	}

	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByIds(ctx *gin.Context, ids []uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), ids, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, ids []uint64, data map[string]interface{}) error {
	if len(ids) == 0 {
		return nil
	}
	if err := tx.Model(&Model{}).Where("id IN (?)", ids).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{})
	if f.UserId != 0 {
		query.Where("user_id = ?", f.UserId)
	}
	if f.EntityId != 0 {
		query.Where("entity_id = ?", f.EntityId)
	}
	if f.EntityType != 0 {
		query.Where("entity_type = ?", f.EntityType)
	}
	if f.EntityRelationId != 0 {
		query.Where("entity_relation_id = ?", f.EntityRelationId)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	ret := ModelList{}
	if err := query.Order(f.Sort).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByEntity(ctx *gin.Context, uid, entityId uint64, entityType EntityType) (*Model, error) {
	ret := &Model{}
	if err := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false),
		&Filter{UserId: uid, EntityId: entityId, EntityType: uint32(entityType)}).
		Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
