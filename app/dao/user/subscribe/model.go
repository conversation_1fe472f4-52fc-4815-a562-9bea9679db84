package subscribe

import (
	"blind_box/app/common/dbs"
	"time"
)

type SubStatus uint32
type EntityType uint32

const (
	SubStatusWaitRemind SubStatus = 1 // 待提醒
	SubStatusReminded   SubStatus = 2 // 已提醒
	SubStatusInvalid    SubStatus = 3 // 已失效

	EntityTypeBoxSaleStart EntityType = 1 // 盲盒开售提醒
	EntityTypeSkuHasStock  EntityType = 2 // 商品到货通知
	EntityTypeActStart     EntityType = 3 // 活动开始提醒
)

type Model struct {
	Id               uint64    `json:"id,omitempty"`
	UserId           uint64    `json:"user_id,omitempty"`            // 用户id
	EntityId         uint64    `json:"entity_id,omitempty"`          // 实体id
	EntityType       uint32    `json:"entity_type,omitempty"`        // 实体类型
	EntityRelationId uint64    `json:"entity_relation_id,omitempty"` // 实体关联id
	Status           uint32    `json:"status,omitempty"`             // 状态 1待提醒 2已提醒 3已过期
	CreatedAt        time.Time `json:"created_at"`                   // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`                   // 修改时间
}

func (m *Model) TableName() string {
	return "user_subscribe"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type ModelList []*Model

func (ml ModelList) GetUserEntityRelationIds() ([]uint64, []uint64, []uint64) {
	userIds := []uint64{}
	userMap := make(map[uint64]struct{}, 0)

	entityIds := []uint64{}
	entityMap := make(map[uint64]struct{}, 0)

	spuIds := []uint64{}
	spuMap := make(map[uint64]struct{}, 0)

	for _, val := range ml {
		if _, ok := userMap[val.UserId]; !ok {
			userIds = append(userIds, val.UserId)
			userMap[val.UserId] = struct{}{}
		}
		if _, ok := entityMap[val.EntityId]; !ok {
			entityIds = append(entityIds, val.EntityId)
			entityMap[val.EntityId] = struct{}{}
		}

		if val.EntityType == uint32(EntityTypeSkuHasStock) {
			if _, ok := spuMap[val.EntityRelationId]; !ok {
				spuIds = append(spuIds, val.EntityRelationId)
				spuMap[val.EntityRelationId] = struct{}{}
			}
		}

	}
	return userIds, entityIds, spuIds
}

type Filter struct {
	Id               uint64
	UserId           uint64
	EntityId         uint64
	EntityType       uint32
	EntityRelationId uint64
	Status           uint32
	Sort             dbs.CommonSort
}
