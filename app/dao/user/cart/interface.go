package cart

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/common"
	"sync"
)

var (
	spuTabler common.Tabler
)

func NewSpuModel(tab common.Tabler) {
	spuTabler = tab
}

type Repo interface {
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	// defaultRepo         Repo
	defaultRepo         *Entry
	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
