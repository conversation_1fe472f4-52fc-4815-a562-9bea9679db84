package cart

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"unit_num", "delivery_id", "shop_id"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapById .
func (e *Entry) UpdateMapById(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIdWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIdWithTx .
func (e *Entry) UpdateMapByIdWithTx(tx *gorm.DB, id uint64, data map[string]interface{}) error {
	query := tx.Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByFilter .
func (e *Entry) UpdateMapByFilter(ctx *gin.Context, userId uint64, f *Filter, data map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), userId, f, data)
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(tx *gorm.DB, userId uint64, f *Filter, data map[string]interface{}) error {
	query := tx.Model(&Model{}).Where("user_id = ?", userId)
	if f.DeliveryId != 0 {
		query.Where("delivery_id = ?", f.DeliveryId)
	}
	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// DelByFilter .
func (e *Entry) DelByFilter(ctx *gin.Context, f *Filter) error {
	if f.UserId == 0 {
		return nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("user_id = ?", f.UserId)
	if len(f.Ids) > 0 {
		query.Where("id in ?", f.Ids)
	}

	if err := query.Updates(map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	}).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DelByFilter err")
		return err
	}
	return nil
}

func (e *Entry) buildJoinQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Table(_Model.TableName() + " c").
		Joins("LEFT JOIN " + _SpuModel.TableName() + " p ON c.spu_id = p.id").
		Joins("LEFT JOIN " + _BatchModel.TableName() + " b ON p.batch_id = b.id").
		Where("p.is_deleted = 0 and c.is_deleted = 0 and b.is_deleted = 0")

	if f.UserId != 0 {
		query.Where("c.user_id = ?", f.UserId)
	}
	if f.SkuId != 0 {
		query.Where("c.sku_id = ?", f.SkuId)
	}
	if f.DeliveryId != 0 {
		query.Where("c.delivery_id = ?", f.DeliveryId)
	}
	if f.SellType != 0 {
		query.Where("b.sell_type = ?", f.SellType)
	}
	if f.SpuTitle != "" {
		query.Where("p.title like ?", "%"+f.SpuTitle+"%")
	}

	return query
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.UserId != 0 {
		query.Where("user_id = ?", f.UserId)
	}
	if f.SpuId != 0 {
		query.Where("spu_id = ?", f.SpuId)
	}
	if f.SkuId != 0 {
		query.Where("sku_id = ?", f.SkuId)
	}
	if f.DeliveryId != 0 {
		query.Where("delivery_id = ?", f.DeliveryId)
	}
	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildJoinQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	orderStr := "c.id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	query.Select("c.id").Count(&total)
	if err = query.Select("c.*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(orderStr).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	ret := ModelList{}
	if err := query.Order(f.Sort).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where("id = ?", id).
		Order("id desc").Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FeatchByFilterSort .
func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	ret := &Model{}
	if err := query.Order(orderStr).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildJoinQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Select("c.id").Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// SumByFilter .
func (e *Entry) SumByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	if f.UserId == 0 {
		return 0, nil
	}
	query := e.buildJoinQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Select("coalesce(sum(c.unit_num), 0) as unit_num")

	if err := query.Find(&num).Error; err != nil {
		return 0, err
	}
	return
}
