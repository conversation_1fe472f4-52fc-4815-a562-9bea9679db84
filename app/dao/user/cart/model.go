package cart

import (
	"blind_box/app/common/dbs"
	batchDao "blind_box/app/dao/goods/batch"
	spuDao "blind_box/app/dao/goods/spu"
	"fmt"
	"time"
)

var (
	_Model      = &Model{}
	_SpuModel   = &spuDao.Model{}
	_BatchModel = &batchDao.Model{}
)

type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	UserId     uint64    `json:"user_id,omitempty"`     // 用户id
	SpuId      uint64    `json:"spu_id,omitempty"`      // spuID
	SkuId      uint64    `json:"sku_id,omitempty"`      // skuID
	UnitNum    uint32    `json:"unit_num,omitempty"`    // 单位数量
	DeliveryId uint32    `json:"delivery_id,omitempty"` // 配送方式
	ShopId     uint64    `json:"shop_id,omitempty"`     // 店铺id
	IsDeleted  uint32    `json:"is_deleted,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_cart"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID         uint64
	Ids        []uint64
	UserId     uint64
	SpuId      uint64
	SpuIds     []uint64
	SkuId      uint64
	SkuIds     []uint64
	SpuTitle   string
	SellType   uint32
	DeliveryId uint32
	Sort       dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetSpuSkuIds() ([]uint64, []uint64) {
	var (
		spuIds, skuIds []uint64

		spuMap = map[uint64]struct{}{}
		skuMap = map[uint64]struct{}{}
	)

	for _, val := range ml {
		if _, ok := spuMap[val.SpuId]; !ok {
			spuIds = append(spuIds, val.SpuId)
			spuMap[val.SpuId] = struct{}{}
		}
		if _, ok := skuMap[val.SkuId]; !ok {
			skuIds = append(skuIds, val.SkuId)
			skuMap[val.SkuId] = struct{}{}
		}

	}
	return spuIds, skuIds
}

func (ml ModelList) GetSkuDeliveryMap() map[string]*Model {
	deliveryMap := map[string]*Model{}
	for _, val := range ml {
		key := fmt.Sprintf("%d_%d", val.SkuId, val.DeliveryId)
		deliveryMap[key] = val
	}
	return deliveryMap
}
