package points

import (
	"fmt"
	"time"

	"blind_box/app/common/dbs"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreatePointsLog 创建积分日志
func (e *Entry) CreatePointsLog(ctx *gin.Context, m *PointsLog) error {
	return e.CreatePointsLogWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreatePointsLogWithTx 使用事务创建积分日志
func (e *Entry) CreatePointsLogWithTx(ctx *gin.Context, tx *gorm.DB, m *PointsLog) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *PointsLogFilter) *gorm.DB {
	query := tx.Model(&PointsLog{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID > 0 {
		query.Where("id = ?", f.ID)
	}
	if f.UserID > 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if t := f.UserIDs; len(t) > 0 {
		query.Where("user_id IN (?)", t)
	}
	if f.ActionType > 0 {
		query.Where("action_type = ?", f.ActionType)
	}
	if len(f.ActionTypes) > 0 {
		query.Where("action_type IN (?)", f.ActionTypes)
	}
	if f.SourceType > 0 {
		query.Where("source_type = ?", f.SourceType)
	}
	if f.SourceID > 0 {
		query.Where("source_id = ?", f.SourceID)
	}
	if f.ReferenceLogID != nil {
		query.Where("reference_log_id = ?", *f.ReferenceLogID)
	}
	if f.StartTime > 0 {
		query.Where("created_at >= ?", carbon.CreateFromTimestamp(f.StartTime))
	}
	if f.EndTime > 0 {
		query.Where("created_at <= ?", carbon.CreateFromTimestamp(f.EndTime))
	}
	if f.Date != "" {
		query.Where("DATE_FORMAT(created_at, '%Y-%m-%d') = ?", f.Date)
	}

	// 积分过期相关过滤条件
	if f.IsExpired != nil {
		now := time.Now()
		if *f.IsExpired {
			// 查询已过期的记录
			query.Where("expire_at IS NOT NULL AND expire_at <= ?", now)
		} else {
			// 查询未过期的记录（包括永不过期的记录）
			query.Where("expire_at IS NULL OR expire_at > ?", now)
		}
	}
	if f.ExpireBefore != nil {
		query.Where("expire_at IS NOT NULL AND expire_at <= ?", *f.ExpireBefore)
	}
	if f.ExpireAfter != nil {
		query.Where("expire_at IS NOT NULL AND expire_at >= ?", *f.ExpireAfter)
	}
	if f.OnlyWithExpire {
		query.Where("expire_at IS NOT NULL")
	}

	// 剩余积分相关过滤条件
	if f.HasRemaining != nil {
		if *f.HasRemaining {
			query.Where("remaining_points IS NOT NULL AND remaining_points > 0")
		} else {
			query.Where("remaining_points IS NULL OR remaining_points = 0")
		}
	}
	if f.MinRemainingPoints != nil {
		query.Where("remaining_points >= ?", *f.MinRemainingPoints)
	}

	return query
}

// DataPageList 分页查询积分日志
func (e *Entry) DataPageList(ctx *gin.Context, f *PointsLogFilter, page, limit int) (total int64, list PointsLogList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList error")
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter 根据条件查询积分日志
func (e *Entry) FindByFilter(ctx *gin.Context, f *PointsLogFilter) (PointsLogList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := PointsLogList{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// CountByFilter 根据条件统计积分日志数量
func (e *Entry) CountByFilter(ctx *gin.Context, f *PointsLogFilter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}

// GetUserPointsSum 获取用户积分总计（按来源统计）
func (e *Entry) GetUserPointsSum(ctx *gin.Context, userID uint64, sourceTypes []uint32) (addSum, subtractSum uint64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID)

	if len(sourceTypes) > 0 {
		query = query.Where("source_type IN (?)", sourceTypes)
	}

	// 统计增加的积分
	var addResult struct {
		Total uint64 `json:"total"`
	}
	if err = query.Where("action_type = ?", PointsActionTypeAdd).
		Select("COALESCE(SUM(points), 0) as total").
		Scan(&addResult).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserPointsSum add error")
		return 0, 0, err
	}
	addSum = addResult.Total

	// 统计减少的积分
	var subtractResult struct {
		Total uint64 `json:"total"`
	}
	if err = query.Where("action_type = ?", PointsActionTypeSubtract).
		Select("COALESCE(SUM(points), 0) as total").
		Scan(&subtractResult).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserPointsSum subtract error")
		return 0, 0, err
	}
	subtractSum = subtractResult.Total

	return addSum, subtractSum, nil
}

// GetUserValidPointsBalance 获取用户有效积分余额（实时计算，排除已过期的积分）
func (e *Entry) GetUserValidPointsBalance(ctx *gin.Context, userID uint64) (balance uint64, err error) {
	now := time.Now()

	// 统计未过期的增加积分
	var addResult struct {
		Total uint64 `json:"total"`
	}
	addQuery := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID).
		Where("action_type = ?", PointsActionTypeAdd).
		Where("expire_at IS NULL OR expire_at > ?", now). // 未过期的记录
		Select("COALESCE(SUM(points), 0) as total")

	if err = addQuery.Scan(&addResult).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserValidPointsBalance add error")
		return 0, err
	}

	// 统计所有减少的积分（减少的积分不考虑过期）
	var subtractResult struct {
		Total uint64 `json:"total"`
	}
	subtractQuery := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID).
		Where("action_type = ?", PointsActionTypeSubtract).
		Select("COALESCE(SUM(points), 0) as total")

	if err = subtractQuery.Scan(&subtractResult).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserValidPointsBalance subtract error")
		return 0, err
	}

	// 计算有效余额
	if addResult.Total >= subtractResult.Total {
		balance = addResult.Total - subtractResult.Total
	} else {
		balance = 0 // 余额不能为负数
	}

	log.Ctx(ctx).Info("GetUserValidPointsBalance: UserID=%d, Add=%d, Subtract=%d, Balance=%d",
		userID, addResult.Total, subtractResult.Total, balance)

	return balance, nil
}

// GetExpiredPointsRecords 获取已过期但未处理的积分记录
func (e *Entry) GetExpiredPointsRecords(ctx *gin.Context, limit int) (PointsLogList, error) {
	now := time.Now()
	ret := PointsLogList{}

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("action_type = ?", PointsActionTypeAdd).                  // 只处理增加积分的过期
		Where("expire_at IS NOT NULL AND expire_at <= ?", now).         // 已过期
		Where("remaining_points IS NOT NULL AND remaining_points > 0"). // 还有剩余积分
		Where("is_expired = 0").                                        // 未标记为已过期
		Order("expire_at ASC").                                         // 按过期时间升序
		Limit(limit)

	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetExpiredPointsRecords error")
		return ret, err
	}

	return ret, nil
}

// GetUserEarnLogsForFIFO 获取用户可用于FIFO消费的积分记录（按过期时间排序）
func (e *Entry) GetUserEarnLogsForFIFO(ctx *gin.Context, userID uint64) (PointsLogList, error) {
	now := time.Now()
	ret := PointsLogList{}

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID).
		Where("action_type = ?", PointsActionTypeAdd).                        // 只查询earn类型
		Where("remaining_points IS NOT NULL AND remaining_points > 0").       // 有剩余积分
		Where("(expire_at IS NULL OR expire_at > ?)", now).                   // 未过期（包括永不过期）
		Where("is_expired = 0").                                              // 未标记为已过期
		Order("CASE WHEN expire_at IS NULL THEN 1 ELSE 0 END, expire_at ASC") // 永不过期的放最后，其他按过期时间升序

	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserEarnLogsForFIFO error")
		return ret, err
	}

	return ret, nil
}

// GetUserEarnLogsForFIFOWithLock 获取用户可用于FIFO消费的积分记录（带行级锁，事务版本）
func (e *Entry) GetUserEarnLogsForFIFOWithLock(ctx *gin.Context, tx *gorm.DB, userID uint64) (PointsLogList, error) {
	now := time.Now()
	ret := PointsLogList{}

	// 使用 FOR UPDATE 加行级锁，防止并发读取时数据不一致
	query := tx.Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID).
		Where("action_type = ?", PointsActionTypeAdd).                         // 只查询earn类型
		Where("remaining_points IS NOT NULL AND remaining_points > 0").        // 有剩余积分
		Where("(expire_at IS NULL OR expire_at > ?)", now).                    // 未过期（包括永不过期）
		Where("is_expired = 0").                                               // 未标记为已过期
		Order("CASE WHEN expire_at IS NULL THEN 1 ELSE 0 END, expire_at ASC"). // 永不过期的放最后，其他按过期时间升序
		Clauses(clause.Locking{Strength: "UPDATE"})                            // 添加行级锁

	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserEarnLogsForFIFOWithLock error")
		return ret, err
	}

	return ret, nil
}

// UpdateRemainingPointsWithTx 更新积分记录的剩余数量（事务版本）
func (e *Entry) UpdateRemainingPointsWithTx(ctx *gin.Context, tx *gorm.DB, logID uint64, remainingPoints uint64) error {
	result := tx.Model(&PointsLog{}).
		Where("id = ?", logID).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Update("remaining_points", remainingPoints)

	if result.Error != nil {
		log.Ctx(ctx).WithError(result.Error).Error("UpdateRemainingPointsWithTx error")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no rows affected when updating remaining points for log ID: %d", logID)
	}

	return nil
}

// MarkPointsAsExpiredWithTx 标记积分记录为已过期（事务版本）
func (e *Entry) MarkPointsAsExpiredWithTx(ctx *gin.Context, tx *gorm.DB, logID uint64) error {
	result := tx.Model(&PointsLog{}).
		Where("id = ?", logID).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Updates(map[string]interface{}{
			"is_expired":       1,
			"remaining_points": 0,
		})

	if result.Error != nil {
		log.Ctx(ctx).WithError(result.Error).Error("MarkPointsAsExpiredWithTx error")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no rows affected when marking points as expired for log ID: %d", logID)
	}

	return nil
}

// BatchUpdateRemainingPointsWithTx 批量更新积分记录的剩余数量（事务版本）
func (e *Entry) BatchUpdateRemainingPointsWithTx(ctx *gin.Context, tx *gorm.DB, updates []struct {
	LogID           uint64
	RemainingPoints uint64
}) error {
	if len(updates) == 0 {
		return nil
	}

	// 构建批量更新语句
	for _, update := range updates {
		if err := e.UpdateRemainingPointsWithTx(ctx, tx, update.LogID, update.RemainingPoints); err != nil {
			return err
		}
	}

	return nil
}

// GetUserValidPointsBalanceFromEarnLogs 从earn日志计算用户有效积分余额（更精确的方法）
func (e *Entry) GetUserValidPointsBalanceFromEarnLogs(ctx *gin.Context, userID uint64) (balance uint64, err error) {
	now := time.Now()

	// 统计未过期且有剩余的积分总和
	var result struct {
		Total uint64 `json:"total"`
	}

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&PointsLog{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("user_id = ?", userID).
		Where("action_type = ?", PointsActionTypeAdd).                  // 只查询earn类型
		Where("remaining_points IS NOT NULL AND remaining_points > 0"). // 有剩余积分
		Where("(expire_at IS NULL OR expire_at > ?)", now).             // 未过期
		Where("is_expired = 0").                                        // 未标记为已过期
		Select("COALESCE(SUM(remaining_points), 0) as total")

	if err = query.Scan(&result).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("GetUserValidPointsBalanceFromEarnLogs error")
		return 0, err
	}

	log.Ctx(ctx).Info("GetUserValidPointsBalanceFromEarnLogs: UserID=%d, Balance=%d", userID, result.Total)
	return result.Total, nil
}
