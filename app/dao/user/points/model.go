package points

import (
	"time"

	"blind_box/app/common/dbs"

	"github.com/samber/lo"
)

type PointsActionType uint32
type PointsSourceType uint32

const (
	PointsActionTypeAdd      PointsActionType = 1 // 增加积分 (earn)
	PointsActionTypeSubtract PointsActionType = 2 // 减少积分 (spend)
	PointsActionTypeOffset   PointsActionType = 3 // 抵扣明细 (offset) - 指向earn记录
	PointsActionTypeExpired  PointsActionType = 4 // 过期扣减 (expired)

	PointsSourceTypeReg      PointsSourceType = 1  // 注册奖励
	PointsSourceTypeSign     PointsSourceType = 2  // 每日签到
	PointsSourceTypeTask     PointsSourceType = 3  // 每日任务
	PointsSourceTypeOrder    PointsSourceType = 4  // 订单奖励
	PointsSourceTypeDiscount PointsSourceType = 5  // 积分抵扣
	PointsSourceTypeRefund   PointsSourceType = 6  // 积分退回
	PointsSourceTypeSystem   PointsSourceType = 7  // 系统发放/扣除
	PointsSourceTypeExchange PointsSourceType = 8  // 兑换商品
	PointsSourceTypeExpire   PointsSourceType = 9  // 积分过期
	PointsSourceTypeAdmin    PointsSourceType = 10 // 管理员操作
)

// PointsLog 积分变更日志
type PointsLog struct {
	ID              uint64     `gorm:"primarykey" json:"id"`
	UserID          uint64     `json:"user_id,omitempty"`          // 用户ID
	ActionType      uint32     `json:"action_type,omitempty"`      // 操作类型: 1增加(earn) 2减少(spend) 3抵扣明细(offset) 4过期扣减(expired)
	Points          uint64     `json:"points,omitempty"`           // 变更积分数
	RemainingPoints *uint64    `json:"remaining_points,omitempty"` // 剩余积分数（仅对earn类型有效，初始值=points）
	ReferenceLogID  *uint64    `json:"reference_log_id,omitempty"` // 关联日志ID（offset类型指向对应的earn记录）
	BalanceBefore   uint64     `json:"balance_before,omitempty"`   // 操作前余额
	BalanceAfter    uint64     `json:"balance_after,omitempty"`    // 操作后余额
	SourceType      uint32     `json:"source_type,omitempty"`      // 来源类型
	SourceID        uint64     `json:"source_id,omitempty"`        // 来源ID（如订单ID）
	Remark          string     `json:"remark,omitempty"`           // 备注
	ExpireAt        *time.Time `json:"expire_at,omitempty"`        // 积分过期时间（NULL表示永不过期，仅对earn类型有效）
	IsExpired       uint32     `json:"is_expired,omitempty"`       // 是否已过期 0-未过期 1-已过期
	IsDeleted       uint32     `json:"is_deleted,omitempty"`       // 软删除
	CreatedAt       time.Time  `json:"created_at"`                 // 创建时间
	UpdatedAt       time.Time  `json:"updated_at"`                 // 修改时间
}

func (m *PointsLog) TableName() string {
	return "user_points_log"
}

func (m *PointsLog) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// IsEarnType 判断是否为获取积分类型
func (m *PointsLog) IsEarnType() bool {
	return m.ActionType == uint32(PointsActionTypeAdd)
}

// IsSpendType 判断是否为消费积分类型
func (m *PointsLog) IsSpendType() bool {
	return m.ActionType == uint32(PointsActionTypeSubtract)
}

// IsOffsetType 判断是否为抵扣明细类型
func (m *PointsLog) IsOffsetType() bool {
	return m.ActionType == uint32(PointsActionTypeOffset)
}

// IsExpiredType 判断是否为过期类型
func (m *PointsLog) IsExpiredType() bool {
	return m.ActionType == uint32(PointsActionTypeExpired)
}

// GetRemainingPoints 获取剩余积分数（安全获取）
func (m *PointsLog) GetRemainingPoints() uint64 {
	if m.RemainingPoints == nil {
		return 0
	}
	return *m.RemainingPoints
}

// GetReferenceLogID 获取关联日志ID（安全获取）
func (m *PointsLog) GetReferenceLogID() uint64 {
	if m.ReferenceLogID == nil {
		return 0
	}
	return *m.ReferenceLogID
}

// SetRemainingPoints 设置剩余积分数
func (m *PointsLog) SetRemainingPoints(points uint64) {
	m.RemainingPoints = &points
}

// SetReferenceLogID 设置关联日志ID
func (m *PointsLog) SetReferenceLogID(logID uint64) {
	m.ReferenceLogID = &logID
}

type PointsLogFilter struct {
	ID             uint64
	UserID         uint64
	UserIDs        []uint64 // 支持多个UserID查询
	ActionType     uint32
	ActionTypes    []uint32 // 支持多个ActionType查询
	SourceType     uint32
	SourceID       uint64
	ReferenceLogID *uint64
	StartTime      int64
	EndTime        int64
	Date           string
	// 积分过期相关过滤条件
	IsExpired          *bool      // 是否已过期（true=已过期，false=未过期，nil=不过滤）
	ExpireBefore       *time.Time // 在指定时间前过期
	ExpireAfter        *time.Time // 在指定时间后过期
	OnlyWithExpire     bool       // 只查询有过期时间的记录
	HasRemaining       *bool      // 是否有剩余积分（true=有剩余，false=无剩余，nil=不过滤）
	MinRemainingPoints *uint64    // 最小剩余积分数
	Sort               dbs.CommonSort
}

type PointsLogList []*PointsLog

func (ml PointsLogList) GetUserIDs() []uint64 {
	ret := lo.Map(ml, func(item *PointsLog, idx int) uint64 {
		return item.UserID
	})
	ret = lo.Uniq(ret)
	return ret
}

// 积分来源映射
var (
	PointsSourceSlice = []PointsSourceType{
		PointsSourceTypeReg,
		PointsSourceTypeSign,
		PointsSourceTypeTask,
		PointsSourceTypeOrder,
		PointsSourceTypeDiscount,
		PointsSourceTypeRefund,
		PointsSourceTypeSystem,
		PointsSourceTypeExchange,
		PointsSourceTypeExpire,
		PointsSourceTypeAdmin,
	}

	PointsSourceMap = map[uint32]string{
		uint32(PointsSourceTypeReg):      "注册奖励",
		uint32(PointsSourceTypeSign):     "每日签到",
		uint32(PointsSourceTypeTask):     "每日任务",
		uint32(PointsSourceTypeOrder):    "订单奖励",
		uint32(PointsSourceTypeDiscount): "积分抵扣",
		uint32(PointsSourceTypeRefund):   "积分退回",
		uint32(PointsSourceTypeSystem):   "系统操作",
		uint32(PointsSourceTypeExchange): "兑换商品",
		uint32(PointsSourceTypeExpire):   "积分过期",
		uint32(PointsSourceTypeAdmin):    "管理员操作",
	}

	PointsActionMap = map[uint32]string{
		uint32(PointsActionTypeAdd):      "获取积分",
		uint32(PointsActionTypeSubtract): "消费积分",
		uint32(PointsActionTypeOffset):   "抵扣明细",
		uint32(PointsActionTypeExpired):  "积分过期",
	}
)

type PointsSource struct {
	ID   uint32 `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// GetPointsSourceList 获取积分来源列表
func GetPointsSourceList() []*PointsSource {
	var list []*PointsSource
	for _, val := range PointsSourceSlice {
		list = append(list, &PointsSource{
			ID:   uint32(val),
			Name: PointsSourceMap[uint32(val)],
		})
	}
	return list
}
