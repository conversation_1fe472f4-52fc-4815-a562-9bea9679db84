package points

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Interface 积分日志接口
type PointsLogRepo interface {
	CreatePointsLog(ctx *gin.Context, m *PointsLog) error
	CreatePointsLogWithTx(ctx *gin.Context, tx *gorm.DB, m *PointsLog) error
	DataPageList(ctx *gin.Context, f *PointsLogFilter, page, limit int) (total int64, list PointsLogList, err error)
	FindByFilter(ctx *gin.Context, f *PointsLogFilter) (PointsLogList, error)
	CountByFilter(ctx *gin.Context, f *PointsLogFilter) (num int64, err error)
	GetUserPointsSum(ctx *gin.Context, userID uint64, sourceTypes []uint32) (addSum, subtractSum uint64, err error)
	// 积分余额和过期相关方法
	GetUserValidPointsBalance(ctx *gin.Context, userID uint64) (balance uint64, err error) // 获取用户有效积分余额（实时计算）
	GetExpiredPointsRecords(ctx *gin.Context, limit int) (PointsLogList, error)            // 获取已过期但未处理的积分记录
	// 新增：FIFO消费和过期处理相关方法
	GetUserEarnLogsForFIFO(ctx *gin.Context, userID uint64) (PointsLogList, error)                         // 获取用户可用于FIFO消费的积分记录
	GetUserEarnLogsForFIFOWithLock(ctx *gin.Context, tx *gorm.DB, userID uint64) (PointsLogList, error)    // 获取用户可用于FIFO消费的积分记录（带行级锁）
	UpdateRemainingPointsWithTx(ctx *gin.Context, tx *gorm.DB, logID uint64, remainingPoints uint64) error // 更新积分记录的剩余数量
	MarkPointsAsExpiredWithTx(ctx *gin.Context, tx *gorm.DB, logID uint64) error                           // 标记积分记录为已过期
	BatchUpdateRemainingPointsWithTx(ctx *gin.Context, tx *gorm.DB, updates []struct {
		LogID           uint64
		RemainingPoints uint64
	}) error // 批量更新剩余积分
	GetUserValidPointsBalanceFromEarnLogs(ctx *gin.Context, userID uint64) (balance uint64, err error) // 从earn日志计算用户有效积分余额
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultEntry         Repo
	defaultEntry *Entry

	defaultEntryInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
