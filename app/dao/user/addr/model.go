package addr

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dto/common"
	userDto "blind_box/app/dto/user"
	"time"
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	UserID    uint64    `json:"user_id,omitempty"`
	Consignee string    `json:"consignee,omitempty"`
	Mobile    string    `json:"mobile,omitempty"`
	Area      string    `json:"area,omitempty"`
	Address   string    `json:"address,omitempty"`
	IsDefault uint32    `json:"is_default,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_address"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) ToItemResp() *common.CommonUserAddrInfo {
	item := &common.CommonUserAddrInfo{
		ID:        m.ID,
		UserID:    m.UserID,
		Consignee: m.Consignee,
		Mobile:    m.Mobile,
		Area:      m.Area,
		Address:   m.Address,
		IsDefault: m.IsDefault,
		CreatedAt: m.GetCreatedTime(),
	}
	return item
}

type Filter struct {
	ID        uint64
	NotID     uint64
	IDS       []uint64
	UID       uint64
	IsDefault uint32
	Status    uint32
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

// SetUserAddrReqToModel .
func SetUserAddrReqToModel(UID uint64, req userDto.SetUserAddrReq) *Model {
	m := &Model{
		ID:        req.ID,
		UserID:    UID,
		Consignee: req.Consignee,
		Mobile:    req.Mobile,
		Area:      req.Area,
		Address:   req.Address,
		IsDefault: req.IsDefault,
	}

	return m
}
