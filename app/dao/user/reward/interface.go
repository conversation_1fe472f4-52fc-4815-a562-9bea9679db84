package reward

import (
	"blind_box/app/common/dbs"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
	CreateWithTx(*gorm.DB, *Model) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
