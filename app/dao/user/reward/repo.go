package reward

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateWithTx .
func (e *Entry) CreateWithTx(tx *gorm.DB, m *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{})
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if f.RewardID != 0 {
		query.Where("reward_id = ?", f.RewardID)
	}

	return query
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}
