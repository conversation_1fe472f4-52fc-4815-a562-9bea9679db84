package reward

import (
	"blind_box/app/common/dbs"
	"time"
)

const (
	NewUserRewardID      uint64 = 1
	NewUserRewardTitle   string = "新人奖励"
	NewUserRewardDesc    string = "欢迎加入，您的新人礼已送到"
	NewUserRewardTideVal uint32 = 30
)

var (
	CardCodes = []string{"newpeople_x_ray_card", "newpeople_hint_card"}

	CardCodeMap = map[string]struct {
		Img string
		Num uint32
	}{
		"newpeople_x_ray_card": {
			Img: "https://cdn.mowantoy.com/mws_img/<EMAIL>",
			Num: 1,
		},
		"newpeople_hint_card": {
			Img: "https://cdn.mowantoy.com/mws_img/<EMAIL>",
			Num: 3,
		},
	}
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	UserID    uint64    `json:"user_id,omitempty"`
	RewardID  uint64    `json:"reward_id,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_reward_log"
}

type Filter struct {
	ID       uint64
	UserID   uint64
	RewardID uint64
	Sort     dbs.CommonSort
}

type ModelList []*Model
