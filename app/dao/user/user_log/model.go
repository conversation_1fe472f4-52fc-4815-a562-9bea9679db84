package userLog

import (
	"blind_box/app/common/dbs"
	"time"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	UserID    uint64    `json:"user_id,omitempty"`
	Method    string    `json:"method,omitempty"`
	TraceID   string    `json:"trace_id,omitempty"`
	Url       string    `json:"url,omitempty"`
	ClientIp  string    `json:"client_ip,omitempty"`
	Header    string    `json:"header,omitempty"`
	Body      string    `json:"body,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_log"
}

type Filter struct {
	ID     uint64
	UserID uint64
	Sort   dbs.CommonSort
}

type ModelList []*Model
