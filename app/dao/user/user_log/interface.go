package userLog

import (
	"blind_box/app/common/dbs"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	BatchCreateWithTx(*gorm.DB, []*Model) error

	CreateLoginLogWithTx(tx *gorm.DB, m *LoginLog) error
	LoginLogPageList(ctx *gin.Context, f *LoginLogFilter, page, limit int) (total int64, list LoginLogList, err error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
