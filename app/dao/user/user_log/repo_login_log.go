package userLog

import (
	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CreateLoginLogWithTx .
func (e *Entry) CreateLoginLogWithTx(tx *gorm.DB, m *LoginLog) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreateLoginLog .
func (e *Entry) BatchCreateLoginLog(ctx *gin.Context, m []*LoginLog) error {
	return e.BatchCreateLoginLogWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateLoginLogWithTx .
func (e *Entry) BatchCreateLoginLogWithTx(ctx *gin.Context, tx *gorm.DB, m []*LoginLog) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// LoginLogPageList .
func (e *Entry) LoginLogPageList(ctx *gin.Context, f *LoginLogFilter, page, limit int) (total int64, list LoginLogList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&LoginLog{})
	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}
