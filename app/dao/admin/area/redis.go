package area

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisAreaList .
func (e *Entry) RedisAreaList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.AreaListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadAreaList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisAreaMap .
func (e *Entry) RedisAreaMap(ctx *gin.Context) (map[uint64]*Model, error) {
	cityList, err := e.RedisAreaList(ctx)
	if err != nil {
		return nil, err
	}
	cityMap := cityList.GetIDMap()
	return cityMap, nil
}

// RedisReloadAreaList redis重载数据列表
func (e *Entry) RedisReloadAreaList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.AreaListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearAreaList .
func (e *Entry) RedisClearAreaList(ctx *gin.Context) error {
	cacheKey := redisPkg.AreaListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
