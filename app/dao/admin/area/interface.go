package area

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	FindByFilter(*gin.Context, *Filter) (ModelList, error)

	RedisAreaList(*gin.Context) (ModelList, error)
	RedisAreaMap(*gin.Context) (map[uint64]*Model, error)
	RedisReloadAreaList(*gin.Context) (ModelList, error)
	RedisClearAreaList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
