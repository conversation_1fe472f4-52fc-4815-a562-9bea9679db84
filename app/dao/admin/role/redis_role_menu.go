package adminRole

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisRoleMenuIds .
func (e *Entry) RedisRoleMenuIds(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error) {
	var (
		cacheKey       = redisPkg.GetRoleMenuIdsKey(rid)
		ret            = RoleAuthMenuIds{}
		cacheData, err = e.RedisCli.Get(ctx, cacheKey).Result()
	)
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadRoleMenuIds(ctx, rid)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		log.Ctx(ctx).WithError(err).With<PERSON>ield("cacheKey", cacheKey).Error("json.Unmarshal err")
		return nil, err
	}
	return ret, nil
}

// RedisReloadRoleList redis重载数据列表
func (e *Entry) RedisReloadRoleMenuIds(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error) {
	var (
		cacheKey   = redisPkg.GetRoleMenuIdsKey(rid)
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = RoleAuthMenuIds{}
		cacheData  []byte
		ids, err   = e.FindMenuIdsByRole(ctx, rid)
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("cacheKey", cacheKey).Error("RedisReloadRoleMenuIds err")
		return tmp, err
	}
	if cacheData, err = json.Marshal(ids); err != nil {
		log.Ctx(ctx).WithError(err).WithField("cacheKey", cacheKey).Error("json.Unmarshal err")
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		log.Ctx(ctx).WithError(err).WithField("cacheKey", cacheKey).Error("RedisReloadRoleMenuIds err")
		return tmp, err
	}
	return ids, nil
}

// RedisClearRoleMenuIds .
func (e *Entry) RedisClearRoleMenuIds(ctx *gin.Context, rid uint64) error {
	cacheKey := redisPkg.GetRoleMenuIdsKey(rid)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
