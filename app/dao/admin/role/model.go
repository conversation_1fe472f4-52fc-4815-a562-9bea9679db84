package adminRole

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	Name      string    `json:"name,omitempty"`
	Desc      string    `json:"desc,omitempty"`
	Sort      uint32    `json:"sort,omitempty"`
	Status    uint32    `json:"status,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "admin_role"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	NotID  uint64
	NotIds []uint64
	Name   string
	EqName string
	Status uint32
	Sort   dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}
