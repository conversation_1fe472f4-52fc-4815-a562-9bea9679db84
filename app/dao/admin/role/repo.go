package adminRole

import (
	"fmt"

	"blind_box/app/common/dbs"
	"blind_box/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"name", "status"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.RedisClearRoleList(ctx)
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.IDS) > 0 {
		query.Where("user_id in (?)", f.IDS)
	}
	if f.NotID != 0 {
		query.Where("id <> ?", f.NotID)
	}
	if len(f.NotIds) > 0 {
		query.Where("id not in (?)", f.NotIds)
	}
	if f.EqName != "" {
		query.Where("name = ?", f.EqName)
	}
	if f.Name != "" {
		query.Where("name like ?", "%"+f.Name+"%")
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	ret := ModelList{}

	if err := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindAll .
func (e *Entry) FindAll(ctx *gin.Context) (ModelList, error) {
	ret := ModelList{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{ID: id}).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// DelByID .
func (e *Entry) DelByID(ctx *gin.Context, tx *gorm.DB, id uint64) error {
	query := tx.Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
		"status":                  dbs.StatusDisable,
	}).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DelByID err")
		return err
	}
	e.RedisClearRoleList(ctx)
	return nil
}
