package log

import (
	"blind_box/app/common/dbs"
	"time"
)

type LogBlack struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	Url       string    `json:"url,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *LogBlack) TableName() string {
	return "admin_log_black"
}

type LogBlackFilter struct {
	ID   uint64
	Url  string
	Sort dbs.CommonSort
}

type LogBlackList []*LogBlack

func (ml LogBlackList) GetUrlEmptyMap() map[string]struct{} {
	retMap := make(map[string]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Url]; !ok {
			retMap[val.Url] = struct{}{}
		}
	}
	return retMap
}
