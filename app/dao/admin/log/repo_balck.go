package log

import (
	"fmt"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *LogBlack) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *LogBlack) error {
	if err := tx.Model(&LogBlack{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"url"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.<PERSON>isClearLogBlackList(ctx)
	return nil
}

func (e *Entry) buildBlackQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&LogBlack{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}

	return query
}

// FindBlackByFilter .
func (e *Entry) FindBlackByFilter(ctx *gin.Context, f *Filter) (LogBlackList, error) {
	query := e.buildBlackQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := LogBlackList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
