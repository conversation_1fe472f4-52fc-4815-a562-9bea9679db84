package log

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	LogRepo
	LogBlackRepo
}

type LogRepo interface {
	BatchCreateWithTx(*gorm.DB, []*Model) error
}

type LogBlackRepo interface {
	RedisLogBlackList(ctx *gin.Context) (LogBlackList, error)
	RedisLogBlackMap(ctx *gin.Context) (map[string]struct{}, error)
	RedisReloadLogBlackList(ctx *gin.Context) (LogBlackList, error)
	RedisClearLogBlackList(ctx *gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
