package menu

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"context"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisMenuList .
func (e *Entry) RedisMenuList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.MenuListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadMenuList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadMenuList redis重载数据列表
func (e *Entry) RedisReloadMenuList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.MenuListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearMenuList .
func (e *Entry) RedisClearMenuList(ctx *gin.Context) error {
	cacheKey := redisPkg.MenuListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
