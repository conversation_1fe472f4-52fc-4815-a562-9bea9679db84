package menu

import (
	"blind_box/app/common/dbs"
	adminDto "blind_box/app/dto/admin"
	"time"
)

type MenuType uint32

const (
	MtDirectory MenuType = 1 // 目录
	MtMenu      MenuType = 2 // 菜单
	MtButton    MenuType = 3 // 按钮
)

type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	Pid        uint64    `json:"pid,omitempty"`
	Title      string    `json:"title,omitempty"`
	Icon       string    `json:"icon,omitempty"`
	MenuType   uint32    `json:"menu_type,omitempty"`   // 1 目录 2 菜单 3按钮
	Component  string    `json:"component,omitempty"`   // 前端页面路径
	BeAuth     string    `json:"be_auth,omitempty"`     // 后端权限标识
	IsShow     uint32    `json:"is_show,omitempty"`     // 是否显示, 1显示 2隐藏
	SystemMenu uint32    `json:"system_menu,omitempty"` // 是否为系统菜单,系统菜单不可删除
	IsRoot     uint32    `json:"is_root,omitempty"`     // 是否需要超管权限
	Sort       uint32    `json:"sort,omitempty"`        // 排序字段
	Status     uint32    `json:"status,omitempty"`      // 状态 1启用 2禁用
	Level      uint32    `json:"level,omitempty"`       // 等级
	KeepAlive  uint32    `json:"keep_alive,omitempty"`  // 前端页面缓存 0不缓存  1缓存
	Path       string    `json:"path,omitempty"`        // 前端路由path
	PathName   string    `json:"path_name,omitempty"`   // 前端缓存所需
	Redirect   string    `json:"redirect,omitempty"`    // 跳转路径
	FeAuth     string    `json:"fe_auth,omitempty"`     // 前端权限标识
	Desc       string    `json:"desc,omitempty"`        // 描述
	IsDeleted  uint32    `json:"is_deleted,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "admin_menu"
}

// GetPathName . 前端框架所需
// func (m *Model) GetPathName() string {
// 	return strUtil.ToCamelInitCase(m.Path, true)
// }

type Filter struct {
	ID    uint64
	IDS   []uint64
	ByPid bool
	Pid   uint64
	Title string
	Sort  dbs.CommonSort
}

type Sort struct {
	SortField  dbs.SortField
	SortMethod dbs.SortMethod
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

// SetMenuReqToModel .
func SetMenuReqToModel(req *adminDto.AdminSetMenuReq) *Model {
	m := &Model{
		ID:        req.ID,
		Pid:       req.Pid,
		Title:     req.Title,
		Icon:      req.Icon,
		MenuType:  req.MenuType,
		Component: req.Component,
		BeAuth:    req.BeAuth,
		IsShow:    req.IsShow,
		Sort:      req.Sort,
		Status:    req.Status,
		Path:      req.Path,
		PathName:  req.PathName,
		Redirect:  req.Redirect,
		FeAuth:    req.FeAuth,
		KeepAlive: req.KeepAlive,
	}
	return m
}
