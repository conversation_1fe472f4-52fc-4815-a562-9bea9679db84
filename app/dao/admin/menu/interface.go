package menu

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	MenuRepo
	RedisRepo
}

type MenuRepo interface {
	CreateOrUpdate(ctx *gin.Context, m *Model) error
	CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error
	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error
	FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
}

type RedisRepo interface {
	RedisMenuList(ctx *gin.Context) (ModelList, error)
	RedisReloadMenuList(ctx *gin.Context) (ModelList, error)
	RedisClearMenuList(ctx *gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
