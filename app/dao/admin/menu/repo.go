package menu

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"pid", "title", "icon", "menu_type", "component", "be_auth",
				"is_show", "sort", "status", "path", "path_name", "redirect", "fe_auth", "keep_alive",
			}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.RedisClearMenuList(ctx)
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	e.RedisClearMenuList(ctx)
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.IDS) > 0 {
		query.Where("id in ?", f.IDS)
	}
	if f.Title != "" {
		query.Where("title like ?", "%"+f.Title+"%")
	}
	if f.ByPid {
		query.Where("pid = ?", f.Pid)
	}
	return query
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	orderStr := "sort desc, id asc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{})
	if err := query.Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Select("id").Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
