package menuBackup

import (
	"fmt"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) (uint64, error) {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) (uint64, error) {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"pid", "title", "icon", "menu_type", "component", "be_auth",
				"is_show", "sort", "status", "level", "path", "path_name", "redirect", "fe_auth", "keep_alive",
			}),
		}).Create(&m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.IDS) > 0 {
		query.Where("id in (?)", f.IDS)
	}
	if f.Title != "" {
		query.Where("title like ?", "%"+f.Title+"%")
	}
	orderStr := "id asc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{})
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FeatchByFilterSort .
func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
	ret := &Model{}
	if f.ID == 0 {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{})
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}

	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.Pid != 0 {
		query.Where("pid = ?", f.Pid)
	}
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
