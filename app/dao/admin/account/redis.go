package account

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisGetAccountToken .
func (e *Entry) RedisGetAccountToken(ctx *gin.Context, aid uint64) (string, error) {
	cacheKey := redisPkg.GetAccountTokenKey(aid)
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return "", nil
		}
		return "", err
	}

	return cacheData, nil
}

// RedisSetAccountToken .
func (e *Entry) RedisSetAccountToken(ctx *gin.Context, aid uint64, token string) error {
	var (
		cacheKey   = redisPkg.GetAccountTokenKey(aid)
		expireTime = dbs.GetRedisExpireTime(dbs.RedisExpireTimeWeek)
	)

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, token, expireTime).Err(); err != nil {
		return err
	}
	return nil
}

// RedisClearAccountToken .
func (e *Entry) RedisClearAccountToken(ctx *gin.Context, aid uint64) error {
	cacheKey := redisPkg.GetAccountTokenKey(aid)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

// RedisEnableAccountList .
func (e *Entry) RedisEnableAccountList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisAccountList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisAccountList .
func (e *Entry) RedisAccountList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.AccountListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadAccountList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisAccountList .
func (e *Entry) RedisAccountMap(ctx *gin.Context) (map[uint64]*Model, error) {
	accountList, err := e.RedisAccountList(ctx)
	if err != nil {
		return nil, err
	}
	return accountList.GetIDMap(), nil
}

// RedisAccountList .
func (e *Entry) RedisAccountNameMap(ctx *gin.Context) (map[string]*Model, error) {
	accountList, err := e.RedisAccountList(ctx)
	if err != nil {
		return nil, err
	}
	return accountList.GetNameMap(), nil
}

// RedisReloadAccountList redis重载数据列表
func (e *Entry) RedisReloadAccountList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.AccountListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindAll(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearAccountList .
func (e *Entry) RedisClearAccountList(ctx *gin.Context) error {
	cacheKey := redisPkg.AccountListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
