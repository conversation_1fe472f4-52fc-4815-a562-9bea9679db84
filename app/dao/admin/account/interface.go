package account

import (
	"sync"
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	AccountRepo
	RedisRepo
}

type AccountRepo interface {
	CreateOrUpdate(ctx *gin.Context, m *Model, clearToken bool) error
	CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model, clearToken bool) error
	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}, clearToken bool) error
	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}, clearToken bool) error
	UpdateMapByFilter(ctx *gin.Context, f *Filter, data map[string]interface{}) error
	UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, f *Filter, data map[string]interface{}) error
	DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error)
	FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
	FindAll(ctx *gin.Context) (ModelList, error)
	FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error)
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error)
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
	DelByID(ctx *gin.Context, id uint64) error
}

type RedisRepo interface {
	RedisGetAccountToken(ctx *gin.Context, aid uint64) (string, error)
	RedisSetAccountToken(ctx *gin.Context, aid uint64, token string) error
	RedisClearAccountToken(ctx *gin.Context, aid uint64) error
	RedisEnableAccountList(ctx *gin.Context) (ModelList, error)
	RedisAccountList(ctx *gin.Context) (ModelList, error)
	RedisAccountMap(ctx *gin.Context) (map[uint64]*Model, error)
	RedisAccountNameMap(ctx *gin.Context) (map[string]*Model, error)
	RedisReloadAccountList(ctx *gin.Context) (ModelList, error)
	RedisClearAccountList(ctx *gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
