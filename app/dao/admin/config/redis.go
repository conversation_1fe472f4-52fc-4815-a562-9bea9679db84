package config

import (
	"encoding/json"
	"strconv"

	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	redisPkg "blind_box/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gogf/gf/v2/util/gconv"
)

func RedisGetConfig[T any](ctx *gin.Context, key ConfigKey) T {
	var (
		zeroVal        T
		configCache, _ = GetRepo().RedisConfigMap(ctx)
		parsedVal      interface{}
		err            error
	)
	val, exists := configCache[string(key)]
	if !exists {
		log.Ctx(ctx).With<PERSON>ield("ConfigKey", key).Warn("config key not found")
		return zeroVal
	}

	switch any(zeroVal).(type) {
	case bool:
		if parsedVal, err = strconv.ParseBool(val); err != nil {
			log.Ctx(ctx).With<PERSON>ield("ConfigKey", key).Warn("parsedValue to bool err")
			return zeroVal
		}
	case int:
		parsedVal = gconv.Int(val)
	case int32:
		parsedVal = gconv.Int32(val)
	case int64:
		parsedVal = gconv.Int64(val)
	case uint32:
		parsedVal = gconv.Uint32(val)
	case uint64:
		parsedVal = gconv.Uint64(val)
	case string:
		parsedVal = val
	case map[string]interface{}:
		var jsonValue map[string]interface{}
		if err := json.Unmarshal([]byte(val), &jsonValue); err != nil {
			log.Ctx(ctx).WithField("ConfigKey", key).Warn("parsedValue to map err")
			return zeroVal
		}
		parsedVal = jsonValue
	default:
		log.Ctx(ctx).WithField("ConfigKey", key).Warn("unsupported type")
		return zeroVal
	}

	return parsedVal.(T)
}

// RedisEnableConfigList .
func (e *Entry) RedisEnableConfigList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisConfigList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisConfigList .
func (e *Entry) RedisConfigList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.ConfigListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadConfigList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisConfigMap .
func (e *Entry) RedisConfigMap(ctx *gin.Context) (map[string]string, error) {
	ConfigList, err := e.RedisConfigList(ctx)
	if err != nil {
		return nil, err
	}
	return ConfigList.GetConfigMap(), nil
}

// RedisReloadConfigList redis重载数据列表
func (e *Entry) RedisReloadConfigList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.ConfigListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearConfigList .
func (e *Entry) RedisClearConfigList(ctx *gin.Context) error {
	cacheKey := redisPkg.ConfigListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

// ================================ 页面配置相关缓存方法 ================================

// RedisPageConfigList 获取页面配置列表缓存
func (e *Entry) RedisPageConfigList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.PageConfigListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadPageConfigList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisPageConfigMap 获取页面配置键值对Map缓存
func (e *Entry) RedisPageConfigMap(ctx *gin.Context) (map[string]string, error) {
	configList, err := e.RedisPageConfigList(ctx)
	if err != nil {
		return nil, err
	}
	return configList.GetConfigMap(), nil
}

// RedisReloadPageConfigList 重新加载页面配置列表缓存
func (e *Entry) RedisReloadPageConfigList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.PageConfigListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)

	// 获取所有启用的配置数据 - 不限制特定的键
	list, err := e.FindByFilter(ctx, &Filter{
		Status:     uint32(dbs.StatusEnable),
		ConfigType: 2,
	})
	if err != nil {
		return tmp, err
	}

	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearPageConfigList 清除页面配置列表缓存
func (e *Entry) RedisClearPageConfigList(ctx *gin.Context) error {
	cacheKey := redisPkg.PageConfigListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

// RedisGetPageConfig 获取单个页面配置缓存
func (e *Entry) RedisGetPageConfig(ctx *gin.Context, key string) (string, error) {
	cacheKey := redisPkg.GetPageConfigKey(key)
	val, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			// 从数据库重新获取并缓存
			return e.redisReloadSinglePageConfig(ctx, key)
		}
		return "", err
	}
	return val, nil
}

// RedisSetPageConfig 设置单个页面配置缓存
func (e *Entry) RedisSetPageConfig(ctx *gin.Context, key, value string) error {
	cacheKey := redisPkg.GetPageConfigKey(key)
	expireTime := dbs.GetRedisExpireTime(dbs.ResidExpireTime)
	return e.RedisCli.Set(ctx.Request.Context(), cacheKey, value, expireTime).Err()
}

// redisReloadSinglePageConfig 重新加载单个页面配置
func (e *Entry) redisReloadSinglePageConfig(ctx *gin.Context, key string) (string, error) {
	list, err := e.FindByFilter(ctx, &Filter{
		ConfigKey: ConfigKey(key),
		Status:    uint32(dbs.StatusEnable),
	})
	if err != nil {
		return "", err
	}

	value := ""
	if len(list) > 0 {
		value = list[0].ConfigValue
		// 缓存到Redis
		if err := e.RedisSetPageConfig(ctx, key, value); err != nil {
			log.Ctx(ctx).WithError(err).Warn("redis set page config error")
		}
	}

	return value, nil
}
