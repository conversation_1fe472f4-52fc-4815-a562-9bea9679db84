package config

import (
	"encoding/json"
	"time"

	"blind_box/app/common/dbs"
)

type ConfigKey string

const (
	KeyCurrentEnv           ConfigKey = "currentEnv"           // 当前环境变量
	KeyBackendDomain        ConfigKey = "backendDomain"        // 后端服务地址
	KeyWxAppletPayNotify    ConfigKey = "wxAppletPayNotify"    // 小程序支付回调通知地址
	KeyWxAppletRefundNotify ConfigKey = "wxAppletRefundNotify" // 小程序退款回调通知地址
	KeyImageCdnDomain       ConfigKey = "imageCdnDomain"       // 图片CDN域名
)

// Box ...
const (
	KeyYaoNum             ConfigKey = "yaoNum"
	KeyPostFee            ConfigKey = "postFee"
	KeyPostCnt            ConfigKey = "postCnt"
	KeyAnnouncement       ConfigKey = "announcement" // 公告
	KeyUnderConstruct     ConfigKey = "underConstruct"
	KeyUnderConstructTime ConfigKey = "underConstructTime"
)

// 快递费配置相关常量
const (
	// 快递费配置开关
	ConfigKeyFreightFeeEnable = "freight_fee_enable"
	// 快递费配置规则 (JSON格式)
	ConfigKeyFreightFeeRule = "freight_fee_rule"
)

// 积分抵扣配置相关常量
const (
	// 积分抵扣功能开关
	ConfigKeyPointsDiscountEnable = "points_discount_enable"
	// 积分抵扣比例配置 (JSON格式)
	ConfigKeyPointsDiscountRule = "points_discount_rule"
	// 积分抵扣最大比例 (订单金额的百分比)
	ConfigKeyPointsDiscountMaxPercent = "points_discount_max_percent"
)

type Model struct {
	ID          uint64    `gorm:"primarykey" json:"id"`
	Name        string    `json:"name,omitempty"`
	ConfigKey   string    `json:"config_key,omitempty"`
	ConfigValue string    `json:"config_value,omitempty"`
	ConfigType  uint32    `json:"config_type,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Status      uint32    `json:"status,omitempty"` // 状态,,1-启用 2-禁用
}

func (m *Model) TableName() string {
	return "config_admin"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// PointsDiscountRule 积分抵扣规则配置
type PointsDiscountRule struct {
	PointsAmount uint64 `json:"pointsAmount"` // 积分数量（如1000积分）
	MoneyAmount  uint64 `json:"moneyAmount"`  // 抵扣金额（分，如5000分=50元）
	Enable       bool   `json:"enable"`       // 是否启用
	Description  string `json:"description"`  // 描述
}

// GetPointsDiscountRule 获取积分抵扣规则配置
func (m *Model) GetPointsDiscountRule() (*PointsDiscountRule, error) {
	if m.ConfigKey != ConfigKeyPointsDiscountRule {
		return nil, nil
	}

	var rule PointsDiscountRule
	if err := json.Unmarshal([]byte(m.ConfigValue), &rule); err != nil {
		return nil, err
	}

	return &rule, nil
}

// SetPointsDiscountRule 设置积分抵扣规则配置
func (m *Model) SetPointsDiscountRule(rule *PointsDiscountRule) error {
	ruleBytes, err := json.Marshal(rule)
	if err != nil {
		return err
	}

	m.ConfigKey = ConfigKeyPointsDiscountRule
	m.ConfigValue = string(ruleBytes)

	return nil
}

// 默认积分抵扣规则：1000积分抵50元
func GetDefaultPointsDiscountRule() *PointsDiscountRule {
	return &PointsDiscountRule{
		PointsAmount: 1000, // 1000积分
		MoneyAmount:  5000, // 5000分 = 50元
		Enable:       true, // 默认启用
		Description:  "1000积分抵扣50元",
	}
}

// CalculatePointsDiscount 根据配置计算积分可抵扣的金额
func CalculatePointsDiscount(userPoints uint64, rule *PointsDiscountRule) uint64 {
	if rule == nil || !rule.Enable || rule.PointsAmount == 0 {
		return 0
	}

	// 计算用户积分可以兑换多少倍数
	multiplier := userPoints / rule.PointsAmount
	if multiplier == 0 {
		return 0
	}

	// 计算可抵扣金额
	return multiplier * rule.MoneyAmount
}

// CalculateRequiredPoints 根据配置计算指定金额需要多少积分
func CalculateRequiredPoints(moneyAmount uint64, rule *PointsDiscountRule) uint64 {
	if rule == nil || !rule.Enable || rule.MoneyAmount == 0 {
		return 0
	}

	// 向上取整计算需要的积分数
	requiredMultiplier := (moneyAmount + rule.MoneyAmount - 1) / rule.MoneyAmount
	return requiredMultiplier * rule.PointsAmount
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	Status uint32

	ConfigType uint32 // 配置类型
	ConfigKey  ConfigKey
	ConfigKeys []ConfigKey

	Sort dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetConfigMap() map[string]string {
	retMap := make(map[string]string, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ConfigKey]; !ok {
			retMap[val.ConfigKey] = val.ConfigValue
		}
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}
