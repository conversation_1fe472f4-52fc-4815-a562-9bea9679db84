# app/dao 目录代码规范

本文档记录 `/app/dao` 目录下的数据访问层代码规范和最佳实践。

## 目录结构规范

### 模块组织方式
```
app/dao/
├── [模块名]/                    # 业务模块目录
│   ├── interface.go           # 接口定义和依赖注入
│   ├── model.go               # GORM 模型定义
│   ├── repo.go                # 数据库操作方法
│   └── redis.go               # Redis 缓存操作（可选）
├── [模块名]/[子模块名]/         # 复杂模块的子模块
│   ├── interface.go
│   ├── model.go
│   └── repo.go
```

### 典型模块示例
- `user/`: 用户模块
- `order/order/`: 订单主表
- `order/order_detail/`: 订单详情
- `goods/sku/`: 商品 SKU
- `goods/spu/`: 商品 SPU
- `box/box_goods/`: 盲盒商品

## 文件规范

### interface.go - 接口定义文件

#### 基本结构
```go
package [模块名]

import (
    "blind_box/app/common/dbs"
    "blind_box/pkg/redis"
    "sync"
    
    jsoniter "github.com/json-iterator/go"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// Repo 接口定义（可选）
type Repo interface {
    // 接口方法定义
}

// Entry 结构体（核心）
type Entry struct {
    MysqlEngine *dbs.MysqlEngines
    RedisCli    *redis.RedisClient
}

// 单例模式
var (
    defaultRepo         *Entry
    defaultRepoInitOnce sync.Once
)

// GetRepo 获取单例
func GetRepo() *Entry {
    if defaultRepo == nil {
        defaultRepoInitOnce.Do(func() {
            ret := newEntry()
            defaultRepo = ret
        })
    }
    return defaultRepo
}

// newEntry 创建实例
func newEntry() *Entry {
    return &Entry{
        MysqlEngine: dbs.NewMysqlEngines(),
        RedisCli:    redis.GetRedisClient(),
    }
}
```

### model.go - 模型定义文件

#### 模型结构规范
```go
package [模块名]

import (
    "blind_box/app/common/dbs"
    "time"
)

// 常量定义（状态、类型等）
const (
    StatusEnable  = 1
    StatusDisable = 2
)

// 类型定义
type OrderStatus uint32

// 包级常量（推荐）
var (
    OrderStatusPayOk    OrderStatus = 3
    OrderStatusDone     OrderStatus = 7
    DeliveryTypeExpress uint32 = 1
)

// Model 数据模型
type Model struct {
    // 主键
    ID uint64 `gorm:"primarykey" json:"id"`
    
    // 业务字段
    Title     string     `json:"title,omitempty"`
    Status    uint32     `json:"status,omitempty"`
    Cover     dbs.CdnImg `json:"cover,omitempty"`  // CDN 图片类型
    
    // 库存相关字段（SKU 等）
    Total     uint32 `json:"total,omitempty"`      // 总库存
    LockNum   uint32 `json:"lock_num,omitempty"`   // 锁定数
    UsedNum   uint32 `json:"used_num,omitempty"`   // 已使用
    RefundNum uint32 `json:"refund_num,omitempty"` // 退款数
    
    // 时间字段
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    
    // 软删除（可选）
    IsDeleted uint32 `json:"is_deleted,omitempty"`
}

// TableName 指定表名
func (m *Model) TableName() string {
    return "table_name"
}

// 辅助方法
func (m *Model) GetCreatedTime() string {
    return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// 库存计算方法示例
func (m *Model) GetUsableNum() uint32 {
    if m.Total+m.RefundNum >= m.LockNum+m.UsedNum {
        return m.Total + m.RefundNum - m.LockNum - m.UsedNum
    }
    return 0
}
```

#### Filter 结构体规范
```go
// Filter 查询过滤器
type Filter struct {
    // 基础查询
    ID      uint64
    IDs     []uint64
    NotID   uint64
    NotIDs  []uint64
    
    // 字段查询
    Status  uint32
    Title   string
    
    // 模糊查询
    TitleLike   string
    CodeLike    string
    
    // 关联查询
    UserID  uint64
    SpuID   uint64
    SpuIDs  []uint64
    
    // 时间范围
    CreatedAtStart int64
    CreatedAtEnd   int64
    
    // 排序
    Sort    dbs.CommonSort
}
```

#### ModelList 集合类型
```go
type ModelList []*Model

// 获取 ID 列表
func (ml ModelList) GetIDs() []uint64 {
    ids := make([]uint64, 0, len(ml))
    for _, m := range ml {
        ids = append(ids, m.ID)
    }
    return lo.Uniq(ids)
}

// 获取 ID Map
func (ml ModelList) GetIDMap() map[uint64]*Model {
    retMap := make(map[uint64]*Model, len(ml))
    for _, val := range ml {
        if _, ok := retMap[val.ID]; !ok {
            retMap[val.ID] = val
        }
    }
    return retMap
}
```

### repo.go - 数据操作文件

#### 基础 CRUD 操作
```go
package [模块名]

import (
    "blind_box/app/common/dbs"
    "fmt"
    
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    "gorm.io/gorm/clause"
)

// Create 创建记录
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
    return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx 带事务创建
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
    if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
        return err
    }
    return nil
}

// CreateOrUpdate 创建或更新
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
    return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx 带事务创建或更新
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
    if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
        Clauses(clause.OnConflict{
            Columns:   []clause.Column{{Name: "id"}},
            DoUpdates: clause.AssignmentColumns([]string{"field1", "field2", "field3"}),
        }).Create(&m).Error; err != nil {
        return err
    }
    return nil
}

// BatchCreate 批量创建
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
    return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx 带事务批量创建
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
    if len(m) == 0 {
        return nil
    }
    if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
        return err
    }
    return nil
}

// UpdateMapByID 通过 ID 更新
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
    return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx 带事务通过 ID 更新
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
    if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
        return err
    }
    // 清理缓存（如果有）
    // e.RedisClearInfo(ctx, id)
    return nil
}
```

#### 查询构建器模式

##### 方式一：内联条件构建
```go
func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
    query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
    
    if f.ID != 0 {
        query.Where("id = ?", f.ID)
    }
    if len(f.IDs) > 0 {
        query.Where("id in ?", f.IDs)
    }
    if f.Status != 0 {
        query.Where("status = ?", f.Status)
    }
    if f.TitleLike != "" {
        query.Where("title like ?", "%"+f.TitleLike+"%")
    }
    
    return query
}
```

##### 方式二：Scopes 链式调用（推荐）
```go
func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
    query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
    
    query.Scopes(
        f._eq_id(),
        f._in_ids(),
        f._eq_status(),
        f._like_title(),
        f._time_range(),
    )
    
    return query
}

// Scope 方法定义
func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if t := f.ID; t > 0 {
            return db.Where("id = ?", t)
        }
        return db
    }
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if len(f.IDs) > 0 {
            return db.Where("id in ?", f.IDs)
        }
        return db
    }
}

func (f Filter) _like_title() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if f.TitleLike != "" {
            return db.Where("title LIKE ?", "%"+f.TitleLike+"%")
        }
        return db
    }
}

func (f Filter) _time_range() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if f.CreatedAtStart > 0 {
            db = db.Where("created_at >= ?", time.Unix(f.CreatedAtStart, 0))
        }
        if f.CreatedAtEnd > 0 {
            db = db.Where("created_at <= ?", time.Unix(f.CreatedAtEnd, 0))
        }
        return db
    }
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if len(f.Sort) <= 0 {
            return db.Order("id DESC")
        }
        for _, val := range f.Sort {
            if val.Column.Name == "" {
                continue
            }
            db = db.Order(val)
        }
        return db
    }
}
```

#### 查询方法
```go
// DataPageList 分页查询
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
    query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
    
    query.Select("id").Count(&total)
    if err = query.Select("*").
        Scopes(f._sort()).
        Offset((page - 1) * limit).Limit(int(limit)).
        Find(&list).Error; err != nil {
        return 0, nil, err
    }
    return total, list, nil
}

// FindByFilter 条件查询
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
    query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
    
    ret := ModelList{}
    if err := query.Scopes(f._sort()).Find(&ret).Error; err != nil {
        return ret, err
    }
    return ret, nil
}

// FetchByID 通过 ID 查询单条
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
    ret := &Model{}
    if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
        Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
        return ret, err
    }
    return ret, nil
}

// CountByFilter 统计数量
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
    query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
    if err := query.Count(&num).Error; err != nil {
        return 0, err
    }
    return
}

// FindXidsByFilter 查询指定字段值列表
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
    query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
    
    ret := []uint64{}
    if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
        return ret, err
    }
    return ret, nil
}
```

### redis.go - 缓存操作文件

#### Redis 操作规范
```go
package [模块名]

import (
    "blind_box/app/common/dbs"
    redisPkg "blind_box/pkg/redis"
    
    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
)

// Redis 读取缓存
func (e *Entry) RedisInfo(ctx *gin.Context, id uint64) (*Model, error) {
    var (
        cacheKey  = redisPkg.GetInfoKey(id)  // 使用统一的 key 生成方法
        ret       = &Model{}
        cacheData string
        err       error
    )
    
    // 尝试从缓存读取
    if cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result(); err != nil {
        if err == redis.Nil {
            // 缓存不存在，重新加载
            return e.RedisReloadInfo(ctx, id)
        }
        return nil, err
    }
    
    // 反序列化
    if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
        return nil, err
    }
    return ret, nil
}

// RedisReloadInfo 重载缓存
func (e *Entry) RedisReloadInfo(ctx *gin.Context, id uint64) (*Model, error) {
    var (
        cacheKey   = redisPkg.GetInfoKey(id)
        expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)  // 使用统一的过期时间
        cacheData  []byte
        info       = &Model{}
        err        error
    )
    
    // 从数据库读取
    if info, err = e.FetchByID(ctx, id); err != nil {
        return nil, err
    }
    
    // 序列化
    if cacheData, err = json.Marshal(info); err != nil {
        return nil, err
    }
    
    // 写入缓存
    if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
        return nil, err
    }
    return info, nil
}

// RedisClearInfo 清理缓存
func (e *Entry) RedisClearInfo(ctx *gin.Context, id uint64) error {
    cacheKey := redisPkg.GetInfoKey(id)
    e.RedisCli.Del(ctx.Request.Context(), cacheKey)
    return nil
}

// Redis Token 操作示例
func (e *Entry) RedisGetToken(ctx *gin.Context, uid uint64) (string, error) {
    var (
        cacheKey       = redisPkg.GetTokenKey(uid)
        cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
    )
    if err != nil {
        if err.Error() == redis.Nil.Error() {
            return "", nil
        }
        return "", err
    }
    return cacheData, nil
}

func (e *Entry) RedisSetToken(ctx *gin.Context, uid uint64, token string) error {
    var (
        cacheKey   = redisPkg.GetTokenKey(uid)
        expireTime = dbs.GetRedisExpireTime(dbs.RedisExpireTimeMonth)
    )
    
    if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, token, expireTime).Err(); err != nil {
        return err
    }
    return nil
}
```

## 数据库操作规范

### 读写分离
```go
// 读操作使用从库
e.MysqlEngine.UseWithGinCtx(ctx, false)  // false = 从库

// 写操作使用主库
e.MysqlEngine.UseWithGinCtx(ctx, true)   // true = 主库
```

### 事务处理
```go
// Service 层事务示例
tx := dbs.MysqlEngine.UseWithGinCtx(ctx, true).Begin()
defer func() {
    if err != nil {
        tx.Rollback()
    } else {
        tx.Commit()
    }
}()

// DAO 层使用事务
err = dao.CreateWithTx(ctx, tx, model)
```

### 软删除处理
```go
// 查询时排除软删除记录
query.Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

// 软删除字段定义
IsDeleted uint32 `json:"is_deleted,omitempty"`
```

### 时间字段处理
```go
// 创建/更新时忽略自动管理的时间字段
tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField))

// 时间格式化
m.CreatedAt.Format(dbs.TimeDateFormatFull)
```

## 库存管理规范

### 库存字段定义
```go
Total     uint32  // 总库存
LockNum   uint32  // 锁定数量
UsedNum   uint32  // 已使用数量
RefundNum uint32  // 退款数量
```

### 库存一致性规则
```go
// 可用库存计算
func (m *Model) GetUsableNum() uint32 {
    if m.Total+m.RefundNum >= m.LockNum+m.UsedNum {
        return m.Total + m.RefundNum - m.LockNum - m.UsedNum
    }
    return 0
}

// 一致性验证
Total + RefundNum >= LockNum + UsedNum
```

## 最佳实践

### 1. 使用包级常量
```go
// 推荐：包级常量
var (
    OrderStatusPayOk = 3
    OrderStatusDone  = 7
)

// 避免：硬编码
if order.Status == 3 { }  // 错误
if order.Status == OrderStatusPayOk { }  // 正确
```

### 2. 批量操作优化
```go
// 批量创建而非循环单个创建
e.BatchCreate(ctx, models)

// 批量更新使用 Updates
tx.Model(&Model{}).Where("id in ?", ids).Updates(data)
```

### 3. 缓存策略
- 高频读取数据使用 Redis 缓存
- 更新数据后清理相关缓存
- 使用统一的 key 生成和过期时间管理

### 4. 错误处理
```go
// DAO 层直接返回错误
if err := tx.Create(&m).Error; err != nil {
    return err
}

// Service 层转换为业务错误
if err != nil {
    return ecode.SystemErr
}
```

### 5. 性能优化
- 使用 `Select` 指定查询字段
- 大数据量使用分页查询
- 合理使用索引
- 避免 N+1 查询问题

### 6. 代码复用
- 通过组合 Filter 实现灵活查询
- 使用 WithTx 方法支持事务
- ModelList 提供通用集合操作

## 注意事项

1. **永远不要硬编码业务值**：使用常量定义
2. **主从同步延迟**：写后立即读使用主库
3. **事务边界**：事务在 Service 层管理
4. **缓存一致性**：更新数据后清理缓存
5. **SQL 注入防护**：使用参数化查询
6. **性能监控**：记录慢查询日志