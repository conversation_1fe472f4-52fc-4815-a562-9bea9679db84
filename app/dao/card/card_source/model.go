package card_source

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel
	SourceName string
}

func (m *Model) TableName() string {
	return "box_card_source"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	SourceNameLike string
	SourceName     string

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model)
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}
