package card_config

import (
	"blind_box/app/common/dbs"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

const (
	CardType_yao = 1
	CardType_tip = 2
	CardType_tou = 3
)

type Model struct {
	dbs.ModelWithDel

	CardStatus uint32

	CardCode  string
	CardType  uint32
	CardDesc  string
	CardName  string
	CardImg   string
	DetailMsg string
	ExpireDay uint32
	Source    uint64
}

func (m *Model) TableName() string {
	return "item_card_config"
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model)
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}

func (ml ModelList) GetSourceIDs() []uint64 {
	sourceIDs := make([]uint64, 0)
	for _, m := range ml {
		sourceIDs = append(sourceIDs, m.Source)
	}
	sourceIDs = lo.Uniq(sourceIDs)
	return sourceIDs
}

type Filter struct {
	ID  uint64
	IDs []uint64

	CardStatus uint32
	CardCode   string
	CardCodes  []string

	CardType uint32

	NameLike string

	Sort []clause.OrderByColumn
}
