package card_config

import (
	"sync"

	"blind_box/app/common/dbs"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	CardConfigRepo
}

type CardConfigRepo interface {
	BatchCreate(ctx *gin.Context, m ModelList) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
	FetchByCode(*gin.Context, string) (*Model, error)

	Create(*gin.Context, *Model) (uint64, error)
	CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) (uint64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
