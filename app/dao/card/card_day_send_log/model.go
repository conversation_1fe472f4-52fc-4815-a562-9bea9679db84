package card_day_send_log

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	UserID uint64 //` int(11) NOT NULL AUTO_INCREMENT,
	Day    string //` varchar(20) NOT NULL DEFAULT '' COMMENT '日期',
}

func (m *Model) TableName() string {
	return "card_day_send_log"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	UserID uint64

	Day string

	Sort []clause.OrderByColumn
}
