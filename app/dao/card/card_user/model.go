package card_user

import (
	"time"

	"blind_box/app/common/dbs"
	cardConfDao "blind_box/app/dao/card/card_config"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

const (
	// Status 使用状态
	UserItemCardStatusUnused = 1 // 未使用
	UserItemCardStatusLock   = 2 // 暂时锁定
	UserItemCardStatusUsed   = 3 // 已使用
	UserItemCardStatusExpire = 4 // 已过期
)

type Model struct {
	dbs.ModelWithDel

	UserID     uint64
	CardCode   string
	CardType   uint32
	CardName   string
	CardImg    string
	DetailMsg  string
	Status     uint32
	UsedTime   int64
	ActiveID   uint64
	BoxID      uint64
	BoxSlot    uint32
	ExpireTime int64
	SourceType uint64
	CreateBy   uint64
	Remark     string
}

func (m *Model) TableName() string {
	return "user_item_card_info"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	CardType  uint32
	CardCode  string
	Status    uint32
	Source    uint64
	UserID    uint64
	AccountID uint64
	StartTime int64
	EndTime   int64

	BoxID uint64
	Slot  uint32

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model)
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}

func (ml ModelList) GetSourceIDs() []uint64 {
	sourceIDs := make([]uint64, 0)
	for _, m := range ml {
		sourceIDs = append(sourceIDs, m.SourceType)
	}
	sourceIDs = lo.Uniq(sourceIDs)
	return sourceIDs
}

func (ml ModelList) GetUserIDs() []uint64 {
	userIDs := make([]uint64, 0)
	for _, m := range ml {
		userIDs = append(userIDs, m.UserID)
	}
	userIDs = lo.Uniq(userIDs)
	return userIDs
}

func (ml ModelList) GetCreateBys() []uint64 {
	createBys := make([]uint64, 0)
	for _, m := range ml {
		createBys = append(createBys, m.CreateBy)
	}
	createBys = lo.Uniq(createBys)
	return createBys
}

func (ml ModelList) GetActiveIDs() []uint64 {
	activeIDs := make([]uint64, 0)
	for _, m := range ml {
		activeIDs = append(activeIDs, m.ActiveID)
	}
	activeIDs = lo.Uniq(activeIDs)
	return activeIDs
}

func NewYaoItemCardModel(
	userID uint64,
	cardConf *cardConfDao.Model,
	status uint32,
) *Model {
	expireTime := time.Now().Add(time.Duration(cardConf.ExpireDay) * time.Hour * 24).Unix()
	return &Model{
		UserID:     userID,
		CardCode:   cardConf.CardCode,
		CardType:   cardConf.CardType,
		CardName:   cardConf.CardName,
		CardImg:    cardConf.CardImg,
		DetailMsg:  cardConf.DetailMsg,
		Status:     status,
		ExpireTime: expireTime,
	}

}
