package box_action_detail

import (
	"blind_box/app/common/dbs"
	"blind_box/app/dao/box/box_record"
	"blind_box/app/dao/order/order"
	"blind_box/app/dao/order/order_detail"
	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

const (
	ACTION_TYPE_1 = 1 // 明确产出
	ACTION_TYPE_2 = 2 // 预产出
	ACTION_TYPE_3 = 3 // 预产出锁定
)

var ActionTypeMap = map[uint32]string{
	ACTION_TYPE_1: "明确产出",
	ACTION_TYPE_2: "预产出",
	ACTION_TYPE_3: "预产出锁定",
}

type Model struct {
	dbs.ModelWithDel

	UserID     uint64 `json:"user_id" gorm:"column:user_id;comment:用户ID"`
	ActiveID   uint64 `json:"active_id" gorm:"column:active_id;comment:活动ID"`
	BoxID      uint64 `json:"box_id" gorm:"column:box_id;comment:箱子ID"`
	BoxSlot    string `json:"box_slot" gorm:"column:box_slot;comment:箱子位置"`
	ActionType uint32 `json:"action_type" gorm:"column:action_type;comment:操作类型"`
	ActionDesc string `json:"action_desc" gorm:"column:action_desc;comment:操作描述"`

	CardID   uint64 `json:"card_id" gorm:"column:card_id;comment:卡片ID"`
	GoodsID  uint64 `json:"goods_id" gorm:"column:goods_id;comment:商品ID"`
	BoxLevel string `json:"box_level" gorm:"column:box_level;comment:箱子等级"`

	UserLotteryNum   uint32 `json:"user_lottery_num" gorm:"column:user_lottery_num;comment:用户抽奖次数"`
	ActiveLotteryNum uint32 `json:"active_lottery_num" gorm:"column:active_lottery_num;comment:活动抽奖次数"`
	NormalLotteryNum uint32 `json:"normal_lottery_num" gorm:"column:normal_lottery_num;comment:普通抽奖次数"`
	SecretLotteryNum uint32

	TradeID uint64 `json:"trade_id" gorm:"column:trade_id;comment:交易ID"`
	UsedFee uint64 `json:"used_fee" gorm:"column:used_fee;comment:实际支付金额"`
}

func (m *Model) TableName() string {
	return "box_action_detail"
}

type BoxLotteryNum struct {
	UserLotteryNum   uint32
	ActiveLotteryNum uint32
	NormalLotteryNum uint32
	SecretLotteryNum uint32
}

func NewBoxActionDetail(
	userId uint64,
	box *box_record.Model,
	boxSlot string,
	actionType uint32,
	nums *BoxLotteryNum,
	orderInfo *order.Model,
	tradeDetailInfo *order_detail.Model,
) *Model {
	bad := &Model{
		UserID:           userId,
		ActiveID:         tradeDetailInfo.ActiveID,
		BoxID:            box.ID,
		BoxSlot:          boxSlot,
		ActionType:       actionType,
		ActionDesc:       ActionTypeMap[actionType],
		GoodsID:          tradeDetailInfo.GoodsID,
		BoxLevel:         tradeDetailInfo.GoodsLevel,
		UserLotteryNum:   nums.UserLotteryNum,
		ActiveLotteryNum: nums.ActiveLotteryNum,
		NormalLotteryNum: nums.NormalLotteryNum,
		SecretLotteryNum: nums.SecretLotteryNum,
	}

	bad.TradeID = tradeDetailInfo.ID

	if orderInfo != nil {
		bad.UsedFee = orderInfo.UsedFee
		//bad.TradeId = orderInfo.Id
	}

	return bad
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	BoxID    uint64
	ActiveID uint64
	Slot     uint32
	Slots    []uint32

	ActionType      uint32
	ActionTypeList  []uint32
	CardID          uint64
	UserID          uint64
	CreateTimeStart string
	CreateTimeEnd   string

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetGoodsIDs() []uint64 {
	goodsIDs := make([]uint64, 0, len(ml))
	for _, item := range ml {
		goodsIDs = append(goodsIDs, item.GoodsID)
	}
	goodsIDs = lo.Uniq(goodsIDs)
	return goodsIDs
}
