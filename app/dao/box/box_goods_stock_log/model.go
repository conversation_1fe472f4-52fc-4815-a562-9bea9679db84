package box_goods_stock_log

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	UserID    uint64 `json:"user_id,omitempty"`    // 用户ID
	GoodsID   uint64 `json:"goods_id,omitempty"`   // 赏品id
	ChangeNum int64  `json:"change_num,omitempty"` // 库存变化量
	TradeID   uint64 `json:"trade_id,omitempty"`   // vision_trade_detail.id
}

func (Model) TableName() string {
	return "box_goods_stock_log"
}

type Filter struct {
	ID  uint64
	IDs []uint64

	UserID  uint64
	GoodsID uint64
	TradeID uint64

	Sort []clause.OrderByColumn
}

type ModelList []*Model
