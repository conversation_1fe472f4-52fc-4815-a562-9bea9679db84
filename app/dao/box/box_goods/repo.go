package box_goods

import (
	"errors"
	"fmt"

	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		f._eq_id(),
		f._in_ids(),
		f._eq_active_id(),
		f._eq_spu_id(),
		f._eq_sku_id(),
		f._eq_level_id(),
		f._eq_level_name(),
	)

	return query
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.ID; t > 0 {
			return db.Where("id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id in ?", f.IDs)
		}
		return db
	}
}

func (f Filter) _eq_active_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.ActiveID; t > 0 {
			return db.Where("active_id = ?", t)
		}
		return db
	}
}

// _eq_spu_id
func (f Filter) _eq_spu_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.SpuID; t > 0 {
			return db.Where("spu_id = ?", t)
		}
		return db
	}
}

// _eq_sku_id
func (f Filter) _eq_sku_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.SkuID; t > 0 {
			return db.Where("sku_id = ?", t)
		}
		return db
	}
}

// _eq_level_id
func (f Filter) _eq_level_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.LevelID; t > 0 {
			return db.Where("level_id = ?", t)
		}
		return db
	}
}

// _eq_level_name
func (f Filter) _eq_level_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.LevelName; t != "" {
			return db.Where("level_name = ?", t)
		}
		return db
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").
		Scopes(f._sort()).
		Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter 避免主从未同步先读取情况, 此处读取主库
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true), f)

	ret := ModelList{}
	if err := query.Scopes(f._sort()).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e *Entry) FetchByActiveID(ctx *gin.Context, activeID uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("active_id = ?", activeID).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

func (e *Entry) ReduceGoodsLockStock(ctx *gin.Context, tx *gorm.DB, id uint64, stock uint32) (rows int64, err error) {
	if tx == nil {
		tx = e.MysqlEngine.UseWithGinCtx(ctx, true)
	}

	// ✅ 添加条件判断，确保锁定库存足够
	result := tx.Model(&Model{}).Where("id = ?", id).Where("lock_stock >= ?", stock).
		Updates(map[string]interface{}{
			"used_stock": gorm.Expr("used_stock + ?", stock),
			"lock_stock": gorm.Expr("lock_stock - ?", stock),
		})

	if result.Error != nil {
		err = result.Error
		return
	}

	// ✅ 检查是否真正更新了记录
	if result.RowsAffected == 0 {
		return 0, errors.New("lock stock not enough")
	}

	return result.RowsAffected, nil
}

func (e *Entry) UpdateBoxGoodsStock(ctx *gin.Context, tx *gorm.DB, id uint64) (rows int64, err error) {
	if tx == nil {
		tx = e.MysqlEngine.UseWithGinCtx(ctx, true)
	}

	result := tx.Model(&Model{}).Where("id = ?", id).Exec(
		"UPDATE box_goods SET over_stock = CASE WHEN cur_stock < 1 THEN over_stock + 1 ELSE over_stock END, cur_stock = GREATEST(cur_stock - 1, 0), used_stock = used_stock + 1 WHERE id = ?",
		id,
	)

	if result.Error != nil {
		err = result.Error
		return
	}
	return result.RowsAffected, nil
}

func (e *Entry) UpdateBoxGoodsLockStock(ctx *gin.Context, tx *gorm.DB, id uint64, stock uint32) (rows int64, err error) {
	if tx == nil {
		tx = e.MysqlEngine.UseWithGinCtx(ctx, true)
	}

	// ✅ 在一个SQL中完成检查和更新，确保原子性
	result := tx.Model(&Model{}).Where("id = ?", id).Where("cur_stock >= ?", stock).
		Updates(map[string]interface{}{
			"lock_stock": gorm.Expr("lock_stock + ?", stock),
			"cur_stock":  gorm.Expr("cur_stock - ?", stock),
		})

	if result.Error != nil {
		err = result.Error
		return
	}
	// ✅ 检查是否真正更新了记录
	if result.RowsAffected == 0 {
		return 0, errors.New("lock stock not enough")
	}
	return result.RowsAffected, nil
}

// AtomicDeductStock 原子扣减库存，防止并发超卖
func (e *Entry) AtomicDeductStock(ctx *gin.Context, tx *gorm.DB, id uint64, quantity uint32) (rows int64, err error) {
	if tx == nil {
		tx = e.MysqlEngine.UseWithGinCtx(ctx, true)
	}

	// 原子操作：只有在可用库存足够时才扣减
	result := tx.Model(&Model{}).
		Where("id = ? AND (stock - used_stock - lock_stock) >= ?", id, quantity).
		Update("used_stock", gorm.Expr("used_stock + ?", quantity))

	if result.Error != nil {
		err = result.Error
		return
	}
	if result.RowsAffected == 0 {
		return 0, errors.New("insufficient stock")
	}
	return result.RowsAffected, nil
}
