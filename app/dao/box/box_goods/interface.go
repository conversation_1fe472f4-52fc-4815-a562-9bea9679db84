package box_goods

import (
	"sync"

	"blind_box/app/common/dbs"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	BoxGoodsRepo
}

type BoxGoodsRepo interface {
	BatchCreate(ctx *gin.Context, m ModelList) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	FetchByActiveID(ctx *gin.Context, activeID uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)

	ReduceGoodsLockStock(ctx *gin.Context, tx *gorm.DB, id uint64, stock uint32) (rows int64, err error)
	UpdateBoxGoodsStock(ctx *gin.Context, tx *gorm.DB, id uint64) (rows int64, err error)
	UpdateBoxGoodsLockStock(ctx *gin.Context, tx *gorm.DB, id uint64, stock uint32) (rows int64, err error)

	AtomicDeductStock(ctx *gin.Context, tx *gorm.DB, id uint64, quantity uint32) (rows int64, err error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
