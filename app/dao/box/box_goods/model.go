package box_goods

import (
	"blind_box/app/common/dbs"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	ActiveID uint64 `json:"activeId" gorm:"column:active_id"` // 活动id

	GoodsName  string `json:"goodsName" gorm:"column:goods_name"` // 商品名称
	GoodsCover string `json:"goodsCover" gorm:"column:goods_cover"`

	SpuID uint64 `json:"spuId" gorm:"column:spu_id"` // 商品id
	SkuID uint64 `json:"skuId" gorm:"column:sku_id"` // 商品id

	LevelID   uint64 `json:"levelId" gorm:"column:level_id"`     // 级别id
	LevelName string `json:"levelName" gorm:"column:level_name"` // 级别名称

	Sort      uint32 `json:"sort" gorm:"column:sort"`
	Stock     uint32 `json:"stock" gorm:"column:stock"`          // 库存
	UsedStock uint32 `json:"usedStock" gorm:"column:used_stock"` // 已用库存
	LockStock uint32 `json:"lockStock" gorm:"column:lock_stock"` // 锁定库存
	OverStock uint32 `json:"overStock" gorm:"column:over_stock"` // 超卖库存

}

func (Model) TableName() string {
	return "box_goods"
}

func (g Model) GetCurStock() uint32 {
	totalUsed := g.UsedStock + g.LockStock
	if totalUsed >= g.Stock {
		return 0
	}
	return g.Stock - totalUsed
}

func (g Model) GetUsedStock() uint32 {
	// 直接返回已用库存，无需检查负数（uint32 永远不会小于0）
	return g.UsedStock
}

func (g Model) GetOverStockWeight() uint32 {
	//stock := g.CurStock - g.LockStock - g.OverStock
	stock := g.OverStock + 1

	return stock
}

func (g Model) GetAllStock() uint32 {
	return g.Stock
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	ActiveID  uint64
	SpuID     uint64
	SkuID     uint64
	LevelID   uint64
	LevelName string

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetIDList() []uint64 {
	idList := make([]uint64, 0, len(ml))
	for _, m := range ml {
		idList = append(idList, m.ID)
	}
	idList = lo.Uniq(idList)
	return idList
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model, len(ml))
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}

func (ml ModelList) GetSkuIDs() []uint64 {
	skuIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		skuIDs = append(skuIDs, m.SkuID)
	}
	skuIDs = lo.Uniq(skuIDs)
	return skuIDs
}

func (ml ModelList) GetSpuIDs() []uint64 {
	spuIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		spuIDs = append(spuIDs, m.SpuID)
	}
	spuIDs = lo.Uniq(spuIDs)
	return spuIDs
}
