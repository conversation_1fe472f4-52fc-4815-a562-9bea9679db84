package box_must_config

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

const (
	CONFIG_TYPE_BAG    = 1 // 必出款式
	CONFIG_TYPE_USER   = 2 // 单人多抽必中
	CONFIG_TYPE_ACTIVE = 3 // 活动多抽必中
)

type Model struct {
	dbs.ModelWithDel

	ActiveID    uint64
	Type        uint32
	LotteryNum  uint32
	OutLevel    string
	NotOutGoods string
	OutGoods    string
	GoodsNum    uint32
}

func (*Model) TableName() string {
	return "box_active_must_config"
}

type ModelList []*Model

type Filter struct {
	ID        uint64
	IDs       []uint64
	ActiveID  uint64
	ActiveIDs []uint64

	Type uint32

	Sort []clause.OrderByColumn
}
