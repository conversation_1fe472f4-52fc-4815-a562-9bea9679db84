package box_active_config

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	ActiveID      uint64
	LotteryNum    uint32
	HeadImage     string
	LocationImage string
	BoxImage      string
	LevelText     string
	ShakeNum      uint32
}

func (Model) TableName() string {
	return "box_active_config"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	ActiveID  uint64
	ActiveIDs []uint64

	Sort []clause.OrderByColumn
}
