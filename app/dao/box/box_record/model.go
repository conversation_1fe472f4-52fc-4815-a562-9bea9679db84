package box_record

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

type Model struct {
	//model.VisionBoxRecordInfo
	dbs.ModelWithDel
	//Id          int    `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	//CreateTime  string `gorm:"column:create_time;default:CURRENT_TIMESTAMP"`
	//UpdateTime  string `gorm:"column:update_time;default:CURRENT_TIMESTAMP"`
	//IsDelete    int    `gorm:"column:is_delete;default:0"`
	ActiveID    uint64 `gorm:"column:active_id;NOT NULL;comment:'活动id'"`
	BoxNo       string `gorm:"column:box_no;NOT NULL;comment:'盒子编号'"`
	Cap         uint32 `gorm:"column:cap;NOT NULL;comment:'盒子容量'"`
	Len         uint32 `gorm:"column:len;NOT NULL;comment:'盒子余量'"`
	AvaSlot     string `gorm:"column:ava_slot;NOT NULL;comment:'可用槽位'"`
	SoldSlot    string `gorm:"column:sold_slot;NOT NULL;comment:'已用槽位'"`
	PreLockSlot string `gorm:"column:pre_lock_slot;NOT NULL;comment:'预锁槽位'"`
	LockSlot    string `gorm:"column:lock_slot;NOT NULL;comment:'锁定槽位'"`
	UserId      uint64
}

func (m *Model) TableName() string {
	return "box_record_info"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	BoxNo    string
	ActiveID uint64

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model)
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}
