package box_level

import (
	"blind_box/app/common/dbs"
	"gorm.io/gorm/clause"
)

const (
	BoxLevelNormal = "normal" // 普通
)

type Model struct {
	dbs.ModelWithDel

	LevelName string `json:"levelName" gorm:"column:level_name;type:varchar(255);not null;comment:等级名称"`
}

func (*Model) TableName() string {
	return "box_level"
}

type ModelList []*Model

type Filter struct {
	ID  uint64   `json:"id" form:"id"`
	IDs []uint64 `json:"ids" form:"ids"`

	LevelName string `json:"levelName" form:"levelName"`

	Sort []clause.OrderByColumn
}
