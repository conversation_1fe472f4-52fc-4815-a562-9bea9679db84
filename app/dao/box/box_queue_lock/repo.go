package box_queue_lock

import (
	"fmt"
	"time"

	"blind_box/app/common/dbs"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		f._eq_id(),
		f._in_ids(),
		f._eq_active_id(),
		f._in_active_ids(),
		f._eq_user_id(),
		f._in_user_ids(),
		f._eq_box_id(),
		f._in_box_ids(),
		f._gt_invalid_time(),
		f._between_created_time(),
	)

	return query
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.ID; t > 0 {
			return db.Where("id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id in ?", f.IDs)
		}
		return db
	}
}

func (f Filter) _eq_active_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.ActiveID; t > 0 {
			return db.Where("active_id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_active_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ActiveIDs) > 0 {
			return db.Where("active_id in ?", f.ActiveIDs)
		}
		return db
	}
}

func (f Filter) _eq_user_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.UserID; t > 0 {
			return db.Where("user_id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_user_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.UserIDs) > 0 {
			return db.Where("user_id in ?", f.UserIDs)
		}
		return db
	}
}

func (f Filter) _eq_box_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.BoxID; t > 0 {
			return db.Where("box_id = ?", t)
		}
		return db
	}
}

func (f Filter) _in_box_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.BoxIDs) > 0 {
			return db.Where("box_id in ?", f.BoxIDs)
		}
		return db
	}
}

func (f Filter) _gt_invalid_time() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.InvalidTimeBegin; t != "" {
			return db.Where("invalid_time > ?", t)
		}
		return db
	}
}

func (f Filter) _between_created_time() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.CreatedTimeBegin != "" && f.CreatedTimeEnd != "" {
			return db.Where("created_at BETWEEN ? AND ?", f.CreatedTimeBegin, f.CreatedTimeEnd)
		}
		return db
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").
		Scopes(f._sort()).
		Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter 避免主从未同步先读取情况, 此处读取主库
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, true), f)

	ret := ModelList{}
	if err := query.Scopes(f._sort()).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

func (e *Entry) GetQueueLockByFilter(ctx *gin.Context, f *Filter) (*Model, error) {
	ret := &Model{}
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Where("invalid_time > ?", time.Now().Format(dbs.TimeDateFormatFull))
	if err := query.Limit(1).Order(clause.OrderByColumn{
		Column: clause.Column{Table: clause.CurrentTable, Name: clause.PrimaryKey},
	}).Find(ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}
