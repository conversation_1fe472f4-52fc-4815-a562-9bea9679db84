package box_queue_lock

import (
	"time"

	"blind_box/app/common/dbs"
	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	UserID          uint64
	ActiveID        uint64
	BoxID           uint64
	BoxNo           string
	InvalidShowTime time.Time
	InvalidTime     time.Time
	LockTime        time.Time
	Remark          string
}

func (Model) TableName() string {
	return "box_queue_lock"
}

type Filter struct {
	ID  uint64
	IDs []uint64

	ActiveID  uint64
	ActiveIDs []uint64

	UserID  uint64
	UserIDs []uint64

	BoxID  uint64
	BoxIDs []uint64

	InvalidTimeBegin string

	CreatedTimeBegin string
	CreatedTimeEnd   string

	Sort []clause.OrderByColumn
}

type ModelList []*Model

func (ml ModelList) GetActiveIDs() []uint64 {
	activeIDs := make([]uint64, 0)
	for _, m := range ml {
		if m.ActiveID == 0 {
			continue
		}
		activeIDs = append(activeIDs, m.ActiveID)
	}
	activeIDs = lo.Uniq(activeIDs)
	return activeIDs
}

func (ml ModelList) GetBoxIDs() []uint64 {
	boxIDs := make([]uint64, 0)
	for _, m := range ml {
		if m.BoxID == 0 {
			continue
		}
		boxIDs = append(boxIDs, m.BoxID)
	}
	boxIDs = lo.Uniq(boxIDs)
	return boxIDs
}
