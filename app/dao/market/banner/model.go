package banner

import (
	"blind_box/app/common/dbs"
	"github.com/samber/lo"
	"gorm.io/gorm/clause"
)

type Model struct {
	dbs.ModelWithDel

	Title     string
	Position  uint32
	Image     string
	JumpType  uint32
	JumpParam string
	JumpUrl   string
	Status    uint32
	Sort      uint32
	ActiveID  uint64
	Channel   uint32 // 暂未使用
}

// TableName ...
func (m *Model) TableName() string {
	return "market_banner"
}

type ModelList []*Model

type Filter struct {
	ID  uint64
	IDs []uint64

	Position uint32
	Status   uint32
	JumpType uint32
	Channel  uint32

	Sort []clause.OrderByColumn
}

func (ml ModelList) GetActiveIDs() []uint64 {
	activeIDs := make([]uint64, 0)
	for _, v := range ml {
		if v.ActiveID > 0 {
			activeIDs = append(activeIDs, v.ActiveID)
		}
	}
	activeIDs = lo.Uniq(activeIDs)
	return activeIDs
}
