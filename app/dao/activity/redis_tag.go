package activity

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v7"
)

// RedisTagList .
func (e *Entry) RedisTagList(ctx *gin.Context) (list TagModelList, err error) {
	cacheKey := redisPkg.ActivityTagListKey
	ret := TagModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadTagList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadTagList redis重载数据列表
func (e *Entry) RedisReloadTagList(ctx *gin.Context) (TagModelList, error) {
	var (
		cacheKey   = redisPkg.ActivityTagListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = TagModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.TagList(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearTagList .
func (e *Entry) RedisClearTagList(ctx *gin.Context) error {
	cacheKey := redisPkg.ActivityTagListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
