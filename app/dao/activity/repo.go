package activity

import (
	"fmt"

	"blind_box/app/common/dbs"
	"blind_box/pkg/log"

	"gorm.io/gorm/clause"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.Ids) > 0 {
		query.Where("id in (?)", f.Ids)
	}
	if len(f.Title) > 0 {
		query.Where("title like ?", "%"+f.Title+"%")
	}
	//if f.StartTime > 0 { // TODO: start_time
	//	query.Where("(start_time <= ? and end_time = 0) or (start_time <= ? and end_time >= ?)", f.StartTime, f.StartTime, f.StartTime)
	//}
	if t := f.TitleLike; len(t) > 0 {
		query.Where("title like ?", "%"+t+"%")
	}
	if t := f.ActStatus; t > 0 {
		query.Where("act_status = ?", t)
	}
	if f.StartTime > 0 {
		query.Where("start_time >= ?", f.StartTime)
	}
	if f.EndTime > 0 {
		query.Where("start_time <= ?", f.EndTime)
	}

	return query
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// CountByID .
func (e *Entry) CountByID(ctx *gin.Context, id uint64) (num int64, err error) {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByID error")
		return 0, err
	}
	return num, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// FindMapByFilter .
func (e *Entry) FindMapByFilter(ctx *gin.Context, f *Filter) (map[uint64]*Model, error) {
	ml, err := e.FindByFilter(ctx, f)
	retMap := make(map[uint64]*Model)
	if err != nil {
		return retMap, err
	}
	for _, val := range ml {
		retMap[val.ID] = val
	}
	return retMap, nil
}

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").
		Scopes(f._sort()).
		Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

func (e *Entry) Create(ctx *gin.Context, m *Model) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) (uint64, error) {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil
}
