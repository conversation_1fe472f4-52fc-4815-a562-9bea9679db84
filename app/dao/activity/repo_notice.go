package activity

import (
	"github.com/gin-gonic/gin"
)

// FindNoticeActIdsByFilter .
func (e *Entry) FindNoticeActIdsByFilter(ctx *gin.Context, f *NoticeFilter) ([]uint64, error) {
	ret := []uint64{}
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&NoticeModel{}).Where("recommend = 1 and type = 1")
	if len(f.ActIDS) > 0 {
		query.Where("active_id in (?)", f.ActIDS)
	}
	if err := query.Distinct().Pluck("active_id", &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
