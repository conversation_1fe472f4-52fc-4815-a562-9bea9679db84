package activity

import (
	"blind_box/app/common/dbs"
)

type NoticeModel struct {
	dbs.ModelWithDel

	ActiveId  int `json:"activeId"`
	Recommend int
	Reorder   int
	Type      int
}

type NoticeFilter struct {
	ActIDS []uint64
	Type   uint32
}

type NoticeList []*NoticeModel

func (ml NoticeList) GetActIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[uint64(val.ActiveId)]; !ok {
			retMap[uint64(val.ActiveId)] = struct{}{}
		}
	}
	return retMap
}
