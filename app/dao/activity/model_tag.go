package activity

import (
	"blind_box/app/common/dbs"
)

type TagModel struct {
	dbs.ModelWithDel

	TagName             string `json:"tagName"`
	TagUse              int    `json:"tagUse"`
	TradeFloorShowQuery int    `json:"tradeFloorShowQuery"`
	TradeFloorShowSort  int    `json:"tradeFloorShowSort"`
}
type TagModelList []*TagModel

func (ml TagModelList) GetIDMap() map[uint64]*TagModel {
	retMap := make(map[uint64]*TagModel, len(ml))
	for _, val := range ml {
		if _, ok := retMap[uint64(val.ID)]; !ok {
			retMap[uint64(val.ID)] = val
		}
	}
	return retMap
}
