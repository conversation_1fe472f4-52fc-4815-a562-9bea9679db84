package activity

import (
	"sync"

	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"github.com/gin-gonic/gin"
)

type Repo interface {
	Activity
	ActNotice
	RedisActTag
}

type Activity interface {
	// FetchByID .
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	// CountByID .
	CountByID(*gin.Context, uint64) (int64, error)
	// FindByFilter .
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	// FindMapByFilter .
	FindMapByFilter(*gin.Context, *Filter) (map[uint64]*Model, error)
}

type ActNotice interface {
	// FindNoticeActIdsByFilter .
	FindNoticeActIdsByFilter(f *NoticeFilter) ([]uint64, error)
}

type RedisActTag interface {
	// RedisTagList .
	RedisTagList() (list TagModelList, err error)
	// RedisReloadTagList redis重载数据列表
	RedisReloadTagList() (TagModelList, error)
	// RedisClearTagList .
	RedisClearTagList() error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
