package actsubscribe

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
)

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// DelSubscribe .
func (e *Entry) DelSubscribe(ctx *gin.Context, uid, actID uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("user_id = ? and act_id = ?", uid, actID).
		Update(fmt.Sprintf("%v", dbs.SoftDelField), 1).Error; err != nil {
		return err
	}
	return nil
}

// UpdateSubRemind .
func (e *Entry) UpdateSubRemind(ctx *gin.Context, actID uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("act_id = ?", actID).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Update("is_remind", 1).Error; err != nil {
		return err
	}
	return nil
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, uid uint64, page, limit int) (total int64, list ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).Where("user_id = ?", uid)

	query.Count(&total)
	if err = query.Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(dbs.CommonSort{})).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return
}

// FindUserSubByFilter .
func (e *Entry) FindUserSubByFilter(ctx *gin.Context, f *UserSubFilter) (list ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if f.ActID != 0 {
		query.Where("act_id = ?", f.ActID)
	}
	if f.ByRemind {
		query.Where("is_remind = ?", f.IsRemind)
	}
	if err = query.Find(&list).Error; err != nil {
		return nil, err
	}
	return
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountUserSubByFilter .
func (e *Entry) CountUserSubByFilter(ctx *gin.Context, f *UserSubFilter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if f.ActID != 0 {
		query.Where("act_id = ?", f.ActID)
	}
	if f.ByRemind {
		query.Where("is_remind = ?", f.IsRemind)
	}
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
