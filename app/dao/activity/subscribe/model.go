package actsubscribe

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type ScsRemindType uint32

const (
	ScsRemindTypeNotRemind ScsRemindType = 0
	ScsRemindTypeReminded  ScsRemindType = 1
)

type Model struct {
	ID        uint64    `json:"id,omitempty"`
	UserID    uint64    `json:"user_id,omitempty"`    // 用户id
	ActID     uint64    `json:"act_id,omitempty"`     // 活动id
	IsRemind  uint32    `json:"is_remind,omitempty"`  // 是否提醒 0未提醒 1已提醒
	IsDeleted uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedAt time.Time `json:"created_at"`           // 创建时间
	UpdatedAt time.Time `json:"updated_at"`           // 修改时间
}

func (m *Model) TableName() string {
	return "activity_subscribe"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type ModelList []*Model

func (ml ModelList) GetActIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ActID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetUIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.UserID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetActIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ActID]; !ok {
			retMap[val.ActID] = struct{}{}
		}
	}
	return retMap
}

type UserSubFilter struct {
	ID       uint64
	UID      uint64
	ActID    uint64
	IsRemind uint32
	ByRemind bool
}

type SubChan struct {
	UID   uint64
	ActID uint64
}
