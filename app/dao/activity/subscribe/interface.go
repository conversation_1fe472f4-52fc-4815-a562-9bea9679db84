package actsubscribe

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"
)

type Repo interface {
	// DataPageList .
	DataPageList(uid uint64, page, limit int) (int64, ModelList, error)
	// Create .
	Create(m *Model) error
	// FetchByID .
	FetchByID(uint64) (*Model, error)
	// CountUserSubByFilter .
	CountUserSubByFilter(*UserSubFilter) (int64, error)
	// FindUserSubByFilter .
	FindUserSubByFilter(*UserSubFilter) (ModelList, error)
	// DelSubscribe .
	DelSubscribe(uid, actID uint64) error
	// UpdateSubRemind .
	UpdateSubRemind(uint64) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
