package activity

import (
	"time"

	"blind_box/app/common/dbs"

	"gorm.io/gorm/clause"

	"github.com/golang-module/carbon/v2"
)

type ActStatus uint32
type ActType uint32

const (
	ActStatusPresale ActStatus = 1 // 预售
	ActStatusSale    ActStatus = 2 // 现货
	ActStatusSaleout ActStatus = 3 // 已售罄

	ActTypeBox ActType = 1 // 盲盒

)

type Model struct {
	ID            uint64    `gorm:"primarykey" json:"id"`
	Title         string    `json:"title,omitempty"`          // 标题
	Image         string    `json:"image,omitempty"`          // 活动图片
	OriginalPrice uint64    `json:"original_price,omitempty"` // 原价
	ActPrice      uint64    `json:"act_price,omitempty"`      // 活动价
	ActStatus     uint32    `json:"act_status,omitempty"`     // 活动状态 1现货 2预售 3已售罄
	ActType       uint32    `json:"act_type,omitempty"`       // 活动类型 1盲盒
	StartTime     int64     `json:"start_time,omitempty"`     // 开始时间
	EndTime       int64     `json:"end_time,omitempty"`       // 结束时间
	Recommend     uint32    `json:"recommend,omitempty"`      // 是否推荐 1是0否
	Sort          uint32    `json:"sort,omitempty"`           // 排序
	IsDeleted     uint32    `json:"is_deleted,omitempty"`     // 软删
	Remark        string    `json:"remark,omitempty"`         // 备注
	Tips          string    `json:"tips,,omitempty"`          // 提示语
	ConfigJson    string    `json:"config_json,omitempty"`    // 活动配置信息
	CreatedAt     time.Time `json:"created_at"`               // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`               // 修改时间
}

func (m *Model) TableName() string {
	return "activity"
}

func (m *Model) GetStartTime() string {
	if m.StartTime == 0 {
		return ""
	}
	return carbon.CreateFromTimestamp(m.StartTime).ToDateTimeString()
}

func (m *Model) GetEndTime() string {
	if m.EndTime == 0 {
		return ""
	}
	return carbon.CreateFromTimestamp(m.EndTime).ToDateTimeString()
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID        uint64
	Ids       []uint64
	Title     string
	StartTime int64
	EndTime   int64
	TitleLike string
	ActStatus ActStatus

	Sort []clause.OrderByColumn
}

type ModelList []*Model

func (ml ModelList) GetIds() []uint64 {
	ret := make([]uint64, 0, len(ml))
	retMap := make(map[uint64]struct{}, len(ml))

	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			ret = append(ret, val.ID)
			retMap[val.ID] = struct{}{}
		}
	}

	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	idMap := make(map[uint64]*Model)
	for _, m := range ml {
		idMap[m.ID] = m
	}
	return idMap
}
