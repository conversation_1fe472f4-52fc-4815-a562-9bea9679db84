package salecalendar

import (
	"blind_box/app/common/dbs"
	"time"
)

type Model struct {
	ID        uint64    `json:"id,omitempty"`
	ActID     uint64    `json:"act_id,omitempty"`     // 活动id
	TimeDesc  string    `json:"time_desc,omitempty"`  // 时间标志说明
	Sort      uint32    `json:"sort,omitempty"`       // 排序值(倒序)
	IsDeleted uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedAt time.Time `json:"created_at"`           // 创建时间
	UpdatedAt time.Time `json:"updated_at"`           // 修改时间
}

func (m *Model) TableName() string {
	return "activity_sale_calendar"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ActIds []uint64
	ActId  uint64
	Id     uint64
}

type ModelList []*Model

func (ml ModelList) GetActIds() []uint64 {
	ret := make([]uint64, 0, len(ml))
	retMap := make(map[uint64]struct{}, len(ml))

	for _, val := range ml {
		if _, ok := retMap[val.ActID]; !ok {
			ret = append(ret, val.ActID)
			retMap[val.ActID] = struct{}{}
		}
	}

	return ret
}
