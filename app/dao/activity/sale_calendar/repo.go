package salecalendar

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"time_desc", "sort"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// DelByID .
func (e *Entry) DelByID(ctx *gin.Context, id uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).Update(fmt.Sprintf("%v", dbs.SoftDelField), 1).Error; err != nil {
		return err
	}
	return nil
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.ActIds) > 0 {
		query.Where("act_id in (?)", f.ActIds)
	}

	query.Count(&total)
	if err = query.Offset((page - 1) * limit).Limit(int(limit)).
		Order("sort desc, id desc").Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (list *ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if err = query.Find(&list).Error; err != nil {
		return nil, err
	}
	return
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.Id != 0 {
		query.Where("id = ?", f.Id)
	}
	if f.ActId != 0 {
		query.Where("act_id = ?", f.ActId)
	}
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
