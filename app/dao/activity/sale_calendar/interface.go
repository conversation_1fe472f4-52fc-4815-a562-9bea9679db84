package salecalendar

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"
)

type Repo interface {
	// DataPageList .
	DataPageList(f *Filter, page, limit int) (int64, ModelList, error)
	// CreateOrUpdate .
	CreateOrUpdate(m *Model) (uint64, error)
	// FetchByID .
	FetchByID(uint64) (*Model, error)
	// FindByFilter .
	FindByFilter(*Filter) (*ModelList, error)
	// CountByFilter .
	CountByFilter(*Filter) (int64, error)
	// DelByID .
	DelByID(uint64) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
