package callbackLog

import (
	"time"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID              uint64    `gorm:"primarykey" json:"id"`
	NotifyID        string    `json:"notify_id,omitempty"`        // 微信通知ID
	ReqID           string    `json:"req_id,omitempty"`           // 请求id
	EntityType      uint64    `json:"entity_type,omitempty"`      // 1:支付,2:退款
	WxEntityID      string    `json:"wx_entity_id,omitempty"`     // 微信侧的实体ID
	OutEntityNo     string    `json:"out_entity_no,omitempty"`    // 商户侧的单号
	NotifyStatus    string    `json:"notify_status,omitempty"`    // 通知状态
	NotifyData      string    `json:"notify_data,omitempty"`      // 通知的数据
	ResourceDecrypt string    `json:"resource_decrypt,omitempty"` // resource解密后的数据
	Status          uint32    `json:"status,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "wxpay_callback_log"
}

type Filter struct {
	ID     uint64
	UserID uint64
}

type ModelList []*Model
