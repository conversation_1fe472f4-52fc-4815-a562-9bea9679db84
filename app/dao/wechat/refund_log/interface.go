package refundLog

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Repo 退款记录仓库接口
type Repo interface {
	// Create 创建退款记录
	Create(ctx *gin.Context, m *Model) error
	// CreateWithTx 使用事务创建退款记录
	CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error

	// UpdateByID 通过ID更新退款记录
	UpdateByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	// UpdateByIDWithTx 使用事务通过ID更新退款记录
	UpdateByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error

	// UpdateByOutRefundNo 通过商户退款单号更新退款记录
	UpdateByOutRefundNo(ctx *gin.Context, outRefundNo string, data map[string]interface{}) error
	// UpdateByOutRefundNoWithTx 使用事务通过商户退款单号更新退款记录
	UpdateByOutRefundNoWithTx(ctx *gin.Context, tx *gorm.DB, outRefundNo string, data map[string]interface{}) error

	// FindByID 通过ID查找退款记录
	FindByID(ctx *gin.Context, id uint64) (*Model, error)
	// FindByOutRefundNo 通过商户退款单号查找退款记录
	FindByOutRefundNo(ctx *gin.Context, outRefundNo string) (*Model, error)
	// FindByWxRefundID 通过微信退款单号查找退款记录
	FindByWxRefundID(ctx *gin.Context, wxRefundID string) (*Model, error)

	// FindByFilter 根据过滤器查找退款记录列表
	FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)
	// FindPageByFilter 根据过滤器分页查找退款记录
	FindPageByFilter(ctx *gin.Context, filter *Filter, page, pageSize int) (ModelList, int64, error)

	// CountByFilter 根据过滤器统计退款记录数量
	CountByFilter(ctx *gin.Context, filter *Filter) (int64, error)
}
