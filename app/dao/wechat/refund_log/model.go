package refundLog

import (
	"blind_box/app/common/dbs"
	"time"
)

// RefundStatus 退款状态枚举
type RefundStatus uint32

const (
	RefundStatusPending   RefundStatus = 1 // 退款中
	RefundStatusSuccess   RefundStatus = 2 // 退款成功
	RefundStatusFailed    RefundStatus = 3 // 退款失败
	RefundStatusCancelled RefundStatus = 4 // 退款已取消
)

// RefundReason 退款原因枚举
type RefundReason uint32

const (
	RefundReasonUserRequest RefundReason = 1 // 用户申请
	RefundReasonAdminRefund RefundReason = 2 // 管理员退款
	RefundReasonSystemError RefundReason = 3 // 系统错误
	RefundReasonOrderCancel RefundReason = 4 // 订单取消
	RefundReasonGoodsDefect RefundReason = 5 // 商品问题
)

// Model 微信退款记录表
type Model struct {
	ID              uint64     `gorm:"primarykey" json:"id"`
	UserID          uint64     `json:"user_id,omitempty"`                                   // 用户ID
	OrderID         uint64     `json:"order_id,omitempty"`                                  // 订单ID
	PayNo           string     `json:"pay_no,omitempty"`                                    // 原支付订单号
	OutRefundNo     string     `gorm:"uniqueIndex;not null" json:"out_refund_no,omitempty"` // 商户退款单号，使用helper.GetAppNo(helper.AppNoBoxRefund)生成
	WxRefundID      string     `json:"wx_refund_id,omitempty"`                              // 微信退款单号
	WxTransactionID string     `json:"wx_transaction_id,omitempty"`                         // 微信支付订单号
	TotalAmount     uint64     `json:"total_amount,omitempty"`                              // 订单总金额(分)
	RefundAmount    uint64     `json:"refund_amount,omitempty"`                             // 退款金额(分)
	RefundStatus    uint32     `json:"refund_status,omitempty"`                             // 退款状态: 1-退款中, 2-退款成功, 3-退款失败, 4-退款已取消
	RefundReason    uint32     `json:"refund_reason,omitempty"`                             // 退款原因: 1-用户申请, 2-管理员退款, 3-系统错误, 4-订单取消, 5-商品问题
	RefundDesc      string     `json:"refund_desc,omitempty"`                               // 退款说明
	SuccessTime     *time.Time `json:"success_time,omitempty"`                              // 退款成功时间
	FailReason      string     `json:"fail_reason,omitempty"`                               // 失败原因
	NotifyUrl       string     `json:"notify_url,omitempty"`                                // 退款通知地址
	RequestData     string     `json:"request_data,omitempty"`                              // 请求数据
	ResponseData    string     `json:"response_data,omitempty"`                             // 响应数据
	AdminID         uint64     `json:"admin_id,omitempty"`                                  // 操作管理员ID（如果是管理员操作）
	Remark          string     `json:"remark,omitempty"`                                    // 备注
	IsDeleted       uint32     `json:"is_deleted,omitempty"`                                // 软删除
	CreatedAt       time.Time  `json:"created_at"`                                          // 创建时间
	UpdatedAt       time.Time  `json:"updated_at"`                                          // 修改时间
}

func (m *Model) TableName() string {
	return "wxpay_refund_log"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// IsSuccess 判断退款是否成功
func (m *Model) IsSuccess() bool {
	return m.RefundStatus == uint32(RefundStatusSuccess)
}

// IsPending 判断退款是否进行中
func (m *Model) IsPending() bool {
	return m.RefundStatus == uint32(RefundStatusPending)
}

// Filter 查询过滤器
type Filter struct {
	ID             uint64
	IDs            []uint64
	UserID         uint64
	UserIDs        []uint64
	OrderID        uint64
	OrderIDs       []uint64
	PayNo          string
	PayNos         []string
	OutRefundNo    string
	OutRefundNos   []string
	WxRefundID     string
	RefundStatus   uint32
	RefundReason   uint32
	AdminID        uint64
	CreatedAtStart int64 // 创建时间开始（秒级时间戳）
	CreatedAtEnd   int64 // 创建时间结束（秒级时间戳）
	Sort           dbs.CommonSort
}

type ModelList []*Model

// GetIDMap 获取ID映射
func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

// GetOutRefundNoMap 获取商户退款单号映射
func (ml ModelList) GetOutRefundNoMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.OutRefundNo]; !ok {
			retMap[val.OutRefundNo] = val
		}
	}
	return retMap
}

// GetOrderIDMap 获取订单ID映射
func (ml ModelList) GetOrderIDMap() map[uint64]ModelList {
	retMap := make(map[uint64]ModelList)
	for _, val := range ml {
		retMap[val.OrderID] = append(retMap[val.OrderID], val)
	}
	return retMap
}
