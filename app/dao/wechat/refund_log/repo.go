package refundLog

import (
	"blind_box/app/common/dbs"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type repo struct{}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

// GetRepo 获取仓库实例
func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			defaultRepo = newRepo()
		})
	}
	return defaultRepo
}

func newRepo() Repo {
	return &repo{}
}

// Create 创建退款记录
func (r *repo) Create(ctx *gin.Context, m *Model) error {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true)
	return db.Create(m).Error
}

// CreateWithTx 使用事务创建退款记录
func (r *repo) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	return tx.Create(m).Error
}

// UpdateByID 通过ID更新退款记录
func (r *repo) UpdateByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true)
	return db.Model(&Model{}).Where("id = ? AND is_deleted = 0", id).Updates(data).Error
}

// UpdateByIDWithTx 使用事务通过ID更新退款记录
func (r *repo) UpdateByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	return tx.Model(&Model{}).Where("id = ? AND is_deleted = 0", id).Updates(data).Error
}

// UpdateByOutRefundNo 通过商户退款单号更新退款记录
func (r *repo) UpdateByOutRefundNo(ctx *gin.Context, outRefundNo string, data map[string]interface{}) error {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true)
	return db.Model(&Model{}).Where("out_refund_no = ? AND is_deleted = 0", outRefundNo).Updates(data).Error
}

// UpdateByOutRefundNoWithTx 使用事务通过商户退款单号更新退款记录
func (r *repo) UpdateByOutRefundNoWithTx(ctx *gin.Context, tx *gorm.DB, outRefundNo string, data map[string]interface{}) error {
	return tx.Model(&Model{}).Where("out_refund_no = ? AND is_deleted = 0", outRefundNo).Updates(data).Error
}

// FindByID 通过ID查找退款记录
func (r *repo) FindByID(ctx *gin.Context, id uint64) (*Model, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)
	var model Model
	err := db.Where("id = ? AND is_deleted = 0", id).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByOutRefundNo 通过商户退款单号查找退款记录
func (r *repo) FindByOutRefundNo(ctx *gin.Context, outRefundNo string) (*Model, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)
	var model Model
	err := db.Where("out_refund_no = ? AND is_deleted = 0", outRefundNo).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByWxRefundID 通过微信退款单号查找退款记录
func (r *repo) FindByWxRefundID(ctx *gin.Context, wxRefundID string) (*Model, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)
	var model Model
	err := db.Where("wx_refund_id = ? AND is_deleted = 0", wxRefundID).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByFilter 根据过滤器查找退款记录列表
func (r *repo) FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)

	query := db.Where("is_deleted = 0")
	query = r.buildQuery(query, filter)

	var list ModelList
	err := query.Find(&list).Error
	return list, err
}

// FindPageByFilter 根据过滤器分页查找退款记录
func (r *repo) FindPageByFilter(ctx *gin.Context, filter *Filter, page, pageSize int) (ModelList, int64, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)

	query := db.Where("is_deleted = 0")
	query = r.buildQuery(query, filter)

	// 统计总数
	var count int64
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var list ModelList
	err := query.Offset(offset).Limit(pageSize).Find(&list).Error

	return list, count, err
}

// CountByFilter 根据过滤器统计退款记录数量
func (r *repo) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	db := dbs.NewMysqlEngines().UseWithGinCtx(ctx, false)

	query := db.Model(&Model{}).Where("is_deleted = 0")
	query = r.buildQuery(query, filter)

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// buildQuery 构建查询条件
func (r *repo) buildQuery(query *gorm.DB, filter *Filter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.ID > 0 {
		query = query.Where("id = ?", filter.ID)
	}
	if len(filter.IDs) > 0 {
		query = query.Where("id IN ?", filter.IDs)
	}
	if filter.UserID > 0 {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if len(filter.UserIDs) > 0 {
		query = query.Where("user_id IN ?", filter.UserIDs)
	}
	if filter.OrderID > 0 {
		query = query.Where("order_id = ?", filter.OrderID)
	}
	if len(filter.OrderIDs) > 0 {
		query = query.Where("order_id IN ?", filter.OrderIDs)
	}
	if filter.PayNo != "" {
		query = query.Where("pay_no = ?", filter.PayNo)
	}
	if len(filter.PayNos) > 0 {
		query = query.Where("pay_no IN ?", filter.PayNos)
	}
	if filter.OutRefundNo != "" {
		query = query.Where("out_refund_no = ?", filter.OutRefundNo)
	}
	if len(filter.OutRefundNos) > 0 {
		query = query.Where("out_refund_no IN ?", filter.OutRefundNos)
	}
	if filter.WxRefundID != "" {
		query = query.Where("wx_refund_id = ?", filter.WxRefundID)
	}
	if filter.RefundStatus > 0 {
		query = query.Where("refund_status = ?", filter.RefundStatus)
	}
	if filter.RefundReason > 0 {
		query = query.Where("refund_reason = ?", filter.RefundReason)
	}
	if filter.AdminID > 0 {
		query = query.Where("admin_id = ?", filter.AdminID)
	}

	// 时间范围过滤
	if filter.CreatedAtStart > 0 {
		query = query.Where("created_at >= ?", time.Unix(filter.CreatedAtStart, 0))
	}
	if filter.CreatedAtEnd > 0 {
		query = query.Where("created_at <= ?", time.Unix(filter.CreatedAtEnd, 0))
	}

	// 排序
	if filter.Sort.Field != "" {
		order := string(filter.Sort.Field) + " " + string(filter.Sort.Method)
		query = query.Order(order)
	} else {
		// 默认按创建时间倒序
		query = query.Order("created_at DESC")
	}

	return query
}
