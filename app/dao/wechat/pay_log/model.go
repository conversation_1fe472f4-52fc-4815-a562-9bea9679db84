package payLog

import (
	"time"
	"blind_box/app/common/dbs"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID            uint64    `gorm:"primarykey" json:"id"`
	UserID        uint64    `json:"user_id,omitempty"`
	ReqID         string    `json:"req_id,omitempty"`
	Amount        uint64    `json:"amount,omitempty"`
	EntityType    uint32    `json:"entity_type,omitempty"`     // 1:支付,2:退款
	RequestFunc   string    `json:"request_func,omitempty"`    // 请求的函数名
	WxEntityID    string    `json:"wx_entity_id,omitempty"`    // 微信侧的实体id
	OutEntityNo   string    `json:"out_entity_no,omitempty"`   // 商户侧的实体单号
	Status        uint32    `json:"status,omitempty"`          // 状态 1:成功,2:失败
	OutEntityInfo string    `json:"out_entity_info,omitempty"` // 商户侧的实体信息
	RequestData   string    `json:"request_data,omitempty"`    // 请求数据
	ResponseData  string    `json:"response_data,omitempty"`   // 响应数据
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "wxpay_log"
}

type Filter struct {
	ID           uint64
	UserID       uint64
	EntityType   uint32
	WxEntityID   string
	OutEntityNo  string
	OutEntityNos []string
	Sort         dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetOutEntityNoMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.OutEntityNo]; !ok {
			retMap[val.OutEntityNo] = val
		}
	}
	return retMap
}
