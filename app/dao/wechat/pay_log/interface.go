package payLog

import (
	"sync"
	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	BatchCreateWithTx(*gorm.DB, []*Model) error

	FetchByUdx(ctx *gin.Context, billNo string) (*Model, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
}

// TODO 替换
var (
	// defaultRepo         Repo
	defaultRepo         *Entry
	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {

	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
	}
}
