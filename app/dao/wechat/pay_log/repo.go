package payLog

import (
	"blind_box/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(tx *gorm.DB, m []*Model) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByUdx .
func (e *Entry) UpdateMapByUdx(ctx *gin.Context, outEntityNo string, data map[string]interface{}) error {
	return e.UpdateMapByUdxWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), outEntityNo, data)
}

// UpdateMapByUdxWithTx .
func (e *Entry) UpdateMapByUdxWithTx(ctx *gin.Context, tx *gorm.DB, outEntityNo string, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("out_entity_no = ?", outEntityNo).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildBillQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{})

	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if f.EntityType != 0 {
		query.Where("entity_type = ?", f.EntityType)
	}
	if f.WxEntityID != "" {
		query.Where("wx_entity_id = ?", f.WxEntityID)
	}
	if f.OutEntityNo != "" {
		query.Where("out_entity_no = ?", f.OutEntityNo)
	}
	return query
}

// FetchByUdx .
func (e *Entry) FetchByUdx(ctx *gin.Context, billNo string) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("bill_no = ?", billNo).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindBillByFilter .
func (e *Entry) FindBillByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildBillQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
