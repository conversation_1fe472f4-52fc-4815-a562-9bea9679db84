package coupon

import (
	"blind_box/app/common/dbs"
	"time"
)

type DFCouponSource uint32

const (
	CouponSourceReg       DFCouponSource = 1 // 新人奖励
	CouponSourceHomepage  DFCouponSource = 2 // 首页弹出
	CouponSourceSign      DFCouponSource = 3 // 签到
	CouponSourceAdminSend DFCouponSource = 4 // 后台发放
)

type SourceModel struct {
	ID        uint64    `json:"id,omitempty"`
	Name      string    `json:"name,omitempty"`       // 名称
	Sort      uint32    `json:"sort,omitempty"`       // 排序值(倒序)
	IsDeleted uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedAt time.Time `json:"created_at"`           // 创建时间
	UpdatedAt time.Time `json:"updated_at"`           // 修改时间
}

func (m *SourceModel) TableName() string {
	return "coupon_source"
}

func (m *SourceModel) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type SourceModelList []*SourceModel

func (ml SourceModelList) GetIDMap() map[uint64]string {
	retMap := make(map[uint64]string, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val.Name
		}
	}
	return retMap
}
