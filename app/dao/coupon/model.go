package coupon

import (
	"blind_box/app/common/dbs"
	couponDto "blind_box/app/dto/coupon"
	"encoding/json"
	"time"

	"github.com/shopspring/decimal"
)

type (
	DFDiscountType   uint32
	DFDiscountMethod uint32
	DFThresholdType  uint32
	DFRangeType      uint32
)

const (
	CouponRangeAll DFRangeType = 0 // 无限制
	CouponRangeAct DFRangeType = 1 // 指定活动id
	// CouponRangeSpu DFRangeType = 2 // spuid
	// CouponRangeSku DFRangeType = 3 // skuid

	CouponDTWholeOrder         DFDiscountType   = 1 // 整单折扣
	CouponDMCashSubtract       DFDiscountMethod = 1 // 现金立减
	CouponTTRealityPaySubtract DFThresholdType  = 1 // 实付满减
)

type Model struct {
	ID              uint64    `json:"id,omitempty"`
	Code            string    `json:"code,omitempty"`                           // 优惠券code
	Name            string    `json:"name,omitempty"`                           // 名称
	DiscountType    uint32    `json:"discount_type,omitempty"`                  // 折扣类型 1整单折扣
	DiscountMethod  uint32    `json:"discount_method,omitempty"`                // 优惠方式 1现金立减
	DiscountAmount  uint64    `json:"discount_amount,omitempty"`                // 优惠金额(分)
	ThresholdType   uint32    `json:"threshold_type,omitempty"`                 // 门槛类型 0无门槛 1实付满减
	ThresholdAmount uint64    `json:"threshold_amount,omitempty"`               // 门槛金额
	ExpireDays      uint32    `json:"expire_days,omitempty"`                    // 领取有效天数
	RangeType       uint32    `json:"range_type,omitempty"`                     // 使用范围 0全场无限制 1指定活动id
	Extra           string    `json:"extra,omitempty" gorm:"type:varchar(255)"` // 文字展示配置
	Status          uint32    `json:"status,omitempty"`                         // 状态 1启用 2停用
	IsDeleted       uint32    `json:"is_deleted,omitempty"`                     // 软删
	CreatedAt       time.Time `json:"created_at"`                               // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`                               // 修改时间
}

type Extra struct {
	NameDesc      string `json:"name_desc,omitempty"`      // 名称说明
	DetailDesc    string `json:"detail_desc,omitempty"`    // 详情说明
	DiscountDesc  string `json:"discount_desc,omitempty"`  // 折扣说明
	ThresholdDesc string `json:"threshold_desc,omitempty"` // 门槛说明
}

func (m *Model) TableName() string {
	return "coupon"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	Name   string
	Code   string
	Status uint32
	Sort   dbs.CommonSort
}

// SetCouponReqToModel .
func SetCouponReqToModel(req couponDto.AdminSetCouponReq) *Model {
	m := &Model{
		ID:              req.ID,
		Code:            req.Code,
		Name:            req.Name,
		DiscountType:    req.DiscountType,
		DiscountMethod:  req.DiscountMethod,
		DiscountAmount:  uint64(decimal.NewFromFloat(req.DiscountAmount).Mul(decimal.NewFromInt(100)).IntPart()),
		ThresholdType:   req.ThresholdType,
		ThresholdAmount: uint64(decimal.NewFromFloat(req.ThresholdAmount).Mul(decimal.NewFromInt(100)).IntPart()),
		ExpireDays:      req.ExpireDays,
		RangeType:       req.RangeType,
	}
	extra, _ := json.Marshal(req.Extra)
	m.Extra = string(extra)
	if req.ID == 0 {
		m.Status = uint32(dbs.StatusEnable)
	}
	return m
}

// GetDiscountAmount .
func (m *Model) GetDiscountAmount() float64 {
	ret, ok := decimal.NewFromInt(int64(m.DiscountAmount)).Div(decimal.NewFromInt(100)).Round(2).Float64()
	if !ok {
		return float64(m.DiscountAmount) / 100
	}
	return ret
}

// GetThresholdAmount .
func (m *Model) GetThresholdAmount() float64 {
	ret, ok := decimal.NewFromInt(int64(m.ThresholdAmount)).Div(decimal.NewFromInt(100)).Round(2).Float64()
	if !ok {
		return float64(m.ThresholdAmount) / 100
	}
	return ret
}

// GetExtra .
func (m *Model) GetExtra() *couponDto.CExtra {
	ret := &couponDto.CExtra{}
	if err := json.Unmarshal([]byte(m.Extra), &ret); err != nil {
		return ret
	}
	return ret
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
