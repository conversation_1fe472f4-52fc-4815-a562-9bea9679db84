package couponRange

import (
	couponDao "blind_box/app/dao/coupon"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// DelByCIDEntityType .
func (e *Entry) DelByCIDEntityType(ctx *gin.Context, cid uint64, eType couponDao.DFRangeType) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("coupon_id = ? and entity_type = ?", cid, eType).
		Delete(&Model{}).Error; err != nil {
		return err
	}
	return nil
}

// FindEntityIDSByFilter .
func (e *Entry) FindEntityIDSByFilter(ctx *gin.Context, f *Filter) ([]uint64, error) {
	ret := []uint64{}
	if f.CouponID == 0 && f.EntityType == 0 {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{})
	if f.CouponID > 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.EntityType != 0 {
		query.Where("entity_type = ?", f.EntityType)
	}
	if err := query.Distinct().Pluck("entity_id", &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{})
	if f.CouponID > 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.EntityType != 0 {
		query.Where("entity_type = ?", f.EntityType)
	}
	if f.EntityID != 0 {
		query.Where("entity_id = ?", f.EntityID)
	}
	if err = query.Count(&num).Error; err != nil {
		return
	}
	return
}
