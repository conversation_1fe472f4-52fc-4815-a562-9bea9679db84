package couponRange

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"
)

type Repo interface {
	// // BatchCreateWithTx .
	// BatchCreateWithTx(*gorm.DB, []*Model) error
	// // DelByCIDEntityType .
	// DelByCIDEntityType(uint64, couponDao.DFRangeType) error
	// // FindEntityIDSByFilter .
	// FindEntityIDSByFilter(*Filter) ([]uint64, error)
	// // CountByFilter .
	// CountByFilter(*Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
