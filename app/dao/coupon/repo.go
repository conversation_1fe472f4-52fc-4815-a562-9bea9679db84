package coupon

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(tx *gorm.DB, m *Model) (uint64, error) {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"name", "discount_type", "discount_method", "discount_amount",
				"threshold_type", "threshold_amount",
				"expire_days", "range_type", "extra",
			}),
		}).Create(&m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil
}

// UpdateStatus .
func (e *Entry) UpdateStatus(ctx *gin.Context, id uint64, status dbs.StatusType) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).
		Update("status", status).Error; err != nil {
		return err
	}
	return nil
}

// DelByID .
func (e *Entry) DelByID(ctx *gin.Context, id uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).
		Update(fmt.Sprintf("%v", dbs.SoftDelField), 1).Error; err != nil {
		return err
	}
	return nil
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list []*Model, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.Name != "" {
		query.Where("name like ?", "%"+f.Name+"%")
	}
	if f.Code != "" {
		query.Where("code = ?", f.Code)
	}

	query.Count(&total)
	if err = query.Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FetchByUdx .
func (e *Entry) FetchByUdx(ctx *gin.Context, f *Filter) (*Model, error) {
	ret := &Model{}
	if f.ID == 0 && f.Code == "" {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.Code != "" {
		query.Where("code = ?", f.Code)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	ret := ModelList{}
	if f.Code == "" && f.Name == "" && len(f.IDS) == 0 {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.Name != "" {
		query.Where("name like ?", "%"+f.Name+"%")
	}
	if f.Code != "" {
		query.Where("code = ?", f.Code)
	}
	if len(f.IDS) != 0 {
		query.Where("id in (?)", f.IDS)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindIdsByFilter .
func (e *Entry) FindIdsByFilter(ctx *gin.Context, f *Filter) ([]uint64, error) {
	ret := []uint64{}
	if f.Code == "" && f.Name == "" && f.Status == 0 {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.Name != "" {
		query.Where("name like ?", "%"+f.Name+"%")
	}
	if f.Code != "" {
		query.Where("code = ?", f.Code)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if err := query.Distinct().Pluck("id", &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.Name) > 0 {
		query.Where("name like ?", "%"+f.Name+"%")
	}
	if len(f.Code) > 0 {
		query.Where("code = ?", f.Code)
	}
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}

	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}
