package couponTarget

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PluckField string

const (
	PluckUID PluckField = "user_id"
	PluckIID PluckField = "issue_id"
)

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreateIgnoreWithTx .
func (e *Entry) BatchCreateIgnoreWithTx(tx *gorm.DB, m []*Model) (int64, error) {
	valStrings := []string{}
	valArgs := []interface{}{}
	for _, item := range m {
		valStrings = append(valStrings, "(?,?)")
		valArgs = append(valArgs, item.IssueID, item.UserID)
	}
	sql := fmt.Sprintf("INSERT IGNORE INTO %s (issue_id, user_id) VALUES %s",
		_Model.TableName(), strings.Join(valStrings, ","))

	ret := tx.Exec(sql, valArgs...)
	if err := ret.Error; err != nil {
		return 0, err
	}
	return ret.RowsAffected, nil
}

// DelByIssueIDAndUidsWithTx .
func (e *Entry) DelByIssueIDAndUidsWithTx(ctx *gin.Context, tx *gorm.DB, issueID uint64, userIDS []uint64) (int64, error) {
	ret := tx.Model(&Model{}).Where("issue_id = ? and user_id in (?)", issueID, userIDS).Delete(&Model{})
	if err := tx.Error; err != nil {
		return 0, err
	}
	return ret.RowsAffected, nil
}

// FindXIDSByFilter .
func (e *Entry) FindXIDSByFilter(ctx *gin.Context, f *Filter, field PluckField) ([]uint64, error) {
	ret := []uint64{}
	if f.IssueID == 0 && f.UserID == 0 {
		return ret, nil
	}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{})
	if f.IssueID != 0 {
		query.Where("issue_id = ?", f.IssueID)
	}
	if f.UserID != 0 {
		query.Where("user_id = ?", f.UserID)
	}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
