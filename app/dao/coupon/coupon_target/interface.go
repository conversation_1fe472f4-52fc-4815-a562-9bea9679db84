package couponTarget

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"
)

type Repo interface {
	// // BatchCreateWithTx .
	// BatchCreateWithTx(*gorm.DB, []*Model) error
	// // BatchCreateIgnoreWithTx .
	// BatchCreateIgnoreWithTx(*gorm.DB, []*Model) (int64, error)
	// // DelByIssueIDAndUidsWithTx .
	// DelByIssueIDAndUidsWithTx(*gorm.DB, uint64, []uint64) (int64, error)
	// // FindXIDSByFilter .
	// FindXIDSByFilter(*Filter, PluckField) ([]uint64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
