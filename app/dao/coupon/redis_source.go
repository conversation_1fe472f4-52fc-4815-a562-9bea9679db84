package coupon

import (
	"blind_box/app/common/dbs"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisSourceList .
func (e *Entry) RedisSourceList(ctx *gin.Context) (list SourceModelList, err error) {
	cacheKey := redisPkg.CouponSourceListKey
	ret := SourceModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadSourceList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadSourceList redis重载数据列表
func (e *Entry) RedisReloadSourceList(ctx *gin.Context) (SourceModelList, error) {
	var (
		cacheKey   = redisPkg.CouponSourceListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = SourceModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.SourceList(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearSourceList .
func (e *Entry) RedisClearSourceList(ctx *gin.Context) error {
	cacheKey := redisPkg.CouponSourceListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
