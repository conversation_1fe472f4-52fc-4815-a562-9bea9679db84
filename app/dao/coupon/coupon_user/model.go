package couponUser

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type DFCUserStatus uint32
type DFCUserFilterStatus string

const (
	CUserGet     DFCUserStatus = 1 // 已领取
	CUserUsing   DFCUserStatus = 2 // 使用中
	CUserUsed    DFCUserStatus = 3 // 已使用
	CUserAbandon DFCUserStatus = 4 // 已废弃

	CUserFilterWaiting DFCUserFilterStatus = "waiting" // 待使用
	CUserFilterUsed    DFCUserFilterStatus = "used"    // 已使用
	CUserFilterExpired DFCUserFilterStatus = "expired" // 已失效
)

type Model struct {
	ID             uint64    `json:"id,omitempty"`
	UserID         uint64    `json:"user_id,omitempty"`
	CouponID       uint64    `json:"coupon_id,omitempty"`
	IssueID        uint64    `json:"issue_id,omitempty"`
	SourceID       uint64    `json:"source_id,omitempty"`
	TradeNo        string    `json:"trade_no,omitempty"`        // 支付流水号
	DiscountAmount uint64    `json:"discount_amount,omitempty"` // 订单优惠金额
	ExpireTime     int64     `json:"expire_time,omitempty"`     // 过期时间戳
	UseTime        int64     `json:"use_time,omitempty"`        // 使用时间戳
	Status         uint32    `json:"status,omitempty"`          // 状态: 1 已领取 2使用中 3已使用 4已作废
	Remark         string    `json:"remark,omitempty"`          // 备注
	IsDeleted      uint32    `json:"is_deleted,omitempty"`      // 软删
	CreatedAt      time.Time `json:"created_at"`                // 创建时间
	UpdatedAt      time.Time `json:"updated_at"`                // 修改时间
}

func (m *Model) TableName() string {
	return "coupon_user"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// GetDiscountAmount .
func (m *Model) GetDiscountAmount() float64 {
	ret, ok := decimal.NewFromInt(int64(m.DiscountAmount)).Div(decimal.NewFromInt(100)).Round(2).Float64()
	if !ok {
		return float64(m.DiscountAmount) / 100
	}
	return ret
}

type Filter struct {
	ID           uint64
	UID          uint64
	CouponID     uint64
	IssueID      uint64
	UIDS         []uint64
	CouponIDS    []uint64
	NotCouponIDS []uint64
	IssueIDS     []uint64
	SourceID     uint64
	TradeNo      string
	StartTime    int64
	EndTime      int64
	Status       uint32
	Valid        bool // 有效:已领取,已占用, 有效期内
	Enable       bool // 可用:已领取,有效期内
	Invalid      bool // 失效：已领取,已占用, 不在有效期内的(不包含已使用的)
	Sort         dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetCids() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.CouponID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetUids() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.UserID
	})
	ret = lo.Uniq(ret)
	return ret
}

type CountGroupIssueUser struct {
	IssueID uint64 `json:"issue_id,omitempty"`
	Count   int64  `json:"count,omitempty"`
}

type CountGroupIssueUserList []*CountGroupIssueUser

func (ml CountGroupIssueUserList) GetIDMap() map[uint64]*CountGroupIssueUser {
	retMap := make(map[uint64]*CountGroupIssueUser, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.IssueID]; !ok {
			retMap[val.IssueID] = val
		}
	}
	return retMap
}
