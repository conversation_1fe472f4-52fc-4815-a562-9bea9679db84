package couponUser

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/log"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
)

type PluckField string

const (
	PluckUID PluckField = "user_id"
	PluckIID PluckField = "issue_id"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m []*Model) error {
	return e.BatchCreateWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(tx *gorm.DB, m []*Model) error {
	if len(m) == 0 {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateByFilterWithTx .
func (e *Entry) UpdateByFilterWithTx(tx *gorm.DB, f *Filter, m *Model) error {
	if f.ID == 0 && f.CouponID == 0 && f.Status == 0 {
		return nil
	}
	query := tx.Model(&Model{})
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.CouponID != 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if err := query.Updates(m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateByFilter .
func (e *Entry) UpdateByFilter(ctx *gin.Context, f *Filter, m *Model) error {
	return e.UpdateByFilterWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), f, m)
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if f.CouponID != 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if len(f.CouponIDS) > 0 {
		query.Where("coupon_id in ?", f.CouponIDS)
	}
	if len(f.NotCouponIDS) > 0 {
		query.Where("coupon_id not in ?", f.NotCouponIDS)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.SourceID != 0 {
		query.Where("source_id = ?", f.SourceID)
	}
	if f.TradeNo != "" {
		query.Where("trade_no = ?", f.TradeNo)
	}
	if f.Valid {
		nowTime := carbon.Now().Timestamp()
		query.Where("(status = ? or status = ?) and expire_time >= ?", CUserGet, CUserUsing, nowTime)
	}
	if f.Invalid {
		nowTime := carbon.Now().Timestamp()
		query.Where("(status = ? or status = ?) and expire_time < ?", CUserGet, CUserUsing, nowTime)
	}
	if f.Enable {
		nowTime := carbon.Now().Timestamp()
		query.Where("status = ? and expire_time >= ?", CUserGet, nowTime)
	}
	if f.StartTime > 0 {
		query.Where("created_at >= ?", carbon.CreateFromTimestamp(f.StartTime))
	}
	if f.EndTime > 0 {
		query.Where("created_at <= ?", carbon.CreateFromTimestamp(f.EndTime))
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	query.Select("id").Count(&total)
	if err = query.Select("*").Order(dbs.GetDefaultSort(f.Sort)).Offset((page - 1) * limit).Limit(int(limit)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList error")
		return 0, nil, err
	}
	return total, list, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXIDSByFilter .
func (e *Entry) FindXIDSByFilter(ctx *gin.Context, f *Filter, field PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if len(f.UIDS) != 0 {
		query.Where("user_id in (?)", f.UIDS)
	}
	if f.IssueID != 0 {
		query.Where("issue_id = ?", f.IssueID)
	}
	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Select("id").
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if f.CouponID != 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.SourceID != 0 {
		query.Where("source_id = ?", f.SourceID)
	}
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// CountGroupIssueUser .
func (e *Entry) CountGroupIssueUser(ctx *gin.Context, issudIDS []uint64, status DFCUserStatus) (CountGroupIssueUserList, error) {
	ret := CountGroupIssueUserList{}
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("issue_id in (?)", issudIDS)
	if status != 0 {
		query.Where("status = ?", status)
	}
	if err := query.Select("issue_id, COUNT(DISTINCT user_id) as count").
		Group("issue_id").Scan(&ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}
