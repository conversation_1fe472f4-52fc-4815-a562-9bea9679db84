package couponUser

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"
)

type Repo interface {
	// // BatchCreateWithTx .
	// BatchCreateWithTx(*gorm.DB, []*Model) error
	// // BatchCreate .
	// BatchCreate([]*Model) error
	// // UpdateByFilterWithTx .
	// UpdateByFilterWithTx(*gorm.DB, *Filter, *Model) error
	// // UpdateByFilter .
	// UpdateByFilter(*Filter, *Model) error
	// // DataPageList .
	// DataPageList(f *Filter, page, limit int) (int64, ModelList, error)
	// // FetchByID .
	// FetchByID(uint64) (*Model, error)
	// // FindByFilter .
	// FindByFilter(*Filter) (ModelList, error)
	// // FindXIDSByFilter .
	// FindXIDSByFilter(*Filter, PluckField) ([]uint64, error)
	// // CountByFilter .
	// CountByFilter(*Filter) (num int64, err error)
	// // CountGroupIssueUser .
	// CountGroupIssueUser([]uint64, DFCUserStatus) (CountGroupIssueUserList, error)
}
type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
