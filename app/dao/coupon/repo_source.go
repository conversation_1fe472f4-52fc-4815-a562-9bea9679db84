package coupon

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

// SourceCreateOrUpdate .
func (e *Entry) SourceCreateOrUpdate(ctx *gin.Context, m *SourceModel) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&SourceModel{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"name", "sort"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.RedisClearSourceList(ctx)
	return nil
}

// SourceDelByID .
func (e *Entry) SourceDelByID(ctx *gin.Context, id uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&SourceModel{}).Where("id = ?", id).
		Update(fmt.Sprintf("%v", dbs.SoftDelField), 1).Error; err != nil {
		return err
	}
	e.RedisClearSourceList(ctx)
	return nil
}

// SourceList .
func (e *Entry) SourceList(ctx *gin.Context) (list SourceModelList, err error) {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&SourceModel{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Select("id", "name").Order("sort asc, id asc").
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// SourceFetchByID .
func (e *Entry) SourceFetchByID(ctx *gin.Context, id uint64) (*SourceModel, error) {
	ret := &SourceModel{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&SourceModel{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
