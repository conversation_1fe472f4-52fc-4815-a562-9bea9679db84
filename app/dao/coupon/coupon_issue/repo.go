package couponIssue

import (
	"blind_box/app/common/dbs"
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(tx *gorm.DB, m *Model) (uint64, error) {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"name", "coupon_id", "source_id", "start_time", "end_time",
			}),
		}).Create(&m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil
}

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) (uint64, error) {
	return e.CreateOrUpdateWithTx(e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// UpdateStatus .
func (e *Entry) UpdateStatus(ctx *gin.Context, id uint64, status dbs.StatusType) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).
		Update("status", status).Error; err != nil {
		return err
	}
	return nil
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.CouponID != 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.IssueType != 0 {
		query.Where("issue_type = ?", f.IssueType)
	}

	query.Count(&total)
	if err = query.Offset((page - 1) * limit).Limit(int(limit)).
		Order(dbs.GetDefaultSort(f.Sort)).Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.SourceID != 0 {
		query.Where("source_id = ?", f.SourceID)
	}
	if f.IsValid {
		nowTime := carbon.Now().Timestamp()
		query.Where("start_time <= ? and end_time >= ? and status = ?", nowTime, nowTime, dbs.StatusEnable)
	}
	if len(f.NotIDS) != 0 {
		query.Where("id not in (?)", f.NotIDS)
	}
	if f.IssueType != 0 {
		query.Where("issue_type = ?", f.IssueType)
	}
	if f.ByAssign {
		if len(f.AssignIDS) > 0 {
			query.Where("(issue_type = ?) or (issue_type = ? and id in (?))", IssueTypeAll, IssueTypeAssign, f.AssignIDS)
		} else {
			query.Where("issue_type = ?", IssueTypeAll)
		}
	}
	ret := ModelList{}
	if err := query.Order(dbs.GetDefaultSort(f.Sort)).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).Select("id")
	if f.CouponID != 0 {
		query.Where("coupon_id = ?", f.CouponID)
	}
	if f.NotID != 0 {
		query.Where("id <> ?", f.NotID)
	}
	if f.IsValid {
		nowTime := carbon.Now().Timestamp()
		query.Where("start_time <= ? and end_time >= ? and status = ?", nowTime, nowTime, dbs.StatusEnable)
	}
	if err = query.Count(&num).Error; err != nil {
		return
	}
	return
}
