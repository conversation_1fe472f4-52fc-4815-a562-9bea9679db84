package couponIssue

import (
	"blind_box/app/common/dbs"
	"time"

	"github.com/samber/lo"
)

type DFIssueType uint32

const (
	IssueTypeAll    DFIssueType = 1 // 全部用户
	IssueTypeAssign DFIssueType = 2 // 指定用户
)

type Model struct {
	ID        uint64    `json:"id,omitempty"`
	Name      string    `json:"name,omitempty"`       // 发放名称
	CouponID  uint64    `json:"coupon_id,omitempty"`  // 优惠券id
	SourceID  uint64    `json:"source_id,omitempty"`  // 优惠券来源id
	IssueType uint32    `json:"issue_type,omitempty"` // 发放类型 1全部用户 2指定用户
	StartTime int64     `json:"start_time,omitempty"` // 发放开始时间
	EndTime   int64     `json:"end_time,omitempty"`   // 发放结束时间
	Stock     uint32    `json:"stock,omitempty"`      // 指定用户-发放数量
	Status    uint32    `json:"status,omitempty"`     // 状态 1 启用 2停用
	IsDeleted uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedAt time.Time `json:"created_at"`           // 创建时间
	UpdatedAt time.Time `json:"updated_at"`           // 修改时间
}

func (m *Model) TableName() string {
	return "coupon_issue"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// GetStockByAction .
func (m *Model) GetStockByAction(num int64, action dbs.OperateAction) uint32 {
	switch action {
	case dbs.ActionAdd:
		return m.Stock + uint32(num)
	case dbs.ActionSubtract:
		if m.Stock >= uint32(num) {
			return m.Stock - uint32(num)
		} else {
			return 0
		}
	}
	return m.Stock
}

type Filter struct {
	IssueType uint32
	CouponID  uint64
	NotID     uint64
	IsValid   bool
	SourceID  uint64
	NotIDS    []uint64
	ByAssign  bool
	AssignIDS []uint64
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetCids() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.CouponID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}
