package couponIssue

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/helper"
	redisPkg "blind_box/pkg/redis"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisGetImportIssueTarget .
func (e *Entry) RedisGetImportIssueTarget(ctx *gin.Context, key string) ([]uint64, error) {
	cacheKey := redisPkg.GetCouponIssueImportTargetKey(key)
	ret := []uint64{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return ret, nil
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisSaveImportIssueTarget .
func (e *Entry) RedisSaveImportIssueTarget(ctx *gin.Context, uids []uint64) (string, error) {
	var (
		randonNum  = helper.GetRandCodeByTime(8)
		cacheKey   = redisPkg.GetCouponIssueImportTargetKey(randonNum)
		expireTime = dbs.GetRedisExpireTime(dbs.RedisExpireTimeTwoHour)

		cacheData []byte
		err       error
	)
	if cacheData, err = json.Marshal(uids); err != nil {
		return randonNum, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return randonNum, err
	}
	return randonNum, nil
}
