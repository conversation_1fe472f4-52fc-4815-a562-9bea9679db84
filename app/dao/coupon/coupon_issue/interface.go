package couponIssue

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"gorm.io/gorm"
)

type Repo interface {
	// CouponIssue
	// RedisCouponIssue
}

type CouponIssue interface {
	// CreateOrUpdateWithTx .
	CreateOrUpdateWithTx(tx *gorm.DB, m *Model) (uint64, error)
	// CreateOrUpdate .
	CreateOrUpdate(m *Model) (uint64, error)
	// UpdateStatus .
	UpdateStatus(id uint64, status dbs.StatusType) error
	// DataPageList .
	DataPageList(f *Filter, page, limit int) (int64, ModelList, error)
	// FetchByID .
	FetchByID(id uint64) (*Model, error)
	// FindByFilter .
	FindByFilter(f *Filter) (ModelList, error)
	// CountByFilter .
	CountByFilter(f *Filter) (int64, error)
}

type RedisCouponIssue interface {
	// RedisGetImportIssueTarget .
	RedisGetImportIssueTarget(key string) ([]uint64, error)
	// RedisSaveImportIssueTarget .
	RedisSaveImportIssueTarget(uids []uint64) (string, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
