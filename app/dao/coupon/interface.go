package coupon

import (
	"blind_box/app/common/dbs"
	"blind_box/pkg/redis"
	"sync"

	"gorm.io/gorm"
)

type Repo interface {
	// Coupon
	// CouponSource
	// RedisCoupon
	// RedisSource
}

type Coupon interface {
	// CreateOrUpdate .
	CreateOrUpdateWithTx(*gorm.DB, *Model) (uint64, error)
	// UpdateStatus .
	UpdateStatus(uint64, dbs.StatusType) error
	// DelByID .
	DelByID(uint64) error
	// DataPageList .
	DataPageList(f *Filter, page, limit int) (int64, []*Model, error)
	// FetchByUdx .
	FetchByUdx(f *Filter) (*Model, error)
	// FetchByID .
	FetchByID(uint64) (*Model, error)
	// FindByFilter .
	FindByFilter(*Filter) (ModelList, error)
	// FindIdsByFilter .
	FindIdsByFilter(f *Filter) ([]uint64, error)
	// CountByFilter .
	CountByFilter(*Filter) (int64, error)
}

type CouponSource interface {
	// SourceCreateOrUpdate .
	SourceCreateOrUpdate(m *SourceModel) error
	// DelByID .
	SourceDelByID(uint64) error
	// SourceList .
	SourceList() (SourceModelList, error)
	// FetchByID .
	SourceFetchByID(uint64) (*SourceModel, error)
}

type RedisCoupon interface {
}

type RedisSource interface {
	// RedisSourceList .
	RedisSourceList() (list SourceModelList, err error)
	// RedisReloadSourceList redis重载数据列表
	RedisReloadSourceList() (SourceModelList, error)
	// RedisClearSourceList .
	RedisClearSourceList() error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo          Repo
	defaultRepoInitOnce sync.Once
	defaultRepo         *Entry
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
