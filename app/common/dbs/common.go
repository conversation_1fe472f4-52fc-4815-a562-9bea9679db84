package dbs

import (
	"database/sql/driver"
	"fmt"
	"reflect"
	"strings"
	"time"

	"blind_box/pkg/util/safe_random"

	"github.com/golang-module/carbon/v2"
)

/**********************************************
* db.Limit(1).Find(&user), Find方法可以接口struct和slice的数据,可以避免 ErrRecordNotFound错误
*
***********************************************/
type (
	BoolEnum           uint32
	StatusType         uint32
	OperateType        uint32
	ClientType         uint32
	ExportLimit        uint32
	OperateAction      string
	CommonField        string
	PluckField         string
	GroupField         string
	SortField          string
	SortMethod         string
	CdnImg             string
	ExportTimeInterval int64
)

const (
	ClientTypeUnknown ClientType = iota
	ClientTypeAdmin              // 管理后台
	ClientTypeWeb                // web端

	StatusDefault       StatusType  = 1 // 启用(默认)
	StatusEnable        StatusType  = 1 // 启用
	StatusDisable       StatusType  = 2 // 禁用
	OperateTypeAdd      OperateType = 1 // 增加
	OperateTypeSubtract OperateType = 2 // 减少

	ActionOpen     OperateAction = "open"     // 打开-启用
	ActionClose    OperateAction = "close"    // 关闭-禁用
	ActionSubmit   OperateAction = "submit"   // 提交
	ActionCancel   OperateAction = "cancel"   // 取消
	ActionPass     OperateAction = "pass"     // 通过
	ActionReject   OperateAction = "reject"   // 驳回
	ActionAbandon  OperateAction = "abandon"  // 作废
	ActionConfirm  OperateAction = "confirm"  // 确认
	ActionRecover  OperateAction = "recover"  // 恢复
	ActionFinish   OperateAction = "finish"   // 完成
	ActionAdd      OperateAction = "add"      // 增加
	ActionSubtract OperateAction = "subtract" // 减少
	ActionSort     OperateAction = "sort"     // 设置排序
	ActionDel      OperateAction = "del"      // 删除

	PluckID       PluckField = "id"        // 主键id
	PluckUID      PluckField = "user_id"   // 用户id
	PluckOrderID  PluckField = "order_id"  // 订单id
	PluckEntityID PluckField = "entity_id" // 实体id
	PluckSpuID    PluckField = "spu_id"    // spu id
	PluckSkuID    PluckField = "sku_id"    // sku id

	GroupOrderID GroupField = "order_id" // 订单id
	GroupSkuID   GroupField = "sku_id"   // sku id

	SoftDelField   CommonField = "is_deleted" // 软删字段
	CreatedAtField CommonField = "created_at" // 创建时间
	UpdatedAtField CommonField = "updated_at" // 更新时间

	SortFieldID         SortField  = "id"          // 主键id
	SortFieldCreatedAt  SortField  = "created_at"  // 创建时间
	SortFieldUpdatedAt  SortField  = "updated_at"  // 更新时间
	SortFieldUseTime    SortField  = "use_time"    // 使用时间
	SortFieldExpireTime SortField  = "expire_time" // 过期时间
	SortFieldSort       SortField  = "sort"        // 排序字段sort
	SortMethodAsc       SortMethod = "asc"         // 正序
	SortMethodDesc      SortMethod = "desc"        // 倒序

	AttachmentNumLimit = 20
)

func (c CommonField) String() string {
	return string(c)
}

func (s SortMethod) String() string {
	return string(s)
}

func BoolToStatusType(b bool) StatusType {
	if b {
		return StatusEnable
	}
	return StatusDisable
}

const (
	RootUID                uint64 = 1
	AdminUID               uint64 = 2
	RoleRootID             uint64 = 1
	RoleAdminID            uint64 = 2
	RedisExpireTime15Min          = 60 * 15
	ResidExpireTime               = 60 * 60 * 24
	RedisExpireTimeWeek           = 60 * 60 * 24 * 7
	RedisExpireTimeMonth          = 60 * 60 * 24 * 30
	RedisExpireTimeYear           = 60 * 60 * 24 * 365
	RedisExpireTimeHour           = 60 * 60
	RedisExpireTimeTwoHour        = 60 * 60 * 2
	True                          = 1
	False                         = 0
	DepositCredisRatio            = 10
	Retry                         = 5
	DefaultPage                   = 1
	DefaultLimit                  = 10
	DefaultSize                   = 100
	TimeFormatFull                = "2006/01/02 15:04:05"
	TimeDateFormat                = "2006-01-02"
	TimeDateFormatFull            = "2006-01-02 15:04:05"
	TimeDateTimeIso8601           = "2006-01-02T15:04:05Z"
	TimeDateFormatAppress         = "20060102-150405"
	TimeDateFormatNumberic        = "20060102150405"

	WebExportTimeInterval   ExportTimeInterval = 60 * 60 * 24 * 31  // 一个月
	AdminExportTimeInterval ExportTimeInterval = 60 * 60 * 24 * 365 // 一年

	ExportLimitDefault ExportLimit = 1000
	ExportLimitReceipt ExportLimit = 100
)

var OperateActionMap = map[OperateAction]uint32{
	ActionOpen:     uint32(StatusEnable),
	ActionClose:    uint32(StatusDisable),
	ActionAdd:      uint32(OperateTypeAdd),
	ActionSubtract: uint32(OperateTypeSubtract),
}

type CommonSort struct {
	Field  SortField
	Method SortMethod
}

func GetRedisExpireTimeDefault(expireTime int64) time.Duration {
	return time.Second * time.Duration(expireTime)
}

// GetRedisExpireTime 随机过期时间
func GetRedisExpireTime(expireTime int64) time.Duration {
	return time.Second * time.Duration(expireTime+RandomRangeTime(100, 600))
}

// GetRedisEndOfDayExpireTime 今日截止过期时间
func GetRedisEndOfDayExpireTime() time.Duration {
	expireTime := carbon.Now().EndOfDay().Timestamp()
	return time.Second * time.Duration(expireTime-time.Now().Unix())
}

// RandomRangeTime 获取随机数
func RandomRangeTime(min, max int64) int64 {
	// 使用安全的随机数生成器
	return safe_random.Int63n(max-min) + min
}

type BaseModel struct {
	ID        uint64     `gorm:"column:id;primarykey" json:"id"`
	CreatedAt *LocalTime `json:"created_at" gorm:"column:created_at"`
	UpdatedAt *LocalTime `json:"updated_at" gorm:"column:updated_at"`
}

// 1. 创建time.Time类型的副本
type LocalTime time.Time

// 2. 重新 MarshalJSON 方法,在此方法中实现自定义格式的转换
func (t *LocalTime) MarshalJSON() ([]byte, error) {
	tlt := time.Time(*t)
	return []byte(fmt.Sprintf("\"%v\"", tlt.Format(TimeDateFormatFull))), nil
}

func (t *LocalTime) UnmarshalJSON(data []byte) error {
	if len(data) == 0 {
		return nil
	}
	// []byte to string
	tStr := string(data)
	// 判断是否有多余的引号
	if tStr[0] == '"' && tStr[len(tStr)-1] == '"' {
		tStr = tStr[1 : len(tStr)-1]
	}

	now, err := time.ParseInLocation(TimeDateFormatFull, tStr, time.Local)
	if err != nil {
		return err
	}
	*t = LocalTime(now)
	return nil
}

func (t LocalTime) Unix() int64 {
	return time.Time(t).Unix()
}

// 3. 实现Value方法: Value方法即在存储时调用,将该方法的返回值进行存储
// 写入数据库时会调用该方法将自定义时间类型转换并写入数据库
func (t LocalTime) Value() (driver.Value, error) {
	var zeroTime time.Time
	tlt := time.Time(t)
	if tlt.UnixNano() == zeroTime.UnixNano() {
		return nil, nil
	}
	return tlt, nil
}

// 4. 实现Scan方法：Scan方法可以实现在数据查询出来之前对数据进行相关操作
func (t *LocalTime) Scan(v interface{}) error {
	if value, ok := v.(time.Time); ok {
		*t = LocalTime(value)
		return nil
	}
	return fmt.Errorf("can not convert %v to timestamp", v)
}

func (t *LocalTime) Format(layout string) string {
	return time.Time(*t).Format(layout)
}

type ModelWithDel struct {
	BaseModel
	IsDeleted uint `json:"is_deleted" gorm:"column:is_deleted"`
}

func (s StatusType) Uint32() uint32 {
	return uint32(s)
}

func (s StatusType) IsEnable() bool {
	return s == StatusEnable
}

func (s StatusType) Reverse() StatusType {
	if s == StatusEnable {
		return StatusDisable
	}
	return StatusEnable
}

// GetColumnTag 获取字段的 tag
// deprecated: 使用 GetColumnName
func GetColumnTag(structPtr interface{}, fieldValue interface{}) string {
	var (
		fieldName = ""
		tag       = ""
	)

	structVal := reflect.ValueOf(structPtr).Elem()
	structType := structVal.Type()

	// 遍历结构体的所有字段
	for i := 0; i < structVal.NumField(); i++ {
		field := structVal.Field(i)

		if field.Kind() == reflect.Struct && field.Addr().Type() != reflect.TypeOf(fieldValue) {
			// 递归处理嵌套结构体
			if res := GetColumnTag(field.Addr().Interface(), fieldValue); res != "" {
				return res
			}
		} else if reflect.DeepEqual(field.Interface(), fieldValue) {
			fieldType := structType.Field(i)
			fieldName = fieldType.Name
			if fieldName == "" {
				return ""
			}
			tag = fieldType.Tag.Get("gorm")

			break
		}
	}

	if tag == "" {
		return ""
	}

	// Split the tag by semicolon to get individual attributes
	attributes := strings.Split(tag, ";")
	for _, attribute := range attributes {
		// Check if the attribute contains "column"
		if strings.HasPrefix(attribute, "column:") {
			return strings.TrimPrefix(attribute, "column:")
		}
	}
	return ""
}

// GetColumnName 获取结构体字段的 GORM column 名称
func GetColumnName(s interface{}, fieldName string) string {
	// 获取传入结构体的类型
	v := reflect.ValueOf(s)
	t := reflect.TypeOf(s)

	// 验证输入是否为结构体指针
	if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
		return ""
	}

	// 获取字段
	field, ok := t.Elem().FieldByName(fieldName)
	if !ok {
		return ""
	}

	// 获取 gorm 标签
	gormTag := field.Tag.Get("gorm")
	if gormTag == "" {
		return ""
	}

	// 解析 gorm 标签获取 column 名称
	tagParts := strings.Split(gormTag, ";")
	for _, part := range tagParts {
		if strings.HasPrefix(part, "column:") {
			return strings.TrimPrefix(part, "column:")
		}
	}

	return ""
}

func GetDefaultSort(sort CommonSort) string {
	orderStr := "id desc"
	if sort.Field != "" && sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", sort.Field, sort.Method, orderStr)
	}
	return orderStr
}

func GetDefaultSortAlias(sort CommonSort, alias string) string {
	if alias == "" {
		return GetDefaultSort(sort)
	}

	orderStr := fmt.Sprintf("%s.id desc", alias)
	if sort.Field != "" && sort.Method != "" {
		orderStr = fmt.Sprintf("%s.%s %s, %s", alias, sort.Field, sort.Method, orderStr)
	}
	return orderStr
}
