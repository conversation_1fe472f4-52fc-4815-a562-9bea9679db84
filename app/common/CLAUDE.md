# app/common 目录规范文档

本文档记录 `app/common` 目录下公共组件的代码规范和最佳实践。

## 目录结构

```
app/common/
├── dbs/        # 数据库相关公共组件
│   ├── common.go   # 数据库公共定义（常量、类型、辅助函数）
│   ├── env.go      # 环境配置定义
│   ├── mysql.go    # MySQL 连接管理
│   └── uuid.go     # 分布式 ID 生成器
└── srv/        # 服务相关公共组件
    └── common.go   # 服务管理（优雅关闭等）
```

## 核心组件规范

### 数据库管理 (dbs/)

#### MySQL 连接管理 (`mysql.go`)

##### 主从架构设计
```go
// MysqlEngines 一主多从数据库架构
type MysqlEngines struct {
    Master *gorm.DB  // 主库（写操作）
    Slave1 *gorm.DB  // 从库1（读操作）
    Slave2 *gorm.DB  // 从库2（读操作）
    Slave3 *gorm.DB  // 从库3（读操作）
}
```

##### 单例模式实现
```go
var (
    defaultEngines         *MysqlEngines
    defaultEnginesInitOnce sync.Once
)

func NewMysqlEngines() *MysqlEngines {
    if defaultEngines == nil {
        defaultEnginesInitOnce.Do(func() {
            defaultEngines = newEngines()
        })
    }
    return defaultEngines
}
```

##### 读写分离使用
```go
// 根据操作类型选择数据库
func (rw *MysqlEngines) Use(userMaster bool) *gorm.DB {
    if userMaster {
        return rw.Master  // 写操作使用主库
    } else {
        return rw.Slave1  // 读操作使用从库
    }
}

// 支持 gin.Context
func (rw *MysqlEngines) UseWithGinCtx(ctx *gin.Context, userMaster bool) *gorm.DB

// 支持 context.Context
func (rw *MysqlEngines) UseWithCtx(ctx context.Context, userMaster bool) *gorm.DB
```

##### 连接池配置
```go
sqlDb.SetMaxIdleConns(32)       // 最大空闲连接数
sqlDb.SetMaxOpenConns(100)      // 最大打开连接数
sqlDb.SetConnMaxLifetime(time.Hour)  // 连接最大生命周期
```

##### 自定义 GORM Logger
```go
type CustomGormLogger struct{}

// 实现 GORM logger 接口
func (c *CustomGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
    elapsed := time.Since(begin)
    sql, rows := fc()
    fields := logrus.Fields{
        "sql":     sql,
        "rows":    rows,
        "elapsed": float64(elapsed.Nanoseconds()) / 1e6,  // 毫秒
    }
    // 记录 SQL 执行日志
}
```

#### 公共定义 (`common.go`)

##### 类型定义
```go
type (
    BoolEnum           uint32
    StatusType         uint32
    OperateType        uint32
    ClientType         uint32
    ExportLimit        uint32
    OperateAction      string
    CommonField        string
    PluckField         string
    GroupField         string
    SortField          string
    SortMethod         string
    CdnImg             string
    ExportTimeInterval int64
)
```

##### 业务常量
```go
const (
    // 状态常量
    StatusDefault       StatusType  = 1  // 启用(默认)
    StatusEnable        StatusType  = 1  // 启用
    StatusDisable       StatusType  = 2  // 禁用
    
    // 操作类型
    OperateTypeAdd      OperateType = 1  // 增加
    OperateTypeSubtract OperateType = 2  // 减少
    
    // 操作动作
    ActionOpen     OperateAction = "open"     // 打开-启用
    ActionClose    OperateAction = "close"    // 关闭-禁用
    ActionSubmit   OperateAction = "submit"   // 提交
    ActionCancel   OperateAction = "cancel"   // 取消
    ActionPass     OperateAction = "pass"     // 通过
    ActionReject   OperateAction = "reject"   // 驳回
)
```

##### 时间格式常量
```go
const (
    TimeFormatFull         = "2006/01/02 15:04:05"
    TimeDateFormat         = "2006-01-02"
    TimeDateFormatFull     = "2006-01-02 15:04:05"
    TimeDateTimeIso8601    = "2006-01-02T15:04:05Z"
    TimeDateFormatAppress  = "20060102-150405"
    TimeDateFormatNumberic = "20060102150405"
)
```

##### Redis 过期时间常量
```go
const (
    RedisExpireTime15Min   = 60 * 15
    ResidExpireTime        = 60 * 60 * 24      // 1天
    RedisExpireTimeWeek    = 60 * 60 * 24 * 7  // 1周
    RedisExpireTimeMonth   = 60 * 60 * 24 * 30 // 1月
    RedisExpireTimeYear    = 60 * 60 * 24 * 365 // 1年
    RedisExpireTimeHour    = 60 * 60           // 1小时
    RedisExpireTimeTwoHour = 60 * 60 * 2       // 2小时
)
```

##### 基础模型定义
```go
// BaseModel 基础模型
type BaseModel struct {
    ID        uint64     `gorm:"column:id;primarykey" json:"id"`
    CreatedAt *LocalTime `json:"created_at" gorm:"column:created_at"`
    UpdatedAt *LocalTime `json:"updated_at" gorm:"column:updated_at"`
}

// ModelWithDel 带软删除的模型
type ModelWithDel struct {
    BaseModel
    IsDeleted uint `json:"is_deleted" gorm:"column:is_deleted"`
}
```

##### LocalTime 自定义时间类型
```go
// LocalTime 自定义时间类型，用于统一 JSON 序列化格式
type LocalTime time.Time

// MarshalJSON 自定义 JSON 序列化
func (t *LocalTime) MarshalJSON() ([]byte, error) {
    tlt := time.Time(*t)
    return []byte(fmt.Sprintf("\"%v\"", tlt.Format(TimeDateFormatFull))), nil
}

// UnmarshalJSON 自定义 JSON 反序列化
func (t *LocalTime) UnmarshalJSON(data []byte) error

// Value 实现 driver.Valuer 接口（写入数据库）
func (t LocalTime) Value() (driver.Value, error)

// Scan 实现 sql.Scanner 接口（从数据库读取）
func (t *LocalTime) Scan(v interface{}) error
```

##### 辅助函数

###### Redis 过期时间
```go
// GetRedisExpireTime 获取带随机扰动的过期时间（防止缓存雪崩）
func GetRedisExpireTime(expireTime int64) time.Duration {
    return time.Second * time.Duration(expireTime+RandomRangeTime(100, 600))
}

// GetRedisExpireTimeDefault 获取默认过期时间
func GetRedisExpireTimeDefault(expireTime int64) time.Duration {
    return time.Second * time.Duration(expireTime)
}

// GetRedisEndOfDayExpireTime 获取到今日结束的过期时间
func GetRedisEndOfDayExpireTime() time.Duration {
    expireTime := carbon.Now().EndOfDay().Timestamp()
    return time.Second * time.Duration(expireTime-time.Now().Unix())
}
```

###### 字段标签获取
```go
// GetColumnName 获取结构体字段的 GORM column 名称
func GetColumnName(s interface{}, fieldName string) string {
    // 通过反射获取 gorm 标签中的 column 名称
    field, ok := t.Elem().FieldByName(fieldName)
    if !ok {
        return ""
    }
    // 解析 gorm 标签
    gormTag := field.Tag.Get("gorm")
    // 提取 column: 部分
}
```

###### 排序辅助
```go
// CommonSort 通用排序结构
type CommonSort struct {
    Field  SortField
    Method SortMethod
}

// GetDefaultSort 获取默认排序（id desc）
func GetDefaultSort(sort CommonSort) string {
    orderStr := "id desc"
    if sort.Field != "" && sort.Method != "" {
        orderStr = fmt.Sprintf("%s %s, %s", sort.Field, sort.Method, orderStr)
    }
    return orderStr
}

// GetDefaultSortAlias 带表别名的排序
func GetDefaultSortAlias(sort CommonSort, alias string) string
```

#### 环境管理 (`env.go`)

```go
type AppEnv string

const (
    EnvDevelop AppEnv = "develop"  // 开发环境
    EnvTest    AppEnv = "test"     // 测试环境
    EnvProd    AppEnv = "prod"     // 生产环境
)

// 环境判断函数
func IsDevelop(env string) bool { return env == string(EnvDevelop) }
func IsTest(env string) bool    { return env == string(EnvTest) }
func IsProd(env string) bool    { return env == string(EnvProd) }
```

#### 分布式 ID 生成器 (`uuid.go`)

基于 Twitter Snowflake 算法实现：

```go
var node *snowflake.Node

func init() {
    node, _ = snowflake.NewNode(getNodeId())
}

// Generate 生成唯一 ID
func Generate() uint64 {
    return uint64(node.Generate())
}

// getNodeId 基于 IP 和进程 ID 生成节点 ID
func getNodeId() int64 {
    ip := GetIP()
    idStr := strings.Replace(ip.String(), ".", "", -1) + strconv.Itoa(os.Getpid())
    id, err := strconv.ParseInt(idStr, 10, 64)
    if err != nil {
        return 1
    }
    return id % 1024  // 节点 ID 范围：0-1023
}
```

### 服务管理 (srv/)

#### 优雅关闭 (`common.go`)

```go
// GracefullyShutdown 优雅关闭服务
func GracefullyShutdown(ctx context.Context, appSrvMgr *helper.AppSrvMgr) {
    quit := make(chan os.Signal, 1)
    // 监听系统信号
    signal.Notify(quit, syscall.SIGQUIT, syscall.SIGTERM)
    <-quit
    
    // 关闭所有服务
    if err := appSrvMgr.Close(ctx); err != nil {
        fmt.Printf("Failed to close services: %v\n", err)
        return
    }
    
    log.WithContext(ctx).Info("Server exiting")
}
```

## 使用规范

### 数据库操作

#### 读写分离原则
```go
// 读操作：使用从库
db := MysqlEngine.UseWithGinCtx(ctx, false)

// 写操作：使用主库
db := MysqlEngine.UseWithGinCtx(ctx, true)

// 事务操作：必须使用主库
tx := MysqlEngine.UseWithGinCtx(ctx, true).Begin()
```

#### 上下文传递
```go
// 优先使用 gin.Context
db := MysqlEngine.UseWithGinCtx(ctx, true)

// 其次使用 context.Context
db := MysqlEngine.UseWithCtx(ctx.Request.Context(), true)

// 最后使用默认
db := MysqlEngine.Use(true)
```

### 时间处理

#### 使用 LocalTime 类型
```go
type Model struct {
    CreatedAt *LocalTime `json:"created_at"`
    UpdatedAt *LocalTime `json:"updated_at"`
}
```

#### 时间格式化
```go
// 格式化为标准格式
timeStr := model.CreatedAt.Format(dbs.TimeDateFormatFull)

// Unix 时间戳
timestamp := model.CreatedAt.Unix()
```

### Redis 过期时间

#### 防止缓存雪崩
```go
// 使用带随机扰动的过期时间
expireTime := dbs.GetRedisExpireTime(dbs.ResidExpireTime)

// 设置到今日结束
expireTime := dbs.GetRedisEndOfDayExpireTime()
```

### ID 生成

#### 使用分布式 ID
```go
// 生成唯一 ID
id := dbs.Generate()
```

## 最佳实践

### 1. 单例模式
- 数据库连接使用单例模式，避免重复创建连接
- 使用 `sync.Once` 确保线程安全

### 2. 连接池管理
- 合理配置连接池大小
- 设置连接最大生命周期，避免长连接问题

### 3. 日志记录
- 使用自定义 Logger 记录 SQL 执行情况
- 包含执行时间、影响行数等关键信息

### 4. 错误处理
- 数据库错误应当记录详细日志
- 避免将数据库错误直接暴露给用户

### 5. 性能优化
- 读写分离，读操作走从库
- 合理使用缓存过期时间的随机扰动
- 批量操作优于循环单个操作

### 6. 安全考虑
- 使用安全的随机数生成器（safe_random）
- 避免 SQL 注入，使用参数化查询

## 常用模式

### 状态管理
```go
// 状态转换
func (s StatusType) Reverse() StatusType {
    if s == StatusEnable {
        return StatusDisable
    }
    return StatusEnable
}

// 状态判断
func (s StatusType) IsEnable() bool {
    return s == StatusEnable
}
```

### 操作映射
```go
var OperateActionMap = map[OperateAction]uint32{
    ActionOpen:     uint32(StatusEnable),
    ActionClose:    uint32(StatusDisable),
    ActionAdd:      uint32(OperateTypeAdd),
    ActionSubtract: uint32(OperateTypeSubtract),
}
```

### 软删除处理
```go
// 查询时排除软删除
query.Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

// 软删除标记
model.IsDeleted = 1
```

## 注意事项

### 1. 主从同步延迟
- 写后立即读的场景必须使用主库
- 关键业务查询考虑使用主库

### 2. 事务边界
- 事务应在 Service 层管理
- DAO 层提供 WithTx 方法支持事务

### 3. 并发安全
- 单例初始化使用 sync.Once
- 分布式 ID 生成器线程安全

### 4. 资源管理
- 确保数据库连接正确释放
- 使用优雅关闭机制

### 5. 监控告警
- 记录慢查询日志
- 监控连接池状态
- 关注主从同步延迟

## 环境差异

### 开发环境
- 可以输出详细的 SQL 日志
- 较短的缓存过期时间

### 测试环境
- 模拟生产环境配置
- 完整的主从架构

### 生产环境
- 优化的连接池配置
- 合理的缓存策略
- 完善的监控告警

## 扩展指南

### 添加新的公共组件

1. 在 `app/common` 下创建新目录
2. 实现单例模式（如需要）
3. 提供清晰的接口定义
4. 编写单元测试
5. 更新本文档

### 修改数据库配置

1. 更新 `config/` 目录下的配置文件
2. 调整连接池参数
3. 测试主从同步
4. 监控性能影响

## 故障排查

### 数据库连接问题
- 检查配置文件中的连接参数
- 验证网络连通性
- 查看连接池状态

### 主从同步延迟
- 监控主从延迟时间
- 考虑强制读主库
- 优化查询性能

### ID 生成冲突
- 检查节点 ID 是否唯一
- 验证时钟同步
- 查看 Snowflake 算法配置